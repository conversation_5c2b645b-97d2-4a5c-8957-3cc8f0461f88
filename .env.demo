# 环境
NODE_ENV=development

# 接口前缀
VITE_API_BASEPATH=base

# 打包路径
VITE_BASE_PATH=/

# 是否sourcemap
VITE_SOURCEMAP=true

# 标题
VITE_APP_TITLE=ERP

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 是否开启gzip
VITE_GZIP=true

# 输出路径
VITE_OUT_DIR=erp-demo

VITE_SERVER_URL= http://**************:58088 # http://win.cbgo.top:5000 #http://erp.nibinu.com:58088   erp.mbtoys.net
VITE_WS_URL=wss://demo-erp.mbtoys.net:4041   #wss://erp.nibinu.com:4041
VITE_SERVER_TYPE='ws'  #服务器连接协议   ws  http


VITE_TEST_MODE = demo
#识别HKB 目录并导出json
import json
from bs4 import BeautifulSoup

with open('tree.html','r',encoding='utf8') as f:
    html = f.read()

soup = BeautifulSoup(html, 'html.parser')

count = 0

def parse_tree(node):
    global count
    name = node.find('div').text
    tree = {"name": name, "children": []} 
    print(name)
    count = count+1
    check = node.find('ul')
    if check == None:
        return tree
    for child in node.find('ul'):
        if child == '\n':
            continue
        ret = parse_tree(child)
        if ret != None:
            tree['children'].append(ret)
    return tree


root = soup.find('li')
data = parse_tree(root)

# json_string = json.dumps(data, ensure_ascii=False)
# print(json_string)
with open('tree.json', 'w', encoding='utf8')as f:
    json.dump(data, f, ensure_ascii=False, indent=4)



print('保存完毕!','共：',count,'个节点')
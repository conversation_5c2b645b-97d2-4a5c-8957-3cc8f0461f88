<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <!-- import CSS -->
    <link rel="stylesheet" href="indexvue.css">
    <style>
        .el-table th.el-table__cell {
            background-color: #8a8ac3 !important;
            color: white !important;
        }
    </style>
</head>

<body>
    <div id="app">
        <template>
            <div>
                <!-- <el-table :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
                    default-expand-all :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                    <el-table-column prop="date" label="日期" sortable width="180">
                    </el-table-column>
                    <el-table-column prop="name" label="姓名" sortable width="180">
                    </el-table-column>
                    <el-table-column prop="address" label="地址">
                    </el-table-column>
                </el-table> -->
                <el-table :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="pdt_data.id" border
                    default-expand-all :tree-props="{ children: 'sub_bom'}" :header-row-style="{background: 'blue', color: 'white'}">
                    <el-table-column prop="pdt_data.nick" label="产品名称"   min-width="350"></el-table-column>
                    <el-table-column prop="pdt_level" label="层级" width="70"></el-table-column>
                    <el-table-column prop="pdt_data.name" label="物料编码" show-overflow-tooltip width="130">
                        <template #default="scope">
                            {{ scope.row.pdt_data.pdt_name ==
                            undefined?scope.row.pdt_data.name:scope.row.pdt_data.pdt_name }}
                        </template>
                    </el-table-column>
                    <el-table-column label="规格" show-overflow-tooltip max-width="230">
                        <template #default="scope">
                            <el-tooltip
                                v-if="scope.row.pdt_data.specs_name != '' && scope.row.pdt_data.specs_name != undefined"
                                class="box-item" effect="dark" :content="scope.row.pdt_data.specs_text"
                                placement="bottom">
                                <el-tag
                                    style="white-space: normal;max-width: 300px;overflow: hidden;text-overflow: ellipsis;"
                                    type="success" effect="dark">{{
                                    scope.row.pdt_data.specs_name=='自定义规格'?scope.row.pdt_data.specs_text:scope.row.pdt_data.specs_name
                                    }}</el-tag>

                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column prop="pdt_data.用量" label="用量" show-overflow-tooltip width="100" ></el-table-column>
                    <el-table-column prop="pdt_data.单价" label="单价" show-overflow-tooltip width="100" ></el-table-column>
                    <el-table-column prop="pdt_data.总价" label="总价" show-overflow-tooltip width="100" ></el-table-column>
                    <el-table-column prop="pdt_data.base_unit" label="单位" show-overflow-tooltip width="100" ></el-table-column>
                    <el-table-column prop="pdt_data.损耗率" label="损耗率" show-overflow-tooltip width="100" ></el-table-column>
                    <el-table-column prop="pdt_data.remark" label="备注" show-overflow-tooltip ></el-table-column>
                </el-table>


            </div>
        </template>
    </div>

</body>
<!-- import Vue before Element -->
<script src="vue.js"></script>
<!-- import JavaScript -->
<script src="indexvue.js"></script>
<script>
    var msg =  localStorage.getItem('bom')
    var param = JSON.parse(msg)
    console.log('--------',[param])

    new Vue({
        el: '#app',
        data: function () {
            return {
                tableData: [param]
            }
        },
        methods: {
        
        },
    })
</script>

</html>
import { defineStore } from 'pinia'
import { asyncRouterMap, constantRouterMap, getAllNoQXRoute } from '@/router'
import { generateRoutesFn1, generateRoutesFn2, flatMultiLevelRoutes } from '@/utils/routerHelper'
import { store } from '../index'
import { cloneDeep } from 'lodash-es'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { UserType } from '@/api/login/types'

const { wsCache } = useCache()
const appStore = useAppStore()

export interface PermissionState {
  routers: AppRouteRecordRaw[]
  addRouters: AppRouteRecordRaw[]
  isAddRouters: boolean
  menuTabRouters: AppRouteRecordRaw[]
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routers: [],
    addRouters: [],
    isAddRouters: false,
    menuTabRouters: []
  }),
  getters: {
    getRouters(): AppRouteRecordRaw[] {
      return this.routers
    },
    getAddRouters(): AppRouteRecordRaw[] {
      return flatMultiLevelRoutes(cloneDeep(this.addRouters))
    },
    getIsAddRouters(): boolean {
      return this.isAddRouters
    },
    getMenuTabRouters(): AppRouteRecordRaw[] {
      return this.menuTabRouters
    }
  },
  actions: {
    generateRoutes(
      srcRouterMap:AppRouteRecordRaw[],
      routers?: AppCustomRouteRecordRaw[] | string[]
    ): Promise<unknown> {
      return new Promise<void>((resolve) => {        
        let routerMap: AppRouteRecordRaw[] = []

        const userinfo:UserType = wsCache.get(appStore.getUserInfo)
        //暂时所有人都动态路由权限
        // if(userinfo.role == "admin") //管理员全部权限
        // {
        //   // 直接读取静态路由表
        //   routerMap = cloneDeep(srcRouterMap)
        // }
        // else
        // {
          console.log("动态路由")
          //routers?.push('/monthlycheck/rrtest')

         //追加所有权限
         const arrayAll = getAllNoQXRoute()
         arrayAll.forEach((one) => {
           routers?.push(one.test)
         })



          routerMap = generateRoutesFn1(cloneDeep(srcRouterMap), routers as string[])
        //}
          //修改主模块默认跳转到有权限的第一个页面
          for(const one of routerMap)
          {
            if(one.children && one.children.length > 0)
            {
              one.redirect = one.path+'/'+one.children[0].path
            }
          }

          //测试
          if(routerMap.length>0)
            {
              routerMap[0].children?.push(...arrayAll)
              // console.log('--------add;',routerMap[0].name)
              // routerMap[0].children?.push({
              //   path: 'rrtest',
              //   component: () => import('@/views/FinanceManage/rrtest.vue'),
              //   name: 'rrtest',
              //   meta: {
              //     title:'rrtest',
              //     noTagsView: true,
              //     noCache: true,
              //     hidden: true,
              //     canTo: true,
              //   }
              // })
            }
         


        // 动态路由，404一定要放到最后面
        this.addRouters = routerMap.concat([
          {
            path: '/:path(.*)*',
            redirect: '/404',
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ])
        // 渲染菜单的所有路由
        this.routers = cloneDeep(constantRouterMap).concat(routerMap)
        resolve()
      })
    },
    setIsAddRouters(state: boolean): void {
      this.isAddRouters = state
    },
    setMenuTabRouters(routers: AppRouteRecordRaw[]): void {
      this.menuTabRouters = routers
    }
  }
})

export const usePermissionStoreWithOut = () => {
  return usePermissionStore(store)
}

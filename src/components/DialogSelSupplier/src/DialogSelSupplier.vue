<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getSupplierListApi } from '@/api/customer'
import { setHotPoint } from '@/api/extra';

const { t } = useI18n()
const show = ref(false);

const supplierData = reactive([]) //人员列表
//查询条件
const searchCondition = reactive({
  supplier_name: '',  //名称
  supplier_nick: '',  //编号
  corp_nick: '',   //公司名称
  _or:true,
  page: 1,
  count: 10,
  inputTxt:'' //界面输入
})

//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getSupplierData()
  }
})

//修改输入后同步其他条件
watch(searchCondition,(newData)=>{
  searchCondition.supplier_name = newData.inputTxt
  searchCondition.supplier_nick = newData.inputTxt
  searchCondition.corp_nick = newData.inputTxt
})

//查询用户列表
const getSupplierData = async (page = 1) => {  
  searchCondition.page = page
  const ret = await getSupplierListApi(searchCondition);
  if (ret) {
    console.log(ret)
    supplierData.splice(0, supplierData.length, ...ret.data);
    totleCount.value =  parseInt(ret.count)
  }
}
//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getSupplierData()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getSupplierData(val)
}

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
  console.log(currentRow.value)
  if(currentRow.value?.id == undefined)
  {
    ElMessage.warning('请选择一条数据')
    return
  }
   
  closeDlg()
  //返回数据给上层
  
  emit('onSubmit',currentRow.value?.id,currentRow.value.supplier_name,currentRow.value.supplier_nick,currentRow.value.tax_type,currentRow.value.tax_rate,currentRow.value)
  setHotPoint(
    {
      path_name: '/erp/supplier/list',
      hot_id: currentRow.value?.id
    }
  )
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}


</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-input style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputNameOrID')" v-model="searchCondition.inputTxt" />
      <el-button type="primary" @click="getSupplierData(1)">{{ t('button.search') }}</el-button>
    </div>
    <el-table  ref="tableRef" :data="supplierData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row @row-dblclick="onSubmit" @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip fixed prop="supplier_name" :label="t('supplier.name')"  />
      <el-table-column show-overflow-tooltip fixed prop="supplier_nick" :label="t('supplier.nick')"  />
      <el-table-column show-overflow-tooltip fixed prop="phone" :label="t('customer.connector_phone')"  >
        <template #default="scope">
          <div >{{ scope.row.corp_linkman.length>0?scope.row.corp_linkman[0].phone:'' }}</div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handlePageSizeChange"
        @current-change="handlePageCurrentChange"
    />
    <template #footer>
      <ElButton type="primary" @click="onSubmit">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
</style>
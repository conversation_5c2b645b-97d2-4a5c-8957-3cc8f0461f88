<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getParterListApi } from '@/api/customer'
import { setHotPoint } from '@/api/extra';

const { t } = useI18n()
const show = ref(false);

const ParterData = reactive([]) //人员列表
//查询条件
const searchCondition = reactive({
  parter_name: '',  //名称
  parter_nick: '',  //编号
  corp_nick: '',   //公司名称
  _or:true,
  page: 1,
  count: 10,
  inputTxt:'' //界面输入
})

//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getParterData()
  }
})

//修改输入后同步其他条件
watch(searchCondition,(newData)=>{
  searchCondition.parter_name = newData.inputTxt
  searchCondition.parter_nick = newData.inputTxt
  searchCondition.corp_nick = newData.inputTxt
})

//查询用户列表
const getParterData = async (page = 1) => {
  searchCondition.page = page
  const ret = await getParterListApi(searchCondition);
  if (ret) {
    console.log(ret)
    ParterData.splice(0, ParterData.length, ...ret.data);
    totleCount.value =  parseInt(ret.count)
  }
}
//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getParterData()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getParterData(val)
}

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
  console.log(currentRow.value)
  if(currentRow.value?.id == undefined)
  {
    ElMessage.warning('请选择一条数据')
    return
  }
   
  closeDlg()
  //返回数据给上层
  
  emit('onSubmit',currentRow.value?.id,currentRow.value.parter_name,currentRow.value)
  setHotPoint(
    {
      path_name: '/erp/parter/list',
      hot_id: currentRow.value?.id
    }
  )
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}


</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-input style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputNameOrID')" v-model="searchCondition.inputTxt" />
      <el-button type="primary" @click="getParterData(1)">{{ t('button.search') }}</el-button>
    </div>
    <el-table  ref="tableRef" :data="ParterData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row @row-dblclick="onSubmit" @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip fixed prop="parter_name" :label="t('parter.name')"  />
      <el-table-column show-overflow-tooltip fixed prop="parter_nick" :label="t('parter.nick')"  />
      <el-table-column show-overflow-tooltip fixed prop="phone" :label="t('customer.connector_phone')"  >
        <template #default="scope">
          <div >{{ scope.row.corp_linkman.length>0?scope.row.corp_linkman[0].phone:'' }}</div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination class="flex justify-end"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handlePageSizeChange"
        @current-change="handlePageCurrentChange"
    />
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
</style>
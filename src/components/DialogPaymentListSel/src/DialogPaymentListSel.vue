<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getSupplierListApi } from '@/api/customer'
import { setHotPoint } from '@/api/extra';
import { DialogSelMonthCheck } from '@/components/DialogSelMonthCheck'


const { t } = useI18n()
const show = ref(false);
const order_list = reactive([])

const supplierData = reactive([]) //人员列表
//查询条件
const searchCondition = reactive({
  page: 1,
  count: 10,
  inputTxt:'' //界面输入
})

//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
  type: string,
  order_list: any[]
  dir_id:string,   //采购商或者委外商ID
 }>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    Object.assign(order_list, props.order_list)
  }
})


//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
   
  closeDlg()
  //返回数据给上层
  
  emit('onSubmit',order_list)
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

//选择采购月结
const showMonthCheckBuy = ref(false)
const onSelMonthCheckBuy = () => {
  showMonthCheckBuy.value = true
}
const onSelCheckBuyCallback = (data) => {
  console.log(data)
  showMonthCheckBuy.value = false
  if (props.type == '采购')
  {
    //检测是否存在，不存在则加到列表
    let find = order_list.find(item => item.关联单号 === data.buy_paybill_num)
    if (find == undefined)
    {
      let one = {
        "供应商": data.supplier_nick,
        "关联类型": "采购月结单",
        "关联单号": data.buy_paybill_num,
        "月份": data.paybill_date,
        "申请金额": data.应付金额-data.已付金额
      }
      order_list.push(one)
      console.log('加入')
    }
  }
  else  
  {
      //检测是否存在，不存在则加到列表
      let find = order_list.find(item => item.关联单号 === data.oem_paybill_num)
      if (find == undefined)
      {
        let one = {
          "供应商": data.parter_nick,
          "关联类型": "委外月结单",
          "关联单号": data.oem_paybill_num,
          "月份": data.paybill_date,
          "申请金额": data.应付金额-data.已付金额
        }
        order_list.push(one)
        console.log('加入')
      }
  }

}


</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-button v-if="props.type=='采购'" type="primary" disabled>关联采购单</el-button>
      <el-button v-if="props.type=='采购'" type="primary" @click="onSelMonthCheckBuy">关联月结单</el-button>
      <el-button v-if="props.type=='委外'" type="primary" disabled>关联委外单</el-button>
      <el-button v-if="props.type=='委外'" type="primary" @click="onSelMonthCheckBuy">关联月结单</el-button>
    </div>
    <el-table  ref="tableRef" :data="order_list" style="width: 100%; margin-bottom: 20px" row-key="guuid" border     
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip fixed prop="" label="序号"  >
        <template #default="scope">
          <span>{{ scope.$index+1 }}</span>
        </template>      
      </el-table-column>
      <el-table-column show-overflow-tooltip fixed prop="供应商" label="供应商"/>
      <el-table-column show-overflow-tooltip fixed prop="关联类型" label="关联类型"/>
      <el-table-column show-overflow-tooltip fixed prop="关联单号" label="关联单号"/>
      <el-table-column show-overflow-tooltip fixed prop="月份" label="月份"/>
      <el-table-column show-overflow-tooltip fixed prop="申请金额" label="申请金额"/>
    </el-table>
    <!-- <el-pagination
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handlePageSizeChange"
        @current-change="handlePageCurrentChange"
    /> -->
    <template #footer>
      <ElButton type="primary" @click="onSubmit">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
    </template>

    <DialogSelMonthCheck v-model:show="showMonthCheckBuy" :dir_id="props.dir_id" :type="props.type"  :title="t('purchase.sel_supplier')" @on-submit="onSelCheckBuyCallback"/>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
</style>
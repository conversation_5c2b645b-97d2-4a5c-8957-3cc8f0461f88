<script setup lang="ts">
import { ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useCache } from '@/hooks/web/useCache'
import { resetRouter } from '@/router'
import { useRouter } from 'vue-router'
import { loginOutApi } from '@/api/login'
import { useDesign } from '@/hooks/web/useDesign'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { reactive, ref, onMounted } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { DialogUserInfo } from "@/components/DialogUserInfo";

const tagsViewStore = useTagsViewStore()
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('user-info')
const { t } = useI18n()
const { wsCache } = useCache()
const { replace } = useRouter()
const appStore = useAppStore()

const loginOut = () => {
  ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    type: 'warning'
  })
    .then(async () => {
      //暂时走直接退出流程
      // const res = await loginOutApi().catch(() => {})
      // if (res) {
      //   wsCache.clear()
      //   tagsViewStore.delAllViews()
      //   resetRouter() // 重置静态路由表
      //   replace('/login')
      // }
        wsCache.clear()
        tagsViewStore.delAllViews()
        resetRouter() // 重置静态路由表
        replace('/login')
        // location.reload()
    })
    .catch(() => {})
}

const userinfo = reactive({})
onMounted(()=>{
  const info = wsCache.get(appStore.getUserInfo)
  Object.assign(userinfo, info)
})

const toDocument = () => {
  window.open('https://element-plus-admin-doc.cn/')
}

const showChangeInfo = ref(false)
const changeInfo = ()=>{
  showChangeInfo.value  = true
  console.log('1111111',showChangeInfo.value)
}
</script>

<template>
  <div>
    <ElDropdown :class="prefixCls" trigger="click">
      <div class="flex items-center">
        <img
          src="@/assets/imgs/avatar.jpg"
          alt=""
          class="w-[calc(var(--logo-height)-25px)] rounded-[50%]"
        />
        <span class="<lg:hidden text-14px pl-[5px] text-[var(--top-header-text-color)] font-bold">{{ userinfo.resident_name }}</span>
      </div>
      <template #dropdown>
        <ElDropdownMenu>
          <!-- <ElDropdownItem>
            <div @click="toDocument">{{ t('common.document') }}</div>
          </ElDropdownItem> -->
          <ElDropdownItem divided>
            <div @click="changeInfo">修改密码</div>
          </ElDropdownItem>
          <ElDropdownItem divided>
            <div @click="loginOut">{{ t('common.loginOut') }}</div>
          </ElDropdownItem>
        </ElDropdownMenu>
      </template>

      
    </ElDropdown>
    <DialogUserInfo v-model:show="showChangeInfo"/>
  </div>

</template>

<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { PropType } from 'vue'
import {RightMenuItem} from '@/types/rightMenu.d'
import { ElDivider } from 'element-plus';

const props = defineProps({
  menuDate: {
    type: Array as PropType<RightMenuItem[]>,
    default: () => []
  },
})

const emit = defineEmits(['onMenuEvent'])

//回调菜单消息给上层
const onMenuEvent = (item: RightMenuItem) => {
  emit('onMenuEvent', item.name)
}


</script>

<template>
  <!-- 右键菜单 -->
  <div id="contextmenu" v-show="false" class="menu font-bold">
    <div v-for="(item, index) in menuDate" :key="index" class="contextmenu_item divide-x-1"
      @click="onMenuEvent(item)">
      <Icon v-show="item.name == '分割线' ? false : true" :icon="item.icon" />
      <div v-show="item.name == '分割线' ? false : true" class="ml-1 pl-3">{{ item.title }}</div>
      <el-divider class="!m-1" v-show="item.name == '分割线' ? true : false" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.menu {
  position: absolute;
  background-color: #fff;
  width: 150px;
  /*height: 106px;*/
  font-size: 12px;
  color: #444040;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  white-space: nowrap;
  z-index: 1000;
}

.contextmenu_item {
  display: flex;
  line-height: 34px;
  text-align: center;
  align-items: center;
  justify-content: left;
  padding-left: 10px;
}

// .contextmenu_item:not(:last-child) {
//   border-bottom: 1px solid rgba(0, 0, 0, 0.1);
// }

.contextmenu_item:hover {
  cursor: pointer;
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}
</style>

<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElMessage,ElMessageBox,ElDescriptions,ElDescriptionsItem,ElFormItem,ElForm,ElInput,ElButton,ElTable,ElTableColumn,ElTooltip,ElTag } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getSaleDemandInfoApi,getBomListApi,getBomInfoApi,updateBomApi } from '@/api/product'

const { t } = useI18n()
const show = ref(false);

const title = ref('')

const demandData = reactive({})

//产品信息表
const pdtInfo:any[] = reactive([])

//查询条件
const searchCondition = reactive({
  page: 1,
  count: 10,  
})

const props = defineProps<{
  show: boolean,
  bom:any,
  old_id:string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    console.log(props.bom)
    // getSaleDemandInfo()
     title.value = '['+props.bom.pdt_name+']'+' 历史BOM版本'
     pdtInfo.splice(0,pdtInfo.length)
     pdtInfo.push({
      pdt_name:props.bom.pdt_name,
      pdt_nick:props.bom.pdt_nick,
      specs_name:props.bom.specs_name,
      specs_text:props.bom.specs_text,
    })

    getBomInfo()
  }
})


//BOM数据
const bomData = reactive({
    name: "",
    pdt_id: "",
    pdt_name: "",
    pdt_nick:"",
    buyer_id: "",
    buyer_name:'',
    buyer_nick:'',
    item_list: [],  //any,
    remark: ""

})

//查询BOM快照列表
const getBomInfo = async () => {
    //查询产品信息 
    const ret = await getBomInfoApi({
        id:props.bom.id,
        is_photo:1,
        page:1,
        count:100
    })
    if(ret)
    {
        console.log(ret)
        // Object.assign(bomData,ret.data)
        Object.assign(bomData, ret.data)
        bomData.item_list = ret.data.item_list;
        bomData.item_list.forEach((item,index)=>{
            if(item.expand == undefined)
            {
                item.expand = true
            }
            if(item.showDlg == undefined)
            {
                item.showDlg = false
            }
        })
        console.log('---',bomData.item_list)
    }
}


//显示快照详情
const onShowInfo = (row)=>
{

}


//定义通知
const emit = defineEmits(['update:show','onSubmit'])

//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

//恢复快照
const rollbackHis = ()=>{
  ElMessageBox.confirm(
      '是否确认恢复该快照?',
      'Warning',
      {
        confirmButtonText: t('button.ok'),
        cancelButtonText: t('button.cancel'),
        type: 'warning',
      }
    )
    .then(async () => {
      bomData.id = props.old_id
      bomData.version = '恢复快照'
      const ret =await updateBomApi(bomData)
      if(ret)
      {
          ElMessage.success(t('msg.updateBomSuccess'))
          closeDlg()
          emit('onSubmit',bomData)
      }
    })
}


</script>

<template>
  <Dialog v-model="show" :title="title" width="85%" max-height="60vh" @close="closeDlg">
    <el-form :model="bomData" ref="ruleFormRef" >
            <div class="flex">
                <el-descriptions class="flex-1 mt-2" :column="2" border>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('bom.id')"
                        class="flex">
                        <el-form-item prop="name" >                        
                            <div class="flex">
                                <el-input  v-model="bomData.name" :disabled="bomData.id!=undefined" />
                                <el-input class="ml-2" v-model="bomData.version" placeholder="版本号"/>
                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('bom.custom')"
                        class="flex">
                        <el-form-item>
                            <div class="mr-2">{{ bomData.buyer_name }}</div> 
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('product_manage.id')"
                        class="flex">
                        <el-form-item prop="pdt_name">
                            <div class="mr-2">{{ bomData.pdt_name }}</div> 
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('product_manage.name')"
                        class="flex">
                        <el-form-item prop="pdt_nick" >
                            <div class="w-[100%] relative">
                                {{ bomData.pdt_nick }}                                
                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('product_manage.categ')"
                        class="flex">
                        <el-form-item prop="name" >
                            <div class="w-[100%] relative">
                                {{ bomData.categ_name }}                                
                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.manager')" >
                        <el-form-item>
                            <div class="mr-2">{{ bomData.mainer_name }}</div> 
                        </el-form-item>
                    </el-descriptions-item>

                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.remark')"
                        class="flex">
                        <el-form-item >
                            <div class="w-[100%] relative">
                                <el-input disabled v-model="bomData.remark"  :autosize="{ minRows: 2, maxRows: 2 }" type="textarea"/>

                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                </el-descriptions>
            </div>          
        </el-form>
        <!-- 工艺列表     -->
        <div class="scrollable-div">
            <!-- 表头 --> 
            <div class="flex header headerBk" style="color: white;">
              <div class="w-[5%] !1min-w-[50px]">{{ t('userTable.id') }}</div>
              <div class="w-[15%] !1min-w-[150px]">{{ t('product_manage.id') }}</div>
              <div class="w-[15%] !1min-w-[150px]">{{ t('product_manage.name') }}</div>
              <div class="w-[10%] !1min-w-[100px]">规格</div>
              <div class="w-[10%] !1min-w-[100px]">{{ t('bom.danwei') }}</div>
              <div class="w-[5%] !1min-w-[80px]">{{ t('bom.cust') }}</div>
              <div class="w-[10%] !1min-w-[120px]">{{ t('bom.price') }}</div>
              <div class="w-[10%] !1min-w-[150px]">{{ t('bom.num') }}</div>
              <div class="w-[10%] !1min-w-[120px]">{{ t('bom.totle_price') }}</div>
              <!-- <div class="w-[10%] !min-w-[80px]">{{ t('process.sort') }}</div> -->
              <div class="w-[10%] !1min-w-[80px]">{{ t('bom.supporter') }}</div>
              <div class="w-[10%] !1min-w-[80px]">{{ t('process.remark') }}</div>
              <div class="w-[10%] !1min-w-[80px]">{{ t('formDemo.operate') }}</div>
            </div>
            <!-- 表内容 -->
            <div class="mt-2" v-for="(item, index) in bomData.item_list" :key="index">
                <!-- 内容头 -->
                <div>
                    <div class=" cursor-pointer bg-light-800 p-2 flex flex-nowrap text-[13px] items-center border border-bottom-0 border-[#c6c6c6] p-1" >
                        <div class="1min-w-[150px]">工序: {{ item.nick }}</div>
                        <div class="1min-w-[100px]">工时/(秒): {{ item.work_hours }}</div>
                        <div class="1min-w-[100px]">人工费: {{ item.job_fee }}</div>
                        <div class="1min-w-[250px] pr-10">(计件:{{ item.piece_fee }}元/件    计时:{{ item.time_fee}}元/小时)</div>
                        <div class="1min-w-[100px]">分摊系数: {{ item.share_rate }}</div>
                        <div class="1min-w-[120px]">质检方式: {{ item.质检方式 }}</div>
                        <div class="w-[100px] text-overflow-ellipsis overflow-hidden">备注: {{ item.remark }}</div>
                        <div class="w-[100px] text-overflow-ellipsis overflow-hidden">工艺: {{ item.desc_txt }}</div>
                        <!-- <div class="min-w-[100px]">排序: {{ item.sort_num }}</div> -->
                        <div class="1min-w-[100px]">工艺标识: {{item.工序标识}}</div>

                        <!-- 靠右的其他信息 -->
                        <div class="ml-auto flex justify-center items-center">
                            <ElButton v-if="item.expand" size="small" @click.stop="item.expand = false">折叠</ElButton>
                            <ElButton v-if="!item.expand" size="small" @click.stop="item.expand = true">展开</ElButton>
                        </div>
                    </div>
                </div>
                <!-- 内容体 -->
                <div v-show="item.expand == undefined?true:item.expand" class="flex content items-center" v-for="(pdt,index) in item.pdt_list" :key="index">
                    <div class="w-[5%] table_item !1min-w-[50px]">{{ index }}</div>
                    <div class="w-[15%] table_item !1min-w-[150px] text-left">
                        <el-tooltip
                            class="box-item"
                            effect="dark"
                            :content="pdt.name"
                            placement="bottom"
                        >
                        {{ pdt.name }} 
                        </el-tooltip>  
                    </div>
                    <div class="w-[15%] table_item !1min-w-[150px]">
                        <el-tooltip
                            class="box-item"
                            effect="dark"
                            :content="pdt.nick"
                            placement="bottom"
                        >
                        {{ pdt.nick }} 
                        </el-tooltip>  
                    </div>
                    <div class="w-[10%] table_item !1min-w-[100px]">
                        <el-tooltip
                            class="box-item"
                            effect="dark"
                            :content="pdt.specs_text"
                            placement="bottom"
                        >
                        {{ pdt.specs_name }} 
                        </el-tooltip>                        
                    </div>
                    <div class="w-[10%] table_item !1min-w-[100px]"> {{ pdt.base_unit }} </div>
                    <div class="w-[5%] table_item !1min-w-[80px]"> 
                        {{ pdt.损耗率 }}                     
                    </div>
                    <div class="w-[10%] table_item !1min-w-[120px]"> {{ pdt.单价 }}</div>
                    <div class="w-[10%] table_item !1min-w-[150px]"> 
                        {{ pdt.用量 }}
                    </div>
                    <div class="w-[10%] table_item !1min-w-[120px]"> {{ pdt.总价 }}</div>
                    <!-- <div class="w-[10%] table_item !min-w-[80px]">
                        <el-input  v-model="pdt.排序"/>
                    </div> -->
                    <div class="w-[10%] table_item !1min-w-[80px]">{{ pdt.供应商 }}</div>
                    <div class="w-[10%] table_item !1min-w-[80px]">
                        {{ pdt.remark }}
                     </div>
                    <div class="w-[10%] table_item !1min-w-[80px]"> 

                     </div>
                </div>
            </div>


            
        </div>



    
    <template #footer>
      <div class="flex justify-center">
        <ElButton type="danger" @click="rollbackHis">
         恢复当前快照
        </ElButton>
        <ElButton type="primary" @click="closeDlg">
         关闭
        </ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 6% !important;
    background-color: aqua;
}

:deep(.conentStyle) {
    width: 30% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}


.header {
  display: flex;
  border: 1px solid #b1b3b8;
  border-collapse: collapse;
  width: 100%;
//   color: var(--el-text-color-regular);
  font-size: 15px;
}
.headerBk{
  background-color: #6d92b4 !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #b1b3b8;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}


//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
:deep(.hide > .el-input__wrapper) {
  box-shadow: none;
  transition: box-shadow 0.3s; /* 添加过渡效果 */
}

/* 鼠标悬停时显示 box-shadow */
:deep(.hide >.el-input__wrapper:hover) {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); /* 修改为悬停状态下的 box-shadow 样式 */
}

/* 获取焦点时显示 box-shadow */
:deep(.hide >.el-input__wrapper:focus) {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); /* 修改为焦点状态下的 box-shadow 样式 */
}

.table_item{
    min-height: 42px;
    align-items: center;
}

.text-overflow-ellipsis {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出文本 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.scrollable-div::-webkit-scrollbar {
  z-index: 1; /* 将滚动条置于内容的下方 */
}

.scrollable-div{
    overflow-x: auto;
}

:deep(.el-tooltip__trigger){
    width: 100%;
}

</style>
<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getSaleListApi } from '@/api/product'

const { t } = useI18n()
const show = ref(false);

const outData = reactive([]) 
//查询条件
const searchCondition = reactive({
  sell_order_num:'',
  // _or:true,
  page: 1,
  count: 10,
  inputTxt:'' //界面输入
})

//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getSaleList()
  }
})

//修改输入后同步其他条件
watch(searchCondition,(newData)=>{
  searchCondition.sell_order_num = newData.inputTxt
})


//查询采购单列表
const getSaleList = async (page = 1) => {
  searchCondition.page = page
  searchCondition.realrole = 'sale.list'
  const ret = await getSaleListApi(searchCondition)
  if(ret)
  {
    outData.splice(0,outData.length, ...ret.data)
    totleCount.value = parseInt( ret.count)
  }
}


//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getSaleList()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getSaleList(val)
}

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
  console.log(currentRow.value)
  if(currentRow.value?.id == undefined)
  {
    ElMessage.warning('请选择一条数据')
    return
  }

  if(currentRow.value.fsm_cur_state != '已审核')
  {
    ElMessage.warning('未审核订单不能发货！')
    return
  }
   
  closeDlg()
  //返回数据给上层
  
  emit('onSubmit',currentRow.value)
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}


</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh"  @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-input style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputNameOrID')" v-model="searchCondition.inputTxt" />
      <el-button type="primary" @click="getSaleList(1)">{{ t('button.search') }}</el-button>
    </div>
    <el-table  ref="tableRef" :data="outData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader" @row-dblclick="onSubmit">
      <el-table-column show-overflow-tooltip fixed prop="sell_order_num" :label="t('sale.name')"  />
      <el-table-column show-overflow-tooltip fixed :label="'审核状态'"  >
        <template #default="scope">
          <div>{{ scope.row.fsm_cur_state }}</div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip fixed :label="t('saleout.status')"  >
        <template #default="scope">
          <div>{{ scope.row.发货状态 }}</div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip fixed prop="buyer_name" :label="t('customer.id')"  />
      <el-table-column show-overflow-tooltip fixed prop="buyer_nick" :label="t('customer.name')"  />
      <el-table-column show-overflow-tooltip fixed prop="合计金额" :label="t('purchase.totle_price')"  />
    </el-table>
    <el-pagination
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handlePageSizeChange"
        @current-change="handlePageCurrentChange"
    />
    <template #footer>
      <ElButton type="primary" @click="onSubmit">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
</style>
<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElTooltip,ElTag } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getSaleDemandInfoApi } from '@/api/product'

const { t } = useI18n()
const show = ref(false);

const title = ref('')

const demandData = reactive({})

//产品信息表
const pdtInfo:any[] = reactive([])

//查询条件
const searchCondition = reactive({
  page: 1,
  count: 10,
})

const props = defineProps<{
  show: boolean,
  pdt:any,
  order_nums:[]
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getSaleDemandInfo()
    title.value = '['+props.pdt.pdt_name+']'+props.pdt.pdt_nick+' 供应需求列表'
  }
})



//查询需求单明细
const getSaleDemandInfo = async (page = 1) => {
  console.log('1111',props.pdt)
  searchCondition.page = page
  searchCondition.pdt_name = props.pdt.pdt_name
  searchCondition.order_nums = props.order_nums
  const ret = await getSaleDemandInfoApi(searchCondition) 
  if(ret)
  {
    Object.assign(demandData,ret.data)
    pdtInfo.splice(0,pdtInfo.length)
    pdtInfo.push({
      pdt_name:ret.data.pdt_info.pdt_name,
      pdt_nick:ret.data.pdt_info.pdt_nick,
      specs_name:ret.data.pdt_info.specs_name,
      specs_text:ret.data.pdt_info.specs_text,
    })
  }
}


//定义通知
const emit = defineEmits(['update:show','onSubmit'])

//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}


</script>

<template>
  <Dialog v-model="show" :title="title" width="85%" @close="closeDlg">

    <div class="text-lg font-black text-center mb-2">产品信息</div>
    <!-- 产品信息表 -->
    <el-table  ref="tableRef" :data="pdtInfo" style="width: 100%; margin-bottom: 20px" row-key="id" border
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip align="center" fixed prop="pdt_name" :label="t('product_manage.id')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="pdt_nick" :label="t('product_manage.name')"  />
      <el-table-column show-overflow-tooltip align="center" fixed :label="t('product_manage.specify_info')"  >
        <template #default="scope">
            <el-tooltip                
                class="box-item"
                effect="dark"
                :content="scope.row.specs_text"
                placement="bottom"
            >
            <el-tag>{{ scope.row.specs_name }}</el-tag>
            </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="text-lg font-black text-center mb-2">需求明细</div>
    <!-- 需求明细表 -->
    <el-table  ref="tableRef" :data="demandData.demand_list" style="width: 100%; margin-bottom: 20px" row-key="id" border
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip align="center" fixed  :label="t('saledemand.refType')"  >
        <template #default="scope">
          销售订单
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip align="center" fixed prop="sell_order_num" :label="t('saledemand.refNum')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="buyer_nick" :label="t('saledemand.refCus')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="需求总量" :label="t('saledemand.totle')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="已发货" :label="t('saledemand.outed')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="已退货" :label="t('saledemand.returned')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="当前需求" :label="t('saledemand.cur')"  />
    </el-table>
    <div class="text-lg font-black text-center mb-2">供应明细</div>
    <!-- 供应明细表 -->
    <el-table  ref="tableRef" :data="pdtInfo" style="width: 100%; margin-bottom: 20px" row-key="id" border
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip align="center" fixed prop="sell_order_num" :label="t('product_manage.id')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="sell_order_num" :label="t('product_manage.name')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="sell_order_num" :label="t('product_manage.specify_info')"  />
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="closeDlg">
          {{ t('msg.ok') }}
        </ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  // background-color: #ffe48d !important;
  // --el-table-current-row-bg-color: #ffe48d !important;
  // --el-table-row-hover-bg-color: #ffe48d;
}

:deep(.tableHeader) {
}
</style>
<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { ElButton, ElTable, ElTableColumn, ElPagination, ElMessage, ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';


const { t } = useI18n()
const show = ref(false);

const checkData = reactive([])

const props = defineProps<{
  show: boolean,
  checklist: []
}>()
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    show.value = true
  }
})

//定义通知
const emit = defineEmits(['update:show', 'onSubmit'])
//提交选择的数据
const onSubmit = () => {

  closeDlg()
  //返回数据给上层  
  // emit('onSubmit',currentRow.value)
}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}


</script>

<template>
  <Dialog v-model="show" :title="'任务历史记录'" max-height="60vh" @close="closeDlg">
    <el-table ref="tableRef" :data="props.checklist" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      header-cell-class-name="tableHeader">
      <el-table-column show-overflow-tooltip  prop="[0]" label="操作时间" width="160"/>
      <el-table-column show-overflow-tooltip  prop="[2]" label="操作" />
      <el-table-column show-overflow-tooltip  prop="[3]" label="当前节点" width="200">
        <template #default="{ row }">
          <span>{{ row[3].replace('等待','') }}</span>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip  prop="[5]" label="状态" >
        <template #default="{ row }">
          <span>已完成</span>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip  prop="[5]" label="备注" />
      <el-table-column show-overflow-tooltip  prop="[4]" label="操作员" />
    </el-table>
    <template #footer>
     <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
     </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #DFF6E5 !important;
//   --el-table-current-row-bg-color: #DFF6E5 !important;
//   --el-table-row-hover-bg-color: #DFF6E5;
// }

:deep(.tableHeader) {
    font-weight: bold !important;
}
</style>
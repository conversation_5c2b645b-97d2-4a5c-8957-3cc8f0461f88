<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElImage, ElTag, ElDialog, ElMessageBox, ElButton, ElForm, ElFormItem, FormRules, ElCheckboxGroup, ElCheckbox, ElTreeSelect, ElUpload, ElCard, ElTabPane, ElTabs, ElRadio, ElRadioGroup, ElSelect, ElOption, ElTooltip, ElInput, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import { getCategListApi, getProductNewnumApi, addProductApi, getProductListApi, updateProductApi, getBomInfoApi } from '@/api/product'
import type { FormInstance, UploadProps, UploadUserFile } from 'element-plus'
import { DialogSpecs } from '@/components/DialogSpecs'
import { checkFormRule } from '@/api/tool'
import { getOssSignApi, ossUpload } from '@/api/oss'
import { Dialog } from '@/components/Dialog'
import { DialogBomStruct } from '@/components/DialogBomStruct'
import { checkPermissionApi } from '@/api/tool'
import { getHashApi } from '@/api/extra'
import { DialogBaseUnit } from '@/components/DialogBaseUnit'

const { t } = useI18n()
const { currentRoute, push } = useRouter()

const show = ref(false);
const props = defineProps<{
  show: boolean,
  title: string,
  id: string, //产品ID
  type: string //模式
  categ: string //目录
  exinfo: any //扩展信息（售前产品信息）
}>()
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    show.value = true
    console.log('--==---------------', props.exinfo)
    reloadShow()
    getBaseUnitData()
  }
})


//定义通知
const emit = defineEmits(['update:show', 'onSubmit'])
//提交选择的数据
const onSubmit = () => {

  closeDlg()
  //返回数据给上层  
  emit('onSubmit', productData)
}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

//----------------------------------------------------------------------

//上传URL
const uploadUrl = ref<string>('')
//标题
const title = ref<string>('')
//当前活动tab
const activeTab = ref<string>('1')
//产品分类Prop属性
const categProps = {
  multiple: true,
  checkStrictly: true,
  value: 'id',
  children: 'sub_categ',
  label: 'name',
  emitPath: false
}
const productDef = {
  "id": "",
  "ids": [] as string[],
  "props": ['销售产品', '采购产品', '生产产品', '半成品', '物料', '委外产品'], //产品属性
  "name": "",  //编码
  "nick": "",  //名称
  "nick_eng": "",  //英文名称
  "nick_brif": "",  //简称
  "note": "",  //助记码
  "brand": "",  //品牌
  "brand_eng": "",  //英文品牌
  "categ": "999", //分类
  "base_unit": "PCS",  //基本计量单位
  "base_digit": 0,  //基本计量精度
  "ext_unit": "",  //辅助计量单位
  "ext_digit": 0,  //辅助计量精度
  "sell_unit": "",  //销售计量单位
  "buy_unit": "",  //采购计量单位
  "sell_buy_digit": "4",  //价格精度
  "stocks_info": [],  //进出库均需要填写的信息
  "specs": "", //规格
  "specs_name": "", //规格名称
  "specs_text": "",//规格内容
  "pics": [], //产品图片
  "buyer_id": "", //客户id
  "buyer_pdt_num": "", //客户产品编码
  "buy_price_bef_tax": 0.00, //采购税前价格
  "buy_price_aft_tax": 0.00, //采购税后价格
  "low_price_bef_tax": 0.00, //最低税前价格
  "low_price_aft_tax": 0.00, //最低税后价格
  "high_price_bef_tax": 0.00, //最高税前价格
  "high_price_aft_tax": 0.00, //最高税后价格
  "cost_price": 0.00, //核定成本价格
  "buy_quality_check": "不质检", //采购质检要求
  "cad_num": "", //图纸号
  "bom_more_specs": "", //BOM关联多规格
  "workshop": "", //默认车间
  "cost_share_pct": 0.00, //费用分摊系数
  "stock_quality_check": "", //入库质检要求
  "pdt_flow_mode": "", //产品生产工序流转模式
  "out_price_bef_tax": 0.00, //委外税前价格
  "out_price_aft_tax": 0.00, //委外税后价格
  "outer_id": "", //委外商id
  "stockroom": "", //默认仓库
  "stockplace": "", //默认仓位
  "type": "标准产品", //产品类型
  "bar_code": "", //产品条形码
  "status": "正常", //产品状态
  "sn_status": "", //产品序列号
  "sn_control": "", //产品序列号数量
  "remark": "",

  "specify_type": '静态规格', //规格类型
  "specify_static": '',        //静态规格内容
  "specify_customer": '',      //客户自定义规格内容

  bom_id: '',
  // oem_order_num:'',
  // sell_order_num:'',
  // oem_order_sub:'',
  "sell_price_bef_tax": 0.00, //销售税前价格
  "sell_price_aft_tax": 0.00, //销售税后价格
  "sell_low_price_bef_tax": 0.00, //销售最低税前价格
  "sell_low_price_aft_tax": 0.00, //销售最低税后价格
  "sell_high_price_bef_tax": 0.00, //销售最高税前价格
  "sell_high_price_aft_tax": 0.00, //销售最高税后价格

  'enable_ext': false, //是否启用辅助计量单位
  库存: '',
}
//产品对象数据源
const productData = reactive({
  ...productDef,
  reset() {
    Object.assign(this, productDef)
  }
})
//基本计量单位数据源
const baseUnitData = reactive([
  { name: 'PCS' },
  { name: '个' },
  { name: '件' },
  { name: '公斤' },
  { name: '台' },
  { name: '套' },
  { name: 'KG' },
  { name: 'G' },
  { name: '吨' }
]);
//是否显示规格选择窗口
const isShowSpecify = ref(false)

// //测试用图片列表  name url
// const fileList = ref<UploadUserFile[]>([])


//产品分类数据源
const categData = reactive([])

//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
  name: [{ required: true, message: t('msg.noProductName'), trigger: 'blur' }],
  nick: [{ required: true, message: t('msg.noProductNick'), trigger: 'blur' }]
})
//查询产品分类树
const getCategList = async () => {
  const ret = await getCategListApi({
    page: 1,
    count: 10000
  })
  if (ret) {
    console.log(ret)
    // categData.splice(0, categData.length,...ret.data.all_categs.sub_categ)    
    categData.splice(0, categData.length, ret.data.all_categs)
  }
}

//更新产品编号
const onChangeProductID = async () => {
  //获取产品编号
  const ret = await getProductNewnumApi({
    categ_id: productData.categ
  })
  if (ret) {
    console.log(ret)
    productData.name = ret.data.new_id
  }
}



//选择规格
const onSelSpec = () => {
  isShowSpecify.value = true
}
//清除选择规格
const onDelSelSpec = async () => {

  //提示是否清除规格
  const confirmRes = await ElMessageBox.confirm(t('msg.confirm_del_spec'), t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'error'
  }).catch(() => {

  })
  if (confirmRes !== 'confirm')
    return
  else {
    console.log("111")
    //清除规格        
    productData.specs = ''
    productData.specs_name = ''
    productData.specs_text = ''
  }


}
//回调设置规格
const onSetSpec = (id, name, text) => {
  productData.specs = id
  productData.specs_name = name
  productData.specs_text = text
  isShowSpecify.value = false
}

//保存产品
const onSave = async () => {
  if (props.type == 'info') {
    onSubmit()
    return
  }

  const rule = await checkFormRule(ruleFormRef.value)
  if (!rule) {
    ElMessage.warning(t('msg.checkRule'))
    return
  }
  //如果包含售前扩展数据则需要写入
  if (props.exinfo.process_list != undefined) {
    productData.process_list = [...props.exinfo.process_list]
    //解决id转换问题
    productData.process_list.forEach(item => {
      for (const pdt of item.pdt_list) {
        pdt.id = pdt.pdt_id
        delete pdt.pdt_id //删除多余属性
      }
    })
  }
  if (props.exinfo.sell_offer_num != undefined) {
    productData.sell_offer_num = props.exinfo.sell_offer_num
  }

  //处理规格
  if (productData.specify_type == '静态规格') {
    productData.specs = productData.specify_static
  }

  //处理辅助价格
  if (productData.enable_ext) {
    if (productData.ext_unit == '') {
      ElMessage.error('请输入辅助价格单位')
      return
    }
  }
  else {
    productData.ext_unit = ''
    productData.ext_digit = 0
  }

  //新增产品
  if (props.id == '') {
    const res = await addProductApi(productData)
    if (res) {
      console.log(res)
      onSubmit()
    }
  }
  else //修改产品
  {
    productData.ids = [productData.id]
    const res = await updateProductApi(productData)
    if (res) {
      console.log(res)
      onSubmit()
    }
  }
}

const reloadShow = async () => {
  activeTab.value = '1'
  console.log('--------', props)
  uploadUrl.value = import.meta.env.VITE_UPLOAD_URL

  await getCategList()
  if (props.id == '') {
    const jParam = JSON.parse(props.categ as string)
    console.log(jParam)
    console.log('------------------------1', props.type)
    title.value = t('product_manage.add_product')

    productData.reset()
    productData.pics.splice(0, productData.pics.length)

    //更新分类节点传递过来的基础配置            
    //更新属性
    if ('pdt_def_types' in jParam)
      productData.props = [...jParam.pdt_def_types]
    //更新分类
    if ('id' in jParam)
      productData.categ = jParam.id
    //更新单位精度
    if ('pdt_def_digits' in jParam)
      productData.base_digit = jParam.pdt_def_digits[1]
    //更新价格精度
    if ('pdt_def_digits' in jParam)
      productData.sell_buy_digit = jParam.pdt_def_digits[0]

    //如果是新建售前产品填入默认名字
    console.log('props.exinfo--', props.exinfo)
    if (props.exinfo.pdt_nick != undefined) {
      productData.nick = props.exinfo.pdt_nick
    }

    if (props.exinfo.pics != undefined) {
      productData.pics = [...props.exinfo.pics]
    }

    onChangeProductID()
  }
  else {
    console.log('------------------------2', props.type)
    if (props.type == 'info') {
      title.value = t('product_manage.look_product')
    }
    else {
      title.value = t('product_manage.update_product')
    }

    //查询产品信息
    const ret = await getProductListApi({
      ids: [props.id],
      page: 1,
      count: 100
    })
    if (ret) {
      console.log(ret)
      Object.assign(productData, ret.data[0])
      if (productData.specs_name === '自定义规格') {
        productData.specify_type = '静态规格'
        productData.specify_static = productData.specs
      }
      else {
        productData.specify_type = '全局规格'
      }
      if (productData.ext_unit != '') {
        productData.enable_ext = true
      }
      else {
        productData.enable_ext = false
      }
    }

    if (props.type === 'info') //查看模式
    {
      let components = document.getElementById('pdtwin').querySelectorAll('.el-input__inner');
      components.forEach((component) => {
        component.setAttribute('disabled', true);
      });
      components = document.querySelectorAll('.el-input__wrapper');
      components.forEach((component) => {
        component.classList.add('infomode')
      });

    }
  }
}

onMounted(async () => {
  //reloadShow()
})


const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
  for (let one of productData.pics) {
    if (one.url == uploadFile.raw.weburl) {
      productData.pics.splice(productData.pics.indexOf(one), 1)
      break
    }
  }
  console.log('删除图片', productData.pics)
}

const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const handlePreviewPic: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  dialogVisible.value = true
  console.log('预览图片')
}

const uploadImg = async (file, type) => {
  let path = 'pdt/' + productData.name + '/'
  console.log('====', file)

  const ret = await getOssSignApi({ upload_dir: path })
  if (ret) {
    const end = await ossUpload(ret.data.token, file.file, path, (pro) => {
      console.log('pppp', pro)
    })


    productData.pics.push({
      name: file.file.name,
      url: end.url
    })
    file.file.weburl = end.url
    console.log('完成', productData.pics, file)
  }
}


//显示BOM结构图
const showBomStruct = ref(false)
const onShowBomStruct = async () => {
  console.log(currentRoute.value.meta.title)
  if (productData.bom_id == "") {
    ElMessage.warning("当前产品没有创建BOM")
    return
  }


  const ret = await getBomInfoApi({
    id: productData.bom_id,
    is_all_level: 1
  })
  if (ret) {
    console.log(ret)
    const msg = JSON.stringify(ret.data)
    localStorage.setItem('bom', msg)
    const url = `${window.location.origin}/bom.html`;
    console.log(url)
    window.open(url, '_blank');

  }
  return
  // showBomStruct.value = true
  if (currentRoute.value.meta.title == 'sale.list') {
    push({
      path: '/salemanage/showbomstruct',
      query: {
        bomid: productData.bom_id,
        nick: productData.nick
      }
    })
  }
  else if (currentRoute.value.meta.title == 'product_manage.product_manage') {
    push({
      path: '/productmanage/showbomstructpdt',
      query: {
        bomid: productData.bom_id,
        nick: productData.nick
      }
    })
  }

}



let timeout: ReturnType<typeof setTimeout>
const querySearchAsync = (queryString: string, cb: (arg: any) => void) => {
  clearTimeout(timeout)
  timeout = setTimeout(async () => {
    if (queryString == '') {
      cb([])
      return
    }
    //查询产品信息
    const ret = await getProductListApi({
      nick: queryString,
      page: 1,
      count: 10
    })
    if (ret) {
      const out = []
      for (let one of ret.data) {
        out.push({
          value: one.nick
        })
      }
      cb(out)
    }
    // cb(results)
  }, 800)
}


const showEditBaseUnit = ref(false)
const onShowEditBaseUnit = () => {
  showEditBaseUnit.value = true
}
const getBaseUnitData = async () => {
  const ret = await getHashApi({
    name: 'baseunit',
    page: 1,
    count: 10000
  })
  if (ret) {
    baseUnitData.splice(0, baseUnitData.length, ...ret.data.json)
  }
}


</script>

<template>
  <ElDialog destroy-on-close id="pdtwin" v-model="show" :title="title" width="800" max-height="60vh" @close="onSubmit">
    <div class="flex justify-center min-h-[600px]">
      <el-card shadow="never" class="box-card">
        <template #header>
          <div style="font-size: larger;">
            <span>{{ t('product_manage.baseinfo') }}</span>
          </div>
        </template>
        <div>
          <div class="oneline flex justify-start items-center">
            <el-form-item prop="name" :label="t('product_manage.id')" class="flex-item">
              {{productData.name}}
            </el-form-item>
            <el-form-item :label="'BOM数量'" class="flex-item">
              <ElTag class="cursor-pointer" @click="onShowBomStruct">{{ productData.bom_id == "" ? 0 : 1 }}个</ElTag>
            </el-form-item>
            <el-form-item :label="'库存数量'" class="flex-item">
              <ElTag>{{ productData.库存 }}</ElTag>
            </el-form-item>
          </div>
          <el-form-item prop="nick" :label="t('product_manage.name')" class="flex-item">
              {{ productData.nick }}
          </el-form-item>
          <el-form-item :label="t('product_manage.props')">
            <el-checkbox-group :disabled="props.type == 'info'" v-model="productData.props">
              <el-checkbox label="销售产品" />
              <el-checkbox label="采购产品" />
              <el-checkbox label="生产产品" />
              <el-checkbox label="半成品" />
              <el-checkbox label="物料" />
              <el-checkbox label="委外产品" />
            </el-checkbox-group>
          </el-form-item>
          <div class="oneline flex justify-start items-center">
            <el-form-item prop="name" :label="t('product_manage.en_name')" class="flex-item">
              {{productData.nick_eng}}
            </el-form-item>
            <el-form-item :label="t('product_manage.short_name')" class="flex-item">
              {{ productData.nick_brif}}
            </el-form-item>
            <el-form-item :label="t('product_manage.help_name')" class="flex-item">
              {{ productData.note }}
            </el-form-item>
          </div>
          <div class="oneline flex justify-start items-center">
            <el-form-item prop="name" :label="t('product_manage.categ')" class="flex-item">
              {{productData.categ_name}}
            </el-form-item>
            <el-form-item :label="t('product_manage.base_unit')" class="flex-item">
              {{ productData.base_unit}}
            </el-form-item>
            <el-form-item :label="'辅助计量单位'" class="flex-item">
              {{ productData.ext_unit }}
            </el-form-item>
          </div>

          <div class="flex">
            <el-form-item :label="t('product_manage.brand')" class="flex-item">
              {{ productData.brand }}
            </el-form-item>
            <el-form-item :label="t('product_manage.en_brand')" class="flex-item">
              {{ productData.brand_eng }}
            </el-form-item>
            <div class="flex-item"></div>
          </div>
          <!-- <el-upload :disabled="true" :file-list="productData.pics" list-type="picture-card"
            :on-preview="handlePreviewPic" :on-remove="handleRemove" :accept="'image/*'">
            <template v-slot="{ file }">
              <div v-if="!$attrs.disabled" class="el-upload__input">
                <Icon icon="fluent:add-24-filled" />
              </div>
            </template>
          </el-upload> -->
          <div>
            <el-image
              v-for="item in productData.pics"
              :key="item.url"
              style="width: 100px; height: 100px"
              :src="item.url"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[item.url]"
              :initial-index="4"
              fit="cover"
            />
          </div>

        </div>

      </el-card>

    </div>





    <template #footer>
      <ElButton type="primary" @click="onSave">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
    </template>


    <DialogSpecs :show="isShowSpecify" :title="'选择规格'" v-model:showHide="isShowSpecify" @on-submit="onSetSpec" />

    <DialogBomStruct v-model:show="showBomStruct" :bomid="productData.bom_id" />

    <el-dialog v-model="dialogVisible"  class="flex justify-center">
        <!-- <img w-full :src="dialogImageUrl" alt="预览" /> -->
        <el-image
          style="width: 100px; height: 100px"
          :src="dialogImageUrl"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="[dialogImageUrl]"
          :initial-index="4"
          fit="cover"
        />
    </el-dialog>

    <DialogBaseUnit v-model:show='showEditBaseUnit' @on-submit="getBaseUnitData" />
  </ElDialog>
</template>


<style lang="less" scoped>
.el-card {
  border-width: 0px;
  background-color: var(--app-content-bg-color);
  padding: 0 !important;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  background-color: #fff;
}

:deep(.el-input__suffix-inner)>:first-child {
  margin-right: 8px;
}

.flex-item {
  flex: 1;
  margin-right: 20px; /* 根据需要调整间距 */
}

/* 可选: 移除最后一个 .flex-item 的 margin-right */
.flex-item:last-child {
  margin-right: 0;
}
:deep(.el-form-item__label::after) {
  content: ":";
  margin-left: 2px; /* 根据需要调整冒号与标签的间距 */
}
.oneline{
  height: 30px;
}
:deep(.el-form-item--default){
  margin-bottom: 20px
}
</style>

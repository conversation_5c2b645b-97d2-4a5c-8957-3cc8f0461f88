<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch, reactive } from 'vue';
import { ElTooltip, ElButton, ElPopconfirm, ElDescriptionsItem, ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElMessage, ElInput, FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { onBeforeUnmount } from 'vue';
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { getProductListApi } from '@/api/product';
import { getGUID } from '@/api/tool';

const { t } = useI18n()
const props = defineProps<{
  show: boolean,
  data:any //传入等待修改BOM数据
}>()

//自我显示
const show = ref(false)
const title = ref('')
//BOM数据
const bomData = reactive({
    name: "",
    pdt_id: "",
    pdt_name: "",
    pdt_nick: "",
    buyer_id: "",
    buyer_name: '',
    buyer_nick: '',
    item_list: [],  //any,
    remark: ""

})

//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    //显示窗口时刷新数据
    show.value = val
    Object.assign(bomData, props.data)
    title.value = '['+bomData.pdt_name+']'+bomData.pdt_nick+'-物料需求修改'
    bomData.item_list = props.data.item_list;
    bomData.item_list.forEach((item, index) => {
        if (item.expand == undefined) {
            item.expand = true
        }
        if (item.showDlg == undefined) {
            item.showDlg = false
        }
    })
  }
})



//定义通知
const emit = defineEmits(['update:show', 'onSubmit', 'update:data'])
//提交选择的数据
const onSubmit = async () => {
  console.log('sub',bomData)
    closeDlg()
    emit('onSubmit',bomData)
}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}


//----------------------------------------------------------
//当前操作工序
const nCurSelItem = ref({})
//查询产品绑定的查询结果
const searchPdtData = reactive<any>([])
//选中某一个产品
const handleCurrentSelectPdt = (row: any, item, fource = false) => {
    if (item == undefined) {
        return
    }
    console.log('设置当前:', nCurSelPdtID.value)
    nCurSelPdtID.value = row.id
    nCurSelItem.value = item
    if (!fource) {
        return
    }
    item.extra_input = ''

    console.log(row, item)
    item.pdt_list.push({
        id: row.id,
        name: row.name,
        nick: row.nick,
        specs_name: row.specs_name,
        specs_text: row.specs_text,
        base_unit: row.base_unit,
        损耗率: 0,
        单价: row.单价,
        用量: 1,
        总价: 0,
        排序: 0,
        供应商: '',
        remark: '',
        sell_buy_digit: row.sell_buy_digit,
        标识: getGUID(20),
        locked_list: [],
        税前赔付单价: 0,
        税后赔付单价: 0
    })


    //隐藏所有浮动选择框
    bomData.item_list.forEach(item => {
        item.showDlg = false
        item.extra_input = ''
    })

}

watch(bomData, (data) => {
    console.log('更新')
    data.item_list.forEach((process) => {
        process.pdt_list.forEach((pdt) => {
            pdt.总价 = ((pdt.单价 + pdt.单价 * (pdt.损耗率 / 100)) * pdt.用量).toFixed(pdt.sell_buy_digit);
        })
    });
}, { deep: true })


//输入发生变化时处理,要支持延时处理
const delaytime = ref<NodeJS.Timeout | null>(null);
const onItemInput = (val, item) => {
    item.showDlg = true
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)

    delaytime.value = setTimeout(() => {
        // 输入停止后触发的方法
        getProductList(val)
    }, 200)
}


//查询产品列表根据编号
const getProductList = async (val = '') => {
    const ret = await getProductListApi({
        name: val,
        nick: val,
        prop_list: ['物料'],
        status: '正常',
        _or: true,
        page: 1,
        count: 10,
    })
    if (ret) {
        searchPdtData.splice(0, searchPdtData.length, ...ret.data)
        if (searchPdtData.length > 0) {
            setCurrent(searchPdtData[0])
        }
    }
}

//显示选中工序窗口
const showSelProcess = ref(false)
//添加一个工序
const onAddProcess = () => {
    showSelProcess.value = true
}
//已选择工序回调
const onSelProcessCallback = (value, array) => {
    array.forEach((item) => {
        //增加产品数组
        item.pdt_list = []
        //追加额外属性
        item.质检方式 = '无需质检' //质检方式
        item.工序标识 = '标准'     //工序标识
        item.showDlg = false  //是否显示弹窗
        item.extra_input = '' //额外输入
        item.expand = true //展开折叠
    })

    //追加选择的工序到BOM列表
    bomData.item_list.push(...array)
    console.log(bomData)
}

//删除工序产品列表中某一个产品
const onDeleteOnePdt = (item, pdt) => {
    // 在这里实现删除操作
    const index = item.pdt_list.indexOf(pdt);
    if (index !== -1) {
        item.pdt_list.splice(index, 1); // 使用splice方法删除元素
    }
}

//删除某一个工序
const onDeleteOneProcess = (item, index) => {
    // 在这里实现删除操作
    bomData.item_list.splice(index, 1); // 使用splice方法删除元素
}

onMounted(async () => {
  document.addEventListener('click', onDocumentClick, false)
})

//unmounted的时候移除监听
onBeforeUnmount(() => {
    document.removeEventListener('click', onDocumentClick, false)
})


const onDocumentClick = (event) => {
    // 检查点击事件的目标元素是否位于任何一个 el-table 外部
    const elTableContainers = document.querySelectorAll('.el-table-container');

    let clickedInsideTable = false;
    for (const container of elTableContainers) {
        if (container.contains(event.target)) {
            // 点击的元素在 el-table 容器内部
            clickedInsideTable = true;
            break;
        }
    }
    if (!clickedInsideTable && !(event.target as HTMLElement).classList.contains('el-input__inner')) {
        // 点击的元素不在任何一个 el-table 容器内部
        //关闭修改bomData.item_list中所有showDlg为false
        bomData.item_list.forEach(item => {
            item.showDlg = false
        })
    }
}

//显示隐藏产品弹窗
const showSelProductDlg = ref(false)
//显示选择产品对话框
const onSelProduct = () => {
    showSelProductDlg.value = true
}
//选择产品完毕回调
const onSelProductCallback = (pdt) => {
    if (pdt.bom_id != '') {
        ElMessage.error(t('msg.exsitBom'))
        return
    }
    bomData.pdt_id = pdt.id
    bomData.pdt_name = pdt.name
    bomData.pdt_nick = pdt.nick
    bomData.categ = pdt.categ
    bomData.categ_name = pdt.categ_name

    console.log(bomData)
}


//键盘上下只切换不选择
const nCurSelPdtID = ref('')
const bKeyDown = ref(true)
const onKeyDownOnePdt = (event) => {
    if (event.keyCode === 38 || event.keyCode === 40) {
        // 阻止默认行为，以保持光标位置不变
        event.preventDefault();
    }
    //esc按键关闭table
    if (event.keyCode === 27) {
        showSelProductDlg.value = false
        return
    }
    bKeyDown.value = true
    if (nCurSelPdtID.value == '') {
        setCurrent(searchPdtData[0])
    }
    else {


        for (let i = 0; i < searchPdtData.length; i++) {
            if (searchPdtData[i].id == nCurSelPdtID.value) {
                if (event.keyCode === 38 && i > 0)
                    setCurrent(searchPdtData[i - 1])
                else if (event.keyCode === 40 && i < searchPdtData.length - 1)
                    setCurrent(searchPdtData[i + 1])
                //如果是回车，直接选择
                else if (event.keyCode === 13) {
                    onRowClick(searchPdtData[i])
                    return
                }
                return
            }
        }
    }

}
const searchRef = ref<InstanceType<typeof ElTable>>()

const setCurrent = (row?) => {
    if (row == undefined)
        nCurSelPdtID.value = ''
    console.log('setCurrent', searchRef)
    searchRef.value![0].setCurrentRow(row)
}
const onRowClick = (row) => {
    console.log('xuanle', row)
    
    //检查所有物料是否重复
    for (let item of bomData.item_list) {
        if (item.pdt_list.findIndex(item => item.id == row.id) != -1) {
            ElMessage({
                message: '['+row.nick +'] 该物料已存在，不能重复添加',
                type: 'error',
            })
            return
        }
    }

    handleCurrentSelectPdt(row, nCurSelItem.value, true)
}

</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg" width="95%">
    <ElButton  type="success" @click="onAddProcess">
        {{ t('project_manage.add_process') }}
    </ElButton>
    <!-- 工艺列表     -->
    <div class="scrollable-div">
            <!-- 表头 -->
            <div class="flex header headerBk" style="color: white;">
                <div class="w-[5%] !1min-w-[50px]">{{ t('userTable.id') }}</div>
                <div class="w-[15%] !1min-w-[150px]">{{ t('product_manage.id') }}</div>
                <div class="w-[15%] !1min-w-[150px]">{{ t('product_manage.name') }}</div>
                <div class="w-[15%] !1min-w-[100px]">规格</div>
                <div class="w-[10%] !1min-w-[100px]">{{ t('bom.danwei') }}</div>
                <div class="w-[5%] !min-w-[70px]">{{ t('bom.cust') }}</div>
                <div class="w-[10%] !1min-w-[120px]">{{ t('bom.price') }}</div>
                <div class="w-[10%] !1min-w-[150px]">{{ t('bom.num') }}</div>
                <div class="w-[10%] !1min-w-[120px]">{{ t('bom.totle_price') }}</div>
                <!-- <div class="w-[10%] !min-w-[80px]">{{ t('process.sort') }}</div> -->
                <div class="w-[10%] !1min-w-[80px]">{{ t('bom.supporter') }}</div>
                <div class="w-[10%] !1min-w-[80px]">{{ t('process.remark') }}</div>
                <div class="w-[10%] !1min-w-[80px]">{{ t('formDemo.operate') }}</div>
            </div>
            <!-- 表内容 -->
            <div class="mt-2" v-for="(item, index) in bomData.item_list" :key="index">
                <!-- 内容头 -->
                <div>
                    <div class=" cursor-pointer bg-light-800 p-2 flex flex-nowrap text-[13px] items-center border border-bottom-0 border-[#c6c6c6] p-1">  
                        <el-popconfirm  title="确定是否删除该工序?" @confirm="onDeleteOneProcess(item, index)">
                          <template #reference>
                            <ElButton  plain class="mr-3 !w-[30px]" type="danger" size="small">
                                <Icon icon="material-symbols:delete-outline" />
                            </ElButton>
                          </template>
                      </el-popconfirm>  

                        <div class="1min-w-[150px]">工序: {{ item.nick }}</div>

                        <!-- 靠右的其他信息 -->
                        <div class="ml-auto flex justify-center items-center">
                            <ElButton v-if="item.expand" size="small" @click.stop="item.expand = false">折叠</ElButton>
                            <ElButton v-if="!item.expand" size="small" @click.stop="item.expand = true">展开</ElButton>
                        </div>
                    </div>
                </div>
                <!-- 内容体 -->
                <div v-show="item.expand == undefined ? true : item.expand" class="test flex content items-center"
                    v-for="(pdt, index) in item.pdt_list" :key="index">
                    <div class="w-[5%] table_item !1min-w-[50px]">{{ index }}</div>
                    <div class="w-[15%] table_item !1min-w-[150px] text-left">
                        <el-tooltip class="box-item" effect="dark" :content="pdt.name" placement="bottom">
                            {{ pdt.name }}
                        </el-tooltip>
                    </div>
                    <div class="w-[15%] table_item !1min-w-[150px]">
                        <div class="whitespace-normal">{{ pdt.nick }}</div>
                    </div>
                    <div class="w-[15%] table_item !1min-w-[100px]">
                        <el-tooltip class="box-item" effect="dark"
                            :content="pdt.specs_name == '自定义规格' ? pdt.specs_text : pdt.specs_name" placement="bottom">
                            {{ pdt.specs_name == '自定义规格' ? pdt.specs_text : pdt.specs_name }}
                        </el-tooltip>
                    </div>
                    <div class="w-[10%] table_item !1min-w-[100px]"> {{ pdt.base_unit }} </div>
                    <div class="w-[5%] table_item !min-w-[70px]">
                        <el-input v-model="pdt.损耗率" />
                    </div>
                    <div class="w-[10%] table_item !1min-w-[120px]"> {{ pdt.单价 }}</div>
                    <div class="w-[10%] table_item !1min-w-[150px]">
                        <el-input v-model="pdt.用量" />
                    </div>
                    <div class="w-[10%] table_item !1min-w-[120px]"> {{ pdt.总价 }}</div>
                    <!-- <div class="w-[10%] table_item !min-w-[80px]">
                        <el-input  v-model="pdt.排序"/>
                    </div> -->
                    <div class="w-[10%] table_item !1min-w-[80px]">{{ pdt.供应商 }}</div>
                    <div class="w-[10%] table_item !1min-w-[80px]">
                        <el-input v-model="pdt.remark" name="bz" autocomplete="on" />
                    </div>
                    <div class="w-[10%] table_item !1min-w-[80px]">

                        <el-popconfirm  title="确定是否删除该物料?" @confirm="onDeleteOnePdt(item, pdt)">
                          <template #reference>
                            <ElButton  type="danger" size="small" class="!w-[30px]">
                                <Icon icon="material-symbols:delete-outline" />
                            </ElButton>
                          </template>
                      </el-popconfirm>  

                    </div>
                </div>
                <div v-show="item.expand == undefined ? true : item.expand" class="flex content items-center">
                    <div class="w-[5%] !1min-w-[50px]">+</div>
                    <div class="w-[15%] !1min-w-[150px]">
                        <el-input @keydown='onKeyDownOnePdt' @keyup='bKeyDown = false' v-model="item.extra_input"
                            @click="() => { item.showDlg = true; getProductList(item.extra_input) }" @blur="() => { }"
                            @input="onItemInput($event, item)" />
                    </div>

                </div>
                <div v-if="item.showDlg == undefined ? false : item.showDlg"
                    class=" bg-light-600 w-[800px] max-h-[400px]  p-2 shadow el-table-container">
                    <el-table ref="searchRef" :data="searchPdtData" style="width: 100%;" row-key="guuid" border
                        highlight-current-row @current-change="handleCurrentSelectPdt($event, item, false)"
                        header-cell-class-name="tableHeader" height="300" @row-click='onRowClick'>
                        <el-table-column show-overflow-tooltip fixed prop="name" :label="t('product_manage.id')"
                            width="150" />
                        <el-table-column show-overflow-tooltip fixed prop="nick" :label="t('product_manage.name')"
                            width="600" />
                        <el-table-column show-overflow-tooltip fixed prop="brand" :label="t('product_manage.brand')" />
                        <el-table-column show-overflow-tooltip fixed prop="specs_text"
                            :label="t('product_manage.specify_info')" />
                        <el-table-column show-overflow-tooltip fixed prop="nick_brif"
                            :label="t('product_manage.short_name')" />
                        <el-table-column show-overflow-tooltip fixed prop="note"
                            :label="t('product_manage.help_name')" />
                    </el-table>
                </div>
            </div>


            <div class="h-[800px]"> </div>
        </div>
        <!-- 选择工序弹窗 -->
        <DialogProcessSel v-model:show="showSelProcess" :title="t('bom.sel_process')"
            @on-submit="onSelProcessCallback" />

    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
// :deep(.labelStyle) {
//   width: 15% !important;
// }

// :deep(.conentStyle) {
//   width: 30%;
// }

.el-form-item--default {
  margin-bottom: unset;
}

.el-input,
.el-select {
  width: 80%;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
  content: '*';
  color: red;
  margin-left: 4px;
}

:deep(.el-date-editor.el-input) {
  width: unset !important;
}

:deep(.labelStyle) {
    width: 6% !important;
    background-color: aqua;
}

:deep(.conentStyle) {
    width: 30% !important;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}


.header {
    display: flex;
    border: 1px solid #b1b3b8;
    border-collapse: collapse;
    width: 100%;
    //   color: var(--el-text-color-regular);
    font-size: 12px;
}

.headerBk {
    background-color: #6d92b4 !important;
}

.content {
    &:extend(.header);
    font-size: 14px;
}

.header>div,
.content>div {
    display: flex;
    border-right: 1px solid #b1b3b8;
    padding: 10px;
    // max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    padding: 5px;
    justify-content: center;
    min-width: 50px;
    max-height: 90px;
}

.header>div:last-child,
.content>div:last-child {
    border-right: none;
}


//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
:deep(.hide > .el-input__wrapper) {
    box-shadow: none;
    transition: box-shadow 0.3s;
    /* 添加过渡效果 */
}

/* 鼠标悬停时显示 box-shadow */
:deep(.hide >.el-input__wrapper:hover) {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    /* 修改为悬停状态下的 box-shadow 样式 */
}

/* 获取焦点时显示 box-shadow */
:deep(.hide >.el-input__wrapper:focus) {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    /* 修改为焦点状态下的 box-shadow 样式 */
}

.table_item {
    min-height: 42px;
    align-items: center;
}

.text-overflow-ellipsis {
    white-space: nowrap;
    /* 防止文本换行 */
    overflow: hidden;
    /* 隐藏溢出文本 */
    text-overflow: ellipsis;
    /* 显示省略号 */
}

.scrollable-div::-webkit-scrollbar {
    z-index: 1;
    /* 将滚动条置于内容的下方 */
}

.scrollable-div {
    overflow-x: auto;
}

:deep(.el-tooltip__trigger) {
    width: 100%;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElSelect,ElOption,ElButton,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getPurchaseListApi } from '@/api/product'
import { checkPermissionApi } from '@/api/tool';
import { cloneDeep } from 'lodash-es';

const { t } = useI18n()
const show = ref(false);

const purchaseData = reactive([]) 
//查询条件
const searchCondition = reactive({
  buy_order_num:'',
  supplier_nick: '',
  收货状态:'',
  _or:true,
  page: 1,
  count: 10,
  inputTxt:'' //界面输入
})

//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getPurchaseList()
  }
})

//修改输入后同步其他条件
watch(searchCondition,(newData)=>{
  searchCondition.buy_order_num = newData.inputTxt
  searchCondition.supplier_nick = newData.inputTxt
})


//查询采购单列表
const getPurchaseList = async (page = 1) => {
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  tmp.收货状态 = tmp.收货状态 == '全部' ? '' : tmp.收货状态
  const ret = await getPurchaseListApi(tmp)
  if(ret)
  {
    purchaseData.splice(0,purchaseData.length, ...ret.data)
    totleCount.value = parseInt( ret.count)
  }
}


//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getPurchaseList()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getPurchaseList(val)
}

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
  console.log(currentRow.value)
  if(currentRow.value?.id == undefined)
  {
    ElMessage.warning('请选择一条数据')
    return
  }

  if(currentRow.value.fsm_cur_state != '已审核')
  {
    ElMessage.warning('未审核订单不能收货！')
    return
  }
   
  closeDlg()
  //返回数据给上层
  
  emit('onSubmit',currentRow.value)
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}



</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center items-center">
      <div class="searchTitle">{{ t('purchase.receipt_status') }}</div>
      <el-select   class="searchItem mr-3" v-model="searchCondition.收货状态" placeholder="请选择" >
        <el-option value="全部">全部</el-option>
        <el-option v-for="item in ['未收货','部分收货','完全收货','超量收货','收货且退货','未收货+部分收货']" :key="item" :label="item" :value="item" />
      </el-select>

      <el-input style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputNameOrID')" v-model="searchCondition.inputTxt" @keyup.enter="getPurchaseList(1)" clearable/>
      <el-button type="primary" @click="getPurchaseList(1)">{{ t('button.search') }}</el-button>
    </div>
    <el-table  ref="tableRef" :data="purchaseData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader" @row-dblclick="onSubmit">
      <el-table-column show-overflow-tooltip fixed prop="buy_order_num" :label="t('purchase.id')"  />
      <el-table-column show-overflow-tooltip fixed :label="'审核状态'"  >
        <template #default="scope">
          <div>{{ scope.row.fsm_cur_state }}</div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip fixed :label="t('purchase.receipt_status')"  >
        <template #default="scope">
          <div>{{ scope.row.收货状态 }}</div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip fixed prop="supplier_name" :label="t('supplier.name')"  >
        <template #default="scope">
          {{ checkPermissionApi('供应商编码显示')?scope.row.supplier_name:'***' }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip fixed prop="supplier_nick" :label="t('supplier.nick')"  :min-width="120">
        <template #default="scope">
          {{ checkPermissionApi('供应商名称显示')?scope.row.supplier_nick:'***' }}
        </template>
      </el-table-column>
      <!-- <el-table-column show-overflow-tooltip fixed prop="合计金额" :label="t('purchase.totle_price')"  /> -->
      <el-table-column show-overflow-tooltip fixed prop="create_date" :label="'下单时间'" :min-width="140"/>
    </el-table>
    <el-pagination class="flex justify-end"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handlePageSizeChange"
        @current-change="handlePageCurrentChange"
    />
    <template #footer>
     <div class="flex justify-end">
      <ElButton type="primary" @click="onSubmit">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
     </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
</style>
<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElMessageBox,ElForm,ElFormItem,ElButton,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { getSpecListApi,addSpecApi,updateSpecApi,delSpesApi } from '@/api/product'

const { t } = useI18n()
const show = ref(false);
const specsData = reactive([]) //规格列表
//查询条件
const searchCondition = reactive({
  name:'',
  page:1,
  count:10
})
//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getSpecsData()
  }
})
//监听show的显示设置 同步到外部
watch(() => show.value, async(val) => {
  if (!val) {
    closeDlg()
  }
})



//查询用户列表
const getSpecsData = async (page = 1) => {
  searchCondition.page = page
  const ret = await getSpecListApi(searchCondition);
  if (ret) {
    specsData.splice(0, specsData.length, ...ret.data);
    totleCount.value =  parseInt(ret.count)
  }
}
//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getSpecsData()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getSpecsData(val)
}

//定义通知
const emit = defineEmits(['update:showHide','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
  console.log(currentRow.value)
  if(currentRow.value?.id == undefined)
  {
    ElMessage.warning('请选择一条数据')
    return
  }
   
  closeDlg()
  //返回数据给上层
  emit('onSubmit',currentRow.value?.id,currentRow.value?.name,currentRow.value?.text)
}
//关闭
const closeDlg = ()=>{
  emit('update:showHide',false)
  show.value = false
}


//创建新规格
const onAddSpecs = ()=>{
  showModifySpecs.value = true
  showTitle.value = t('specs_manage.add')
  specsDataCache.reset()
}
//是否显示规格编辑窗口
const showModifySpecs = ref(false)
//规格编辑窗口标题
const showTitle = ref('')
//规避编辑界面缓存数据
const defaultSpecsData = {
  id:'',
  name:'',
  text:'',
  json:[]
}
const specsDataCache = reactive({
  ...defaultSpecsData,
  reset() {
    Object.assign(this, defaultSpecsData)
  }
})
const onSaveSpecs = async()=>{
  if(specsDataCache.id == '') //新增
  {
    const ret = await addSpecApi(specsDataCache)
    if(ret)
    {
      getSpecsData()
      showModifySpecs.value = false
    }
  } 
  else //修改
  {
    const ret = await updateSpecApi(specsDataCache)
    if(ret)
    {
      getSpecsData()
      showModifySpecs.value = false
      ElMessage.success(t('msg.success'))
    }
  }
}

const onDelSpes = async(row)=>{
      //确认是否删除规格
      const confirmRes = await ElMessageBox.confirm(t('msg.confirm_del_spec'), t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error'
    }).catch(()=>{
      
    })
    if(confirmRes !== 'confirm')
      return

    //删除规格
    const ret = await delSpesApi({
      ids:[row.id]
    })
    if(ret)
    {
      //提示操作成功
      ElMessage.success(t('msg.delOK'))
      getSpecsData()
    }
}

</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-input style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputSpesName')" v-model="searchCondition.name" />
      <el-button type="primary" @click="getSpecsData(1)">{{ t('button.search') }}</el-button>
      <el-button type="success" @click="onAddSpecs()">{{ t('button.add') }}</el-button>
    </div>
    <el-table  ref="tableRef" :data="specsData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip fixed prop="id" :label="'规格ID'" width="120" />
      <el-table-column show-overflow-tooltip fixed prop="name" :label="t('specs_manage.name')"  width="220"/>
      <el-table-column show-overflow-tooltip fixed prop="text" :label="t('specs_manage.content')"  />
      <el-table-column show-overflow-tooltip fixed :label="'操作'"  >
        <template #default="scope">
          <el-button size="small" type="danger" @click="onDelSpes(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handlePageSizeChange"
        @current-change="handlePageCurrentChange"
    />
    <template #footer>
      <ElButton type="primary" @click="onSubmit">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
    </template>

     <!-- 编辑规格窗口 -->
     <Dialog v-model="showModifySpecs" :title="showTitle" >
      <el-form ref="form" :model="specsDataCache" label-width="100px">
        <el-form-item v-if="specsDataCache.id == ''?false:true" :label="t('specs_manage.id')">
          <div>{{ specsDataCache.id }}</div>
        </el-form-item>
        <el-form-item :label="t('specs_manage.name')">
          <el-input v-model="specsDataCache.name" class="!w-[50%]"/>
        </el-form-item>
        <el-form-item :label="t('specs_manage.content')">
          <el-input v-model="specsDataCache.text" :autosize="{ minRows: 10, maxRows: 10 }"   type="textarea"/>
        </el-form-item>
      </el-form>

        <template #footer>
          <ElButton type="primary" @click="onSaveSpecs">
            {{ t('msg.ok') }}
          </ElButton>
          <ElButton @click="showModifySpecs = false">{{ t('common.channel') }}</ElButton>
        </template>
      </Dialog>

  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
</style>
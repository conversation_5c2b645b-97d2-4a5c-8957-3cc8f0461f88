<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElCheckbox,ElMessage } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';

const { t } = useI18n()
const showDeptSel = ref(false);
const arrExpandRowKeys = reactive([])
const depList = reactive([]) //部门树，过滤了人员

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
  depData: Array<any>,
  param:any
}>()
//监听外部传入的显示设置
watch(() => props.show, (val) => {
  console.log("监听到变化刷新界面")
  if (val) {
    showDeptSel.value = true

    arrExpandRowKeys.splice(0,arrExpandRowKeys.length)
    const tmpData = cloneDeep(props.depData)
    //处理界面需要显示的数据
    tmpData.forEach(it=>{
      deelData(it)
      arrExpandRowKeys.push(it.guuid)
    })
    depList.splice(0,depList.length,...tmpData)
  }
})

//监听show的显示设置 同步到外部
watch(() => showDeptSel.value, async(val) => {
  if (!val) {
    closeDlg()
  }
})



const deelData = (item)=>{
  item.check = false
  //遍历children节点如果type为user则删除该节点
  if(item.children){
    //从后往前删除所有type是user的子节点
    for(let i=item.children.length-1;i>=0;i--)
    {
      if(item.children[i].type==='user'){
        item.children.splice(i,1)
      }
      else
      {
        deelData(item.children[i])
      }
    }


    // item.children.forEach(it=>{
    //   if(it.type==='user'){
    //     item.children.splice(item.children.indexOf(it),1)
    //   }
    //   else
    //   {
    //     deelData(it)
    //   }
    // })
  }
}
//获取depList中所有被选中的内容
//获取所有选中的人员
const getAllSelectDept = ()=>{
  //遍历depData
  let arrSel = []
  for(let one of depList)
  {
    getSelectDept(one,arrSel)
  }
  return arrSel
}
const getSelectDept = (item,arraySel)=>{
  if(item.check && item.type == 'dept')
  {
    arraySel.push(item.id)
    console.log("-------------------")
    console.log(item)
  }
  //遍历children
  if(item.children)
  {
    for(let one of item.children)
    {
      getSelectDept(one,arraySel)
    }
  }
}


//定义通知
const emit = defineEmits(['update:showHide','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
  console.log(depList)
  let arrSel = getAllSelectDept()
  if(arrSel.length == 0)
  {
    if(currentRow.value == undefined)
    {
      ElMessage.error('请选择部门')
      return
    }
    else
    {
      console.log("2222222222222222")
      console.log(currentRow.value)
      arrSel.push(currentRow.value.id)
    }
  }
  console.log(arrSel)
  closeDlg()
  //返回数据给上层
  emit('onSubmit',arrSel,props.param)
}
//关闭
const closeDlg = ()=>{
  emit('update:showHide',false)
  showDeptSel.value = false
}


</script>

<template>
  <Dialog v-model="showDeptSel" :title="title" max-height="60vh" @close="closeDlg">
    <el-table  ref="tableRef" :data="depList" style="width: 100%; margin-bottom: 20px" row-key="guuid" border  :expand-row-keys="arrExpandRowKeys"
      highlight-current-row @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader" >
      <el-table-column  :label="t('userTable.name')" min-width="60%">
        <template #default="scope" >
          <el-checkbox v-model="scope.row.check" size="small" class="!mr-2"/>
          <Icon :icon="scope.row.type == 'dept' ? 'octicon:organization-16' : 'teenyicons:user-outline'" :class="scope.row.name?'iconDept':'iconUser'"/>
          <span style="margin-left: 10px">{{ scope.row.name?(scope.row.name+" ("+scope.row.total_user_count+"人)"):scope.row.username }}</span>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <ElButton type="primary" @click="onSubmit">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }



</style>
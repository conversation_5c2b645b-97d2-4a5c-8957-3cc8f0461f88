<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElTooltip,ElTag, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { getProductListApi } from '@/api/product'

const { t } = useI18n()
const show = ref(false);

const productData = reactive([]) //产品列表

//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
  param: []
}>()

watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    Object.assign(productData,props.param)
  }
})


//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
  console.log(currentRow.value)
  if(currentRow.value?.id == undefined)
  {
    ElMessage.warning('请选择一条数据')
    return
  }
   
  closeDlg()
  //返回数据给上层
  emit('onSubmit',currentRow.value)
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}


</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" width="80%" @close="closeDlg">
    <el-table  ref="tableRef" :data="productData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader" cell-class-name="tableCell">
      <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index+1 }}
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130"/>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip                        
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('purchase.count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.采购数量 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('purchase.receipt_count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.已收货 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('purchase.return_count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.已退货 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="交货日期" :label="t('purchase.business_date')" min-width="90"/>
            <el-table-column show-overflow-tooltip  prop="单价" :label="t('receipt.price')" />
            <el-table-column show-overflow-tooltip  prop="折扣" :label="t('receipt.cut')" />
            <el-table-column show-overflow-tooltip  prop="发票税率" :label="t('receipt.tax')" />
            <el-table-column show-overflow-tooltip  prop="总价" :label="t('receipt.totle_price')" />
            <el-table-column show-overflow-tooltip  prop="remark" :label="t('purchase.remark')" />
            <el-table-column show-overflow-tooltip  prop="供应商产品编码" :label="t('receipt.supplier_pdt_num')" min-width="130"/>
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
//   text-align:center;
// }
// :deep(.tableCell){
//   text-align:center;
// }
</style>
<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTag,ElForm,ElFormItem,ElSelect,ElOption,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput, FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import {checkFormRule} from '@/api/tool'
import { addStoreApi,updateStoreApi,getStoreNewnumApi } from '@/api/product'
import { DialogUser } from '@/components/DialogUser'

const { t } = useI18n()
const props = defineProps<{
  show: boolean,
  title: string,
  data: {},
  bReturnData: boolean,
}>()

//自我显示
const show = ref(false)

//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    //显示窗口时刷新数据
    show.value = val
    console.log('1122',props.data)

    if(props.data && props.data.id)
    {
      storeData.value = cloneDeep(props.data)
    }
    else{
      storeData.value = {
          "name": "",
          "nick": "",
          "type": "良品库",
          "mainer": "",
          "mainer_name":'',
          "linkman": "",
          "telephone": "",
          "email": "",
          "address": "",
          "note": "",
          "warnning_man": [],
          "warnning_name": [],
          "config": {},
          "remark": "",
      }
      onUpdateID()
    }
  }
})


//数据缓存
const storeData = ref<any>({});
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    name: [{ required: true, message: t('msg.noStoreName'), trigger: 'blur' }],
    nick: [{ required: true, message: t('msg.noStoreNick'), trigger: 'blur' }],    
})

//更新ID
const onUpdateID = async()=>{  
  const ret = await getStoreNewnumApi()
    if(ret)
    {
        console.log(ret)
        storeData.value.name = ret.data.new_id
    }
}

//显示隐藏选择负责人窗口
const showSelManager = ref(false)
const onSelManager = ()=>{
  showSelManager.value = true 
}
//选择管理员回调
const onSelManagerCallBack = (id,name,param)=>{
  storeData.value.mainer = id
  storeData.value.mainer_name = name
  showSelManager.value = false
}



//定义通知
const emit = defineEmits(['update:show','onSubmit','update:data'])
//提交选择的数据
const onSubmit = async()=>{
  const rule = await checkFormRule(ruleFormRef.value)
  if(!rule)
  {
      ElMessage.warning(t('msg.checkRule'))
      return
  }

  if(props.bReturnData)
  {
    console.log('已提交修改')
    emit('update:data',storeData.value)
    emit('onSubmit')
    closeDlg()
  }
  else
  {
    if(storeData.value.id == undefined)
    {
      const ret = await addStoreApi(storeData.value)
      if(ret)
      {        
        closeDlg()
        //返回数据给上层
        emit('onSubmit')
      }
    }
    else
    {
      const ret = await updateStoreApi(storeData.value)
      if(ret)
      {
        closeDlg()
        //返回数据给上层
        emit('onSubmit')
      }
    }
  }
}

//删除数组人员
const funDelPeople = (arrID,arrName,id)=>{
  for(let i=0;i<arrID.length;i++)
  {
    if(arrID[i] == id)
    {
      arrID.splice(i,1)
      arrName.splice(i,1)
      return
    }
  }
}

//显示隐藏人员选择
const showWarnSelDlg = ref(false)
//新增报价人员
const onAddWarnPeople = ()=>{
  showWarnSelDlg.value = true
}
//人员选择回调
const onSelPeopleOK =  (id,name,param)=>{
  funAddPeople(storeData.value.warnning_man,storeData.value.warnning_name,id,name)
}
//添加人员到数组
const funAddPeople = (arrID,arrName,id,name)=>{
  //检测是否已经存在
  for(let i=0;i<arrID.length;i++)
  {
    if(arrID[i] == id)
    {
      return
    }
  }
  //新增
  arrID.push(id)
  arrName.push(name)
}


//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}


</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg" width="80%" class="max-w-[800px]">
    <el-form :rules="rules" :model="storeData" ref="ruleFormRef"> 
          <el-form-item prop="name" :label="t('store.name')">
              <el-input class="!w-[70%] mr-3" v-model="storeData.name"  />
              <ElButton type="success" @click="onUpdateID">
                <Icon class="mr-0.5" icon="radix-icons:update" />
                刷新
              </ElButton>
          </el-form-item>
          <el-form-item prop="nick" :label="t('store.nick')">
              <el-input  v-model="storeData.nick"  />
          </el-form-item>
          <el-form-item  :label="t('store.prop')">
            <el-select class="ml-3" v-model="storeData.type" placeholder="Select" >
                <el-option v-for="item in ['良品库','不良品库','线边仓']" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item  :label="t('store.manager')">
            <div>{{ storeData.mainer_name }}</div>
            <ElButton color="#409EFF" style="color:#fff" @click="onSelManager">设置</ElButton>
          </el-form-item>
          <el-form-item  :label="t('store.linkman')">
              <el-input  v-model="storeData.linkman"  />
          </el-form-item>
          <el-form-item  :label="t('store.phone')">
              <el-input  v-model="storeData.telephone"  />
          </el-form-item>
          <el-form-item :label="t('store.email')">
              <el-input  v-model="storeData.email"  />
          </el-form-item>
          <el-form-item  :label="t('store.address')">
              <el-input  v-model="storeData.address"  />
          </el-form-item>
          <el-form-item  :label="t('store.remark')">
            <el-input v-model="storeData.remark" clearable :autosize="{ minRows: 2, maxRows: 2 }"
                            type="textarea" />
          </el-form-item>
          <el-form-item :label="t('store.alarm_user')">            
            <el-tag v-for="(item, index) in storeData.warnning_name" :key="index" :closable="true" @close="funDelPeople(storeData.warnning_man,storeData.warnning_name,storeData.warnning_man[index])">{{ item }}</el-tag>
            <el-tag class="tag cursor-pointer"  effect="dark" @click="onAddWarnPeople">新增</el-tag>
          </el-form-item>
    </el-form>

    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
         {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>

    <!-- 选择管理员 -->
    <DialogUser :param="{}" :title="t('title.seluser')" v-model:show="showSelManager" @on-submit="onSelManagerCallBack"/>
    <!-- 选择预警人员 -->
    <DialogUser :param="{}" :title="t('title.seluser')" v-model:show="showWarnSelDlg" @on-submit="onSelPeopleOK"/>

  </Dialog>
</template>

<style lang="less" scoped>
//修改form标签样式
:deep(.el-form-item__label){
    font-weight: 800;
    font-size: 16px;
    width: 120px;
}
//标签后批量加冒号
:deep(.el-form-item__label::after){
    content: ':';
    margin-left: 4px;
}
</style>
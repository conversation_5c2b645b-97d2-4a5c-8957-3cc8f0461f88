<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElImageViewer,ElDialog,ElImage,ElButton,ElTable,ElTableColumn,ElUpload, ElPopconfirm,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { getProductListApi } from '@/api/product'
import { DialogProduct } from '@/components/DialogProduct';
import { DialogProductSel } from '@/components/DialogProductSel'
import { useRouter } from 'vue-router'
import { useCache } from '@/hooks/web/useCache'
import { getOssSignApi,ossUpload } from '@/api/oss'

const { push} = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

const show = ref(false);
const props = defineProps<{
  show: boolean,
  files:any  //文件列表
  path:string  //文件夹路径
  title:string //标题
  type:string //类型
}>()

const fileData = reactive(props.files) //文件列表
const title = ref('')
const accept = ref('')

//是否有变更
const bModify = ref(false)

//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    title.value = props.title
    console.log(props.title)
    if(props.type == '图片')
    {
      accept.value = 'image/*'
    }
    else  
    {
      accept.value = ''
    }
  }
})

watch(() => props.files, async(val) => {
  if(val != undefined)
    fileData.splice(0,fileData.length,...val)
})

//定义通知
const emit = defineEmits(['update:show','onSubmit','onUpdate'])
//提交选择的数据
const onSubmit = ()=>{
  closeDlg()
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

watch(fileData, () => {
  
  if(bModify.value)
  {
    emit('onUpdate', fileData);
    bModify.value = false
    console.log('变了！！')
  }

  
})

//删除某个文件
const onDeleteFile = (row)=>{
  console.log(row)
  fileData.splice(row.index,1)
  bModify.value = true
}

const uploadImg = async(file) => {
    let path = props.path
    const ret =await getOssSignApi({upload_dir:path})
    if(ret)
    {    
        loading.value = true
        const end = await ossUpload(ret.data.token,file.file,path,(pro)=>{
            console.log('pppp',pro)
            
        })
        bModify.value = true
        console.log('完成',end)     
        loading.value = false   
        fileData.push({
            name:file.file.name,
            url:end.url
        })
    }
}

//正在上传
const loading = ref(false)

//下载文件
const onDownloadFile = (row)=>{
  downloadFile(row.url,row.name)
}

//实现downloadFile直接调用浏览器下载文件
function downloadFile(url, filename) {
  fetch(url)
    .then(res => res.blob())
    .then(blob => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      a.remove();
    });
}

const isImage = (url) => {
  // 此处你可以根据实际情况判断链接是否是图片链接
  // 这里只是一个简单的示例，你可能需要使用更复杂的逻辑来判断链接是否是图片链接
  return url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif');
}

const onPreViewFile = (url) => {
  if (isImage(url.url))
  {
    handlePreviewPic(url)
  }
  else
      window.open('https://view.officeapps.live.com/op/view.aspx?src='+url.url, '_blank');
}

const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const handlePreviewPic = (uploadFile) => {
    dialogImageUrl.value = uploadFile.url!
    dialogVisible.value = true
    console.log('预览图片')
}
</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" width="80%" @close="closeDlg">
    <el-upload
    :http-request="(file) => uploadImg(file)"
    :auto-upload="true"
    :accept="accept"
    >
      <template #trigger>
        <el-button type="primary" :loading="loading" :disabled="loading">{{ loading?'上传中..':'选择文件' }} </el-button>
      </template>

      <template #tip>
        <div class="el-upload__tip">
          {{ loading?'上传中请勿关闭当前页面..':'点击选择上传的文件!' }}
        </div>
      </template>
    </el-upload>
    <el-table  ref="tableRef" :data="fileData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip fixed prop="name" label="文件名"/>
      <el-table-column show-overflow-tooltip fixed label="预览">
        <template #default="scope">
          <el-image v-if="scope.row.url && isImage(scope.row.url)"  class="object-fill w-[80px] h-[80px] min-w-[80px] cursor-pointer" :src="scope.row.url" @click='onPreViewFile(scope.row)'/>
        </template>
      </el-table-column>
      <el-table-column fixed label="操作"  width="200px">
        
        <template #default="scope">
          <div class="flex">
            <ElButton size="small" type="warning" @click="onPreViewFile(scope.row)">预览</ElButton>
            <ElButton v-if="scope.row.url && !isImage(scope.row.url)" size="small" type="success" @click="onDownloadFile(scope.row)">下载</ElButton>
            <el-popconfirm  title="是否确认删除该文件?" @confirm="onDeleteFile(scope)">
                <template #reference>
                  <ElButton size="small" type="danger" >删除</ElButton>
                </template>
            </el-popconfirm>            
          </div>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>

    <!-- <el-dialog v-model="dialogVisible" width="100%" class="flex justify-center">
        <img w-full :src="dialogImageUrl" alt="预览" />
    </el-dialog> -->
    <el-image-viewer
      v-if="dialogVisible"
      :url-list="[dialogImageUrl]"
      :initial-index="0"
      @close="dialogVisible = false"
    />

  </Dialog>
</template>

<style lang="less" scoped>
:deep(.cell){
  color: black;
}
</style>
<script setup lang="ts">
import { reactive,watch } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElSelect,ElOption, ElMessage,ElInput } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { addFollowInfoApi,getFollowInfoListApi } from '@/api/customer'

const { t } = useI18n()
//跟进方式
const followTypeData = reactive([
    '微信',
    '企业微信',
    '电话',
    '上门',
    '其他',
])
//跟进状态
const followStatusData = reactive([
    '有效跟进',
    '无效跟进'
])
//跟踪记录提交缓存数据
const forllowSubmitData = reactive({
    buyer_id:'',        //客户ID
    creater_id:'',      //操作者ID
    follow_type: '',    //跟进类型
    follow_status: '',  //跟进状态
    follow_text: '',    //跟进内容
    log_type:'',
})
//跟踪记录数据源
const followData = reactive([])
//新增新的跟踪记录
const onAddNewFollweInfo = async()=>{
    forllowSubmitData.buyer_id = props.cus_id
    forllowSubmitData.creater_id = props.self_id
    forllowSubmitData.log_type = props.log_type

    const ret = await addFollowInfoApi(forllowSubmitData)
    if(ret)
    {               
        ElMessage.success(t('msg.success'))
        //关闭窗口
        forllowSubmitData.follow_type = ''
        forllowSubmitData.follow_status = ''
        forllowSubmitData.follow_text = ''
        await getFullowList()
    }
}
//获取跟踪列表
const getFullowList = async()=>{
    const ret = await getFollowInfoListApi({
        buyer_id:props.cus_id,
        log_type:props.log_type,
        page:1,
        count:1000
    })
    if(ret)
    {
      console.log('---------------')
        console.log(ret)
        followData.splice(0,followData.length,...ret.data)
    }
}


const props = defineProps<{
  cus_id:string,  //传入得客户id
  self_id:string, //自己ID
  log_type:string,//跟踪类型
}>()

watch(() => props.cus_id, async(val) => {
  if (val) {
    getFullowList()
  }
})

//定义通知
//const emit = defineEmits(['update:showHide','onSubmit'])




const ontest = ()=>{
  console.log(followData)
  console.log(props.cus_id)
}
</script>

<template>
  <div>
    <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
        <div class="inline-flex items-center mr-5">
            <div class="text-sm">{{ t('customer.follow_type') }}</div>
            <el-select class="ml-3" v-model="forllowSubmitData.follow_type" placeholder="请选择" size="small">
                <el-option v-for="item in followTypeData" :key="item" :label="item" :value="item" />
            </el-select>
        </div>
        <div class="inline-flex items-center mr-5">
            <div class="text-sm">{{ t('customer.follow_status') }}</div>
            <el-select class="ml-3" v-model="forllowSubmitData.follow_status" placeholder="请选择" size="small">
                <el-option v-for="item in followStatusData" :key="item" :label="item" :value="item" />
            </el-select>
        </div>
        <div class="inline-flex items-center mr-5">
            <ElButton type="danger" @click="onAddNewFollweInfo">{{ t('button.addNewFollowinfo') }}</ElButton>            
        </div>
    </div>
    <el-input v-model="forllowSubmitData.follow_text" clearable :autosize="{ minRows: 10, maxRows: 4 }"     type="textarea" class="!w-[50%]" />
    <el-table class="mt-5" header-cell-class-name="tableHeader" :data="followData" style="width: 100%"  border stripe>
        <el-table-column show-overflow-tooltip fixed prop="create_date" :label="t('customer.time')" width="180"/>
        <el-table-column show-overflow-tooltip fixed  :label="t('customer.operator')" width="180">
            <template #default="scope">
                <div>{{ "("+scope.row.creater_usrname+")"+scope.row.creater_resname }}</div>
            </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip fixed prop="follow_type" :label="t('customer.follow_type')" width="180"/>
        <el-table-column show-overflow-tooltip fixed prop="follow_text" :label="t('customer.follow_text')" />
        <el-table-column show-overflow-tooltip fixed prop="follow_status" :label="t('customer.follow_status')" width="180"/>
    </el-table>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }
</style>
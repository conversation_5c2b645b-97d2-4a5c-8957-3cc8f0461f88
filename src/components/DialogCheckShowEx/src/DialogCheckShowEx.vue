<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getSysLogApi } from '@/api/product'
import { DialogPreSaleCap } from '@/components/DialogPreSaleCap'

const { t } = useI18n()
const show = ref(false);

const props = defineProps<{
  show: boolean,
  type: string,   //类别
  module:string,  //日志模块
  title: string, //单号
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getSysLog()
  }
})

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
   
  closeDlg()
  //返回数据给上层  
  // emit('onSubmit',currentRow.value)
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

const logData = reactive([])
const getSysLog = async()=>{
  const ret = await getSysLogApi({
    type: props.type,
    module: props.module,
    title: props.title,
    page:1,
    count:1000
  })
  if(ret)
  {
     console.log(ret)
     logData.splice(0, logData.length, ...ret.data);
  }
}

//当前选择快照
const curSelCap = ref({})
//显示隐藏快照
const showHisCap = ref(false)
//查看快照
const onShow = (item)=>{
  console.log(item)
  showHisCap.value = true
  curSelCap.value = item.json
}


</script>

<template>
  <Dialog v-model="show" :title="'任务历史记录'" max-height="60vh" @close="closeDlg">
    <el-table  ref="tableRef" :data="logData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip fixed prop="['json'].modify_date" label="操作时间"  />
      <el-table-column show-overflow-tooltip fixed prop="['json'].fsm_exe_trig" label="操作"  />
      <el-table-column show-overflow-tooltip fixed prop="['json'].fsm_exe_log" label="备注"  />
      <el-table-column show-overflow-tooltip fixed prop="['json'].fsm_exe_man_name" label="操作员"  />
      <el-table-column show-overflow-tooltip align="center" fixed label="快照"  >
        <template #default="scope">
          <el-button type="primary" size="small" @click="onShow(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>

    <DialogPreSaleCap v-model:show="showHisCap" :data="curSelCap"/>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色

</style>
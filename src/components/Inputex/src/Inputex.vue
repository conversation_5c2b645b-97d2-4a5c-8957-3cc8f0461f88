<script setup lang="ts">
import { ElInput } from 'element-plus';
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue';



const data = ref('---')
const props = defineProps<{
  inputvalue: string
}>()


watch(() => props.inputvalue, async(val) => {
  data.value = val
})
const emit =  defineEmits(['change']);

onMounted(() => {
  data.value = props.inputvalue
})
</script>

<template>
  <!-- <ElInput v-model="data"  @change="emit('change',data)" size="small"/> -->
  <input
  class="!w-[100%]"
    type="text"
    :value="data"
    @change="emit('change',$event.target.value)"
  />
</template>

<style lang="less" scoped>
input {
    background-color: #1469bd00;
    border: #aca9a97d solid 1px;
	box-sizing: conte-box;
}

input:focus {
    border-style: solid;
    border-color: #03a9f4;
    box-shadow: 0 0 15px #03a9f4;
    outline: none;
}

</style>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { ElDropdownMenu,ElDropdown,ElDropdownItem, ElButton, ElTable, ElTableColumn, ElMessage, ElMessageBox } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { getHashApi, setHashApi } from '@/api/extra';

const { t } = useI18n()
const show = ref(false);

const baseunitData = reactive([]) //人员列表


const props = defineProps<{
  show: boolean,
}>()
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    show.value = true
    getBaseUnitData()

  }
})

const getBaseUnitData = async () => {
  const ret = await getHashApi({
    name: 'baseunit',
    page: 1,
    count: 10000
  })
  if (ret) {
    baseunitData.splice(0, baseunitData.length, ...ret.data.json)
  }
}

//定义通知
const emit = defineEmits(['update:show', 'onSubmit'])
//提交选择的数据
const onSubmit = () => {
  closeDlg()
  //返回数据给上层
  emit('onSubmit')
}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

const onAddBaseUnit = async () => {
  ElMessageBox.prompt(
    '请输入单位',
    '新增单位',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }
  )
    .then(async ({ value }) => {
      //修改备注
      const ret = await setHashApi({
        name: 'baseunit',
        json: [...baseunitData, { name: value }]
      })
      if (ret) {
        ElMessage.success('新增成功')
        getBaseUnitData()
      }

    })
    .catch(() => {

    })
}

const handleOper = (type, row) => {
  if (type === 'edit') {
    ElMessageBox.prompt(
      '请输入单位',
      '修改单位',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.name
      }
    )
      .then(async ({ value }) => {

        for (let i = 0; i < baseunitData.length; i++) {
          if (baseunitData[i].name === row.name) {
            baseunitData[i].name = value;
          }
        } 
        console.log(baseunitData)
        const ret = await setHashApi({
          name: 'baseunit',
          json: baseunitData
        })
        if (ret) {
          ElMessage.success('修改成功')
          getBaseUnitData()
        }

      })
  }
  else if (type === 'del') {
    ElMessageBox.confirm(
      '确定删除该单位吗？',
      '删除单位',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }
    )
      .then(async () => {
        const ret = await setHashApi({
          name: 'baseunit',
          json: baseunitData.filter(item => item.name !== row.name)
        })
        if (ret) {
          ElMessage.success('删除成功')
          getBaseUnitData()
        }

      })
      .catch(() => {

      })
  }
}

</script>

<template>
  <Dialog v-model="show" :title="'计量单位编辑'" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-button type="primary" @click="onAddBaseUnit">新增</el-button>
    </div>
    <el-table ref="tableRef" :data="baseunitData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      header-cell-class-name="tableHeader">
      <el-table-column align="center" show-overflow-tooltip fixed prop="name" :label="'单位'" />
      <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
        <template #default="scope">
          <el-dropdown trigger="click" placement="left">
            <span class="el-dropdown-link">
              <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleOper('edit', scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                <el-dropdown-item @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit" class="mr-4">
          关闭
        </ElButton>
      </div>

    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #f0f9eb !important;
  --el-table-current-row-bg-color: #f0f9eb !important;
  --el-table-row-hover-bg-color: #f0f9eb;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
}
</style>
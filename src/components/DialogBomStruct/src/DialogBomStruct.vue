<script setup lang="ts">
import { ref, reactive,watch } from 'vue'
import { ElTable,ElTableColumn,ElTag,ElButton,ElForm,ElFormItem,FormRules,ElCheckboxGroup,ElCheckbox,ElTreeSelect,ElUpload,ElCard, ElTabPane,ElTabs, ElRadio,ElRadioGroup, ElSelect, ElOption, ElTooltip, ElInput,ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import { getCategListApi,getProductNewnumApi,addProductApi,getProductListApi,updateProductApi, getBomInfoApi } from '@/api/product'
import type { FormInstance, UploadProps, UploadUserFile } from 'element-plus'
import { DialogSpecs } from '@/components/DialogSpecs'
import {checkFormRule} from '@/api/tool'
import { getOssSignApi,ossUpload } from '@/api/oss'
import { Dialog } from '@/components/Dialog'

const { t } = useI18n()
const { currentRoute } = useRouter()


const tableData = ref([])

const show = ref(false);
const props = defineProps<{
  show: boolean,
  bomid: string, //BOMID
 }>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getBomStruct()
  }
})


//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
   
  closeDlg()
  //返回数据给上层  
  emit('onSubmit')
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}


//查询BOM结构图
const getBomStruct = async()=>{
  const ret = await getBomInfoApi({
    id:props.bomid,
    is_all_level :1
  })
  if(ret)
  {
    console.log(ret)
    tableData.value = [ret.data]

    console.log(tableData.value)
  }
}

onMounted(async () => {    
  
})




</script>

<template>
  <Dialog v-model="show" :title="'BOM结构图'" width="99%" :max-height="700" @close="closeDlg">
    <el-table
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="pdt_data.id"
      border
      default-expand-all
      :tree-props="{ children: 'sub_bom'}"
    >
      <el-table-column prop="pdt_data.nick" label="产品名称" show-overflow-tooltip min-width="350"/>
      <el-table-column prop="pdt_level" label="层级" show-overflow-tooltip width="70"/>
      <el-table-column prop="pdt_data.name" label="物料编码" show-overflow-tooltip  width="130" >
        <template #default="scope">
          {{  scope.row.pdt_data.pdt_name == undefined?scope.row.pdt_data.name:scope.row.pdt_data.pdt_name }}
        </template>
      </el-table-column>
      <el-table-column label="规格" show-overflow-tooltip  max-width="230" >
        <template #default="scope">        
          <el-tooltip
            v-if="scope.row.pdt_data.specs_name != '' && scope.row.pdt_data.specs_name != undefined"
            class="box-item"
            effect="dark"
            :content="scope.row.pdt_data.specs_text"
            placement="bottom"
          >
          <el-tag style="white-space: normal;max-width: 300px;overflow: hidden;text-overflow: ellipsis;" type="success" effect="dark">{{ scope.row.pdt_data.specs_name=='自定义规格'?scope.row.pdt_data.specs_text:scope.row.pdt_data.specs_name }}</el-tag>
          
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="pdt_data.用量" label="用量" show-overflow-tooltip width="100"/>
      <el-table-column prop="pdt_data.base_unit" label="单位" show-overflow-tooltip width="100"/>
      <el-table-column prop="pdt_data.损耗率" label="损耗率" show-overflow-tooltip width="100"/>
      <el-table-column prop="pdt_data.remark" label="备注" show-overflow-tooltip />
    </el-table>
    <template #footer>
      <ElButton type="primary" @click="onSubmit">
        关闭
      </ElButton>
    </template>
  </Dialog>
</template>


<script setup lang="ts">
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>utton } from 'element-plus'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'
import { ref, onMounted, defineEmits } from 'vue'
import { Sticky } from '@/components/Sticky'
import { useI18n } from '@/hooks/web/useI18n'
const { t } = useI18n()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('content-detail-wrap')

defineProps({
  title: propTypes.string.def(''),
  message: propTypes.string.def('')
})
const emit = defineEmits(['back'])
const offset = ref(85)
const contentDetailWrap = ref()
onMounted(() => {
  // --app-content-padding 20px
  offset.value = contentDetailWrap.value.getBoundingClientRect().top - 20;
})
</script>

<template>
  <div :class="[`${prefixCls}-container`, 'relative bg-[#fff] min-h-[100%]']" ref="contentDetailWrap">
    <Sticky :offset="offset" class="w-[100%] bbb">
      <div :class="[
        `${prefixCls}-header`,
        'flex border-bottom-1 h-50px items-center text-center bg-white pr-10px   aaa', 'pl-4', 'pr-4'
      ]">
        <div :class="[`${prefixCls}-header__back`, 'flex pl-10px pr-10px ']">
          <el-button @click="emit('back')">
            <Icon icon="ep:arrow-left" class="mr-5px" />
            {{ t('common.back') }}
          </el-button>
          <slot name="left"></slot>
        </div>
        <div :class="[`${prefixCls}-header__title`, 'flex flex-1  justify-center']">
          <slot name="title">
            <label class="text-16px font-700">{{ title }}</label>
          </slot>
          <slot name="mid"></slot>
        </div>
        <div :class="[`${prefixCls}-header__right`, 'flex  pl-10px pr-10px']">
          <slot name="right"></slot>
        </div>
      </div>
    </Sticky>
    <!-- <div style="padding: var(--app-content-padding)"> -->
    <div style="padding: var(--app-content-padding);">
      <ElCard :class="[`${prefixCls}-body`, 'mb-20px']" shadow="never">
        <div>
          <slot></slot>
        </div>
      </ElCard>
    </div>
  </div>
</template>

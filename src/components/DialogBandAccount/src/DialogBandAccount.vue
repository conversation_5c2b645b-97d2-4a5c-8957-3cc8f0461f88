<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch, reactive } from 'vue';
import { ElInputNumber, ElButton, ElDescriptions, ElDescriptionsItem, ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElMessage, ElInput, FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { checkFormRule } from '@/api/tool'
import { addFinanceAccountApi, getFinanceAccountInfoApi, getFinanceAccountNewnumApi, updateFinanceAccountApi } from '@/api/finance';
import { DialogUser } from '@/components/DialogUser'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'

const { wsCache } = useCache()
const appStore = useAppStore()

const { t } = useI18n()
const props = defineProps<{
  show: boolean,
  id: string,
}>()

//自我显示
const show = ref(false)
const title = ref('')

//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    //显示窗口时刷新数据
    show.value = val
    console.log('====',props.id)
    if (props.id == '') {
      onChangeID()
      title.value = '新增账号'
      const info = wsCache.get(appStore.getUserInfo)
      accountData.main_man_id = info.id
      accountData.main_man_name = info.resident_name
    }
    else {
      title.value = '编辑账号'
      const ret = await getFinanceAccountInfoApi({
        id: props.id
      })
      if (ret) {
        Object.assign(accountData, ret.data)
      }
    }
  }
})
//默认币种
const moneyTypeData = reactive([
  '人民币',
  '美元',
  '港币',
  '日元',
  '欧元',
  '英镑',
  '韩元',
  '澳大利亚元',
])
//支付方式
const payTypeData = reactive([
  '银行转账',
  '现金',
  '支票',
  'POS机',
  '支付宝',
  '微信',
  '银联',
])

//账号数据
const accountData = reactive({
  name: '',
  nick: '',
  bank_account: '',
  money_type: '人民币',
  pay_type: '银行转账',
  init_money: 0,
  main_man_id: '',
  main_man_name: '',
  status: '已启用'
});
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
  nick: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  bank_account: [{ required: true, message: '请输入卡号', trigger: 'blur' }]
})

//定义通知
const emit = defineEmits(['update:show', 'onSubmit', 'update:data'])
//提交选择的数据
const onSubmit = async () => {
  const rule = await checkFormRule(ruleFormRef.value)
  if (!rule) {
    ElMessage.warning(t('msg.checkRule'))
    return
  }

  if(props.id == '')
  {
     const ret = await addFinanceAccountApi(accountData)
     if(ret)
     {
       ElMessage.success('创建成功')
       emit('onSubmit')
       closeDlg()
     }
  }
  else
  {
    const ret = await updateFinanceAccountApi(accountData)
    if(ret)
    {
      ElMessage.success('修改成功')
      emit('onSubmit')
      closeDlg()
     }
  }

}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

const onChangeID = async () => {
  const ret = await getFinanceAccountNewnumApi()
  if (ret) {
    console.log(ret)
    accountData.name = ret.data.new_id
  }
}


//显示隐藏选择销售员窗口变量
const showSelManageUserDlg = ref(false)
//显示选择销售员弹窗
const onSelManager = () => {
  showSelManageUserDlg.value = true
}
//选择销售员回调
const onSelManagerCallback = (id, name) => {
  console.log(id, name)
  accountData.main_man_id = id
  accountData.main_man_name = name
}
</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg" width="80%">
    <el-form :rules="rules" :model="accountData" ref="ruleFormRef">
      <el-descriptions class="flex-1 mt-2" :column="1" border>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'户名'">
          <el-form-item prop="nick">
            <el-input v-model="accountData.nick" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'银行账号'">
          <el-form-item prop="bank_account">
            <el-input v-model="accountData.bank_account" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'账户币种'">
          <el-form-item prop="money_type">
            <el-select class="w-[100%]" v-model="accountData.money_type">
              <el-option v-for="item in moneyTypeData" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle" :label="'支付方式'">
          <el-form-item prop="pay_type">
            <el-select class="w-[100%]" v-model="accountData.pay_type">
              <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle" :label="'初始余额'">
          <el-form-item prop="init_money">
            <el-input-number v-model="accountData.init_money" :min="0" :controls="false" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="'责任人'" class="flex">
          <el-form-item>
            <div class="rounded mr-2 border pl-2 pr-2 w-150px" style="color: #606266;">
              {{ accountData.main_man_id ? accountData.main_man_name : '请选择责任人' }}
            </div>
            <ElButton @click="onSelManager">
              <Icon icon="iconamoon:search-bold" />
            </ElButton>
          </el-form-item>
        </el-descriptions-item>

      </el-descriptions>
    </el-form>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
    <DialogUser :param="''" v-model:show="showSelManageUserDlg" :title="t('msg.selectUser')"
      @on-submit="onSelManagerCallback" />
  </Dialog>
</template>

<style lang="less" scoped>
// :deep(.labelStyle1) {
//   width: 15% !important;
// }

// :deep(.conentStyle1) {
//   width: 30%;
// }

.el-form-item--default {
  margin-bottom: unset;
}

.el-input,
.el-select {
  width: 80%;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
  content: '*';
  color: red;
  margin-left: 4px;
}
</style>
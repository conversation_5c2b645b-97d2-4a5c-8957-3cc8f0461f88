<script setup lang="ts">
import { ref, reactive,watch } from 'vue'
import { ElTable,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElCheckboxGroup,ElCheckbox,ElTreeSelect,ElUpload,ElCard, ElTabPane,ElTabs, ElRadio,ElRadioGroup, ElSelect, ElOption, ElTooltip, ElInput,ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import { getSaleBakInfoApi } from '@/api/product'

import { Dialog } from '@/components/Dialog'


const { t } = useI18n()
const { currentRoute } = useRouter()

const show = ref(false);
const props = defineProps<{
  show: boolean,
  sell_order_num:string, //销售单号
  pdt_biaoshi:string, //产品标识
 }>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    reloadShow()
  }
})


//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
   
  closeDlg()
  //返回数据给上层  
  emit('onSubmit')
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

//----------------------------------------------------------------------

//委外数据源
const oemData = ref<any>([])
//采购数据源
const buyData = ref<any>([])

const reloadShow = async()=>{
    const ret = await getSaleBakInfoApi({
      sell_order_num:props.sell_order_num,
      pdt_biaoshi:props.pdt_biaoshi,
    })
    if(ret)
    {
      console.log(ret)
      oemData.value = [...ret.data.oem_order_list]
      buyData.value = [...ret.data.buy_order_list]
    }
}

onMounted(async () => {    

})



</script>

<template>
  <Dialog v-model="show" title="产品备货数据" width="80%" max-height="60vh" @close="closeDlg">
    <div class="flex justify-center">
      <div class="font-bold" style="font-size: 20px;">采购单数据</div>
    </div>
    <el-table  ref="tableRef" :data="buyData" style="width: 100%; margin-bottom: 20px" row-key="id" border
      header-cell-class-name="tableHeaderDlg">
      <el-table-column show-overflow-tooltip align="center" fixed prop="产品编号" label="产品编号"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="产品名称" label="产品名称"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="采购单号" label="采购单号"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="采购数量" label="采购数量"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="入库数量" label="入库数量"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="采购中数量" label="采购中数量"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="采购单状态" label="采购单状态"  />
    </el-table>
    <div class="flex justify-center">
      <div class="font-bold" style="font-size: 20px;">委外单数据</div>
    </div>
    <el-table  ref="tableRef" :data="oemData" style="width: 100%; margin-bottom: 20px" row-key="id" border
      header-cell-class-name="tableHeaderDlg">
      <el-table-column show-overflow-tooltip align="center" fixed prop="产品编号" label="产品编号"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="产品名称" label="产品名称"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="委外单号" label="委外单号"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="委外数量" label="委外数量"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="入库数量" label="入库数量"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="委外中数量" label="委外中数量"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="委外单状态" label="委外单状态"  />
    </el-table>

    <template #footer>
      <ElButton type="primary" @click="closeDlg">关闭</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.tableHeaderDlg) {
  background-color: #67a4ed !important;
  color: #fff;
  font-weight: 400;
}
</style>
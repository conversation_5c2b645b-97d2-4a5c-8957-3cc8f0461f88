<script setup lang="ts">
import {ref} from 'vue'
import { ElMenu,ElMenuItem } from 'element-plus'
import { useCache } from '@/hooks/web/useCache'
import { useI18n } from '@/hooks/web/useI18n'
import router, {getAbilityTabMap} from '@/router'
import { usePermissionStore } from '@/store/modules/permission'
import { useRouter } from 'vue-router'
import { RouteRecordRaw } from 'vue-router'
import { closeOneTagByPath } from '@/api/tool'


const { t } = useI18n()

const { wsCache } = useCache()
const permissionStore = usePermissionStore()
const { addRoute,push,currentRoute,removeRoute } = useRouter()
const abilityTabMap = getAbilityTabMap()

const handleSelect = async (key: string) => {
  console.log(key)
  //切换tab后同时更新路由
  await reFreshMenu(key,abilityTabMap)
  console.log('切换大路由')
  // closeOneTagByPath(permissionStore.addRouters[0].path)
  // closeOneTagByPath(permissionStore.addRouters[0].redirect)
  //调跳转到路由的第一个页面
  push({ path:permissionStore.addRouters[0].path })
}

const getMap = (map)=>{

  //用ROLE的角色设置过滤真实的TAB
  const roleMap = wsCache.get('roleRoutersLaba')
  let arrayRet:any[] = []
  for(let item of map)
  {
    let bFind = false
    for(let one of roleMap)
    {
      if(one["name"] == item.name)
      {
        bFind = true
        break
      }
    } 
    if(bFind) 
    {
      arrayRet.push(item)
    }
  }

  return arrayRet
}
 
//console.log( getAbilityTabMap())    

const tabMap = getMap(abilityTabMap)

 

const reFreshMenu = async (tabName:string,abilityTabMap)=>{
  console.log("进入刷新")
  const { wsCache } = useCache()
  const routersMap =  wsCache.get('roleRoutersLaba')

  //找到对应的权限列表
  let srcMap:AppRouteRecordRaw[] = []
  for(let i=0;i<abilityTabMap.length;i++)
  {
    if(abilityTabMap[i].name == tabName)
    {
      srcMap = abilityTabMap[i].routes
      break
    }
  }

  //找到对应的用户角色权限
  let roleMap = []
  for(let item of routersMap)
  {
    if(item["name"] == tabName)
    {
      roleMap = item["routes"]
      break
    }
  }


  await permissionStore.generateRoutes(srcMap, roleMap).catch(() => {})

  // router.getRoutes().forEach((route) => {
  //   if(!['/','/redirect','/login','/404'].includes(route.path as string))
  //     removeRoute(route.name as string)
  // })

 
  permissionStore.getAddRouters.forEach((route) => {
    addRoute(route as RouteRecordRaw) // 动态添加可访问路由表
  })
  permissionStore.setIsAddRouters(true)
}

let activeIndex = ""
//根据当前路由确定TAB当前选中位置
const roleMap = wsCache.get('roleRoutersLaba')
for(let one of roleMap)
{
  let bFind = false
  for(let item of one["routes"])
  {    
    if((item == currentRoute.value.path)) //找到
    {
      activeIndex = one.name
      bFind = true
      break
    }

  }
  if(bFind)
  {
    break
  }
}
</script>

<template>
  <div class=" bg-[#545c64] h-[48%] w-[100%] flex items-center ">
    <ElMenu
    :default-active="activeIndex"
    class="ElMenu-demo h-[100%] w-[100%]"
    mode="horizontal"
    background-color="#545c64"
    text-color="#fff"
    active-text-color="#00BA80"
    @select="handleSelect"
  >
    <ElMenuItem class="!ml-4" v-for="(item,index) in tabMap" :key="index" :index="item.name">
      {{ t(item.title) }}
    </ElMenuItem>
  </ElMenu>
  </div>
</template>

<style lang="less" scoped>
.el-menu--horizontal {
    display: flex;
    flex-wrap: nowrap;
    border-bottom: none; 
    border-right: none;
}
</style>

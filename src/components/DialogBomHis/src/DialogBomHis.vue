<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElTooltip,ElTag } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getSaleDemandInfoApi,getBomListApi } from '@/api/product'
import { DialogBomHisDetail } from '@/components/DialogBomHisDetail'

const { t } = useI18n()
const show = ref(false);

const title = ref('')

const demandData = reactive({})

//产品信息表
const pdtInfo:any[] = reactive([])

//查询条件
const searchCondition = reactive({
  page: 1,
  count: 10,
})

const props = defineProps<{
  show: boolean,
  bom:any,
  old_id:string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    console.log(props.bom)
    // getSaleDemandInfo()
     title.value = '['+props.bom.pdt_name+']'+' 历史BOM版本'
     pdtInfo.splice(0,pdtInfo.length)
     pdtInfo.push({
      pdt_name:props.bom.pdt_name,
      pdt_nick:props.bom.pdt_nick,
      specs_name:props.bom.specs_name,
      specs_text:props.bom.specs_text,
    })

    getBomList()
  }
})

const bomList = reactive([])
//查询BOM快照列表
const getBomList = async (page = 1) => {
  searchCondition.name = props.bom.name
  searchCondition.is_photo = 1
  const ret = await getBomListApi(searchCondition)
  if(ret)
  {
    bomList.splice(0,bomList.length)
    bomList.push(...ret.data)
  }
}


//查询需求单明细
const getSaleDemandInfo = async (page = 1) => {
  console.log('1111',props.pdt)
  searchCondition.page = page
  searchCondition.pdt_name = props.pdt.pdt_name
  searchCondition.order_nums = props.order_nums
  const ret = await getSaleDemandInfoApi(searchCondition) 
  if(ret)
  {
    Object.assign(demandData,ret.data)
    pdtInfo.splice(0,pdtInfo.length)
    pdtInfo.push({
      pdt_name:ret.data.pdt_info.pdt_name,
      pdt_nick:ret.data.pdt_info.pdt_nick,
      specs_name:ret.data.pdt_info.specs_name,
      specs_text:ret.data.pdt_info.specs_text,
    })
  }
}

//显示快照详情
const onShowInfo = (row)=>
{

}


//定义通知
const emit = defineEmits(['update:show','onSubmit'])

//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}



//显示BOM历史
const showBomHisDetail = ref(false)
const selBom = ref(null)
const onShowBomHisDetail = (bom)=>{
  console.log(bom)
  selBom.value = bom
  showBomHisDetail.value = true
}

</script>

<template>
  <Dialog v-model="show" :title="title" width="85%" @close="closeDlg">

    <div class="text-lg font-black text-center mb-2">产品信息</div>
    <!-- 产品信息表 -->
    <el-table  ref="tableRef" :data="pdtInfo" style="width: 100%; margin-bottom: 20px" row-key="id" border
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip align="center" fixed prop="pdt_name" :label="t('product_manage.id')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="pdt_nick" :label="t('product_manage.name')"  />
      <el-table-column show-overflow-tooltip align="center" fixed :label="t('product_manage.specify_info')"  >
        <template #default="scope">
            <el-tooltip                
                class="box-item"
                effect="dark"
                :content="scope.row.specs_text"
                placement="bottom"
            >
            <el-tag>{{ scope.row.specs_name }}</el-tag>
            </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="text-lg font-black text-center mb-2">BOM历史快照</div>
    <!-- 需求明细表 -->
    <el-table  ref="tableRef" :data="bomList" style="width: 100%; margin-bottom: 20px" row-key="id" border
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip align="center" fixed prop="version" :label="'版本号'"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="modify_man_name" :label="'员工'"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="modify_date" :label="'时间'"  />
      <el-table-column fixed="right"  show-overflow-tooltip :label="t('userTable.operate')" width="100">
            <template #default="scope">
              <ElButton size="small"  type="success" @click="onShowBomHisDetail(scope.row)">
                <div>查看</div>
              </ElButton>
            </template>
          </el-table-column>
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="closeDlg">
          {{ t('msg.ok') }}
        </ElButton>
      </div>
    </template>

    <DialogBomHisDetail v-model:show="showBomHisDetail" :bom="selBom" @on-submit="closeDlg()" :old_id="props.old_id"/>
  </Dialog>
</template>

<style lang="less" scoped>


</style>
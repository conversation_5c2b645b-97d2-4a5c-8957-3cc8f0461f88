<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElMessageBox,ElButton,ElForm,ElDescriptions,ElFormItem,ElDescriptionsItem, ElMessage,ElInput,FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { DialogPreSaleCap } from '@/components/DialogPreSaleCap'
import { updateUserApi, getUserListApi, } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import type { FormInstance, UploadProps } from 'element-plus'
import { checkFormRule } from '@/api/tool';
import { useTagsViewStore } from '@/store/modules/tagsView'
import { resetRouter } from '@/router'
import { useRouter } from 'vue-router'

const { wsCache } = useCache()
const appStore = useAppStore()
const tagsViewStore = useTagsViewStore()
const { replace } = useRouter()

const { t } = useI18n()
const show = ref(false);

const props = defineProps<{
  show: boolean,
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    console.log('222222222')
    show.value = true
    getUserInfo()
  }
})


//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = async()=>{
  const rule = await checkFormRule(ruleFormRef.value)
  if(!rule)
  {
      ElMessage.error(t('msg.checkRule'))
      return
  }
  if(tmpUserData.old_pass !== userData.password)
  {
      ElMessage.error('旧密码输入错误，请检查！')
      return
  }

  if(tmpUserData.new_pass1 !== tmpUserData.new_pass2)
  {
      ElMessage.error('两次输入的密码不一致，请检查！')
      return
  }

  //修改密码
  userData.password = tmpUserData.new_pass1
  userData.pwd_check = tmpUserData.new_pass2
  const ret =await updateUserApi(userData)
  if(ret)
  {
    ElMessageBox.alert('密码修改成功，请重新登录！', t('msg.notify'), {
    confirmButtonText: '确定',
    callback: () => {
        wsCache.clear()
        tagsViewStore.delAllViews()
        resetRouter() // 重置静态路由表
        replace('/login')
      }
    })


    
      closeDlg()
  }
   
  
  //返回数据给上层  
  // emit('onSubmit',currentRow.value)
  
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

const tmpUserData = reactive({
  old_pass:'',
  new_pass1:'',
  new_pass2:''
})

const userData = reactive({})
const getUserInfo = async()=>{  
    //查询人员信息
    const info = wsCache.get(appStore.getUserInfo)

    const ret = await getUserListApi({
        "ids": [info.id],
        "page": 1,
        "count": 100
    })
    console.log(ret)
    if (ret) {
        if (ret.data.length < 0) {
            //提示获取查询失败
            ElMessage({
                type: 'error',
                message: t('msg.searchFalse'),
            })
            return
        }
        else
        {
            Object.assign(userData, ret.data[0]);
            userData.pwd_check = userData.password
        }
    }
}

//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
  old_pass: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  new_pass1: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
  new_pass2: [{ required: true, message: '请再次输入密码', trigger: 'blur' }],
})

</script>

<template>
  <Dialog v-model="show" :title="'修改密码'" :max-height="300" @close="closeDlg">
    <el-form :rules="rules" :model="tmpUserData" ref="ruleFormRef">
      <el-descriptions class="flex-1 mt-2" :column="2" border>           
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'旧密码'"
            class="flex" :span="2">
            <el-form-item prop="old_pass" >
                <el-input type="password"  v-model="tmpUserData.old_pass"  />
            </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'新的密码'"
            class="flex" :span="2">
            <el-form-item prop="new_pass1" >
                <el-input type="password"  v-model="tmpUserData.new_pass1"  />
            </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'确认密码'"
            class="flex" :span="2">
            <el-form-item prop="new_pass2" >
                <el-input type="password"  v-model="tmpUserData.new_pass2"  />
            </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>



    <template #footer>
      <ElButton type="primary" @click="onSubmit">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #ffe48d !important;
  --el-table-current-row-bg-color: #ffe48d !important;
  --el-table-row-hover-bg-color: #ffe48d;
}

:deep(.tableHeader) {
  background-color: #73b0e8 !important;
  color: #fff;
}

/* 只针对 .require 元素添加伪元素 */
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
</style>
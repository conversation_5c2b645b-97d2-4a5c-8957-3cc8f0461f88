<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch, reactive } from 'vue';
import { ElCheckbox,ElButton, ElTable, ElTableColumn, ElPagination, ElMessage, ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { getProcessListApi } from '@/api/product'
import { DialogProcess } from '@/components/DialogProcess'

const { t } = useI18n()
const show = ref(false);

const processData = reactive([]) //列表
//查询条件
const searchCondition = reactive({
  name: '',
  nick: '',
  _or: 'true',
  page: 1,
  count: 30
})
//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}



const props = defineProps<{
  show: boolean,
  title: string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    show.value = true
    getProcessList()
    arraySel.splice(0, arraySel.length)
  }
})

//查询用户列表
const getProcessList = async (page = 1) => {
  searchCondition.page = page
  searchCondition.nick = searchCondition.name
  const ret = await getProcessListApi(searchCondition);
  if (ret) {
    processData.splice(0, processData.length, ...ret.data);
    totleCount.value = parseInt(ret.count)
  }
}
//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getProcessList()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getProcessList(val)
}

//定义通知
const emit = defineEmits(['update:show', 'onSubmit'])
//提交选择的数据
const onSubmit = () => {
  // if (currentRow.value?.id == undefined) {
  //   ElMessage.warning('请选择一条数据')
  //   return
  // }
  if(arraySel.length<=0)
  {
    ElMessage.warning('请选择一条数据')
    return
  }

  closeDlg()
  //返回数据给上层
  emit('onSubmit', currentRow.value,arraySel)
}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

const curOptProcessData = ref({})
//显示隐藏工艺编辑窗口
const showProcessDialog = ref(false)
//新增工序
const onAddNewProcess = () => {
  showProcessDialog.value = true
  curOptProcessData.value = {}
}

const arraySel = reactive([])
const onChangeCheck = (row,value)=>{
  console.log(row,value)
  if(value)
  {
    arraySel.push(row)
  }
  else
  {
    const index = arraySel.findIndex(item=>item.id==row.id)
    arraySel.splice(index,1)
  }
  console.log(arraySel)
}


</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-input style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputProcessName')"
        v-model="searchCondition.name" clearable />
      <el-button type="primary" @click="getProcessList(1)">{{ t('button.search') }}</el-button>
      <el-button type="success" @click="onAddNewProcess">{{ t('button.add') + '工序' }}</el-button>
    </div>
    <el-table ref="tableRef" :data="processData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row111 @row-dblclick="onSubmit" @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader">
      <!-- <el-table-column align="center" type="selection" width="40" /> -->
      <el-table-column align="center" show-overflow-tooltip fixed prop="name" :label="'-'" width="40">
        <template #default="scope">
          <el-checkbox v-model="scope.row.check" label="" size="large" @change="onChangeCheck(scope.row,$event)"/>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip fixed prop="name" :label="t('process.name')" />
      <el-table-column show-overflow-tooltip fixed prop="nick" :label="t('process.nick')" />
      <el-table-column show-overflow-tooltip fixed prop="remark" :label="t('process.remark')" />
    </el-table>
    <el-pagination class="flex justify-end" v-model:current-page="searchCondition.page"
      v-model:page-size="searchCondition.count" :page-sizes="[30, 50, 100, 300]" :background="true"
      layout="sizes, prev, pager, next" :total="totleCount" @size-change="handlePageSizeChange"
      @current-change="handlePageCurrentChange" />
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>

    <!-- 工序创建窗口 -->
    <DialogProcess :bReturnData="false" v-model:show="showProcessDialog" v-model:data="curOptProcessData"
      :title="t('project_manage.process_config')" @on-submit="getProcessList" />

  </Dialog>
</template>

<style lang="less" scoped></style>
<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch, reactive } from 'vue';
import { ElDatePicker, ElInputNumber, ElButton, ElDescriptions, ElDescriptionsItem, ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElMessage, ElInput, FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { checkFormRule, getTodayDate } from '@/api/tool'
import { addFinanceAccountApi, addFinanceWaterApi, getFinanceAccountInfoApi, getFinanceAccountListApi, getFinanceAccountNewnumApi, getFinanceWaterNewnumApi, updateFinanceAccountApi, updateFinanceWaterApi } from '@/api/finance';
import { DialogUser } from '@/components/DialogUser'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getPayBillSellListApi } from '@/api/product';

const { wsCache } = useCache()
const appStore = useAppStore()
const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const { t } = useI18n()
const props = defineProps<{
  show: boolean,
  data: any,
  type:string //info 查看 edit 编辑
}>()

//自我显示
const show = ref(false)
const title = ref('')

//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    //显示窗口时刷新数据
    show.value = val
    title.value = '流水明细'

    Object.assign(detailData, props.data)

  }
})
const checkData = reactive([])


const defData = {
  name: '',
  share_type: '自上而下均摊',
  account_id: '',
  buyer_id: '',
  money_type: '',
  money_in: 0,
  money_out: 0,
  pay_type: '银行转账',
  use_type: '销售收入',
  ref_list: [],
  note: '',
  main_man_id: '',
  pay_date: ''
}
const detailData = reactive({
  ...defData,
  reset() {
    Object.assign(this, defData)
  }
})

const accountData = reactive([])
//定义通知
const emit = defineEmits(['update:show', 'onSubmit', 'update:data'])
//提交选择的数据
const onSubmit = async () => {
  //修改
  let tmp = cloneDeep(detailData)
  delete tmp.money_bal
  let ret = await updateFinanceWaterApi(tmp)
  if (ret)
  {
    ElMessage.success('修改成功')
    
    closeDlg()
    emit('onSubmit')
  }
  else {
    ElMessage.error('修改失败')
  }

}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

//更新收款总额
const reCoutInTotle = () => {
  let nCount = 0
  detailData.ref_list.forEach((item) => {
    nCount += parseFloat(item.实收金额)
  })
  detailData.money_in = parseFloat(nCount.toFixed(2))
}

//更新付款总额
const reCoutOutTotle = () => {
  let nCount = 0
  detailData.ref_list.forEach((item) => {
    nCount += parseFloat(item.实付金额)
  })
  detailData.money_out = parseFloat(nCount.toFixed(2))
}

</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg" width="80%">
    <el-form :model="detailData" ref="ruleFormRef">
      <el-descriptions class="flex-1 mt-2" :column="2" border>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="'均摊类型'"
          class="flex">
          {{ detailData.share_type }}
        </el-descriptions-item>
        <el-descriptions-item v-if="detailData.ref_list.some(item => item.hasOwnProperty('实收金额'))" label-class-name='labelStyle require' class-name="conentStyle" :label="'收款总额'"
          class="flex">
          <el-form-item prop="name">
            {{ detailData.money_in }}
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="detailData.ref_list.some(item => item.hasOwnProperty('实付金额'))" label-class-name='labelStyle require' class-name="conentStyle" :label="'付款总额'"
          class="flex">
          <el-form-item prop="name">
            {{ detailData.money_out }}
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'账户'" class="flex">
          <el-form-item prop="name">
            {{ detailData.account_nick }}
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="'支付方式'" class="flex">
          {{ detailData.pay_type }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="'用途分类'" class="flex">
          {{ detailData.use_type }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="'收款时间'" class="flex">
          {{ detailData.pay_date }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'备注'" class="flex">
          <el-form-item prop="name">
            {{ detailData.note }}
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <el-table ref="multipleTableRef" header-cell-class-name="tableHeader" :data="detailData.ref_list"
      style="width: 97%;color: #666666;" border>
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('sell_paybill_num'))"  prop="sell_paybill_num" :label="'销售对账单'" />
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('buy_paybill_num'))"  prop="buy_paybill_num" :label="'采购对账单'" />
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('oem_paybill_num'))"  prop="oem_paybill_num" :label="'委外对账单'" />
      <el-table-column align="center" show-overflow-tooltip prop="create_date" :label="'发生日期'" />
      <el-table-column align="center" show-overflow-tooltip prop="paybill_date" :label="'月份'" />
      <el-table-column align="center" show-overflow-tooltip prop="create_man_name" :label="'创建人'" />
      <el-table-column align="center" show-overflow-tooltip prop="币种" :label="'币种'" />
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('应收金额'))"  prop="应收金额" :label="'应收金额'" />
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('已收金额'))"  prop="已收金额" :label="'已收金额'" />
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('未收金额'))"  prop="未收金额" :label="'未收金额'" />
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('实收金额'))"  prop="实收金额" :label="'实收金额'" >
        <template #default="scope">
          <el-input-number :disabled="props.type =='info'" class="!w-[100%]" :controls="false" v-model="scope.row.实收金额" :min="0" @blur="reCoutInTotle"/>
        </template>    
      </el-table-column>

      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('应付金额'))"  prop="应付金额" :label="'应付金额'" />
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('已付金额'))"  prop="已付金额" :label="'已付金额'" />
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('未付金额'))"  prop="未付金额" :label="'未付金额'" />
      <el-table-column align="center" v-if="detailData.ref_list.some(item => item.hasOwnProperty('实付金额'))"  prop="实付金额" :label="'实付金额'" >
        <template #default="scope">
          <el-input-number :disabled="props.type =='info'" class="!w-[100%]" :controls="false" v-model="scope.row.实付金额" :min="0" @blur="reCoutOutTotle"/>
        </template> 
      </el-table-column>

    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="danger" @click="onSubmit">
          修改
        </ElButton>
        <ElButton @click="closeDlg">关闭</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
// :deep(.labelStyle) {
//   width: 15% !important;
// }

// :deep(.conentStyle) {
//   width: 30%;
// }

.el-form-item--default {
  margin-bottom: unset;
}

.el-input,
.el-select {
  width: 80%;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
  content: '*';
  color: red;
  margin-left: 4px;
}

:deep(.el-date-editor.el-input) {
  width: unset !important;
}
</style>
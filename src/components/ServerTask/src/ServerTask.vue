<script setup lang="ts">
import { ElButton,ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useCache } from '@/hooks/web/useCache'
import { resetRouter } from '@/router'
import { useRouter } from 'vue-router'
import { loginOutApi } from '@/api/login'
import { useDesign } from '@/hooks/web/useDesign'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { reactive, ref, onMounted } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { DialogUserInfo } from "@/components/DialogUserInfo";
import { DialogImportTask } from "@/components/DialogImportTask";

const tagsViewStore = useTagsViewStore()
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('user-info')
const { t } = useI18n()
const { wsCache } = useCache()
const { replace } = useRouter()
const appStore = useAppStore()

const userinfo = reactive({})
onMounted(()=>{
  const info = wsCache.get(appStore.getUserInfo)
  Object.assign(userinfo, info)
})

const bShowImport = ref(false)
const showImportDlg = () => {
  bShowImport.value = true
}

</script>

<template>
  <div>
    <ElDropdown :class="prefixCls" trigger="click">
      <div class="flex items-center">
        <ElButton title='任务' size='small' type="info" class='mr-5'>
            <Icon  icon="mdi:import-export-bold" />
          </ElButton>
      </div>
      <template #dropdown>
        <ElDropdownMenu>
          <ElDropdownItem divided>
            <div @click="showImportDlg">导入任务</div>
          </ElDropdownItem>
        </ElDropdownMenu>
      </template>

      
    </ElDropdown>
    <DialogImportTask v-model:show='bShowImport' mod='' cmd="" /> 
  </div>

</template>

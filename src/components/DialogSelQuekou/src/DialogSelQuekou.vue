<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getQuekouListApi } from '@/api/product'
import { nextTick } from 'process';


const { t } = useI18n()
const show = ref(false);

const queDate = reactive([]) //列表
//查询条件
const searchCondition = reactive({
  pdt_id:'',
  sell_order_num: '',  //名称
  page: 1,
  count: 100,
  inputTxt:'' //界面输入
})

//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
  pdtid:string,
}>()


//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getQuekouList()

    
  }
})

const tableRef = ref<InstanceType<typeof ElTable>>()
onMounted(()=>{
  // nextTick(()=>{
  //   tableRef.value!.clearSelection()
  // })
  
})

//查询列表
const getQuekouList = async (page = 1) => {
  searchCondition.pdt_id = props.pdtid
  searchCondition.page = page
  const ret = await getQuekouListApi(searchCondition);
  if (ret) {
    console.log(ret)
    queDate.splice(0, queDate.length, ...ret.data.quekou_list);
    totleCount.value =  parseInt(ret.count)
  }
}
//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getQuekouList()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getQuekouList(val)
}

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{

   
  closeDlg()
  //返回数据给上层
  
  emit('onSubmit',selRow)
  tableRef.value!.clearSelection()
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
  
}


//选择某项后
let selRow = []
const handleSelectionChange = (val)=>{
  console.log(val)
  selRow = val
  console.log(selRow)
}

</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-input style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputNameOrID')" v-model="searchCondition.inputTxt" />
      <el-button type="primary" @click="getQuekouList(1)">{{ t('button.search') }}</el-button>
    </div>
    <el-table  ref="tableRef" :data="queDate" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column show-overflow-tooltip align="center" fixed prop="销售单号" :label="t('sale.name')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="客户名称" :label="t('customer.name')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="客户订单号" :label="t('sale.cus_sale_name')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="销售数量" :label="t('sale.count')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="锁定数量" :label="t('sale.lock')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="备货数量" :label="t('sale.prepare_count')"  />
      <el-table-column show-overflow-tooltip align="center" fixed prop="缺口数量" :label="t('sale.need_count')"  />

    </el-table>
    <el-pagination
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handlePageSizeChange"
        @current-change="handlePageCurrentChange"
    />
    <template #footer>
      <ElButton type="primary" @click="onSubmit">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
</style>
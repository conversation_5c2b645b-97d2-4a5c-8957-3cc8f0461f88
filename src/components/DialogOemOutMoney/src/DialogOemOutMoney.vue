<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { ElDatePicker, ElInputNumber, ElButton, ElDescriptions, ElDescriptionsItem, ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElMessage, ElInput, FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import {  addFinanceWaterApi, getFinanceAccountListApi, getFinanceWaterNewnumApi } from '@/api/finance';
import { DialogUser } from '@/components/DialogUser'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getPayBillOemListApi } from '@/api/product';

const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const { t } = useI18n()
const props = defineProps<{
  show: boolean,
  cus_id: string, //客户id
  cus_nick: string,
  money_type:string
}>()

//自我显示
const show = ref(false)
const title = ref('')

//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    //显示窗口时刷新数据
    show.value = val
    title.value = props.cus_nick + ' 账户付款'
    const currentDate = new Date();
    const formattedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')} ${String(currentDate.getHours()).padStart(2, '0')}:${String(currentDate.getMinutes()).padStart(2, '0')}:${String(currentDate.getSeconds()).padStart(2, '0')}`;
    console.log(formattedDate)

    getMoneyData.reset()
    getMoneyData.pay_date = formattedDate
    getMoneyData.note = props.cus_nick + ' 账户付款'
    getMoneyData.parter_id = props.cus_id
    getMoneyData.money_type = props.money_type
    
    onChangeID()

    getFinanceAccountList()
    getOemMonthlyCheckListData()
  }
})
const checkData = reactive([])
const getOemMonthlyCheckListData = async () => {
  const ret = await getPayBillOemListApi({
    page: 1,
    count: 1000,
    parter_id: props.cus_id
  })
  if (ret) {
    checkData.splice(0, checkData.length, ...[...ret.data.filter(item => parseFloat( (item.应付金额 - item.已付金额).toFixed(2))>0)].reverse())
    for (let one of checkData) {
      one.未付金额 = parseFloat( (one.应付金额 - one.已付金额).toFixed(2))
    }
    if(checkData.length>0)
    {
      multipleTableRef.value!.toggleRowSelection(checkData[0], true)
    }
  }
}


//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
  nick: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  bank_account: [{ required: true, message: '请输入卡号', trigger: 'blur' }]
})

const defData = {
  name: '',
  share_type: '自上而下均摊',
  account_id: '',
  parter_id: '',
  money_type: '',
  money_in: 0,
  money_out: 0,
  pay_type: '银行转账',
  use_type: '委外支出',
  ref_list: [],
  note: '',
  main_man_id: '',
  pay_date: ''
}
const getMoneyData = reactive({
    ...defData,
    reset() {
        Object.assign(this, defData)
    }
})

const accountData = reactive([])
//查询账号列表
const getFinanceAccountList = async () => {
  const ret = await getFinanceAccountListApi({
    page: 1,
    count: 200
  })
  if (ret) {
    accountData.splice(0, accountData.length, ...ret.data.filter(item => item.status != '已禁用'))
    if (accountData.length > 0)
      getMoneyData.account_id = accountData[0].id
  }
}
//支付方式
const payTypeData = reactive([
  '现金',
  '支票',
  'POS机',
  '银行转账',
  '支付宝',
  '微信',
  '银联',
])

//定义通知
const emit = defineEmits(['update:show', 'onSubmit', 'update:data'])
//提交选择的数据
const onSubmit = async () => {
  // const rule = await checkFormRule(ruleFormRef.value)
  // if (!rule) {
  //   ElMessage.warning(t('msg.checkRule'))
  //   return
  // }

  let nTmp = 0
  for(let one of arraySel)
  {
    nTmp += one.实付金额
  }
  if(nTmp != getMoneyData.money_out)
  {
    ElMessage.warning('付款总额不等于输入金额'+nTmp)
    getMoneyData.money_out = nTmp
    return
  }


  getMoneyData.ref_list.splice(0, getMoneyData.ref_list.length, ...arraySel.filter(item => item.实付金额 > 0))
  console.log(getMoneyData)
  const ret = await addFinanceWaterApi(getMoneyData)
  if(ret)
  {
    closeDlg()
    emit('onSubmit')
    ElMessage.success('付款成功')
  }

}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

const onChangeID = async () => {
  const ret = await getFinanceWaterNewnumApi()
  if (ret) {
    console.log(ret)
    getMoneyData.name = ret.data.new_id
  }
}

//显示合计
const getSummaries = (param) => {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['未付金额', '实付金额'].includes(column.property)) {
      const values = data.map(item => Number(item[column.property]));
      if (!values.every(value => isNaN(value))) {
        sums[index] = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return parseFloat((prev + curr).toFixed(2));
          } else {
            return prev;
          }
        }, 0);
      } else {
        sums[index] = '/';
      }
    }
  })
  return sums;
}

const onAvgMoney = () => {
  console.log(arraySel)
  if(arraySel.length<=0)
  {
    ElMessage.warning('请选择对账单')
    return
  }
  if(getMoneyData.money_out<=0)
  {
    ElMessage.warning('请输入付款总额')
    return
  }
  if(getMoneyData.account_id==='')
  {
    ElMessage.warning('请选择账户')
    return
  }
  if(getMoneyData.pay_type==='')
  {
    ElMessage.warning('请选择支付方式')
    return
  }
  if(getMoneyData.use_type==='')
  {
    ElMessage.warning('请选择用途分类')
    return
  }
  //计算总金额 arraySel数组中中所有未付金额只和
  let nTmp = 0
  for(let one of arraySel)
  {
    nTmp += one.未付金额
  }
  if(nTmp<getMoneyData.money_out)
  {
    ElMessage.warning('付款总额大于未付金额'+nTmp)
    getMoneyData.money_out = nTmp
    return
  }


  
  
  //开始均摊
  if(getMoneyData.share_type==='自上而下均摊')
  {
    let nTotle = getMoneyData.money_out
    for(let one of arraySel)
    {
      if(nTotle<=0)
      {
        one.实付金额 = 0
        continue
      }
      if(nTotle>one.未付金额)
      {
        one.实付金额 = one.未付金额
        nTotle -= one.未付金额
      }
      else
      {
        one.实付金额 = nTotle
        nTotle = 0
      }
    }
  }
  else   //平均均摊
  {
    //计算每个单子的比重
    for(let one of arraySel)
    {
       let bl = one.未付金额/nTmp
       one.实付金额 = getMoneyData.money_out*bl
    }
  }
}

const arraySel = reactive([])
const handleSelectionChange = (value) => {
  arraySel.splice(0, arraySel.length, ...value)

}
</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg" width="80%">
    <el-form :rules="rules" :model="getMoneyData" ref="ruleFormRef">
      <el-descriptions class="flex-1 mt-2" :column="2" border>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="'均摊类型'"
          class="flex">
          <el-form-item prop="buy_man_nick">
            <el-select v-model="getMoneyData.share_type" placeholder="Select">
              <el-option v-for="item in ['自上而下均摊', '平均均摊']" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'付款总额'"
          class="flex">
          <el-form-item prop="name">
            <div class="flex">
              <el-input-number :controls="false" v-model="getMoneyData.money_out" :min="0" />
              <ElButton type="primary" @click="onAvgMoney">均摊</ELButton>
            </div>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'账户'" class="flex">
          <el-form-item prop="name">
            <el-select v-model="getMoneyData.account_id" placeholder="Select">
              <el-option v-for="item in accountData" :key="item.id" :label="item.nick" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="'支付方式'" class="flex">
          <el-select v-model="getMoneyData.pay_type" placeholder="Select">
            <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="'用途分类'" class="flex">
          <el-select v-model="getMoneyData.use_type" placeholder="Select">
            <el-option v-for="item in ['采购支出']" :key="item" :label="item" :value="item" />
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="'付款时间'" class="flex">
          <el-date-picker :clearable="false" style="width: unset !important;" v-model="getMoneyData.pay_date" type="datetime"
            placeholder="选择时间" format="YYYY-MM-DD hh:mm:ss" value-format="YYYY-MM-DD hh:mm:ss" />
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'备注'" class="flex">
          <el-form-item prop="name">
            <div class="flex w-[100%]">
              <el-input style="width: 100% !important;" v-model="getMoneyData.note" />
            </div>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <el-table ref="multipleTableRef" header-cell-class-name="tableHeader" :data="checkData" style="width: 97%;color: #666666;" show-summary
      :summary-method="getSummaries" border @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" width="40" />
      <el-table-column align="center" show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60" />
      <el-table-column align="center" show-overflow-tooltip prop="create_date" :label="'发生日期'" />
      <el-table-column align="center" show-overflow-tooltip prop="paybill_date" :label="'月份'" />
      <el-table-column align="center" show-overflow-tooltip prop="prepaid_date" :label="'预付款日期'" />
      <el-table-column align="center" show-overflow-tooltip prop="create_man_name" :label="'创建人'" />
      <el-table-column align="center" show-overflow-tooltip prop="币种" :label="'币种'" />
      <el-table-column align="center" show-overflow-tooltip prop="应付金额" :label="'应付金额'" />
      <el-table-column align="center" show-overflow-tooltip prop="已付金额" :label="'已付金额'" />
      <el-table-column align="center" show-overflow-tooltip prop="未付金额" :label="'未付金额'">
        <template #default="scope">
          {{ scope.row.未付金额 }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="实付金额" :label="'实付金额'">
        <template #default="scope">
          <el-input-number class="!w-[100%]" :controls="false" v-model="scope.row.实付金额" :min="0" />
        </template>
      </el-table-column>
      <!-- <el-table-column align="center"  prop="实付金额" :label="'明细'" >
        <template #default="scope">
         <ElButton size="small">明细</ElButton>
        </template>
      </el-table-column> -->
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
// :deep(.labelStyle) {
//   width: 15% !important;
// }

// :deep(.conentStyle) {
//   width: 30%;
// }

.el-form-item--default {
  margin-bottom: unset;
}

.el-input,
.el-select {
  width: 80%;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
  content: '*';
  color: red;
  margin-left: 4px;
}

:deep(.el-date-editor.el-input) {
  width: unset !important;
}
</style>
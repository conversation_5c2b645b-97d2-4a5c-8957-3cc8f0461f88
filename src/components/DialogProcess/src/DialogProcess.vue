<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElButton,ElDescriptions,ElDescriptionsItem,ElForm,ElFormItem,ElSelect,ElOption,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput, FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import {checkFormRule} from '@/api/tool'
import { addProcessApi,updateProcessApi,getProcessNewnumApi } from '@/api/product'


const { t } = useI18n()
const props = defineProps<{
  show: boolean,
  title: string,
  data: {},
  bReturnData: boolean,
}>()

//自我显示
const show = ref(false)

//监听外部传入的显示设置
watch(() => props.show, (val) => {
  if (val) {
    //显示窗口时刷新数据
    show.value = val
  }
})

//更新外部传入的DATA
watch(() => props.data, (val) => {
  if (val) {    
    if(val && val.id)
    {
      processData.value = cloneDeep(val)
    }
    else{
      onChangeID()
      processData.value = {
        "name": "",
        "nick": "",
        "work_hours": "60.0",
        "job_fee": "0.00",
        "piece_fee": "0.00",
        "time_fee": 0,
        "depn_fee": "0.00",
        "share_rate": 1,
        "status": "启用",
        "label": "标准",
        "entrust": "不委外",
        "sync": "不同步",
        "remark": "",
        "desc_txt": "",
        "sort": 1
      }
    }
  }
})

//工序数据缓存
const processData = ref<any>({});
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    name: [{ required: true, message: t('msg.noProductName'), trigger: 'blur' }],
    nick: [{ required: true, message: t('msg.noProductNick'), trigger: 'blur' }],
    work_hours: [{ required: true, message: t('msg.no_work_hours'), trigger: 'blur' }]
})

//定义通知
const emit = defineEmits(['update:show','onSubmit','update:data'])
//提交选择的数据
const onSubmit = async()=>{
  const rule = await checkFormRule(ruleFormRef.value)
  if(!rule)
  {
      ElMessage.warning(t('msg.checkRule'))
      return
  }

  if(props.bReturnData)
  {
    console.log('已提交修改')
    emit('update:data',processData.value)
    emit('onSubmit')
    closeDlg()
  }
  else
  {
    if(processData.value.id == undefined)
    {
      const ret = await addProcessApi(processData.value)
      if(ret)
      {
        
        closeDlg()
        //返回数据给上层
        emit('onSubmit')
      }
    }
    else
    {
      const ret = await updateProcessApi(processData.value)
      if(ret)
      {
        closeDlg()
        //返回数据给上层
        emit('onSubmit')
      }
    }
  }

}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

const onChangeID = async()=>{
    const ret = await getProcessNewnumApi()
    if(ret)
    {
        console.log(ret)
        processData.value.name = ret.data.new_id
    }
}
</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg" width="80%">
    <el-form :rules="rules" :model="processData" ref="ruleFormRef">
      <el-descriptions class="flex-1 mt-2" :column="3" border>    
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('process.name')">
          <el-form-item prop="name" >
              <el-input v-model="processData.name"  />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('process.nick')">
          <el-form-item prop="nick" >
              <el-input v-model="processData.nick"  />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('process.work_hours')">
          <el-form-item prop="work_hours" >
              <el-input v-model="processData.work_hours"  />
              <div class="ml-2">秒</div>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.job_fee')">
          <el-form-item>
              <el-input v-model="processData.job_fee"  />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.piece_fee')">
          <el-form-item>
              <el-input v-model="processData.piece_fee"  />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.time_fee')">
          <el-form-item>
              <el-input v-model="processData.time_fee"  />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.depn_fee')">
          <el-form-item>
              <el-input v-model="processData.depn_fee"  />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.share_rate')">
          <el-form-item>
              <el-input v-model="processData.share_rate"  />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.status')">
          <el-form-item>
            <el-select class="w-[100%]" v-model="processData.status">
                <el-option v-for="item in ['启用','不启用']" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <!-- <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.label')">
          <el-form-item>
            <el-select class="w-[100%]" v-model="processData.label">
                <el-option v-for="item in ['标准','称重','装箱']" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-descriptions-item> -->
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.entrust')">
          <el-form-item>
            <el-select class="w-[100%]" v-model="processData.entrust">
                <el-option v-for="item in ['委外','不委外']" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.sync')">
          <el-form-item>
            <el-select class="w-[100%]" v-model="processData.sync">
                <el-option v-for="item in ['同步','不同步']" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <!-- <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('bom.check_type')">
          <el-form-item>
            <el-select class="w-[100%]" v-model="processData.质检方式">
                <el-option v-for="item in ['无需质检','抽检','全检']" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-descriptions-item> -->
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('bom.process_type')">
          <el-form-item>
            <el-select class="w-[100%]" v-model="processData.工序标识">
                <el-option v-for="item in ['标准','称重','装箱']" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <!-- <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label=""/> -->
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.remark')" :span="3">
          <el-form-item>
            <el-input v-model="processData.remark" clearable :autosize="{ minRows: 2, maxRows: 2 }"
                            type="textarea" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.desc')" :span="3">
          <el-form-item>
            <el-input v-model="processData.desc_txt" clearable :autosize="{ minRows: 5, maxRows: 5 }"
                            type="textarea" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('process.sort')">
          <el-form-item>
              <el-input v-model="processData.sort_num"  />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>

    </el-form>

    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.labelStyle1) {
    width: 15% !important;
}

:deep(.conentStyle1) {
    width: 30%;
}
.el-form-item--default{
    margin-bottom: unset;
}
.el-input,.el-select{
  width: 80%;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
</style>
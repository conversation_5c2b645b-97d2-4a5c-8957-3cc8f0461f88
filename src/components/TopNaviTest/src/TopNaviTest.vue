<script setup lang="ts">
import {  computed} from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()
const title = computed(() => appStore.getTitle)


</script>

<template>
  <div class=" bg-light-50 h-[100%] flex items-center">
    <router-link
      :class="[
        prefixCls,
        true ? `${prefixCls}__Top` : '',
        'flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px relative',
        'dark:bg-[var(--el-bg-color)]'
      ]"
      to="/"
    >
      <img
        src="@/assets/imgs/logo.png"
        class="w-[calc(var(--logo-height)-10px)] h-[calc(var(--logo-height)-10px)]"
      />
      <div
        :class="[
          'ml-10px text-23px font-700',
        ''
        ]"
      >
        {{ "22"+title }}
      </div>
    </router-link>
  </div>
</template>

<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElButton,ElDescriptions,ElDescriptionsItem,ElForm,ElFormItem,ElSelect,ElOption,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput, FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getServerTaskApi } from '@/api/task'

const { t } = useI18n()
const props = defineProps<{
  show: boolean,
  data: any[],
}>()

//自我显示
const show = ref(false)


//监听外部传入的显示设置
watch(() => props.show, (val) => {
  if (val) {
    //显示窗口时刷新数据
    show.value = val
    errorData.splice(0,errorData.length,...props.data)
  }

})

//定义通知
const emit = defineEmits(['update:show','onSubmit','update:data'])
//提交选择的数据
const onSubmit = async()=>{
  
    emit('onSubmit')
    closeDlg()

}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

//显示错误提示
const errorData = reactive([])

</script>

<template>
  <Dialog v-model="show" title="错误列表" max-height="60vh" @close="closeDlg" width="800">
    <el-table  ref="tableRef" :data="errorData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      header-cell-class-name="tableHeader2" >
      <el-table-column show-overflow-tooltip fixed prop="name" label="类型">
          <template #default="scope">
            {{scope.row}}
          </template>

        </el-table-column>
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>

  </Dialog>
</template>

<style lang="less" scoped>
:deep(.labelStyle1) {
    width: 15% !important;
}

:deep(.conentStyle1) {
    width: 30%;
}
.el-form-item--default{
    margin-bottom: unset;
}
.el-input,.el-select{
  width: 80%;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

:deep(.tableHeader2) {
  background-color: #f6f6f6 !important;
  color: #575757;
  font-weight: 400;
}
</style>
<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElButton,ElDescriptions,ElDescriptionsItem,ElForm,ElFormItem,ElSelect,ElOption,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput, FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getServerTaskApi } from '@/api/task'
import { DialogErrorList } from '@/components/DialogErrorList'

const { t } = useI18n()
const props = defineProps<{
  show: boolean,
  mod:string,
  cmd:string
}>()

//自我显示
const show = ref(false)

let timerId: number;
//监听外部传入的显示设置
watch(() => props.show, (val) => {
  if (val) {
    //显示窗口时刷新数据
    show.value = val
    getImportTask()
    timerId = setInterval(()=>{
      console.log('开启')
      getImportTask()
    },1000)
  }
  else
  {
    console.log('关闭')
    clearInterval(timerId)
  }
})
const taskData = reactive([])
//定义通知
const emit = defineEmits(['update:show','onSubmit','update:data'])
//提交选择的数据
const onSubmit = async()=>{
  
    emit('onSubmit')
    closeDlg()

}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

const getImportTask = async()=>{
  const ret = await getServerTaskApi({mod:props.mod,cmd:props.cmd})
  if(ret)
  {
    taskData.splice(0,taskData.length,...ret.data)
  }
}

//获取一个url地址中的文件名
const getFileName = (url:string)=>{
  return url.substring(url.lastIndexOf('/')+1)
}
//显示错误提示
const errorData = reactive([])
const bShowError = ref(false)
const onShowErrorList = (data)=>{
  bShowError.value = true
  errorData.splice(0,errorData.length,...data.err_info)
}

const tableRowClassName = (row,index)=>{
  if(row.row.err_info == undefined)
    return 'error-row'
  if(row.row.err_info.length>0)
  {
    return 'error-row'
  }
}
</script>

<template>
  <Dialog v-model="show" title="后台任务" max-height="60vh" @close="closeDlg" width="800">
    <el-table  ref="tableRef" :data="taskData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      header-cell-class-name="tableHeader2" :row-class-name="tableRowClassName" >
      <!-- <el-table-column show-overflow-tooltip  prop="name" label="类型"/> -->
      <el-table-column show-overflow-tooltip  prop="name" label="文件名">
        <template #default="scope">
          {{getFileName(scope.row.url)}}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip  prop="create_man_name" label="操作员"/>
      <el-table-column show-overflow-tooltip  prop="create_date" label="导入时间" width="200"/>
      <el-table-column show-overflow-tooltip  prop="max_row" label="总行数"/>
      <el-table-column show-overflow-tooltip  prop="max_row" label="失败行数">
        <template #default="scope">
        {{ scope.row.err_info == undefined?'异常': scope.row.err_info.length }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip  prop="status" label="状态"/>
      <el-table-column  label="操作" >
        <template #default="scope">
          <div class="flex">
            <ElButton size="small" type="success" @click="onShowErrorList(scope.row)">失败明细</ElButton>
            <!-- <el-popconfirm  title="是否确认删除该文件?" @confirm="onDeleteFile(scope)">
                <template #reference>
                  <ElButton size="small" type="danger" >删除</ElButton>
                </template>
            </el-popconfirm>             -->
          </div>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <!-- <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton> -->
      </div>
    </template>

    <DialogErrorList v-model:show="bShowError" :data="errorData"/>

  </Dialog>
</template>

<style lang="less" scoped>
:deep(.labelStyle1) {
    width: 15% !important;
}

:deep(.conentStyle1) {
    width: 30%;
}
.el-form-item--default{
    margin-bottom: unset;
}
.el-input,.el-select{
  width: 80%;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

:deep(.tableHeader2) {
  background-color: #f6f6f6 !important;
  color: #575757;
  font-weight: 400;
}

:deep(.error-row .el-table__cell){
  // background-color: rgb(252, 196, 196);
  color: #F56C6C;
  --el-table-border: 1px solid #dedfe0;
}
</style>
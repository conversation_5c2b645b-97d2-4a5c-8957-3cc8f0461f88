<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTable,ElTableColumn,ElTag, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { getProductListApi } from '@/api/product'
import { DialogProduct } from '@/components/DialogProduct';
import { DialogProductSel } from '@/components/DialogProductSel'
import { useRouter } from 'vue-router'
import { useCache } from '@/hooks/web/useCache'

const { push} = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

const show = ref(false);

const productData = reactive([]) //产品列表



const props = defineProps<{
  show: boolean,
  data:any  //售前单数据
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    productData.splice(0, productData.length, ...props.data.pdt_list);
    console.log('00000000',productData)
  }
})

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
   
  // closeDlg()
  // //返回数据给上层  
  // emit('onSubmit')
  //校验是否所有产品都以及绑定了pdt
  for(let pdt of productData)
  {
    if(pdt.pdt_name == undefined || pdt.pdt_name == '')
    {
      ElMessage.error('请绑定销售产品！')
      return
    }
  }

  //整理数据并跳转到新增销售订单页面
  //构造pdt列表

  //缓存数据
  wsCache.set('tosell', productData)

  console.log('???',productData)

  // addRouter()
  push({
    path: '/salemanage/addsale',
    query:{
        id:'',
        tosell:'1',
        source:'售前转销售',
        buyer_id:props.data.buyer_id,
        buyer_nick:props.data.buyer_nick,
    },    
    })
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

//最后一次选择的pdt
const lastSelPdt = ref({})

//新建产品
const showAddProduct = ref(false)
const onAddNewPdt = (item)=>{
  showAddProduct.value = true
  lastSelPdt.value = item
  lastSelPdt.value.sell_offer_num = props.data.sell_offer_num
}
//创建产品回调
const onAddPdtCallback = (pdt)=>{
  console.log('000000000',pdt)
  lastSelPdt.value.id = pdt.id
  lastSelPdt.value.pdt_name = pdt.name
}


//绑定产品
const showBindProduct = ref(false)
const onBindPdt = (item)=>{
  showBindProduct.value = true
  lastSelPdt.value = item
  lastSelPdt.value.sell_offer_num = props.data.sell_offer_num
}
//选择产品完毕回调
const onSelProductCallback = (pdt)=>{

  lastSelPdt.value.id = pdt.id
  lastSelPdt.value.pdt_name = pdt.name
  console.log('lastSelPdt',pdt,lastSelPdt.value)
}






</script>

<template>
  <Dialog v-model="show" title="售前转销售数据绑定" max-height="60vh" width="80%" @close="closeDlg">
    <el-table  ref="tableRef" :data="productData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip  prop="pdt_nick" :label="t('product_manage.name')"/>
      <el-table-column show-overflow-tooltip  prop="pdt_amount" label="数量"/>
      <el-table-column show-overflow-tooltip  prop="pdt_stuff" label="材质"/>
      <el-table-column show-overflow-tooltip  prop="pdt_specs" label="规格/克重"/>
      <el-table-column show-overflow-tooltip  prop="pdt_skill" label="工艺要求"/>
      <el-table-column show-overflow-tooltip  prop="pdt_print" label="Logo要求/印刷"/>
      <el-table-column show-overflow-tooltip  prop="pdt_color" label="颜色"/>
      <el-table-column show-overflow-tooltip  prop="pdt_pack" label="包装要求"/>
      <el-table-column show-overflow-tooltip  prop="pdt_name" label="当前绑定">
        <template #default="scope">
          <div class="cursor-pointer" v-show="scope.row.pdt_name != undefined">
            <ElTag >{{ scope.row.pdt_name }}</ElTag>
          </div>
        </template>
      </el-table-column>

      <el-table-column  label="产品关联"  width="150">
        <template #default="scope">
          <div class="flex">
            <ElButton size="small" type="success" @click="onAddNewPdt(scope.row)" >新建</ElButton>
            <ElButton size="small" type="primary" @click="onBindPdt(scope.row)" >关联</ElButton>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>

    <!-- 新建产品 -->
    <DialogProduct id="" type="" categ="{}" :exinfo="lastSelPdt"  :title="t('product_manage.add_product')" v-model:show="showAddProduct" @on-submit="onAddPdtCallback"/>
    <!-- 产品选择弹窗 -->
    <DialogProductSel v-model:show="showBindProduct"  :title="t('bom.sel_pdt')" @on-submit="onSelProductCallback"/>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
</style>
<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch, reactive } from 'vue';
import { ElRadioGroup, ElRadioButton, ElButton, ElTable, ElTableColumn, ElPagination, ElMessage, ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { geBuyerListApi } from '@/api/customer'
import { checkPermissionApi } from '@/api/tool';
import { setHotPoint } from '@/api/extra';

const { t } = useI18n()
const show = ref(false);

const customerData = reactive([]) //人员列表
//查询条件
const searchCondition = reactive({
  type: '',
  buyer_nick: '',  //客户名称
  buyer_name: '',  //客户编号
  corp_name: '',   //公司名称
  _or: true,
  page: 1,
  count: 10,
  inputTxt: '' //界面输入
})
//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    show.value = true
    getCustomerData()

  }
})

//修改输入后同步其他条件
watch(searchCondition, (newData) => {
  searchCondition.buyer_name = newData.inputTxt
  searchCondition.buyer_nick = newData.inputTxt
  searchCondition.corp_name = newData.inputTxt
})

//查询用户列表
const getCustomerData = async (page = 1) => {

  if (comMode.value == '个人客户') {
    searchCondition.realrole = 'customer.perCustomerMgr'
    searchCondition.type = '个人'
  }
  else if (comMode.value == '公司客户') {
    searchCondition.realrole = 'customer.comCustomerMgr'
    searchCondition.type = '公司'
  }
  else {
    searchCondition.realrole = 'customer.comCustomerMgr'
    searchCondition.type = ''
  }
  searchCondition.page = page
  const ret = await geBuyerListApi(searchCondition);
  if (ret) {
    console.log(ret)
    customerData.splice(0, customerData.length, ...ret.data);
    totleCount.value = parseInt(ret.count)
  }
}
//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getCustomerData()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getCustomerData(val)
}

//定义通知
const emit = defineEmits(['update:show', 'onSubmit'])
//提交选择的数据
const onSubmit = () => {
  console.log(currentRow.value)
  if (currentRow.value?.id == undefined) {
    ElMessage.warning('请选择一条数据')
    return
  }

  closeDlg()
  //返回数据给上层

  emit('onSubmit', currentRow.value?.id, currentRow.value.buyer_name, currentRow.value.type == '个人' ? currentRow.value.buyer_nick : currentRow.value.corp_name, currentRow.value)

  setHotPoint({
    path_name: '/erp/buyer/list',
    hot_id: currentRow.value?.id
  })
}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

const comMode = ref('全部')
const onChangeComMode = () => {
  getCustomerData()
}


</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-input style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputNameOrID')"
        v-model="searchCondition.inputTxt" />
      <el-button type="primary" @click="getCustomerData(1)">{{ t('button.search') }}</el-button>
    </div>
    <el-radio-group v-model="comMode" size="large" @change="onChangeComMode">
      <el-radio-button label="全部" />
      <el-radio-button label="个人客户" />
      <el-radio-button label="公司客户" />
    </el-radio-group>
    <el-table ref="tableRef" :data="customerData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row @row-dblclick="onSubmit" @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader">
      <el-table-column show-overflow-tooltip fixed prop="type" :label="t('customer.type')" :width="70" />
      <el-table-column show-overflow-tooltip fixed prop="buyer_name" :label="t('customer.id')">
        <template #default="scope">
          {{ checkPermissionApi('客户编码显示') ? scope.row.buyer_name : '***' }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip fixed :label="t('customer.name')">
        <template #default="scope">
          <div>{{ checkPermissionApi('客户名称显示') ? ((scope.row.type == '个人' ? scope.row.buyer_nick :
            scope.row.corp_name)):'***' }}</div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip fixed prop="phone" :label="t('customer.connector_phone')">
        <template #default="scope">
          <div>{{ (scope.row.type == '个人' ?
            scope.row.phone1 : (scope.row.corp_linkman.length > 0 ? scope.row.corp_linkman[0].phone :
              scope.row.corp_phone)) }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination class="flex justify-end" v-model:current-page="searchCondition.page"
      v-model:page-size="searchCondition.count" :page-sizes="[10, 50, 100, 300]" :background="true"
      layout="sizes, prev, pager, next" :total="totleCount" @size-change="handlePageSizeChange"
      @current-change="handlePageCurrentChange" />
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit" class="mr-4">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>

    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #f0f9eb !important;
  --el-table-current-row-bg-color: #f0f9eb !important;
  --el-table-row-hover-bg-color: #f0f9eb;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
}
</style>
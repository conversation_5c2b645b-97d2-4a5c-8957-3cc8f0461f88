<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElButton,ElTabs,ElTabPane } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted,computed } from 'vue';
import { getSysLogApi } from '@/api/product'
import  TabRequestPreSale  from '@/views/SaleManage/components/TabRequestPreSale.vue'
import  TabBasePricePreSale  from '@/views/SaleManage/components/TabBasePricePreSale.vue'
import  TabFinalPricePreSale  from '@/views/SaleManage/components/TabFinalPricePreSale.vue'

const { t } = useI18n()
const show = ref(false);

//当前切换页
const activeTab = ref('0')

const props = defineProps<{
  show: boolean,
  data:any //快照
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    activeTab.value = '0'
  }
})

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
   
  closeDlg()
  //返回数据给上层  
  // emit('onSubmit',currentRow.value)
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}


//是否显示出厂报价单
const showBasePricePreSale = computed(()=>{
  return true
})
//是否显示销售报价
const showFinalPricePreSale = computed(()=>{
  return true
})

</script>

<template>
  <Dialog v-model="show" :title="'任务历史记录'" max-height="60vh" width="60%" @close="closeDlg">

    <el-tabs type="border-card" v-model="activeTab" >
        <el-tab-pane v-for="pdt,index in props.data.pdt_list" :key="pdt.标识" :label="'产品'+(index+1)" :name="index.toString()">
            <el-tabs  v-model="pdt.activeTab" class="mb-20" >
                <el-tab-pane label="产品信息" name="0">      
                    <!-- <TabRequestPreSale :order="props.data" :param="pdt"/> -->
                    <TabRequestPreSale :capmode="true" :fsm_cur_state="props.data.fsm_cur_state" :pdt="pdt" :presale-data="props.data" />
                </el-tab-pane>
                <el-tab-pane v-if="showBasePricePreSale" label="产品成本分析" name="1">
                    <TabBasePricePreSale :capmode="true" :fsm_cur_state="props.data.fsm_cur_state" :pdt="pdt" />
                </el-tab-pane>
                <el-tab-pane v-if="showFinalPricePreSale" label="产品成交报价" name="2">
                    <TabFinalPricePreSale :capmode="true" :fsm_cur_state="props.data.fsm_cur_state" :pdt="pdt"/>
                </el-tab-pane>
            </el-tabs>
        </el-tab-pane>

    </el-tabs>


    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
.el-card .el-tabs__header {
    border: 0px solid #CFCFCF;
    border-bottom: none;
    border-radius: 4px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
</style>
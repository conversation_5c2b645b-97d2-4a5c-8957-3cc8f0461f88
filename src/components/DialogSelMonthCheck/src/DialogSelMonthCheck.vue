<script setup lang="ts">
import { ref, watch,reactive } from 'vue';
import { ElSelect,ElOption,ElButton,ElTable,ElTableColumn,ElPagination, ElMessage,ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { getPayBillBuyListApi, getPayBillOemListApi, getPurchaseListApi } from '@/api/product'
import { checkPermissionApi } from '@/api/tool';
import { cloneDeep } from 'lodash-es';

const { t } = useI18n()
const show = ref(false);

const checkData = reactive([]) 
//查询条件
const searchCondition = reactive({
  供应商编号: '',
  受托方编号:'',
  page: 1,
  count: 10,
  inputTxt:'' //界面输入
})

//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
  type: string,
  dir_id:string
}>()
//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getOutList()
  }
})

//修改输入后同步其他条件
// watch(searchCondition,(newData)=>{
//   searchCondition.buy_order_num = newData.inputTxt
//   searchCondition.supplier_nick = newData.inputTxt
// })



//查询采购月结
const getBuyMonthlyCheckListData = async () => {
  let tmp = cloneDeep(searchCondition)
  tmp.buy_paybill_num = tmp.inputTxt
  const ret = await getPayBillBuyListApi(tmp)
  if(ret)
  {
    checkData.splice(0,checkData.length,...ret.data)
    totleCount.value =  parseInt(ret.count)
  }
}
//查询受托方数据
const getOemMonthlyCheckListData = async () => {
  let tmp = cloneDeep(searchCondition)
  tmp.oem_paybill_num = tmp.inputTxt
  const ret = await getPayBillOemListApi(tmp)
  if(ret)
  {
    checkData.splice(0,checkData.length,...ret.data)
    totleCount.value =  parseInt(ret.count)
  }
}

const getOutList = () => {
  if (props.type == '采购') {
      searchCondition.供应商编号 = props.dir_id
      getBuyMonthlyCheckListData()
    }
    else if (props.type == '委外') {
      searchCondition.受托方编号 = props.dir_id
      getOemMonthlyCheckListData()
    }
}

//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getOutList()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getOutList()
}

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
  console.log(currentRow.value)
  if(currentRow.value?.id == undefined)
  {
    ElMessage.warning('请选择一条数据')
    return
  }

  if(currentRow.value.fsm_cur_state != '已对账')
  {
    ElMessage.warning('该订单未对账，不能选择！')
    return
  }
   
  closeDlg()
  //返回数据给上层
  
  emit('onSubmit',currentRow.value)
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}



</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg" width="80%">
    <div class="mb-5 flex justify-center items-center">
      <el-input style="width: 300px;margin-right: 10px;" :placeholder="'请输入月结单号'" v-model="searchCondition.inputTxt" @keyup.enter="getOutList()" clearable/>
      <el-button type="primary" @click="getOutList()">{{ t('button.search') }}</el-button>
    </div>
    <el-table  ref="tableRef" :data="checkData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      highlight-current-row @current-change="handleCurrentChange"
      header-cell-class-name="tableHeader" @row-dblclick="onSubmit">
      <el-table-column v-if="props.type=='采购'" show-overflow-tooltip  prop="buy_paybill_num" label="月结单号"  />
      <el-table-column v-if="props.type=='采购'" show-overflow-tooltip  prop="supplier_nick" label="供应商"  width="250"/>
      <el-table-column v-if="props.type=='委外'" show-overflow-tooltip  prop="oem_paybill_num" label="月结单号"  />
      <el-table-column v-if="props.type=='委外'" show-overflow-tooltip  prop="parter_nick" label="委外商"  width="250"/>

      <el-table-column show-overflow-tooltip  prop="prepaid_date" label="月结月份"  width="100"/>
      <el-table-column show-overflow-tooltip  prop="应付金额" label="订单金额"  />
      <el-table-column show-overflow-tooltip  prop="已付金额" label="已付金额"  />
      <el-table-column show-overflow-tooltip  prop="已申请金额" label="已申请金额"  />
      <el-table-column show-overflow-tooltip  prop="fsm_cur_state" label="状态"  />
    </el-table>
    <el-pagination class="flex justify-end"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handlePageSizeChange"
        @current-change="handlePageCurrentChange"
    />
    <template #footer>
     <div class="flex justify-end">
      <ElButton type="primary" @click="onSubmit">
        {{ t('msg.ok') }}
      </ElButton>
      <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
     </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
</style>
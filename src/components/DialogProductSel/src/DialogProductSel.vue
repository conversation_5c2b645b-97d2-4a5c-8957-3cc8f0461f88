<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch, reactive } from 'vue';
import { ElButton, ElTable, ElTableColumn, ElPagination, ElMessage, ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { getProductListApi } from '@/api/product'
import { DialogProduct } from '@/components/DialogProduct';


const { t } = useI18n()
const show = ref(false);

const productData = reactive([]) //产品列表
//查询条件
const searchCondition = reactive({
  name: '',
  nick: '',
  sttus:'正常',
  _or: true,
  page: 1,
  count: 10,
  inputTxt: '' //界面输入
})
//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
}>()
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    show.value = true
    getProductList()
  }
})

//修改输入后同步其他条件
watch(searchCondition, (newData) => {
  searchCondition.name = newData.inputTxt
  searchCondition.nick = newData.inputTxt
  // searchCondition.corp_name = newData.inputTxt
})

//查询用户列表
const getProductList = async (page = 1) => {
  console.log('111111')
  searchCondition.page = page
  const ret = await getProductListApi(searchCondition);
  if (ret) {
    console.log(ret)
    productData.splice(0, productData.length, ...ret.data);
    totleCount.value = parseInt(ret.count)
  }
}
//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getProductList()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getProductList(val)
}

//定义通知
const emit = defineEmits(['update:show', 'onSubmit'])
//提交选择的数据
const onSubmit = () => {
  console.log(currentRow.value)
  if (currentRow.value?.id == undefined) {
    ElMessage.warning('请选择一条数据')
    return
  }

  closeDlg()
  //返回数据给上层

  emit('onSubmit', currentRow.value)
}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}


const showAddProduct = ref(false)
const onShowAddProduct = () => {
  showAddProduct.value = true
}

</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-input style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputProductInfo')"
        v-model="searchCondition.inputTxt" clearable/>
      <el-button type="primary" @click="getProductList(1)">{{ t('button.search') }}</el-button>
      <el-button type="warning" @click="onShowAddProduct">{{ t('button.add') + '产品' }}</el-button>
    </div>
    <el-table ref="tableRef" :data="productData" style="width: 100%; margin-bottom: 20px;height: 76%;" row-key="guuid" border height="100%"
      highlight-current-row @current-change="handleCurrentChange" @row-dblclick="onSubmit" header-cell-class-name="tableHeader">
      <el-table-column show-overflow-tooltip fixed prop="categ_name" :label="t('customer.type')" />
      <el-table-column show-overflow-tooltip fixed prop="name" :label="t('product_manage.id')" />
      <el-table-column show-overflow-tooltip fixed prop="nick" :label="t('product_manage.name')" />
      <el-table-column show-overflow-tooltip fixed prop="brand" :label="t('product_manage.brand')" />
      <el-table-column show-overflow-tooltip fixed prop="specs_text" :label="t('product_manage.specify_info')" />
      <el-table-column show-overflow-tooltip fixed label="是否创建BOM">
        <template #default="scope">
          <div>{{ (scope.row.bom_id == '' ? '' : '已创建') }}</div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination class="flex justify-end" v-model:current-page="searchCondition.page"
      v-model:page-size="searchCondition.count" :page-sizes="[10, 50, 100, 300]" :background="true"
      layout="sizes, prev, pager, next" :total="totleCount" @size-change="handlePageSizeChange"
      @current-change="handlePageCurrentChange" />
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>

    <DialogProduct id="" type="" categ="{}" :exinfo="{}" :title="t('product_manage.add_product')"
      v-model:show="showAddProduct" @on-submit="getProductList(1)" />
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
}
</style>
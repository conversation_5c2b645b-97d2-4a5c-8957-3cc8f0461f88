<script setup lang="ts">
import { ElInputNumber } from 'element-plus';
import { ref, watch, onMounted } from 'vue';

const data = ref(0)
const props = defineProps<{
  inputvalue: any,
}>()

watch(() => props.inputvalue, async(val) => {
  data.value =  parseFloat(val)
})
const emit =  defineEmits(['change']);

onMounted(() => {
  data.value =  parseFloat(props.inputvalue)
})
</script>

<template>
  <!-- <el-input-number class="!w-[100%]" :controls="false"  :min="0" v-model="data"  @change="emit('change',data)"  size="small"/> -->
  <input
    class="!w-[100%]"
    type="number"
    :value="data"
    @change="emit('change',data)"
  />
</template>

<style lang="less" scoped>
input {
    background-color: #1469bd00;
    border: #aca9a97d solid 1px;
	box-sizing: conte-box;
}

input:focus {
    border-style: solid;
    border-color: #03a9f4;
    box-shadow: 0 0 15px #03a9f4;
    outline: none;
}
</style>

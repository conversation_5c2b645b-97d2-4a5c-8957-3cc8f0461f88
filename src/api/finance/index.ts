import request from '@/config/axios'


interface SearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getFinanceAccountListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/fin/account/list', params:restParams })
}
//删除
export const delFinanceAccountApi = (param): Promise<IResponse> => {
    return request.post({ url: '/fin/account/del', data: param })
}
//获取入库单最新ID
export const getFinanceAccountNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/fin/account/new_num' })
}
//新增入库单
export const addFinanceAccountApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/fin/account/add', data: param })
}
//修改入库单
export const updateFinanceAccountApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/fin/account/update', data: param })
}
export const getFinanceAccountInfoApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/fin/account/info', params:restParams })
}






interface SearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getFinanceWaterListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/fin/water/list', params:restParams })
}
//删除
export const delFinanceWaterApi = (param): Promise<IResponse> => {
    return request.post({ url: '/fin/water/del', data: param })
}
//获取入库单最新ID
export const getFinanceWaterNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/fin/water/new_num' })
}
//新增入库单
export const addFinanceWaterApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/fin/water/add', data: param })
}
//修改入库单
export const updateFinanceWaterApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/fin/water/update', data: param })
}
export const getFinanceWaterInfoApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/fin/water/info', params:restParams })
}
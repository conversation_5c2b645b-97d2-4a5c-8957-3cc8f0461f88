//部门用户管理相关API
import request from '@/config/axios'

//新增客户
export const addBuyerApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/buyer/add', data: param })
}

//修改客户
export const updateBuyerApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/buyer/update', data: param })
}

//查询客户
interface BuyerSearchParam{
    buyer_nick?: string,  //客户名称
    buyer_name?: string,  //客户编号
    corp_name?: string,   //公司名称
    phone1?: string,      //手机1
    telephone?: string,    //电话
    mainer?: string,      //负责人
    follower?: string,    //跟单员
    phone_status?: string, //电话状态
    address?: string,     //地址
    remark?: string,      //备注
    create_date?: Array<string>,  //创建时间范围
    modify_date?: Array<string>,  //修改时间范围
    page?: number,
    count?: number
}
export const geBuyerListApi = (params:BuyerSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
  //  restParams.realrole = 'customer.perCustomerMgr'
    return request.get({ url: '/erp/buyer/list', params:restParams })
}
export const getBuyerInfoApi = (params:BuyerSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/buyer/info', params:restParams })
}

//获取最新客户编号ID
export const getNewBuyerIDApi = ():Promise<IResponse> =>{
    return request.get({ url: '/erp/buyer/new_num' })
}

//删除客户
export const delBuyerApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/buyer/del', data: param })
}

//新增跟踪记录
export const addFollowInfoApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/buyer/log/add', data: param })
}

//查询客户
interface FollowSearchParam{
    buyer_id?: string,  //客户名称
    log_type?: string,  //客户编号
    page?: number,
    count?: number
}
export const getFollowInfoListApi = (params:FollowSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buyer/log/list', params:restParams })
}


//查询供应商
interface SupplierSearchParam{
    page?: number,
    count?: number
}
export const getSupplierListApi = (params:SupplierSearchParam): Promise<IResponse> => {
    params.realrole = 'purchase.supplier_manage'
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/supplier/list', params:restParams })
}
export const getSupplierInfoApi = (params:SupplierSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/supplier/info', params:restParams })
}
//获取最新供应商编号ID
export const getNewSupplierIDApi = ():Promise<IResponse> =>{
    return request.get({ url: '/erp/supplier/new_num' })
}
//新增供应商
export const addSupplierApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/supplier/add', data: param })
}

//修改供应商
export const updateSupplierApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/supplier/update', data: param })
}
//删除客户
export const delSupplierApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/supplier/del', data: param })
}


//查询受托商
interface ParterSearchParam{
    page?: number,
    count?: number
}
export const getParterListApi = (params:ParterSearchParam): Promise<IResponse> => {
    params.realrole = 'oem.order_list'
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/parter/list', params:restParams })
}
export const getParterInfoApi = (params:ParterSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/parter/info', params:restParams })
}
//获取最新受托商编号ID
export const getNewParterIDApi = ():Promise<IResponse> =>{
    return request.get({ url: '/erp/parter/new_num' })
}
//新增受托商
export const addParterApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/parter/add', data: param })
}

//修改受托商
export const updateParterApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/parter/update', data: param })
}

//删除客户
export const delParterApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/parter/del', data: param })
}

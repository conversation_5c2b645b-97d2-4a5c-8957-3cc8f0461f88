//laba接口
import request from '@/config/axios'



interface SearchParam{
    page?: number,
    count?: number
}
export const getWithdrawListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/user/withdraw/list', params:restParams })
}

//修改提现
export const updateWithdrawApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/withdraw/update', data: param })
}
//提现数据统计
export const getWithdrawTJApi = (param): Promise<IResponse> => {
    return request.get({ url: '/user/withdraw/list_stat' , params:param})
}

//-----------------充值------------------
export const getCZListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/cashappay/order/list', params:restParams })
}

//充值成功
export const updateCZSuccessApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cashappay/order/pay_success?id='+param.id, data: param })
}

//充值失败
export const updateCZFailedApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cashappay/order/pay_fail?id='+param.id, data: param })
}
//充值删除
export const deleteCZApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cashappay/order/delete?id='+param.id, data: param })
}
//充值数据统计
export const getCZTJApi = (param): Promise<IResponse> => {
    return request.get({ url: '/cashappay/order/list_stat' , params:param})
}

//----------------------收款配置---------------------------
export const getPayUrlListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/cashappay/payurl/list', params:restParams })
}
export const addPayUrlApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cashappay/payurl/add', data: param })
}
export const updatePayUrlApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cashappay/payurl/update', data: param })
}
export const delPayUrlApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cashappay/payurl/delete?id='+param.id, data: param })
}

//----------------------统计------------------------------
export const getTongjiPlayerListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/game/board/list', params:restParams })
}
export const getTongjiFishPlayerListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/fish/board/list', params:restParams })
}
//注册统计
export const getTongjiRegisterApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/user/board/info', params:restParams })
}
//渠道统计
export const getTongjiChannelListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/app/static/list', params:restParams })
}
//资金流水
export const getCashWaterListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/user/cash_water/list', params:restParams })
}

//查询玩家流水信息
export const getUserCashWaterInfoApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/user/cash_water/user_info', params:restParams })
}
export const addUserScoreApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/cash_water/game_balance', data: param })
}
export const delUserScoreApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/cash_water/game_balance_sub', data: param })
}

//各游戏输赢统计
export const getGameWinLoseListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/user/cash_water/list_stat', params:restParams })
}

//复位收款账号优先级
export const resetAccountOrder = (param): Promise<IResponse> => {
    return request.post({ url: '/cashappay/payurl/reset', data: param })
}

//历史活跃
export const getOtherDateInfoApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/user/board/other_date', params:restParams })
}

//历史某天活跃
export const getActiveOneDayApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/game/board/other_date', params:restParams })
}

//修改用户VIP类型
export const modifyUserVipTypeApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/cash_water/game_user_type', data: param })
}




//----------------------兑换码配置---------------------------
export const getExchangeCodeListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/cashappay/exchange_code/list', params:restParams })
}
export const addExchangeCodeApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cashappay/exchange_code/add', data: param })
}
export const updateExchangeCodeApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cashappay/exchange_code/update', data: param })
}
export const delExchangeCodeApi = (param): Promise<IResponse> => {
    // return request.post({ url: '/cashappay/exchange_code/delete?id='+param.id, data: param })
    return request.post({ url: '/cashappay/exchange_code/delete?id='+param.id, data: param })
}


//下载统计明细
export const getDownloadListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    restParams.status = 'download'
    return request.get({ url: '/app/static/list_download_info', params:restParams })
}
//注册统计明细
export const getRegisteListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    restParams.status = 'register'
    return request.get({ url: '/app/static/list_download_info', params:restParams })
}


//查询用户列表明细
export const getPlayerTongjiListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/app/static/list_player_info', params:restParams })
}
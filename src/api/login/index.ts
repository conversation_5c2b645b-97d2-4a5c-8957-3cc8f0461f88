import request from '@/config/axios'
import type { UserType } from './types'
import { sendCommand } from '@/websocket'

interface RoleParams {
  role_id: string
}

export const loginApi = (data: UserType): Promise<IResponse<UserType>> => {
  return request.post({ url: '/erp/user/login', data })
}

export const loginOutApi = (): Promise<IResponse> => {
  return request.get({ url: '/erp/user/logout' })
}

export const getUserListApi = ({ params }: AxiosConfig) => {
  return request.get<{
    code: string
    data: {
      list: UserType[]
      total: number
    }
  }>({ url: '/user/list', params })
}

export const getAdminRoleApi = (
  params: RoleParams
): Promise<IResponse<AppCustomRouteRecordRaw[]>> => {
  return request.get({ url: '/role/list', params })
}

export const getRoleApi = (params: RoleParams): Promise<IResponse<string[]>> => {
  return request.get({ url: '/erp/role/list', params })
}


export const testApi = (): Promise<IResponse> => {
  return request.post({ url: '/erp/home/<USER>' })
}

export const wstest = (cmd:string):Promise<IResponse<string>> => {
  return sendCommand(cmd)
}
//OSS相关接口
import request from '@/config/axios'
import axios from 'axios'
import { ElMessage } from 'element-plus';
import { getCurrentDateTimeString, getGUID } from '../tool';


interface ossParam{
    upload_dir:string
}
//获取上传认证
export const getOssSignApi = (params:ossParam): Promise<any> => {
    return request.get({ url: '/erp/home/<USER>', params })
}

export const ossUpload = async(res,file,path,progressCallback)=>{
    const filename = path+getCurrentDateTimeString()+getGUID(3)+get_suffix(file.name)
    const formData = new FormData();
    formData.append("OSSAccessKeyId", res.accessid);
    formData.append("key", filename);//res.filePath
    formData.append("policy", res.policy);
    formData.append("signature", res.signature);
    // 注意：file文件必须放在表单的最后面，OSS官网有说明
    formData.append("file", file);

    return await axios({
        url: res.host,
        timeout: 1000 * 60 * 10,
        method: 'post',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data' },
        // onUploadProgress: function (progressEvent) {
        //     progressCallback(progressEvent)
        // },
    }).then(() => {
        return { 'url': res.host+'/'+filename};
    }).catch(() => {
        ElMessage.error('上传失败，请联系管理员');
        return { 'url': res.host+filename };
    });
}

const  get_suffix = (filename)=> {
    const pos = filename.lastIndexOf('.')
    let suffix = ''
    if (pos != -1) {
        suffix = filename.substring(pos)
    }
    return suffix;
}
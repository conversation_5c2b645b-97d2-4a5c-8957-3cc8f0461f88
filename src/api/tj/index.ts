//统计接口
import request from '@/config/axios'


//月结统计
interface SearchParam{
    page?: number,
    count?: number
}
export const getSellBayBillTJApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/paybill/list_stat', params:restParams })
}
export const getBuyBayBillTJApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/paybill/list_stat', params:restParams })
}
export const getOemBayBillTJApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/paybill/list_stat', params:restParams })
}


//销售统计总表
export const getSellReportAllApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_stat', params:restParams })
}
export const getSellReportCusApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_stat_buyer', params:restParams })
}
export const getSellReportUserApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_stat_user', params:restParams })
}

//出库统计
export const getSellOutReportCusApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/takeout/list_stat_buyer', params:restParams })
}
export const getSellOutReportUserApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/takeout/list_stat_user', params:restParams })
}
//毛利统计
export const getSellValueReportCusApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_profit_buyer', params:restParams })
}
export const getSellValueReportOrderApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_profit_date', params:restParams })
}
export const getSellValueReportOutApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/takeout/list_profit_date', params:restParams })
}
export const getSellValueReportUserApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_profit_user', params:restParams })
}

//采购统计
export const getBuyReportAllApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/order/list_stat', params:restParams })
}
export const getBuyReportSupplierApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/order/list_stat_supplier', params:restParams })
}
export const getBuyReportInApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/putin/list_stat_supplier', params:restParams })
}

//委外
export const getOemReportAllApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/order/list_stat', params:restParams })
}

//仓库
export const getStoneReportMonthApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/stock/list_stat', params:restParams })
}
export const getStoneReportMonthDetailApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/stock/list_stat_pdt', params:restParams })
}
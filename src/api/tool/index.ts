
import { FormInstance } from 'element-plus';
import pako from 'pako'
import { v4 as uuidv4 } from 'uuid';
import { useCache } from '@/hooks/web/useCache'
import { useTagsViewStore } from '@/store/modules/tagsView'

const { wsCache } = useCache()
const tagsViewStore = useTagsViewStore()

//对输入的字符串先进行gzip压缩然后再base64编码
export function encodeString(input: string,isGzipped = false): string {
    // 1. 字符串转为字节数组
    let bytes = new TextEncoder().encode(input);
    // 2. 判断是否压缩
    if (isGzipped) {
        bytes = pako.gzip(bytes);  
    }
    // 3. 字节数组转为 base64 编码
    // const base64 = btoa(String.fromCharCode(...bytes));
    const base64 = btoa(encodeURIComponent(new TextDecoder().decode(bytes)));
    return base64;
}

//对输入的字符串先进行base64解码然后在gzip解压
export function decodeString(input: string,isGzipped = false): string {
    // // 1. 先解码 base64 
    // let bytes = Uint8Array.from(atob(input), c => c.charCodeAt(0));
    // // 2. 判断是否解压
    // if (isGzipped) {
    //     bytes = pako.ungzip(bytes); 
    // }
    // // 3. 转字符串返回
    // return decodeURIComponent(new TextDecoder().decode(bytes));
    const arr = new Uint8Array([...atob(input)].map(c => c.charCodeAt(0)));
    const data = pako.inflate(arr, { to: "string" });
    return data;
}
export function zip(str) {
    const arr = pako.deflate(str, { gzip: true });
    const ret = btoa(String.fromCharCode.apply(null, arr));
    return ret;
  }
 
export function unzip(str) {
    const arr = new Uint8Array([...atob(str)].map(c => c.charCodeAt(0)));
    const data = pako.inflate(arr, { to: "string" });
    return data;
}

// const chineseUpperCaseDigits = {
//     '0': '零',
//     '1': '壹',
//     '2': '贰',
//     '3': '叁',
//     '4': '肆',
//     '5': '伍',
//     '6': '陆',
//     '7': '柒',
//     '8': '捌',
//     '9': '玖'
//   };
  
// export  function toUperNumber(number: number): string {
//     return number.toString().replace(/[0-9]/g, match => chineseUpperCaseDigits[match]);
// }

export function toUperNumber(n: number): string {
    const fraction = ['角', '分'];
    const digit = [
        '零', '壹', '贰', '叁', '肆',
        '伍', '陆', '柒', '捌', '玖'
    ];
    const unit = [
        ['元', '万', '亿'],
        ['', '拾', '佰', '仟']
    ];
    const head = n < 0 ? '欠' : '';
    n = Math.abs(n);

    let s = '';

    for (let i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }

    s = s || '整';
    n = Math.floor(n);

    for (let i = 0; i < unit[0].length && n > 0; i++) {
        let p = '';
        for (let j = 0; j < unit[1].length && n > 0; j++) {
            p = digit[n % 10] + unit[1][j] + p;
            n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }

    return head + s.replace(/(零.)*零元/, '元')
        .replace(/(零.)+/g, '零')
        .replace(/^整$/, '零元整');
}





//校验表单结果
export async function  checkFormRule(formEl: FormInstance | undefined){
    if (!formEl) return false;
    let valid;
    await formEl.validate(v => valid = v);
    return valid;
}

//返回今天年月日
export function getTodayDate(addDays = 0) {
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate() + addDays);
    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const day = String(currentDate.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;
    return formattedDate;
}
export function getLastDayOfMonth(): string {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1; // JavaScript 的月份从 0 开始，所以需要 +1
    const lastDay = new Date(year, month, 0).getDate(); // 获取本月最后一天的日期
  
    // 格式化日期
    const formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`;
  
    return formattedDate;
  }
  export function getCurMonth(): string {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1; // JavaScript 的月份从 0 开始，所以需要 +1
    const lastDay = new Date(year, month, 0).getDate(); // 获取本月最后一天的日期
  
    // 格式化日期
    const formattedDate = `${year}-${String(month).padStart(2, '0')}`;
  
    return formattedDate;
  }

//返回指定位数的GUUID
export function getGUID(length: number): string {
    let result = '';
    //const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
}

//返回当前年月日时分秒字符串
export function getCurrentDateTimeString(): string {
    const now = new Date();
  
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
  
    const dateTimeString = `${year}${month}${day}${hours}${minutes}${seconds}`;
  
    return dateTimeString;
  }

  
//检测用户是否有某个权限
export function checkPermissionApi(permission: string): boolean {
    const perAll = wsCache.get('roleRoutersLaba')
    //console.log('checkrole',permission,perAll)
    //校验是否有同名权限
    for(const map of perAll)
    {
        if(map.others == undefined)
            continue
        for(const other of map.others)
        {
            if(other == permission)
            {
                return true;
            }
        }
    }
    return false
}
//检测用户是否有某个路由权限
export function checkRouterPermissionApi(path: string): boolean {
    const perAll = wsCache.get('roleRoutersLaba')
    //console.log('checkrole',permission,perAll)
    //校验是否有同名权限
    for(const map of perAll)
    {
        if(map.routes == undefined)
            continue
        for(const route of map.routes)
        {

            if(path == route)
            {
                return true;    
            }
        }
    }
    return false
}


//-------------------------------------------------
//测试xlsx读取修改
export function test(){
    fetch('tmp.xlsx')  
    .then(response => response.blob())  // 将响应转化为Blob对象  
    .then(blob => {  
        const reader = new FileReader();  
        reader.onload = function(event) {  
            const data = new Uint8Array(event.target.result);  
            const workbook = XLSX.read(data, {type: 'array'});  // 读取二进制数据并转化为工作簿  
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];  // 获取第一个工作表  
            const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            // // 如果需要将修改后的工作簿保存为新的Excel文件  
            XLSX.writeFile(workbook, 'new_file.xlsx');
        };  
        reader.readAsArrayBuffer(blob);  // 开始读取Blob对象  
    })  
    .catch(error => console.error(`无法访问文件: ${error}`));
}


export function getMoneyFlag(type = '人民币'){
    if(type == '人民币')
    {
        return '￥'
    }
    else if(type == '美元')
    {
        return '$'
    }
}

//关闭指定TAG
export function closeOneTagByName(name: string){
    for(const tag of tagsViewStore.visitedViews)
    {
        if(tag.title == name)
        {
            tagsViewStore.delView(tag)
            return
        }
    }
}
//关闭指定TAG
export function closeOneTagByPath(path: string){
    for(const tag of tagsViewStore.visitedViews)
    {
        if(tag.path == path)
        {
            tagsViewStore.delView(tag)
            return
        }
    }
}


export function downloadFile(url, filename) {
    fetch(url)
      .then(res => res.blob())
      .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
      });
  }


export function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}
  

//取小数点后指定位数并向前进位
export function ceilToFixed(num, decimals,oldvalue,bFouceFix = false) {  //bFouceFix：强制不进位
    // // 将输入转换为数字
    // num = parseFloat(num);
  
    // // 检查转换是否成功
    // if (isNaN(num)) {
    //   throw new Error("输入不是有效的数字");
    // }
  
    // // 计算并返回结果
    // const factor = Math.pow(10, decimals);
    // const tempNumber = num * factor;
    // const nextDigit = Math.floor(tempNumber * 10) % 10;

    // // 检查小数点后 decimals 位的下一位
    // if (nextDigit === 0 || bFouceFix) {
    //     // 下一位是0，不进行进位
    //     return parseFloat((Math.floor(tempNumber) / factor).toFixed(decimals));
    // } else {
    //     // 下一位不是0，进行进位
    //     return parseFloat((Math.ceil(tempNumber) / factor).toFixed(decimals));
    // }
    // 将输入转换为数字
    num = parseFloat(num);
    oldvalue = parseFloat(oldvalue);
    
    // 检查转换是否成功
    if (isNaN(num)) {
        throw new Error("输入不是有效的数字");
    }
    
    // 计算并返回结果
    const factor = Math.pow(10, decimals);
    const tempNumber = num * factor;
    const nextDigit = Math.floor(tempNumber * 10) % 10;

    // 计算结果
    let result;
    if (nextDigit === 0 || bFouceFix) {
        // 下一位是0，不进行进位
        result = parseFloat((Math.floor(tempNumber) / factor).toFixed(decimals));
    } else {
        // 下一位不是0，进行进位
        result = parseFloat((Math.ceil(tempNumber) / factor).toFixed(decimals));
    }

    // 检查与 oldvalue 的误差
    const epsilon = 1 / factor*2; // 一个进位的误差范围
    if (Math.abs(result - oldvalue) <= epsilon) {
        return oldvalue;
    }

    return result;
}

export function getDateArea(days: number) {
    const currentDate = new Date();
    const pastDate = new Date(currentDate);

    // 计算days天前的日期
    pastDate.setDate(currentDate.getDate() - days);

    // 格式化days天前的日期
    const startYear = pastDate.getFullYear();
    const startMonth = String(pastDate.getMonth() + 1).padStart(2, '0');
    const startDay = String(pastDate.getDate()).padStart(2, '0');

    const startDateFormatted = `${startYear}-${startMonth}-${startDay}`;

    // 格式化当前日期
    const endYear = currentDate.getFullYear();
    const endMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
    const endDay = String(currentDate.getDate()).padStart(2, '0');

    const endDateFormatted = `${endYear}-${endMonth}-${endDay}`;

    return [startDateFormatted, endDateFormatted];
}


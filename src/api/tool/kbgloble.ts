import KBEngine from '@/api/tool/kbengine';

const kb = new KBEngine.KBEngineArgs();
kb.ip =  import.meta.env.VITE_SERVER_KB;
kb.port = import.meta.env.VITE_SERVER_KB_PORT;
KBEngine.create(kb);

export { KBEngine };





// KBEngine.app.login("admin999", "123123", "");
// const onLoginSuccessfully = () => {
//   // 登录成功
//   console.log("登录成功")
// }

// KBEngine.Event.registerFun("onLoginSuccessfully", onLoginSuccessfully);
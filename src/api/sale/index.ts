import request from '@/config/axios'


//新增分类
export const addSaleApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/sales/add', data: param })
}

//修改分类
export const updateSaleApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/sales/update', data: param })
}

//删除部分类
export const delSaleApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/sales/del', data: param })
}

//查询分类树
interface SaleSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getSaleListApi = (params:SaleSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/sales/list', params:restParams })
}
//获取订单编号
export const getSaleNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/erp/sales/new_num' })
}

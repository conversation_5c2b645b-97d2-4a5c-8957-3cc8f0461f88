//OSS相关接口
import request from '@/config/axios'

interface taskParam{
    url:string,
    type?:string,
    page?:number,
    count?:number
}
//上传导入产品任务
export const importPdtApi = (params:taskParam): Promise<any> => {
    return request.get({ url: '/erp/pdt/import_excel', params })
}

//查询任务进度
export const getServerTaskApi = (params:taskParam): Promise<any> => {
    return request.get({ url: '/erp/home/<USER>', params })
}

//上传导入销售订单任务
export const importSellOrderApi = (params:taskParam): Promise<any> => {
    return request.get({ url: '/sell/order/import_excel', params })
}

//上传导入产品任务
export const importBomApi = (params:taskParam): Promise<any> => {
    return request.get({ url: '/erp/bom/import_excel', params })
}

//上传导入采购订单任务
export const importBuyOrderApi = (params:taskParam): Promise<any> => {
    return request.get({ url: '/buy/order/import_excel', params })
}
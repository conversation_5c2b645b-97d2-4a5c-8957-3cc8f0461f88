import request from '@/config/axios'

//新增分类
export const addCategApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/pdt/categ/add', data: param })
}

//修改分类
export const updateCategApi = (param): Promise<IResponse> => {
    return request.post({ url: '/pdt/categ/update', data: param })
}

//删除部分类
export const delCategApi = (param): Promise<IResponse> => {
    return request.post({ url: '/pdt/categ/del', data: param })
}

//查询分类树
interface CategSearchParam{
    page?: number,
    count?: number
}
export const getCategListApi = (params:CategSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/pdt/categ/list', params:restParams })
}


//获取产品编号
interface ProductNumParam{
    categ_id:number
}
export const getProductNewnumApi = (params:ProductNumParam): Promise<IResponse> => {
    return request.get({ url: '/erp/pdt/new_num', params })
}

//新增产品
export const addProductApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/pdt/add', data: param })
}

//查询产品列表
interface ProductSearchParam{
    name?:string,
    nick?:string,
    _or?:boolean,
    page?: number,
    count?: number
}
export const getProductListApi = (params:ProductSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/pdt/list', params:restParams })
}
export const getProductInfoApi = (params:BomSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/pdt/info', params:restParams })
}

//修改产品
export const updateProductApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/erp/pdt/update', data: param })
}

//删除产品
export const delProductApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/pdt/del', data: param })
}

//查询规格
interface SpecSearchParam{
    id?: string,
    name?: string,
    page?: number,
    count?: number,
}
export const getSpecListApi = (params:SpecSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/pdt/specs/list', params:restParams })
}

//新增规格
export const addSpecApi = (param): Promise<IResponse> => {
    return request.post({ url: '/pdt/specs/add', data: param })
}

//修改规格
export const updateSpecApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/pdt/specs/update', data: param })
}

//删除规格
export const delSpesApi = (param): Promise<IResponse> => {
    return request.post({ url: '/pdt/specs/del', data: param })
}



//查询工序
interface ProcessSearchParam{
    id?: string,
    name?: string,
    page?: number,
    count?: number,
}
export const getProcessListApi = (params:ProcessSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/bom/proce/list', params:restParams })
}

//新增工序
export const addProcessApi = (param): Promise<IResponse> => {
    return request.post({ url: '/bom/proce/add', data: param })
}

//修改工序
export const updateProcessApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/bom/proce/update', data: param })
}

//删除工序
export const delProcessApi = (param): Promise<IResponse> => {
    return request.post({ url: '/bom/proce/del', data: param })
}
//获取工序编号
export const getProcessNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/bom/proce/new_num' })
}





//查询BOM
interface BomSearchParam{
    id?: string,
    categ?:string,
    name?: string,
    page?: number,
    count?: number,
}
export const getBomListApi = (params:BomSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/bom/list', params:restParams })
}
//查询BOM详情
export const getBomInfoApi = (params:BomSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/bom/info', params:restParams })
}
//新增BOM
export const addBomApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/bom/add', data: param })
}

//修改BOM
export const updateBomApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/erp/bom/update', data: param })
}

//删除BOM
export const delBomApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/bom/del', data: param })
}
//获取产品编号
export const getBomNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/erp/bom/new_num' })
}



//查询仓库
interface StoreSearchParam{
    id?: string,
    name?: string,
    page?: number,
    count?: number,
}
export const getStoreListApi = (params:StoreSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/store/list', params:restParams })
}
//新增仓库
export const addStoreApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/store/add', data: param })
}

//修改仓库
export const updateStoreApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/erp/store/update', data: param })
}

//删除仓库
export const delStoreApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/store/del', data: param })
}
//获取仓库编号
export const getStoreNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/erp/store/new_num' })
}




//新增库存
export const addInventoryApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/stock/add', data: param })
}

//查询总库存列表
interface InventorySearchParam{
    page?: number,
    count?: number
}
export const getInventoryListApi = (params:InventorySearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/stock/list_all', params:restParams })
}
//查询产品库存分布列表
export const getInventoryDetailListApi = (params:InventorySearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/stock/list_one', params:restParams })
}
//查询产品库存流水列表
export const getInventoryStatementListApi = (params:InventorySearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/stock/list', params:restParams })
}


//查询仓库锁定情况
export const getSellLockListApi = (params:InventorySearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_locked', params:restParams })
}
export const getOemLockListApi = (params:InventorySearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/order/list_locked', params:restParams })
}



//新增采购单
export const addPurchaseApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/order/add', data: param })
}

//查询采购单列表
interface PurchaseSearchParam{
    id?:number,
    buy_order_num?:string,
    page?: number,
    count?: number
}
export const getPurchaseListApi = (params:PurchaseSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/order/list', params:restParams })
}

//查询单条
export const getPurchaseInfoApi = (params:PurchaseSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/order/info', params:restParams })
}

//修改采购单
export const updatePurchaseApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/buy/order/update', data: param })
}

//删除采购单
export const delPurchaseApi = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/order/del', data: param })
}
//获取采购单最新ID
export const getPurchaseNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/buy/order/new_num' })
}



//新增收货单
export const addReceiptApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/drawin/add', data: param })
}

//查询收货单列表
interface ReceiptSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getReceiptListApi = (params:ReceiptSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/drawin/list', params:restParams })
}

//查询单条收货单
export const getReceiptInfoApi = (params:ReceiptSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/drawin/info', params:restParams })
}

//修改收货单
export const updateReceiptApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/buy/drawin/update', data: param })
}

//删除收货单
export const delReceiptApi = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/drawin/del', data: param })
}
//获取收货单最新ID
export const getReceiptNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/buy/drawin/new_num' })
}





//新增质检单
export const addQualityCheckApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/check/add', data: param })
}

//查询质检单列表
interface QualityCheckSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getQualityCheckListApi = (params:QualityCheckSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/check/list', params:restParams })
}

//查询单条质检单
export const getQualityCheckInfoApi = (params:QualityCheckSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/check/info', params:restParams })
}

//修改质检单
export const updateQualityCheckApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/buy/check/update', data: param })
}

//删除质检单
export const delQualityCheckApi = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/check/del', data: param })
}
//获取质检单最新ID
export const getQualityCheckNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/buy/check/new_num' })
}




//新增入库单
export const addPutinApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/putin/add', data: param })
}

//查询入库单列表
interface PutinSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getPutinListApi = (params:PutinSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/putin/list', params:restParams })
}

//查询单条入库单
export const getPutinInfoApi = (params:PutinSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/putin/info', params:restParams })
}

//修改入库单
export const updatePutinApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/buy/putin/update', data: param })
}

//删除入库单
export const delPutinApi = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/putin/del', data: param })
}
//获取入库单最新ID
export const getPutinNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/buy/putin/new_num' })
}


//新增质检单+入库单
export const addQualityCheckPutinApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/bridge/buy_check_add', data: param })
}
//修改质检+入库
export const updateQualityCheckPutinApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/erp/bridge/buy_check_update', data: param })
}






//新增出库单
export const addReturnApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/cancel/add', data: param })
}

//查询出库单列表
interface ReturnSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getReturnListApi = (params:ReturnSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/cancel/list', params:restParams })
}

//查询单条出库单
export const getReturnInfoApi = (params:PutinSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/cancel/info', params:restParams })
}

//修改出库单
export const updateReturnApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/buy/cancel/update', data: param })
}

//删除出库单
export const delReturnApi = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/cancel/del', data: param })
}
//获取出库单最新ID
export const getReturnNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/buy/cancel/new_num' })
}


//新增售前报价单
export const addPreSaleApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/offer/add', data: param })
}

//查询售前单列表
interface PreSaleSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getPreSaleListApi = (params:PreSaleSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/offer/list', params:restParams })
}

//查询售前出库单
export const getPreSaleInfoApi = (params:PreSaleSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/offer/info', params:restParams })
}

//修改售前单
export const updatePreSaleApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/sell/offer/update', data: param })
}

//删除售前单
export const delPreSaleApi = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/offer/del', data: param })
}
//获取售前单最新ID
export const getPreSaleNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/sell/offer/new_num' })
}

//查询售前单列表
interface PriceExcelParam{
    id?:number
}
//获取售前报价单
export const getPreSalePriceExcelApi = (params:PriceExcelParam): Promise<IResponse> => {
    return request.get({ url: '/sell/offer/save_excel',params })
}







//新增销售报价单
export const addSaleApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/order/add', data: param })
}

//查询销售单列表
interface SaleSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getSaleListApi = (params:SaleSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list', params:restParams })
}

//查询售前出库单
export const getSaleInfoApi = (params:SaleSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/info', params:restParams })
}

//修改销售单
export const updateSaleApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/sell/order/update', data: param })
}

//删除销售单
export const delSaleApi = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/order/del', data: param })
}
//获取销售单最新ID
export const getSaleNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/sell/order/new_num' })
}

//获取销售标识对应备货情况
interface SaleBakParam{
    sell_order_num?:string,
    pdt_biaoshi?:string,
}
export const getSaleBakInfoApi = (params): Promise<IResponse> => {
    return request.get({ url: '/sell/order/list_out', params:params })
}




//新增销售出库单
export const addSaleOutApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/takeout/add', data: param })
}

//查询销售出库单列表
interface SaleOutSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getSaleOutListApi = (params:SaleOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/takeout/list', params:restParams })
}

//查询销售出库单
export const getSaleOutInfoApi = (params:SaleOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/takeout/info', params:restParams })
}

//修改销售出库单
export const updateSaleOutApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/sell/takeout/update', data: param })
}

//删除销售出库单
export const delSaleOutApi = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/takeout/del', data: param })
}
//获取销售出库单最新ID
export const getSaleOutNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/sell/takeout/new_num' })
}








//新增销售退货单
export const addSaleReturnApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/cancel/add', data: param })
}

//查询销售退货单列表
interface SaleReturnSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getSaleReturnListApi = (params:SaleReturnSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/cancel/list', params:restParams })
}

//查询销售退货单
export const getSaleReturnInfoApi = (params:SaleOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/cancel/info', params:restParams })
}

//修改销售退货单
export const updateSaleReturnApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/sell/cancel/update', data: param })
}

//删除销售退货单
export const delSaleReturnApi = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/cancel/del', data: param })
}
//获取销售退货单最新ID
export const getSaleReturnNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/sell/cancel/new_num' })
}

//查询销售需求列表
interface SaleDemandSearchParam{
    page?: number,
    count?: number
}
export const getSaleDemandListApi = (params:SaleDemandSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_demand', params:restParams })
}
//销售需求详情
export const getSaleDemandInfoApi = (params:SaleDemandSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/info_demand', params:restParams })
}





//新增委外单
export const addOemOrderApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/order/add', data: param })
}

//查询委外单列表
interface OemOrderSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOemOrderListApi = (params:OemOrderSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/order/list', params:restParams })
}

//查询委外单
export const getOemOrderInfoApi = (params:OemOrderSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/order/info', params:restParams })
}

//修改委外单
export const updateOemOrderApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/order/update', data: param })
}

//删除委外单
export const delOemOrderApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/order/del', data: param })
}
//获取委外单最新ID
export const getOemOrderNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/order/new_num' })
}

//根据某个产品查询缺口
interface NeedSearchParam{
    id?:number,
}
export const getQuekouListApi = (params:NeedSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_quekou', params:restParams })
}



//查询委外物料需求列表
interface SaleDemandSearchParam{
    page?: number,
    count?: number
}
export const getOemDemandListApi = (params:SaleDemandSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/order/list_demand', params:restParams })
}
//委外物料需求详情
export const getOemDemandInfoApi = (params:SaleDemandSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/order/info_demand', params:restParams })
}

//物料需求查询根节点
export const getOemPrepareListApi = (params:SaleDemandSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/order/list_prepare', params:restParams })
}



//新增发料单
export const addOemTakeOutApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/takeout/add', data: param })
}

//查询发料单列表
interface OemTakeOutSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOemTakeOutListApi = (params:OemTakeOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/takeout/list', params:restParams })
}

//查询发料单
export const getOemTakeOutInfoApi = (params:OemTakeOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/takeout/info', params:restParams })
}

//修改发料单
export const updateOemTakeOutApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/takeout/update', data: param })
}

//删除发料单
export const delOemTakeOutApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/takeout/del', data: param })
}
//获取发料单最新ID
export const getOemTakeOutNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/takeout/new_num' })
}




//查询委外总库存列表
interface InventorySearchParam{
    page?: number,
    count?: number
}
export const getParterInventoryListApi = (params:InventorySearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/stock/list_parter', params:restParams })
}






//新增领料单
export const addOemTakeInApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/takein/add', data: param })
}

//查询领料单列表
interface OemTakeInSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOemTakeInListApi = (params:OemTakeInSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/takein/list', params:restParams })
}

//查询领料单
export const getOemTakeInInfoApi = (params:OemTakeOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/takein/info', params:restParams })
}

//修改领料单
export const updateOemTakeInApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/takein/update', data: param })
}

//删除领料单
export const delOemTakeInApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/takein/del', data: param })
}
//获取发料单最新ID
export const getOemTakeInNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/takein/new_num' })
}





//新增物料退回单
export const addOemTakeChannelApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/reback/add', data: param })
}

//查询物料退回单列表
interface OemTakeInSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOemTakeChannelListApi = (params:OemTakeInSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/reback/list', params:restParams })
}

//查询物料退回单
export const getOemTakeChannelInfoApi = (params:OemTakeOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/reback/info', params:restParams })
}

//修改物料退回单
export const updateOemTakeChannelApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/reback/update', data: param })
}

//删除物料退回单
export const delOemTakeChannelApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/reback/del', data: param })
}
//获取物料退回单最新ID
export const getOemTakeChannelNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/reback/new_num' })
}












//新增余料返回单
export const addOemRemainApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/remain/add', data: param })
}

//查询余料返回单列表
interface OemTakeInSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOemRemainListApi = (params:OemTakeInSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/remain/list', params:restParams })
}

//查询余料返回单
export const getOemRemainInfoApi = (params:OemTakeOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/remain/info', params:restParams })
}

//修改余料返回单
export const updateOemRemainApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/remain/update', data: param })
}

//删除余料返回单
export const delOemRemainApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/remain/del', data: param })
}
//获取余料返回单最新ID
export const getOemRemainNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/remain/new_num' })
}







//新增委外收货单
export const addOemDrawinApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/drawin/add', data: param })
}

//查询委外收货单列表
interface OemTakeInSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOemDrawinListApi = (params:OemTakeInSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/drawin/list', params:restParams })
}

//查询委外收货单
export const getOemDrawinInfoApi = (params:OemTakeOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/drawin/info', params:restParams })
}

//修改委外收货单
export const updateOemDrawinApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/drawin/update', data: param })
}

//删除委外收货单
export const delOemDrawinApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/drawin/del', data: param })
}
//获取委外收货单最新ID
export const getOemDrawinNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/drawin/new_num' })
}







//新增委外质检单
export const addOemQualityCheckApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/check/add', data: param })
}

//查询委外质检单列表
interface OemQualityCheckSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOemQualityCheckListApi = (params:OemTakeInSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/check/list', params:restParams })
}

//查询委外质检单
export const getOemQualityCheckInfoApi = (params:OemTakeOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/check/info', params:restParams })
}

//修改委外质检单
export const updateOemQualityCheckApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/check/update', data: param })
}

//删除委外质检单
export const delOemQualityCheckApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/check/del', data: param })
}
//获取委外质检单最新ID
export const getOemQualityCheckNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/check/new_num' })
}





//新增委外入库单
export const addOemPutinApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/putin/add', data: param })
}
//查询委外入库列表
interface OemPutinSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOemPutinListApi = (params:OemPutinSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/putin/list', params:restParams })
}

//查询委外入库
export const getOemPutinInfoApi = (params:OemTakeOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/putin/info', params:restParams })
}

//修改委外入库
export const updateOemPutinApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/putin/update', data: param })
}

//删除委入库
export const delOemPutinApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/putin/del', data: param })
}
//获取委入库最新ID
export const getOemPutinNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/putin/new_num' })
}




//新增委外退货单
export const addOemReturnApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/cancel/add', data: param })
}
//查询委外退货列表
interface OemPutinSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOemReturnListApi = (params:OemPutinSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/cancel/list', params:restParams })
}

//查询委外退货
export const getOemReturnInfoApi = (params:OemTakeOutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/cancel/info', params:restParams })
}

//修改委外退货
export const updateOemReturnApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/cancel/update', data: param })
}

//删除委退货
export const delOemReturnApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/cancel/del', data: param })
}
//获取委退货库最新ID
export const getOemReturnNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/cancel/new_num' })
}





//新增日志
export const addSysLogApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/log/add', data: param })
}
//查询日志
interface sysLogSearchParam{
    page?: number,
    count?: number
}
export const getSysLogApi = (params:sysLogSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/log/list', params:restParams })
}



//查询其他入库
interface OtherPutInSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOtherPutInListApi = (params:OtherPutInSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/hand/putin/list', params:restParams })
}
//删除
export const delOtherPutInApi = (param): Promise<IResponse> => {
    return request.post({ url: '/hand/putin/del', data: param })
}
//获取入库单最新ID
export const getOtherPutinNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/hand/putin/new_num' })
}
//新增入库单
export const addOtherPutinApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/hand/putin/add', data: param })
}
//修改入库单
export const updateOtherPutinApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/hand/putin/update', data: param })
}
export const getOtherPutinInfoApi = (params:OtherPutInSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/hand/putin/info', params:restParams })
}




//查询其他出库
interface OtherPutoutSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getOtherPutoutListApi = (params:OtherPutoutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/hand/putout/list', params:restParams })
}
//删除
export const delOtherPutoutApi = (param): Promise<IResponse> => {
    return request.post({ url: '/hand/putout/del', data: param })
}
//获取入库单最新ID
export const getOtherPutoutNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/hand/putout/new_num' })
}
//新增入库单
export const addOtherPutoutApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/hand/putout/add', data: param })
}
//修改入库单
export const updateOtherPutoutApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/hand/putout/update', data: param })
}
export const getOtherPutoutInfoApi = (params:OtherPutoutSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/hand/putout/info', params:restParams })
}




//查询调拨
interface StoneMoveSearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getStoneMoveListApi = (params:StoneMoveSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/hand/move/list', params:restParams })
}
//删除
export const delStoneMoveApi = (param): Promise<IResponse> => {
    return request.post({ url: '/hand/move/del', data: param })
}
//获取最新ID
export const getStoneMoveNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/hand/move/new_num' })
}
//新增
export const addStoneMoveApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/hand/move/add', data: param })
}
//修改
export const updateStoneMoveApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/hand/move/update', data: param })
}
export const getStoneMoveInfoApi = (params:StoneMoveSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/hand/move/info', params:restParams })
}


//物料需求查询根节点
export const getSellPrepareListApi = (params:SaleDemandSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/list_prepare', params:restParams })
}

//--------------------------------------
//销售月结客户出库列表
export const getPayBillListSellerApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/paybill/list_buyer', params:restParams })
}
//销售月结客户出库单明细
export const getPayBillListTakeOutApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/paybill/list_takeout', params:restParams })
}
//获取最新ID
export const getPayBillSellNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/sell/paybill/new_num' })
}
//新增
export const addPayBillSellApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/paybill/add', data: param })
}
//修改
export const updatePayBillSellApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/sell/paybill/update', data: param })
}
//删除
export const delPayBillSellApi = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/paybill/del', data: param })
}
export const getPayBillSellListApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/paybill/list', params:restParams })
}
export const getPayBillSellInfoApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/paybill/info', params:restParams })
}

//----------------------------------------------------
//采购月结客户出库列表
export const getPayBillListBuyerApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/paybill/list_supplier', params:restParams })
}
//采购月结客户收货单明细
export const getPayBillListDrawinApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/paybill/list_drawin', params:restParams })
}
//获取最新ID
export const getPayBillBuyNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/buy/paybill/new_num' })
}
//新增
export const addPayBillBuyApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/paybill/add', data: param })
}
//修改
export const updatePayBillBuyApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/buy/paybill/update', data: param })
}
//删除
export const delPayBillBuyApi = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/paybill/del', data: param })
}
export const getPayBillBuyListApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/paybill/list', params:restParams })
}
export const getPayBillBuyInfoApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/paybill/info', params:restParams })
}



//----------------------------------------------------
//委外月结客户出库列表
export const getPayBillListParterApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/paybill/list_parter', params:restParams })
}
//委外月结客户收货单明细
export const getOemPayBillListDrawinApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/paybill/list_drawin', params:restParams })
}
//获取最新ID
export const getPayBillOemNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/oem/paybill/new_num' })
}
//新增
export const addPayBillOemApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/paybill/add', data: param })
}
//修改
export const updatePayBillOemApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/oem/paybill/update', data: param })
}
//删除
export const delPayBillOemApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/paybill/del', data: param })
}
export const getPayBillOemListApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/paybill/list', params:restParams })
}
export const getPayBillOemInfoApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/paybill/info', params:restParams })
}



//新增质检单+入库单
export const addOemQualityCheckPutinApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/bridge/oem_check_add', data: param })
}
//修改质检+入库
export const updateOemQualityCheckPutinApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/erp/bridge/oem_check_update', data: param })
}











//委外月结客户出库列表
export const getPaymentApplicationListApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/book/payment/list', params:restParams })
}
//获取最新ID
export const getPaymentApplicationNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/book/payment/new_num' })
}
//新增
export const addPaymentApplicationApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/book/payment/add', data: param })
}
//修改
export const updatePaymentApplicationApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/book/payment/update', data: param })
}
//删除
export const delPaymentApplicationApi = (param): Promise<IResponse> => {
    return request.post({ url: '/book/payment/del', data: param })
}
export const getPaymentApplicationInfoApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/book/payment/info', params:restParams })
}







//----------------库存盘点---------------------------------------------------
export const getStoreCheckListApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/pdt/check/list', params:restParams })
}
//获取最新ID
export const getStoreCheckNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/pdt/check/new_num' })
}
//新增
export const addStoreCheckApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/pdt/check/add', data: param })
}
//修改
export const updateStoreCheckApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/pdt/check/update', data: param })
}
//删除
export const delStoreCheckApi = (param): Promise<IResponse> => {
    return request.post({ url: '/pdt/check/del', data: param })
}
export const getStoreCheckInfoApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/pdt/check/info', params:restParams })
}
//查询盘点单产品列表
export const getStoreCheckPdtListApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/pdt/check/list_pdt_all', params:restParams })
}
//修改盘点单产品盘点数据
export const updateStoreCheckPdtApi = (param): Promise<IResponse> => {
    return request.post({ url: '/pdt/check/update_pdt_one', data: param })
}










//修改采购收货单pdt属性
export const updateBuyDrawinPdtApi = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/drawin/update_pdt', data: param })
}
//修改委外收货单pdt属性
export const updateOemDrawinPdtApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/drawin/update_pdt', data: param })
}
//修改销售发货单pdt属性
export const updateSellTakeoutPdtApi = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/takeout/update_pdt', data: param })
}


//修改采购收货单pdt属性
export const updateBuyCancelPdtApi = (param): Promise<IResponse> => {
    return request.post({ url: '/buy/cancel/update_pdt', data: param })
}
//修改委外收货单pdt属性
export const updateOemCancelPdtApi = (param): Promise<IResponse> => {
    return request.post({ url: '/oem/cancel/update_pdt', data: param })
}
//修改销售发货单pdt属性
export const updateSellCancelPdtApi = (param): Promise<IResponse> => {
    return request.post({ url: '/sell/cancel/update_pdt', data: param })
}
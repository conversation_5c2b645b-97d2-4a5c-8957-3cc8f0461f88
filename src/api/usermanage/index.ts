//部门用户管理相关API
import request from '@/config/axios'

//新增部门
export const addDepApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/dept/add', data: param })
}

//修改部门
export const modifyDepApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/dept/update', data: param })
}

//删除部门
export const delDepApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/dept/del', data: param })
}

//查询部门列表
interface DepSearchParam{
    page?: number,
    count?: number
}
export const getDepartmentListApi = (params:DepSearchParam): Promise<IResponse> => {
    return request.get({ url: '/erp/dept/list', params })
}


//新增用户
export const addUserApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/user/add', data: param })
}

//修改用户
export const updateUserApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/user/update', data: param })
}


//删除用户
export const delUserApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/user/del', data: param })
}

//查询用户列表
interface UserSearchParam {
    username?:string,
    resident_name?:string,
    dept?:string,
    role?:string,
    job?:string,
    emp_status?:string,
    page: number,
    count: number
}
export const getUserListApi = (params: UserSearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/erp/user/list',params })
}


//新增角色
export const addRoleApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/role/add', data: param })
}

//修改角色
export const updateRoleApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/role/update', data: param })
}

//删除角色
export const delRoleApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/role/del', data: param })
}

//查询角色
interface UserSearchParam {
    ids?:Array<number>,
    name?:string,
    page: number,
    count: number
}
export const getRoleListApi = (params: UserSearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/erp/role/list',params })
}





//修改用户登录状态 
export const setCanlogin  = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/user/update', data: param })
}




//新增职务
export const addJobApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/job/add', data: param })
}

//修改职务
export const updateJobApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/job/update', data: param })
}

//删除职务
export const delJobApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/job/del', data: param })
}

//查询职务
interface UserSearchParam {
    ids?:Array<number>,
    name?:string,
    page: number,
    count: number
}
export const getJobListApi = (params: UserSearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/erp/job/list',params })
}

//--------------------------------------------
//新增合同
export const addLcmApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/pact/add', data: param })
}

//修改合同
export const updateLcmApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/erp/pact/update', data: param })
}

//删除合同
export const delLcmApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/pact/del', data: param })
}

//查询合同
interface SearchParam {
    ids?:Array<number>,
    name?:string,
    page: number,
    count: number
}
export const getLcmListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/erp/pact/list',params })
}

//--------------------------------------
//新增请假
export const addHolidayApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/holiday/add', data: param })
}

//修改请假
export const updateHolidayApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/erp/holiday/update', data: param })
}

//删除请假
export const delHolidayApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/holiday/del', data: param })
}

//查询请假
interface SearchParam {
    ids?:Array<number>,
    name?:string,
    page: number,
    count: number
}
export const getHolidayListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/erp/holiday/list',params })
}
//获取最新ID
export const getHolidayNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/erp/holiday/new_num' })
}
export const getHolidayInfoApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/holiday/info', params:restParams })
}

//查询请假(按人头)
interface SearchParam {
    ids?:Array<number>,
    name?:string,
    page: number,
    count: number
}
export const getHolidayUserListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/erp/holiday/list_user',params })
}

//查询年休假
interface SearchParam {
    ids?:Array<number>,
    name?:string,
    page: number,
    count: number
}
export const getYearHolidayUserListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/erp/holiday/list_holiday_by_user',params })
}

//-------------------------------------
//新增假期
export const addHolidayTypeApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/holiday_type/add', data: param })
}

//修改假期
export const updateHolidayTypeApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/holiday_type/update', data: param })
}

//删除假期
export const delHolidayTypeApi = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/holiday_type/del', data: param })
}

//查询假期
interface SearchParam {
    ids?:Array<number>,
    name?:string,
    page: number,
    count: number
}
export const getHolidayTypeListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/erp/holiday_type/list',params })
}


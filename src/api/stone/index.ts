import request from '@/config/axios'


//新增领料单
export const addBookTakeinApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/book/takein/add', data: param })
}

//查询单列表
interface SearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getBookTakeinListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/book/takein/list', params:restParams })
}

//查询
export const getBookTakeinInfoApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/book/takein/info', params:restParams })
}

//修改
export const updateBookTakeinApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/book/takein/update', data: param })
}

//删除
export const delBookTakeinApi = (param): Promise<IResponse> => {
    return request.post({ url: '/book/takein/del', data: param })
}
//获取最新ID
export const getBookTakeinNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/book/takein/new_num' })
}

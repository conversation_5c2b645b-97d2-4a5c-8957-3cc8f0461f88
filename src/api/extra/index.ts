//其他服务器接口
import request from '@/config/axios'


//发起服务器通知
export const notifyMsg = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/home/<USER>', data: param })
}

//置顶记录接口
export const setHotPoint = (param): Promise<IResponse> => {
    return request.post({ url: '/erp/user/hotpoint', data: param })
}





//hash查询写入
interface SearchParam{
    page?: number,
    count?: number
}
export const getHashApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/user/memo/info', params:restParams })
}

export const getHashListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/user/memo/list', params:restParams })
}
export const setHashApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/memo/set', data: param })
}
export const delHashApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/memo/del', data: param })
}

export const getHashSysListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sys/memo/list', params:restParams })
}
export const setHashSyspi = (params): Promise<IResponse> => {
    return request.post({ url: '/sys/memo/set', data: params })
}
export const getHashSysInfoApi = (params): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sys/memo/info', params:restParams })
}



















//质检数据导出
export const exportCheckListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/check/export', params:restParams })
}

//委外质检导出
export const exportOemCheckListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/check/export', params:restParams })
}

//供应商导入
export const importSupplierListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/supplier/import_excel', params:restParams })
}

//仓库月报表导出
export const exportStoneReportMonthListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/stock/export_stat', params:restParams })
}

//仓库查询导出
export const exportStoneListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/stock/export_all', params:restParams })
}

//PDT查询导出
export const exportPdtListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/pdt/export', params:restParams })
}

//采购订单查询导出
export const exportBuyListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/order/export', params:restParams })
}

//委外查询导出
export const exportOemListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/order/export', params:restParams })
}

//销售查询导出
export const exportSellListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/order/export', params:restParams })
}

//销售月结查询导出
export const exportSellReportMonthApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/paybill/export', params:restParams })
}

//采购月结查询导出
export const exportBuyReportMonthApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/paybill/export', params:restParams })
}

//委外月结查询导出
export const exportOemReportMonthApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/paybill/export', params:restParams })
}

//采购收货单查询导出
export const exportBuyReceiptListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/drawin/export', params:restParams })
}


//销售发货单查询导出
export const exportSellOutListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/takeout/export', params:restParams })
}

//委外收货单查询导出
export const exportOemReceiptListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/drawin/export', params:restParams })
}

//采购入库查询导出
export const exportBuyPutinListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/putin/export', params:restParams })
}

//委外入库查询导出
export const exportOemPutinListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/putin/export', params:restParams })
}



//付款申请导出
export const exportPaymentApplicationApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/payment/export', params:restParams })
}




//采购月结查询导出单条
export const exportBuyReportMonthInfoApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/buy/paybill/export_pdt', params:restParams })
}

//委外月结查询导出单条
export const exportOemReportMonthInfoApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/oem/paybill/export_pdt', params:restParams })
}

//销售月结查询导出单条
export const exportSellReportMonthInfoApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/sell/paybill/export_pdt', params:restParams })
}



//BOM完整结构导出
export const exportBomListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/erp/bom/export', params:restParams })
}






//查询奖池
interface SearchParam{
    id?:number,
    page?: number,
    count?: number
}
export const getJiangchiListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/gambiling/game/list', params:restParams })
}

//修改
export const updateJiangchiPoolApi = (param): Promise<IResponse> => {
    return request.post({ url: '/gambiling/game/update_pool', data: param })
}
export const updateJiangchiChangeApi = (param): Promise<IResponse> => {
    return request.post({ url: '/gambiling/game/update_chance', data: param })
}


export const getFishListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/fish/control/pool/list', params:restParams })
}
//修改基础数据
export const updateFishPoolApi = (param): Promise<IResponse> => {
    return request.post({ url: '/fish/control/pool/update_pool', data: param })
}
//修改难度
export const updateFishChanceApi = (param): Promise<IResponse> => {
    return request.post({ url: '/fish/control/pool/update_chance', data: param })
}
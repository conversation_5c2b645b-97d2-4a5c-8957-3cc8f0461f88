import request from '@/config/axios'


//新增分类
export const addPrintTmpApi  = (param): Promise<IResponse> => {
    return request.post({ url: '/cfg/print/add', data: param })
}

//修改分类
export const updatePrintTmpApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cfg/print/update', data: param })
}

//删除部分类
export const delPrintTmpApi = (param): Promise<IResponse> => {
    return request.post({ url: '/cfg/print/del', data: param })
}

//查询分类树
interface PrintTmpSearchParam{
    id?:number,
    type?:string,
    page?: number,
    count?: number
}
export const getPrintTmpListApi = (params:PrintTmpSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/cfg/print/list', params:restParams })
}

export const getPrintTmpInfoApi = (params:PrintTmpSearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/cfg/print/info', params:restParams })
}

//获取编号
export const getPrintTmpNewnumApi = (): Promise<IResponse> => {
    return request.get({ url: '/cfg/print/new_num' })
}

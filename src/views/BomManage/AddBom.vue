<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive, nextTick } from 'vue'
import { ElTable, ElTooltip, ElTableColumn, ElButton, ElForm, ElFormItem, FormRules, ElDescriptions, ElDescriptionsItem, ElInput, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import { getCategListApi, addBomApi, getProductListApi, getBomNewnumApi, updateBomApi, getBomInfoApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount, watch } from 'vue'
import { DialogSelCustomer } from '@/components/DialogSelCustomer'
import { DialogProductSel } from '@/components/DialogProductSel'
import { checkFormRule, closeOneTagByName, closeOneTagByPath, getGUID } from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
// import { Editor } from '@/components/Editor'

const { currentRoute, back,push } = useRouter()
const { t } = useI18n()

//标题
const title = ref(t('project_manage.bom_set'))
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    name: [{ required: true, message: t('msg.noBomID'), trigger: 'blur' }],
    pdt_name: [{ required: true, message: t('msg.noPdtName'), trigger: 'blur' }],
    pdt_nick: [{ required: true, message: t('msg.noPdtNick'), trigger: 'blur' }],
})

//产品分类Prop属性
const categProps = {
    multiple: true,
    checkStrictly: true,
    value: 'id',
    children: 'sub_categ',
    label: 'name',
    emitPath: false
}

//BOM数据
const bomData = reactive({
    name: "",
    pdt_id: "",
    pdt_name: "",
    pdt_nick: "",
    buyer_id: "",
    buyer_name: '',
    buyer_nick: '',
    item_list: [],  //any,
    remark: ""

})

//当前操作工序
const nCurSelItem = ref({})
//查询产品绑定的查询结果
const searchPdtData = reactive<any>([])
//选中某一个产品
const handleCurrentSelectPdt = (row: any, item, fource = false) => {
    if (item == undefined) {
        return
    }
    console.log('设置当前:', nCurSelPdtID.value)
    nCurSelPdtID.value = row.id
    nCurSelItem.value = item
    if (!fource) {
        return
    }
    item.extra_input = ''

    // setTimeout(() => {
    //     const inputElement = document.querySelector('.input-search input');
    //     inputElement?.focus()
    //     onClickSearchPdt()
    // },500)





    console.log(row, item)
    item.pdt_list.push({
        id: row.id,
        name: row.name,
        nick: row.nick,
        specs_name: row.specs_name,
        specs_text: row.specs_text,
        base_unit: row.base_unit,
        损耗率: 0,
        单价: row.单价,
        用量: 1,
        总价: 0,
        排序: 0,
        供应商: '',
        remark: '',
        sell_buy_digit: row.sell_buy_digit,
        标识: getGUID(20),
        locked_list: [],
        税前赔付单价: 0,
        税后赔付单价: 0
    })


    //隐藏所有浮动选择框
    bomData.item_list.forEach(item => {
        item.showDlg = false
        item.extra_input = ''
    })

}

watch(bomData, (data) => {
    console.log('更新')
    data.item_list.forEach((process) => {
        process.pdt_list.forEach((pdt) => {
            pdt.总价 = ((pdt.单价 + pdt.单价 * (pdt.损耗率 / 100)) * pdt.用量).toFixed(pdt.sell_buy_digit);
        })
    });
}, { deep: true })


//获取最新bomid
const onChangeID = async () => {
    const ret = await getBomNewnumApi()
    if (ret) {
        console.log(ret)
        bomData.name = ret.data.new_id
    }
}


//输入发生变化时处理,要支持延时处理
const delaytime = ref<NodeJS.Timeout | null>(null);
const onItemInput = (val, item) => {
    item.showDlg = true
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)

    delaytime.value = setTimeout(() => {
        // 输入停止后触发的方法
        getProductList(val)
    }, 200)
}


//查询产品列表根据编号
const getProductList = async (val = '') => {
    const ret = await getProductListApi({
        name: val,
        nick: val,
        prop_list: ['物料'],
        status: '正常',
        _or: true,
        page: 1,
        count: 10,
    })
    if (ret) {
        searchPdtData.splice(0, searchPdtData.length, ...ret.data)
        if (searchPdtData.length > 0) {
            setCurrent(searchPdtData[0])
        }
    }
}

//显示选中工序窗口
const showSelProcess = ref(false)
//添加一个工序
const onAddProcess = () => {
    showSelProcess.value = true
}
//已选择工序回调
const onSelProcessCallback = (value, array) => {
    array.forEach((item) => {
        //增加产品数组
        item.pdt_list = []
        //追加额外属性
        item.质检方式 = '无需质检' //质检方式
        item.工序标识 = '标准'     //工序标识
        item.showDlg = false  //是否显示弹窗
        item.extra_input = '' //额外输入
        item.expand = true //展开折叠
    })

    //追加选择的工序到BOM列表
    bomData.item_list.push(...array)
    console.log(bomData)
}

//删除工序产品列表中某一个产品
const onDeleteOnePdt = (item, pdt) => {
    // 在这里实现删除操作
    const index = item.pdt_list.indexOf(pdt);
    if (index !== -1) {
        item.pdt_list.splice(index, 1); // 使用splice方法删除元素
    }
}

//删除某一个工序
const onDeleteOneProcess = (item, index) => {
    // 在这里实现删除操作
    bomData.item_list.splice(index, 1); // 使用splice方法删除元素
}


//显示隐藏工序编辑窗口
const showProcessEditDialog = ref(false)
//当前要编辑的工序内容
const curOptProcessData = ref({})
//显示工序编辑窗口
const onShowProcessModify = (item, index) => {
    showProcessEditDialog.value = true
    // Object.assign(curOptProcessData,item)
    curOptProcessData.value = item
    curOptProcessData.value.callback_index = index
    console.log(curOptProcessData)
}
//更新弹窗已经修改过的工序内容
const onUpdateSelProcessData = () => {
    console.log('-------------', curOptProcessData.value)
    //更新工序内容
    bomData.item_list[curOptProcessData.value.callback_index] = curOptProcessData.value
}

onMounted(async () => {
    document.addEventListener('click', onDocumentClick, false)
    // getCategList()    
    if (currentRoute.value.query.id == '') {


        //复制BOM
        if (currentRoute.value.query.copy_id != undefined && currentRoute.value.query.copy_id != '') {
            //查询产品信息 
            const ret = await getBomInfoApi({
                id: currentRoute.value.query.copy_id,
                page: 1,
                count: 100
            })
            if (ret) {
                console.log(ret)
                Object.assign(bomData, ret.data)
                bomData.item_list = ret.data.item_list;
                bomData.item_list.forEach((item, index) => {
                    if (item.expand == undefined) {
                        item.expand = true
                    }
                    if (item.showDlg == undefined) {
                        item.showDlg = false
                    }
                })
                //清除产品信息
                onChangeID()
                bomData.id = undefined
                bomData.name = ""
                bomData.pdt_id = ''
                bomData.pdt_name = ''
                bomData.pdt_nick = ''
                //更新bom中产品标识ID
                bomData.item_list.forEach((item, index) => {
                    item.pdt_list.forEach((item2, index2) => {
                        item2.标识 = getGUID(20)
                    })
                })

                console.log('新bom', bomData)
            }
        }
        else {
            onChangeID()
        }
    }
    else {

        if (currentRoute.value.query.type == 'info') {
            title.value = '查看BOM'
        }
        else {
            title.value = '修改BOM'
        }


        console.log('1111111111111111')
        //查询产品信息 
        const ret = await getBomInfoApi({
            id: currentRoute.value.query.id,
            page: 1,
            count: 100
        })
        if (ret) {
            console.log(ret)
            // Object.assign(bomData,ret.data)
            Object.assign(bomData, ret.data)
            bomData.item_list = ret.data.item_list;
            bomData.item_list.forEach((item, index) => {
                if (item.expand == undefined) {
                    item.expand = true
                }
                if (item.showDlg == undefined) {
                    item.showDlg = false
                }
            })
            console.log('---', bomData.item_list)
        }
        nextTick(() => {
            if (currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if (excludeDiv != null) {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }


})

//unmounted的时候移除监听
onBeforeUnmount(() => {
    document.removeEventListener('click', onDocumentClick, false)
})

const onDocumentClick = (event) => {
    // 检查点击事件的目标元素是否位于任何一个 el-table 外部
    const elTableContainers = document.querySelectorAll('.el-table-container');

    let clickedInsideTable = false;
    for (const container of elTableContainers) {
        if (container.contains(event.target)) {
            // 点击的元素在 el-table 容器内部
            clickedInsideTable = true;
            break;
        }
    }
    if (!clickedInsideTable && !(event.target as HTMLElement).classList.contains('el-input__inner')) {
        // 点击的元素不在任何一个 el-table 容器内部
        //关闭修改bomData.item_list中所有showDlg为false
        bomData.item_list.forEach(item => {
            item.showDlg = false
        })
    }
}

//显示隐藏选择客户弹窗
const showSelCustomerDlg = ref(false)
//选择BOM关联客户
const onSelCustomer = () => {
    showSelCustomerDlg.value = true
}
//选择客户回调
const onSelCustomerCallback = (id, name, nick) => {
    console.log(id, name, nick)
    bomData.buyer_id = id
    bomData.buyer_name = name
    bomData.buyer_nick = nick
}

//产品分类数据源
const categData = reactive([])
//查询产品分类树
const getCategList = async () => {
    const ret = await getCategListApi({
        page: 1,
        count: 10000
    })
    if (ret) {
        console.log(ret)
        categData.splice(0, categData.length, ret.data.all_categs)
    }
}

//显示隐藏产品弹窗
const showSelProductDlg = ref(false)
//显示选择产品对话框
const onSelProduct = () => {
    showSelProductDlg.value = true
}
//选择产品完毕回调
const onSelProductCallback = (pdt) => {
    if (pdt.bom_id != '') {
        ElMessage.error(t('msg.exsitBom'))
        return
    }
    bomData.pdt_id = pdt.id
    bomData.pdt_name = pdt.name
    bomData.pdt_nick = pdt.nick
    bomData.categ = pdt.categ
    bomData.categ_name = pdt.categ_name

    console.log(bomData)
}

//显示隐藏选择负责人窗口变量
const showSelUserDlg = ref(false)
//显示选择负责人弹窗
const onSelManager = () => {
    showSelUserDlg.value = true
}
//选择负责人回调
const onSelManagerCallback = (id, name) => {
    console.log(id, name)
    bomData.mainer = id
    bomData.mainer_name = name
}

//保存BOM
const onSave = async (type) => {
    console.log(bomData)
    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        ElMessage.warning(t('msg.checkRule'))
        return
    }
    bomData.status = type

    if (bomData.id == undefined) {
        const ret = await addBomApi(bomData)
        if (ret) {
            ElMessage.success(t('msg.newBomSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        const ret = await updateBomApi(bomData)
        if (ret) {
            ElMessage.success(t('msg.updateBomSuccess'))
            // baskFront()
            closeOneTagByName(currentRoute.value.meta.title)
            push({
                path: '/bommanage/addbom',
                query: {
                    id: bomData.id,
                    type: 'info'
                }
            })
        }
    }

}


//返回上一页
const baskFront = () => {
    closeOneTagByPath('/bommanage/bommanage')
    back()
    closeOneTagByName(currentRoute.value.meta.title)
    //刷新上一页
    // closeOneTagByPath('/bommanage/bommanage')
}

const baskFrontNoFreshFront = () => {
    if(currentRoute.value.query.type == 'info')
    {
        closeOneTagByPath('/bommanage/bommanageex')
        push({
                path: '/bommanage/bommanageex',
                query: {
                    id: bomData.id,
                    type: 'info'
                }
            })
    }
    else    
    {
        back()
        closeOneTagByName(currentRoute.value.meta.title)
    }

}



//键盘上下只切换不选择
const nCurSelPdtID = ref('')
const bKeyDown = ref(true)
const onKeyDownOnePdt = (event) => {
    if (event.keyCode === 38 || event.keyCode === 40) {
        // 阻止默认行为，以保持光标位置不变
        event.preventDefault();
    }
    //esc按键关闭table
    if (event.keyCode === 27) {
        showSelProductDlg.value = false
        return
    }
    bKeyDown.value = true
    if (nCurSelPdtID.value == '') {
        setCurrent(searchPdtData[0])
    }
    else {


        for (let i = 0; i < searchPdtData.length; i++) {
            if (searchPdtData[i].id == nCurSelPdtID.value) {
                if (event.keyCode === 38 && i > 0)
                    setCurrent(searchPdtData[i - 1])
                else if (event.keyCode === 40 && i < searchPdtData.length - 1)
                    setCurrent(searchPdtData[i + 1])
                //如果是回车，直接选择
                else if (event.keyCode === 13) {
                    onRowClick(searchPdtData[i])
                    return
                }
                return
            }
        }
    }

}
const searchRef = ref<InstanceType<typeof ElTable>>()

const setCurrent = (row?) => {
    if (row == undefined)
        nCurSelPdtID.value = ''
    console.log('setCurrent', searchRef)
    searchRef.value![0].setCurrentRow(row)
}
const onRowClick = (row) => {
    console.log('xuanle', row)
    console.log('item', nCurSelItem.value)
    console.log('bom', bomData)

    //检查所有物料是否重复
    for (let item of bomData.item_list) {
        if (item.pdt_list.findIndex(item => item.id == row.id) != -1) {
            ElMessage({
                message: '['+row.nick +'] 该物料已存在，不能重复添加',
                type: 'error',
            })
            return
        }
    }


    handleCurrentSelectPdt(row, nCurSelItem.value, true)
}
</script>

<template>
    <ContentDetailWrap :title="title" @back="baskFrontNoFreshFront()">
        <template #left>
            <ElButton v-if="currentRoute.query.type != 'info'" type="success" @click="onAddProcess">
                {{ t('project_manage.add_process') }}
            </ElButton>
        </template>
        <template #right>
            <ElButton v-if="currentRoute.query.type != 'info'" color="#799EFF" style="color: white;" @click="onSave('草稿')">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') + '草稿' }}
            </ElButton>
            <ElButton v-if="currentRoute.query.type != 'info'" native-type="submit" color="#409EFF" style="color: white;" @click="onSave('启用')">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <el-form :rules="rules" :model="bomData" ref="ruleFormRef">
            <div class="flex">
                <el-descriptions class="flex-1 mt-2" :column="2" border>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                        :label="t('bom.id')" class="flex">
                        <el-form-item prop="name">
                            <!-- <div class="w-[100%] relative">
                                <el-input style="width: 80%;margin-right: 10px;"  v-model="bomData.name"  />
                                <ElButton v-if="bomData.id==undefined" type="warning" class=" absolute top-0 right-1" @click="onChangeID">{{ t('button.update') }}</ElButton>
                            </div>                             -->
                            <div class="flex">
                                <el-input v-model="bomData.name" :disabled="bomData.id != undefined" />
                                <ElButton class="ml-2" v-if="bomData.id == undefined" type="primary" @click="onChangeID">
                                    <Icon class="mr-0.5" icon="radix-icons:update" />
                                    {{ t('button.update') }}
                                </ElButton>
                                <el-input class="ml-2" v-model="bomData.version" placeholder="版本号" />
                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle"
                        :label="t('bom.custom')" class="flex">
                        <el-form-item>
                            <div class="mr-2">{{ bomData.buyer_name }}</div>
                            <ElButton @click="onSelCustomer">
                                <Icon icon="iconamoon:search-bold" />
                            </ElButton>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                        :label="t('product_manage.id')" class="flex">
                        <el-form-item prop="pdt_name">
                            <div class="mr-2">{{ bomData.pdt_name }}</div>
                            <ElButton @click="onSelProduct">
                                <Icon icon="iconamoon:search-bold" />
                            </ElButton>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                        :label="t('product_manage.name')" class="flex">
                        <el-form-item prop="pdt_nick">
                            <div class="w-[100%] relative">
                                {{ bomData.pdt_nick }}
                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                        :label="t('product_manage.categ')" class="flex">
                        <el-form-item prop="name">
                            <div class="w-[100%] relative">
                                {{ bomData.categ_name }}
                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle"
                        :label="t('process.manager')">
                        <el-form-item>
                            <div class="mr-2">{{ bomData.mainer_name }}</div>
                            <ElButton @click="onSelManager">
                                <Icon icon="iconamoon:search-bold" />
                            </ElButton>
                        </el-form-item>
                    </el-descriptions-item>

                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle"
                        :label="t('process.remark')" class="flex">
                        <el-form-item>
                            <div class="w-[100%] relative">
                                <el-input v-model="bomData.remark" :autosize="{ minRows: 2, maxRows: 2 }"
                                    type="textarea" />
                                <!-- <Editor height="200px" v-model="bomData.remark" ref="editorRef" @change="changeHtml" /> -->
                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                </el-descriptions>
                <!-- <div class="flex items-start w-[100px] h-[140px]">
                    <el-image class="xiangpian" src="/nopic.jpg" />
                </div> -->
            </div>
        </el-form>
        <!-- 工艺列表     -->
        <div class="scrollable-div">
            <!-- 表头 -->
            <div class="flex header headerBk" style="color: white;">
                <div class="w-[5%] !1min-w-[50px]">{{ t('userTable.id') }}</div>
                <div class="w-[15%] !1min-w-[150px]">{{ t('product_manage.id') }}</div>
                <div class="w-[15%] !1min-w-[150px]">{{ t('product_manage.name') }}</div>
                <div class="w-[15%] !1min-w-[100px]">规格</div>
                <div class="w-[10%] !1min-w-[100px]">{{ t('bom.danwei') }}</div>
                <div class="w-[5%] !1min-w-[80px]">{{ t('bom.cust') }}</div>
                <div class="w-[10%] !1min-w-[120px]">{{ t('bom.price') }}</div>
                <div class="w-[10%] !1min-w-[150px]">{{ t('bom.num') }}</div>
                <div class="w-[10%] !1min-w-[120px]">{{ t('bom.totle_price') }}</div>
                <!-- <div class="w-[10%] !min-w-[80px]">{{ t('process.sort') }}</div> -->
                <div class="w-[10%] !1min-w-[80px]">{{ t('bom.supporter') }}</div>
                <div class="w-[10%] !1min-w-[80px]">{{ t('process.remark') }}</div>
                <div class="w-[10%] !1min-w-[80px]">{{ t('formDemo.operate') }}</div>
            </div>
            <!-- 表内容 -->
            <div class="mt-2" v-for="(item, index) in bomData.item_list" :key="index">
                <!-- 内容头 -->
                <div>
                    <div class=" cursor-pointer bg-light-800 p-2 flex flex-nowrap text-[13px] items-center border border-bottom-0 border-[#c6c6c6] p-1"
                        @click="onShowProcessModify(item, index)">
                        <ElButton v-if="currentRoute.query.type != 'info'" plain class="mr-3" type="danger" size="small"
                            @click.stop="onDeleteOneProcess(item, index)">
                            <Icon icon="material-symbols:delete-outline" />
                        </ElButton>
                        <div class="1min-w-[150px]">工序: {{ item.nick }}</div>
                        <div class="1min-w-[100px]">工时/(秒): {{ item.work_hours }}</div>
                        <div class="1min-w-[100px]">人工费: {{ item.job_fee }}</div>
                        <div class="1min-w-[250px] pr-10">(计件:{{ item.piece_fee }}元/件 计时:{{ item.time_fee }}元/小时)</div>
                        <div class="1min-w-[100px]">分摊系数: {{ item.share_rate }}</div>
                        <div class="1min-w-[120px]">质检方式: {{ item.质检方式 }}</div>
                        <div class="w-[100px] text-overflow-ellipsis overflow-hidden">备注: {{ item.remark }}</div>
                        <div class="w-[100px] text-overflow-ellipsis overflow-hidden">工艺: {{ item.desc_txt }}</div>
                        <!-- <div class="min-w-[100px]">排序: {{ item.sort_num }}</div> -->
                        <div class="1min-w-[100px]">工艺标识: {{ item.工序标识 }}</div>

                        <!-- 靠右的其他信息 -->
                        <div class="ml-auto flex justify-center items-center">
                            <ElButton v-if="item.expand" size="small" @click.stop="item.expand = false">折叠</ElButton>
                            <ElButton v-if="!item.expand" size="small" @click.stop="item.expand = true">展开</ElButton>
                        </div>
                    </div>
                </div>
                <!-- 内容体 -->
                <div v-show="item.expand == undefined ? true : item.expand" class="test flex content items-center"
                    v-for="(pdt, index) in item.pdt_list" :key="index">
                    <div class="w-[5%] table_item !1min-w-[50px]">{{ index }}</div>
                    <div class="w-[15%] table_item !1min-w-[150px] text-left">
                        <el-tooltip class="box-item" effect="dark" :content="pdt.name" placement="bottom">
                            {{ pdt.name }}
                        </el-tooltip>
                    </div>
                    <div class="w-[15%] table_item !1min-w-[150px]">
                        <!-- <el-tooltip
                            class="box-item"
                            effect="dark"
                            :content="pdt.nick"
                            placement="bottom"
                        >
                        {{ pdt.nick }} 
                        </el-tooltip>   -->
                        <div class="whitespace-normal">{{ pdt.nick }}</div>
                    </div>
                    <div class="w-[15%] table_item !1min-w-[100px]">
                        <el-tooltip class="box-item" effect="dark"
                            :content="pdt.specs_name == '自定义规格' ? pdt.specs_text : pdt.specs_name" placement="bottom">
                            {{ pdt.specs_name == '自定义规格' ? pdt.specs_text : pdt.specs_name }}
                        </el-tooltip>
                    </div>
                    <div class="w-[10%] table_item !1min-w-[100px]"> {{ pdt.base_unit }} </div>
                    <div class="w-[5%] table_item !1min-w-[80px]">
                        <el-input v-model="pdt.损耗率" />
                    </div>
                    <div class="w-[10%] table_item !1min-w-[120px]"> {{ pdt.单价 }}</div>
                    <div class="w-[10%] table_item !1min-w-[150px]">
                        <el-input v-model="pdt.用量" />
                    </div>
                    <div class="w-[10%] table_item !1min-w-[120px]"> {{ pdt.总价 }}</div>
                    <!-- <div class="w-[10%] table_item !min-w-[80px]">
                        <el-input  v-model="pdt.排序"/>
                    </div> -->
                    <div class="w-[10%] table_item !1min-w-[80px]">{{ pdt.供应商 }}</div>
                    <div class="w-[10%] table_item !1min-w-[80px]">
                        <el-input v-model="pdt.remark" name="bz" autocomplete="on" />
                    </div>
                    <div class="w-[10%] table_item !1min-w-[80px]">
                        <ElButton v-if="currentRoute.query.type != 'info'" type="danger" size="small" @click="onDeleteOnePdt(item, pdt)">
                            <Icon icon="material-symbols:delete-outline" />
                        </ElButton>
                    </div>
                </div>
                <div v-show="item.expand == undefined ? true : item.expand" class="flex content items-center">
                    <div class="w-[5%] !1min-w-[50px]">+</div>
                    <div class="w-[15%] !1min-w-[150px]">
                        <el-input @keydown='onKeyDownOnePdt' @keyup='bKeyDown = false' v-model="item.extra_input"
                            @click="() => { item.showDlg = true; getProductList(item.extra_input) }" @blur="() => { }"
                            @input="onItemInput($event, item)" />
                    </div>

                </div>
                <div v-if="item.showDlg == undefined ? false : item.showDlg"
                    class=" bg-light-600 w-[80%] max-h-[400px] absolute z-99 p-2 shadow el-table-container">
                    <el-table ref="searchRef" :data="searchPdtData" style="width: 100%;" row-key="guuid" border
                        highlight-current-row @current-change="handleCurrentSelectPdt($event, item, false)"
                        header-cell-class-name="tableHeader" height="300" @row-click='onRowClick'>
                        <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')"
                            width="150" />
                        <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')"
                            width="600" />
                            <el-table-column show-overflow-tooltip  prop="specs_text"
                            :label="t('product_manage.specify_info')" />
                    </el-table>
                </div>
            </div>


            <div class="h-[800px]"> </div>
        </div>
        <!-- <div class="mb-80"></div> -->
        <!-- 选择工序弹窗 -->
        <DialogProcessSel v-model:show="showSelProcess" :title="t('bom.sel_process')"
            @on-submit="onSelProcessCallback" />

        <!-- 修改工序弹窗 -->
        <DialogProcess :bReturnData="true" v-model:show="showProcessEditDialog"
            :title="t('project_manage.process_config')" v-model:data="curOptProcessData"
            @on-submit="onUpdateSelProcessData" />

        <!-- 选择客户弹窗 -->
        <DialogSelCustomer v-model:show="showSelCustomerDlg" :title="t('bom.sel_cus')"
            @on-submit="onSelCustomerCallback" />

        <!-- 产品选择弹窗 -->
        <DialogProductSel v-model:show="showSelProductDlg" :title="t('bom.sel_pdt')"
            @on-submit="onSelProductCallback" />

        <!-- 选择负责人 -->
        <DialogUser :param="''" v-model:show="showSelUserDlg" :title="t('msg.selectUser')"
            @on-submit="onSelManagerCallback" />

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 6% !important;
    background-color: aqua;
}

:deep(.conentStyle) {
    width: 30% !important;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}


.header {
    display: flex;
    border: 1px solid #b1b3b8;
    border-collapse: collapse;
    width: 100%;
    //   color: var(--el-text-color-regular);
    font-size: 15px;
}

.headerBk {
    background-color: #6d92b4 !important;
}

.content {
    &:extend(.header);
    font-size: 14px;
}

.header>div,
.content>div {
    display: flex;
    border-right: 1px solid #b1b3b8;
    padding: 10px;
    // max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    padding: 5px;
    justify-content: center;
    min-width: 50px;
    max-height: 90px;
}

.header>div:last-child,
.content>div:last-child {
    border-right: none;
}


//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #73b0e8 !important;
//   color: #fff;
// }
:deep(.hide > .el-input__wrapper) {
    box-shadow: none;
    transition: box-shadow 0.3s;
    /* 添加过渡效果 */
}

/* 鼠标悬停时显示 box-shadow */
:deep(.hide >.el-input__wrapper:hover) {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    /* 修改为悬停状态下的 box-shadow 样式 */
}

/* 获取焦点时显示 box-shadow */
:deep(.hide >.el-input__wrapper:focus) {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    /* 修改为焦点状态下的 box-shadow 样式 */
}

.table_item {
    min-height: 42px;
    align-items: center;
}

.text-overflow-ellipsis {
    white-space: nowrap;
    /* 防止文本换行 */
    overflow: hidden;
    /* 隐藏溢出文本 */
    text-overflow: ellipsis;
    /* 显示省略号 */
}

.scrollable-div::-webkit-scrollbar {
    z-index: 1;
    /* 将滚动条置于内容的下方 */
}

.scrollable-div {
    overflow-x: auto;
}

:deep(.el-tooltip__trigger) {
    width: 100%;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
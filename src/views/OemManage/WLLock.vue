<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import type { FormInstance } from 'element-plus'
import {checkFormRule} from '@/api/tool'
import { updateOemOrderApi,getOemLockListApi,getOemOrderInfoApi,getOemTakeOutInfoApi,getSaleInfoApi  } from '@/api/product'
import { getTodayDate } from '@/api/tool'
import { DialogSelReceiptPdt } from '@/components/DialogSelReceiptPdt'
import { Dialog } from '@/components/Dialog'


const { currentRoute,back,push } = useRouter()
const { t } = useI18n()

//标题
const title = ref('')

//定义搜索条件
const defaultCondition = {
    pdt_id:'',
    page: 1,
    count: 10000
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//库存明细列表
const detailData = reactive([])


//查询物料库存明细列表
const getOemLockList = async (page = 1) => {
    searchCondition.page = page
    searchCondition.oem_order_num  = currentRoute.value.query.oem_order_num
    searchCondition.pdt_biaoshi  = currentRoute.value.query.pdt_biaoshi
    const ret = await getOemLockListApi(searchCondition)
    if(ret)
    {
        detailData.splice(0,detailData.length, ...ret.data)
        console.log(detailData)
    }
}


//委外单数据源
const oemData = reactive({})

//pdt销售数量
const pdtCur = reactive({})

// //锁定

const onUpdateInfo = (item)=>{
  ElMessageBox.confirm(t('msg.confirm_update_wl')+'-->'+item.nick, t('common.reminder'), {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    type: 'warning'
  })
  .then(async () => {
    for(let pdt of oemData.pdt_list)
    {
        if(pdt.标识 == currentRoute.value.query.pdt_biaoshi)
        {
            console.log('找到！！',pdt)
            for(let bom of pdt.bom_data.item_list)
            {
                for(let one of bom.pdt_list)
                {
                    if(one.标识 == item.标识)
                    {
                        //设置赔付价格
                        one.税前赔付单价 = item.税前赔付单价
                        one.税后赔付单价 = item.税后赔付单价

                        let index = one.locked_list.findIndex(item=>item.parter_id == oemData.parter_id)
                        if(index !== -1)
                        {
                            one.locked_list[index].amount = parseInt(item.此单锁定)
                        }
                        else{
                            one.locked_list.push({
                                parter_id:oemData.parter_id,
                                amount: parseFloat(item.委外商此单锁定)
                            })
                        }
                        console.log('找到最后位置',one)

                        //更新oem单子
                        const ret = await updateOemOrderApi(oemData)
                        if(ret)
                        {
                            getOemLockList()
                            //提示修改成功
                            ElMessage.success('修改成功')
                        }
                        return 

                    }
                }
            }
        }
    }

  })
  .catch(() => {})
}


//锁定数字校验
const onValueCheck = (item)=>{
    if(item.委外商此单锁定>item.委外商可用库存)
    {
        item.委外商此单锁定 = item.委外商可用库存
    }
}

onMounted(async () => {        
    await getOemLockList()
    //查询委外单信息
    const ret = await getOemOrderInfoApi({
            oem_order_num:currentRoute.value.query.oem_order_num,
            page:1,
            count:100
        })
    if(ret)
    {
        title.value = '['+currentRoute.value.query.pdt_name+']'+currentRoute.value.query.pdt_nick+' 物料信息'
        Object.assign(oemData,ret.data)
    }
    
})


</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">

        <el-table ref="tableRef" :data="detailData" style="width: 100%; margin-bottom: 20px" row-key="id" border stripe
          highlight-current-row header-cell-class-name="tableHeader">
          <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index+1 }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div  class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130"/>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip                        
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip align="center"   prop="单个用量" :label="t('takeout.one_use')" />
            <el-table-column show-overflow-tooltip align="center"   prop="委外数量" :label="t('oem.count')" />            
            <el-table-column show-overflow-tooltip align="center"   prop="计划数量" :label="t('takeout.jh_count')" />
            <el-table-column show-overflow-tooltip align="center"   prop="损耗率" :label="t('bom.cust')" />
            <el-table-column show-overflow-tooltip align="center"   prop="实际发料数量" :label="t('takeout.actual_count')" />
            <el-table-column show-overflow-tooltip align="center"   prop="委外商库存" :label="t('takeout.parter_stone')" />
            <el-table-column show-overflow-tooltip align="center"    :label="t('oem.pay_per')" >
                <template #default="scope">
                    <el-input  v-model="scope.row.税后赔付单价"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip align="center"    :label="t('oem.pay_per_aft')" >
                <template #default="scope">
                    <el-input  v-model="scope.row.税前赔付单价"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip align="center"    :label="t('takeout.cur_lock')" >
                <template #default="scope">
                    <el-input  v-model="scope.row.仓库此单锁定" @input="onValueCheck(scope.row)"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip align="center"   prop="已领料" :label="t('oem.get')" />
            <el-table-column show-overflow-tooltip align="center"   prop="base_unit" :label="t('product_manage.b_unit')" />

          <el-table-column show-overflow-tooltip fixed="right" align="center" width="100px" prop="name" :label="t('roleTable.opt')" >
            <template #default="scope">
              <ElButton type="success" size="small" @click="onUpdateInfo(scope.row)">{{ t('oem.btn_modify') }}</ElButton>
            </template>
          </el-table-column>
        </el-table>

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}

:deep(.el-input__inner){
  text-align: center;
}


</style>
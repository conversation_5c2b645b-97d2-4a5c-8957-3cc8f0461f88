<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getOemReturnListApi,delOemQualityCheckApi, delOemReturnApi, updateOemReturnApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { checkPermissionApi } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import PrintModal from '@/views/PrintManage/PrintModal.vue'

const { currentRoute,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()

//退货单数据源
const returnData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  委外单号: '',
  任务编号:'',
  退货单号: '',
  产品编码: '', 
  产品名称: '',
  受托商: '',
  fsm_cur_state: '',
  收货状态: '',
  产品规格: '',
  委外单价0: '',
  委外单价1: '',
  store_type: '', //退货类型
  退货时间: ['', ''],
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getOemReturnList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getOemReturnList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getOemReturnList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
    console.log(item)
  //编辑
  if(type === 'edit' || type === 'info')
  {
    const retPath = (currentRoute.value.name?.indexOf("Stone")>=0)?'/oeminventorymanage/stoneaddoemreturn':'/oemmanage/addoemreturn'
    
    push({
      path: retPath,
      query:{
          id:item.id,
          oem_order_num:item.oem_order_num,
          mode:type,
          type:item.store_type,
          cmd:bCheckMode.value?'审核':''
      }
    })
  }
  else if(type === 'del')
  {
    ElMessageBox.confirm(t('msg.confirm_del_qualitycheck')+'--> '+item.buy_drawin_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)
      const ret = await delOemReturnApi({
        ids: [item.id],
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getOemReturnList()
      }
    }
    ).catch(() => {})
  }
  else if(type === 'takeout')
  {
    ElMessageBox.confirm('是否出库，操作后仓库库存将发生变化！', t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)
      const ret = await updateOemReturnApi({
        id: item.id,
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '出库'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getOemReturnList()
      }
    }
    ).catch(() => {})
  }
}


//查询退货单列表
const getOemReturnList = async (page = 1) => {
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  tmp.委外单价 = tmp.委外单价0 + ',' + tmp.委外单价1
  delete tmp.委外单价0
  delete tmp.委外单价1
  tmp.退货时间 = tmp.退货时间[0]+','+tmp.退货时间[1]
  const ret = await getOemReturnListApi(tmp)
  if(ret)
  {
    returnData.splice(0,returnData.length, ...ret.data)
    console.log(returnData)
    totleCount.value = parseInt(ret.count)
    for (let one of returnData) {
      one.checked = false
    }
  }
}
//审核模式
const bCheckMode = ref(false)
onMounted(() => {
    if(currentRoute.value.name.indexOf("OemReturnCheck")>=0)
    {
      bCheckMode.value = true
      searchCondition.fsm_cur_state = '等待审核'
    }

    if(currentRoute.value.query.oem_cancel_num != undefined)
      searchCondition.退货单号 = currentRoute.value.query.oem_cancel_num as string
    if(currentRoute.value.query.oem_order_num != undefined)
      searchCondition.委外单号 = currentRoute.value.query.oem_order_num as string
    if(currentRoute.value.query.任务编号 != undefined)
        searchCondition.任务编号 = currentRoute.value.query.任务编号 as string
    getOemReturnList()  

      //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})

onBeforeMount(() => {
  
});



//去打印
const dialogVisible = ref(false)
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: '委外退货单' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(() => { dialogVisible.value = false })
}

//批量打印
const toPrintPageMul = () => {
  //拿到所有勾选
  let arrayAll = returnData.filter((one) => one.checked)
  if (arrayAll.length == 0)
  {
    ElMessage.warning('请先勾选要打印的单据！')
    return
  }
  console.log(arrayAll)
  let params = []
  for(let one of arrayAll)
  {
    params.push({ ...one, printType: '委外退货单' })
  }

  sessionStorage.setItem('printInfo', JSON.stringify(params))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}


const checkAll = ref(false)
const onCheckedAll = () => {
  for (let one of returnData)
  {
    one.checked = checkAll.value
  }
}
</script>

<template>
    <!-- 列表 -->
    <div ref="rootRef" class="flex-col w-[100%] relative">

      <div class="bp-7 pt-0">
        <div  class="pt-7 pr-5 pl-5 pb-7 mb-5 bg-white">
          <div class="text-center mb-10 font-bold">{{ t('oemreturn.list')+(bCheckMode?'-审核':'') }}</div>
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.check_status') }}</div>
            <el-select size="small" class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择" >
              <el-option v-for="item in ['订单创建', '等待审核', '等待修改', '等待提交', '审核通过', '已拒绝', '已关闭']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
                   <!-- 检索条件 -->
                   <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">退货单号</div>
            <el-input size="small" v-model="searchCondition.退货单号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">委外单号</div>
            <el-input size="small" v-model="searchCondition.委外单号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">任务编号</div>
            <el-input size="small" v-model="searchCondition.任务编号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input size="small" v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">受托商</div>
            <el-input size="small" v-model="searchCondition.受托商" placeholder="" class="searchItem" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
            <el-input size="small" v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
          </div>

          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">委外价格</div>
            <el-input size="small" v-model="searchCondition.委外单价0" placeholder="" class="!w-[60px]" />
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small" v-model="searchCondition.委外单价1" placeholder="" class="!w-[60px]" />
          </div>

          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">退货时间</div>
              <el-date-picker size="small" class="searchItem" v-model="searchCondition.退货时间" type="daterange"
                range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>


          <div  class="flex justify-end items-center mr-6 mt-6 mb-2">
            <!-- <el-checkbox :label="t('customer.senior')" v-model="senior" size="small"/> -->
            <ElButton class="ml-5" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <div class="flex">
          <div class="flex text-sm items-center mr-3">
            <div class="title_create w-[14px] h-[14px] rounded-[50%] mr-1"></div>
            已创建
          </div>
          <div class="flex text-sm items-center mr-3">
            <div class="title_checked w-[14px] h-[14px] rounded-[50%] mr-1"></div>
            审核通过
          </div>
          <div class="flex text-sm items-center mr-3">
            <div class="title_wait w-[14px] h-[14px] rounded-[50%] mr-1"></div>
            驳回
          </div>
          <div class="flex text-sm items-center mr-3">
            <div class="title_ok w-[14px] h-[14px] rounded-[50%] mr-1"></div>
            已出库
          </div>
          <ElButton size="small" type="success" class="ml-auto" plain @click="toPrintPageMul">
            <Icon title="打印" icon="material-symbols:print-outline"/>
            批量打印
          </ElButton>
        </div>
        <!-- 产品列表 -->
        <div>
            <!-- 表头 -->
            <div class="flex header headerBk">
              <div class="w-[70%] flex items-center">
                <ElCheckbox class="min-w-[14px] !h-[14px] ml-2" v-model="checkAll" @change="onCheckedAll" /> 
                <div class="min-w-[60%] text-center">产品名称</div>
                <div class="min-w-[20%] text-center border-l-1px">出库数量</div>
                <div class="min-w-[20%] text-center border-l-1px">价格</div>
              </div>
              <div class="flex flex-grow !p-0">
                <div class="rightcss rightcss_title">状态</div>
                <div class="rightcss rightcss_title">备注</div>
                <div class="rightcss_title !min-w-[70px]">操作</div>
              </div>
            </div>
            <!-- 表内容 -->
            <div class="mt-3 bg-white" v-for="item in returnData" :key="item.id" style="box-shadow:var(--el-box-shadow-lighter);">
              <!-- 内容头 -->
              <div>
                <div class="p-2 flex flex-nowrap text-[13px] h-[30px] border-b-1px font-bold items-center">
                  <ElCheckbox v-model="item.checked" />
                  <div class="ml-2 w-[45%]  flex items-center">
                      <div class="flex items-center mr-1 w-80px flex-grow-0 flex-shrink-0" >
                        <div class="rounded p-1 pl-2 pr-2" style="color: #fff;" :class="{'title_create': ['订单创建','等待审核','等待修改','等待提交'].includes(item.fsm_cur_state), 'title_checked': item.fsm_cur_state === '审核通过', 'title_ok': ['已出库','已关闭','已拒绝'].includes(item.fsm_cur_state), 'title_wait': item.fsm_cur_state === '等待修改'}">
                          {{ item.fsm_cur_state }}
                        </div>
                      </div>
                      <div class="mr-3 font-bold">
                          {{ item.create_date.split(' ')[0] }}
                      </div> 
                      <div class="mr-2">出库单号: {{ item.oem_cancel_num }}</div>
                      <div>委外单号: {{ item.oem_order_num }}</div>
                  </div>
                  <div class="min-w-[200px] flex items-center">
                    <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                    {{ checkPermissionApi('受托商名称显示')?item.parter_nick:'***' }}
                  </div>

                  <!-- 靠右的其他信息 -->
                  <div class="ml-auto flex justify-center items-center min-w-[200px]">
                    <div class="mr-5">出库人:{{ item.cancel_man_name }}</div>
                    <div class="mr-5">仓库:{{ item.store_nick }}</div>
                    <Icon class='mr-3'
                        style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;"
                        icon="material-symbols:print-outline" @click="toPrintPage(item)" />
                  </div>
                </div>
              </div>
              <!-- 内容体 -->
              <div class="flex">
                <!-- 左边产品列表 -->
                <div class="w-[70%]  table_self">
                  <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]"> 
                    <div class="min-w-[60%] text-center flex-grow ">
                      <div class="flex justify-start items-center w-[100%] p-1">
                        <el-image v-if="pdt.pics.length>0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" :src="pdt.pics[0].url" />
                        <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" src="/nopic.jpg" />
                        <div class="inline-block text-left max-w-[100%] ml-2">
                          <div style="white-space: normal;" class="nameStyle" @click="handleOper('info', item)">{{ '['+pdt.name+']'+pdt.nick }}</div>
                        </div>
                      </div>
                    </div>                  
                    <div class="min-w-[20%] flex justify-center items-center flex-col border-l-1px">{{ pdt.退货数量+' '+pdt.base_unit }}</div>
                    <div class="min-w-[20%] flex justify-center items-center flex-col border-l-1px">{{ checkPermissionApi('委外订单价格显示')?( pdt.退货数量+'*'+pdt.oem_price_aft_tax+'='+ parseFloat(pdt.退货数量)*pdt.oem_price_aft_tax):'*' }}</div>
                  </div>
                </div>
                <!-- 右边其他数据 -->
                <div class="flex flex-grow text-center  right">
                  <div class="rightcss" style="text-align:center;"> 
                    <div style="font-size: 13px; font-weight: 800;">{{ item.fsm_cur_state }}</div>
                    <div class="text-red-400 mt-1">{{ item.fsm_log_list.length>0?item.fsm_log_list[0][5]:'' }}</div>
                  </div>
                  <div class="rightcss">{{ item.note }}</div>
                  <div class="!min-w-[70px] flex justify-center items-center flex-col" style="text-align: center;">
                    <ElButton v-if="bCheckMode && item.fsm_can_trig_data.审核触发.length>0" type="success" size="small" @click="handleOper('info',item)">{{ t('cmd.check') }}</ElButton>
                    <el-dropdown v-if="!bCheckMode" trigger="click" placement="bottom">
                      <span class="el-dropdown-link">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                      </span>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.detail') }}</el-dropdown-item>
                          <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('出库')" @click="handleOper('takeout', item)">{{ t('cmd.takeout') }}</el-dropdown-item>
                          <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('保存')" @click="handleOper('edit', item)">{{ t('userOpt.edit') }}</el-dropdown-item>
                          <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('保存')" @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>

      <el-pagination class="flex justify-end mb-4 mt-4"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
    <!-- <div class="h-[300px]"></div> -->
    <PrintModal v-model:show="dialogVisible" />
    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  cursor: pointer;
}
.nameStyle:hover{
  color: rgb(130, 130, 255);
}


:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  color: #333;
  font-weight: bold;
}
.headerBk{
  background-color: #fff;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
 // white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: left; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

.rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}

.title_create{ //已创建
  background-color: #79bbff;
}
.title_checked{ //已审核
  background-color: #95d475;
}
.title_ok{ //已入库
  background-color: #b1b3b8;
}
.title_wait{//等待修改
  background-color: #f89898;
}

:deep(.el-checkbox__input){
  border: 1px solid #999;
}
</style>

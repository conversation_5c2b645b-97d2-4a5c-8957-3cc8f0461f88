<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted,onBeforeUnmount,watch } from 'vue'
import { useRouter } from 'vue-router'
import { getOemPutinListApi,delOemPutinApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { DialogPurchaseSel } from '@/components/DialogPurchaseSel'
import { useAppStore } from '@/store/modules/app'
import { useCache } from '@/hooks/web/useCache'
import { ceilToFixed, checkPermissionApi, downloadFile } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { exportOemPutinListApi } from '@/api/extra';
import PrintModal from '@/views/PrintManage/PrintModal.vue'

import { DynamicScroller,DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const { currentRoute,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()

//入库单数据源
const putinData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state: '',
  store_type:'',
  委外单号: '',
  任务编号:'',
  入库单号:'',
  产品编号:'',
  产品名称:'',
  入库人员:'',
  仓库:'',
  受托商:'',
  入库日期:['',''],
  入库数量0:'',
  入库数量1:'',
  默认排序:'入库时间倒序',
  入库备注:'',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getOemPutinList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getOemPutinList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getOemPutinList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
  //编辑
  if(type === 'edit' || type === 'info')
  {
    const retPath = (currentRoute.value.name?.indexOf("Stone")>=0)?'/oeminventorymanage/stoneaddoemputin':'/oemmanage/addoemputin'
    
    push({
      path: retPath,
      query:{
          id:item.id,
          mode:type,
      }
    })
  }
  else if(type === 'del')
  { 
    ElMessageBox.confirm(t('msg.confirm_del_putin')+'--> '+item.buy_drawin_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      const info = wsCache.get(appStore.getUserInfo)
      const ret = await delOemPutinApi({
        ids: [item.id],
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getOemPutinList()
      }
    }
    ).catch(() => {})
  }
}


//查询入库单列表
const getOemPutinList = async (page = 1) => {
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  delete tmp.入库数量0
  delete tmp.入库数量1

  tmp.入库数量 = searchCondition.入库数量0+','+searchCondition.入库数量1
  tmp.入库日期 = searchCondition.入库日期[0]+','+searchCondition.入库日期[1]
  

  isLoading.value = true
  const ret = await getOemPutinListApi(tmp)
  if(ret)
  {
    putinData.splice(0, putinData.length, ...ret.data)
    for (let one of putinData) {
      one.checked = false
    }
    totleCount.value = parseInt(ret.count)
  }
  isLoading.value = false
}

onMounted(() => {
  if(currentRoute.value.query.buy_drawin_num != undefined)
    searchCondition.入库单号 = currentRoute.value.query.buy_drawin_num as string
  // if(currentRoute.value.query.type != undefined)
  //   searchCondition.type = currentRoute.value.query.type as string
  if(currentRoute.value.query.oem_order_num != undefined)
      searchCondition.委外单号 = currentRoute.value.query.oem_order_num as string
  if(currentRoute.value.query.任务编号 != undefined)
      searchCondition.任务编号 = currentRoute.value.query.任务编号 as string
  console.log(putinData)
  getOemPutinList()  
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  adjustScrollerHeight()
  window.addEventListener('resize', adjustScrollerHeight)
})

onBeforeMount(() => {
  
});



const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    delete tmp.入库数量0
    delete tmp.入库数量1

    tmp.入库数量 = searchCondition.入库数量0+','+searchCondition.入库数量1
    tmp.入库日期 = searchCondition.入库日期[0]+','+searchCondition.入库日期[1]
    const ret = await exportOemPutinListApi(tmp)
    if (ret) {
       
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}


const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: '委外入库单' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}
//批量打印
const toPrintPageMul = () => {
  //拿到所有勾选
  let arrayAll = putinData.filter((one) => one.checked)
  if (arrayAll.length == 0)
  {
    ElMessage.warning('请先勾选要打印的单据！')
    return
  }
  console.log(arrayAll)
  let params = []
  for(let one of arrayAll)
  {
    params.push({ ...one, printType: '委外入库单' })
  }

  sessionStorage.setItem('printInfo', JSON.stringify(params))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}

const checkAll = ref(false)
const onCheckedAll = () => {
  for (let one of putinData)
  {
    one.checked = checkAll.value
  }
}


const adjustScrollerHeight = () => {
  const height = document.getElementById('mainscroll')?.clientHeight
  const scroller = document.getElementById('dynamicScroller');
  if (scroller) {
    scroller.style.height = `${height}px`;
  }
}
onBeforeUnmount(() => {
  window.removeEventListener('resize', adjustScrollerHeight)
})

watch(putinData, () => {
  adjustScrollerHeight()
})

const isLoading = ref(false)
</script>

<template>
    <!-- 列表 -->
    <div ref="rootRef">
      <div class="p-7 pt-0">
        <div  class="pt-7 pr-5 pl-5 pb-7 mb-5 bg-white relative">
          <div class="absolute top-3 left-10">
            <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                <Icon icon="carbon:export" />
                <div class="pl-2">{{ t('button.export') }}</div>
            </ElButton>
          </div>
          <div class="text-center mb-5 font-bold">{{ t('oemputin.list') }}</div>
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">委外单号</div>
            <el-input size="small" v-model="searchCondition.委外单号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="inline-flex items-center ml-1 mb-1">
              <div class="searchTitle">任务编号</div>
              <el-input size="small" v-model="searchCondition.任务编号" placeholder="" class="searchItem" />
            </div>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">入库单号</div>
            <el-input size="small" v-model="searchCondition.入库单号" placeholder="" class="searchItem" />
          </div>
          <div class="searchTitle">入库状态</div>
            <el-select size="small" class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择">
              <el-option v-for="item in ['订单创建', '等待审核', '等待修改', '等待提交', '审核通过', '已拒绝', '已关闭']" :key="item" :label="item"
                :value="item" />
            </el-select>
          </div>

          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">入库类型</div>
            <el-select size="small" class="searchItem" v-model="searchCondition.store_type" placeholder="请选择">
              <el-option v-for="item in ['良品库','不良品库']" :key="item" :label="item"
                :value="item" />
            </el-select>
          </div>

          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品名称</div>
            <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品编号</div>
            <el-input size="small" v-model="searchCondition.产品编号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">入库人员</div>
            <el-input size="small" v-model="searchCondition.入库人员" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">仓库</div>
            <el-input size="small" v-model="searchCondition.仓库" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">受托商</div>
            <el-input size="small" v-model="searchCondition.受托商" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">入库日期</div>
          <el-date-picker size="small" class="searchItem" v-model="searchCondition.入库日期" type="daterange"
            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">入库数量</div>
            <el-input size="small"  v-model="searchCondition.入库数量0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.入库数量1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">默认排序</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.默认排序" placeholder="请选择">
            <el-option v-for="item in ['入库时间正序','入库时间倒序','入库编号正序','入库编号倒序']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">入库备注</div>
          <el-input size="small" v-model="searchCondition.入库备注" placeholder="" class="searchItem" />
        </div>



          <div  class="flex justify-end items-center mr-6 mt-6">
            <el-checkbox :label="t('customer.senior')" v-model="senior" size="small"/>
            <ElButton class="ml-5" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 产品列表 -->
        <div v-loading.lock="isLoading">
          <div class="flex mb-2">
            <ElButton type="success" class="ml-auto" plain @click="toPrintPageMul">
              <Icon title="打印" icon="material-symbols:print-outline"/>
              批量打印
            </ElButton>
          </div>
            <!-- 表头 -->
            <div class="flex header headerBk">              
              <div class="w-[70%] flex">
                <ElCheckbox class="min-w-[14px] !h-[14px] ml-5" v-model="checkAll" @change="onCheckedAll" />
                <div class="min-w-[50%] text-center">产品名称</div>
                <div class="min-w-[10%] text-center border-l-1px">收货数量</div>
                <div class="min-w-[10%] text-center border-l-1px">入库数量</div>
                <div class="min-w-[10%] text-center border-l-1px">单价</div>
                <div class="min-w-[20%] text-center border-l-1px">金额</div>
              </div>
              <div class="flex flex-grow !p-0">
                <div class="rightcss rightcss_title">状态</div>
                <div class="rightcss rightcss_title">备注</div>
                <div class="rightcss_title !min-w-[70px]">操作</div>
              </div>
            </div>


        <!-- 表内容 -->
        <DynamicScroller :items="putinData" :min-item-size="89.57" key-field="id" class="scroller" id="dynamicScroller">
          <template #default="{ item, index,active }">
            <DynamicScrollerItem 
              :item="item" :size-dependencies="[
                  item.pdt_list.length
                ]" 
              :data-index="index"
              :active="active"
            >

            <div>
              <!-- 内容头 -->
              <div class="mt-3 bg-blue-200" style="box-shadow:var(--el-box-shadow-lighter);">
                <div class="h-[30px] bg-light-500 p-2 flex flex-nowrap text-[13px] border-b-1px font-bold items-center">
                    <ElCheckbox v-model="item.checked" />
                    <div class="ml-2 w-[45%]  flex items-center">
                        <div class="mr-3 font-bold">
                            {{ item.create_date.split(' ')[0] }}
                        </div> 
                        <div class="mr-2">入库单号: {{ item.oem_putin_num }}</div>
                        <div class="mr-2">质检单号: {{ item.oem_check_num }}</div>
                        <div class="mr-2">收货单编号: {{ item.oem_drawin_num }}</div>
                        <div>委外单编号: {{ item.oem_order_num }}</div>
                    </div>
                  <div class="min-w-[200px] flex items-center">
                    <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                    {{ checkPermissionApi('受托商名称显示')?item.parter_nick:'***' }}
                  </div>
                  <div class="flex justify-center items-center min-w-[250px]">
                    <div class="mr-5">收货人:{{ item.drawin_man_name }}</div>

                  </div>
                  <!-- 靠右的其他信息 -->
                  <div class="ml-auto flex justify-center items-center min-w-[200px]">
                    <div class="mr-5">仓库: {{ item.store_nick }}</div>
                    <div>入库员: {{ item.drawin_man_name }}</div>
                    <Icon class='mr-3'
                        style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;"
                        icon="material-symbols:print-outline" @click="toPrintPage(item)" />
                  </div>
                </div>
              </div>
              <!-- 内容体 -->
              <div class="flex">
                <!-- 左边产品列表 -->
                <div class="w-[70%]  table_self">
                  <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                    <div class="min-w-[50%] text-center flex-grow ">
                      <div class="flex justify-start items-center w-[100%] p-1">
                        <el-image v-if="pdt.pics.length>0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" :src="pdt.pics[0].url" />
                        <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" src="/nopic.jpg" />
                        <div class="inline-block text-left max-w-[100%] ml-2">
                          <div style="white-space: normal;" class="nameStyle" @click="handleOper('info', item)">{{ '['+pdt.name+']'+pdt.nick }}</div>
                        </div>
                      </div>
                    </div>                  
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">{{ pdt.收货数量+' '+pdt.base_unit }}</div>
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">{{ pdt.入库数量 }}</div>
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">                      
                        {{ checkPermissionApi('委外订单价格显示')?('￥'+pdt.oem_price_bef_tax+'/'+pdt.oem_price_aft_tax):'*' }}
                        <div class="ex_text">含税{{ pdt.发票税率 }}%</div>                      
                    </div>
                    <div class="min-w-[20%] flex justify-center items-center flex-col border-l-1px">{{ checkPermissionApi('委外订单价格显示')?(ceilToFixed(pdt.入库数量*pdt.oem_price_aft_tax,4,0)):'*' }}</div>

                  </div>
                </div>
                <!-- 右边其他数据 -->
                <div class="flex flex-grow text-center  right">
                  <div class="rightcss" style="text-align: center;">{{ item.store_type+'-已入库' }}</div>
                  <div class="rightcss">{{ item.note }}</div>
                  <div class="!min-w-[70px] flex justify-center items-center" style="text-align: center;">
                    <el-dropdown trigger="click" placement="bottom">
                      <span class="el-dropdown-link">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                      </span>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.detail') }}</el-dropdown-item>
                          <el-dropdown-item @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>


            </div>

            </DynamicScrollerItem>
          </template>
        </DynamicScroller>




        </div>
      </div>

      <el-pagination class="flex justify-end mb-4"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />

    <PrintModal v-model:show="dialogVisible" />
    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  cursor: pointer;
}
.nameStyle:hover{
  color: rgb(130, 130, 255);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  color: #333;
  font-weight: bold;
}
.headerBk{
  background-color: #fff;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
 // white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: left; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

.rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}
:deep(.el-checkbox__input){
  border: 1px solid #999;
}
</style>

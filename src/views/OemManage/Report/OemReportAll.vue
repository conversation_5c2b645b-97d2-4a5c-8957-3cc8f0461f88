<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi, updatePayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, closeOneTagByPath } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { DialogGetMoney } from '@/components/DialogGetMoney'
import { getBuyReportInApi, getOemReportAllApi, getSellOutReportCusApi } from '@/api/tj';

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    委外业务日期: ['', ''],
    委外单编号:'',
    任务单编号:'',
    委外商:'',
    产品编号:'',
    产品名称:'',
    page: 1,
    count: 50
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//数据源
const reportData = reactive([])
//当前选中行
const currentRow = ref(null)


const getOemInReportAll = async () => {
    let tmp = cloneDeep(searchCondition)
    tmp.委外业务日期 = searchCondition.委外业务日期[0] + ',' + searchCondition.委外业务日期[1]
    loading.value = true
    const ret = await getOemReportAllApi(tmp)
    if (ret) {
        reportData.splice(0, reportData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
    loading.value = false
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getOemInReportAll()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getOemInReportAll()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getOemInReportAll()
}

//处理表格对象操作
const handleOper = (type, row) => {
    if (type === 'edit' || type == 'info') {

    }

}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}

function formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}
const initTime = () => {
    const currentDate = new Date();
    const firstDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    const tmp = [formatDate(firstDate), formatDate(lastDate)];

    defaultCondition.委外业务日期 = []
    defaultCondition.委外业务日期.push(...tmp);
    searchCondition.reset()
}

onMounted(() => {
    initTime()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    //刷新表格
    getOemInReportAll()
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if (['委托数量'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                const sum = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
                if(column.property.indexOf('金额')>=0)
                    sums[index] = '￥' + sum.toLocaleString(); // 添加千位分隔符
                else
                    sums[index] = sum;
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}
const loading = ref(false)
</script>

<template>
    <div ref="rootRef">
        <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">

            <div class="text-center pt-2 mb-2 font-bold" style="color:#333">委外任务报表</div>
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">

                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">委外单编号</div>
                    <el-input size="small" v-model="searchCondition.委外单编号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">任务单编号</div>
                    <el-input size="small" v-model="searchCondition.任务单编号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">委外商</div>
                    <el-input size="small" v-model="searchCondition.委外商" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">产品编号</div>
                    <el-input size="small" v-model="searchCondition.产品编号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">产品名称</div>
                    <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
                </div>

                <div class="inline-flex items-center mr-1 mb-1">
                    <div class="searchTitle">委外日期</div>
                    <el-date-picker size="small" class="searchItem" v-model="searchCondition.委外业务日期" type="daterange"
                        range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间"
                        value-format="YYYY-MM-DD" />
                </div>

                <div class="flex justify-end items-center mr-6 mt-2">
                    <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="ml-1">查询</div>
                    </ElButton>
                    <ElButton class="ml-4" type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="ml-1">清除</div>
                    </ElButton>
                </div>
            </div>

            <el-table v-loading="loading" scrollbar-always-on header-cell-class-name="tableHeader" :data="reportData" style="width: 100%;color: #666666;"
                show-summary :summary-method="getSummaries" @current-change="setCurrentRow" border stripe>
                <el-table-column align="center" show-overflow-tooltip prop="委外单号" :label="'委外单号'" />
                <el-table-column align="center" show-overflow-tooltip prop="任务单号" :label="'任务单号'" />
                <el-table-column align="center" show-overflow-tooltip prop="委托公司" :label="'委托公司'" width="200"/>
                <el-table-column align="center" show-overflow-tooltip prop="工序描述" :label="'工序描述'" />
                <el-table-column align="center" show-overflow-tooltip prop="产品编号" :label="'产品编号'" />
                <el-table-column align="center" show-overflow-tooltip prop="产品名称" :label="'产品名称'" width="300"/>
                <el-table-column align="center" show-overflow-tooltip prop="规格" :label="'规格'" />
                <el-table-column align="center" show-overflow-tooltip prop="委托数量" :label="'委托数量'" />
                <el-table-column align="center" show-overflow-tooltip prop="入库数量" :label="'入库数量'" />
                <el-table-column align="center" show-overflow-tooltip prop="发料比例" :label="'发料比例'" />


                <!-- 
                <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
                    <template #default="scope">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                    </template>
                </el-table-column> -->
            </el-table>
            <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>

<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,nextTick } from 'vue'
import { getStoreListApi,getOemPutinNewnumApi,getOemDrawinInfoApi,getOemPutinInfoApi,addOemPutinApi,updateOemPutinApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount,watch } from 'vue'
import {checkFormRule} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'



const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//入库单数据
const putinData = reactive(
{
    "parter_id": "",
    "oem_drawin_num": "",
    "oem_putin_num": "",
    "store_id": "", //仓库ID
    "type": "良品", //入库方式
    "pdt_list": [],
    "note": "", //入库单备注    
    fsm_can_trig_data:{
        审批触发:[],
        操作触发:['保存']
    }, //审批决策
    fsm_cur_state:'',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)


//获取最新ID
const onChangeID = async()=>{
    const ret = await getOemPutinNewnumApi()
    if(ret)
    {
        console.log(ret)
        putinData.oem_putin_num = ret.data.new_id
    }
}

const storeCur = computed(()=>{
    return currentRoute.value.query.type == '良品库'?storeData.filter(item => item.type === '良品库'):storeData.filter(item => item.type === '不良品库')
})

//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    type:currentRoute.value.query.type,
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)
  }

}


onMounted(async () => {    
    await getStoreList()
    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = currentRoute.value.query.type == '良品库'?t('putin.good_putin'):t('putin.bad_putin')

        onChangeID()
        //同步收货单数据
        const ret = await getOemDrawinInfoApi({
            oem_drawin_num:currentRoute.value.query.oem_drawin_num,
            page:1,
            count:100
        })
        console.log(ret)

        putinData.oem_drawin_num = ret.data.oem_drawin_num
        putinData.oem_order_num = ret.data.oem_order_num
        putinData.oem_order_sub = ret.data.oem_order_sub
        putinData.order_note = ret.data.order_note
        putinData.drawin_note = ret.data.note
        putinData.parter_nick = ret.data.parter_nick
        putinData.drawin_man_name = ret.data.drawin_man_name
        putinData.oem_man_name = ret.data.oem_man_name
        putinData.type = currentRoute.value.query.type as string
        putinData.pdt_list = ret.data.pdt_list

        //默认选择第一个仓库
        if(storeCur.value.length > 0)
            putinData.store_id = storeCur.value[0].id

        //追加产品额外属性
        for(let item of putinData.pdt_list)
        {
            item.已入库良品数量 = 0
            item.已入库不良品数量 = 0
          //  item.类型 = currentRoute.value.query.type
            item.入库备注 = ''
            if(putinData.type == '良品库')
                item.入库数量 = item.良品数量-item.已入库良品
            else
                item.入库数量 = item.不良品数量-item.已入库不良品
            
        }

        //设置入库人员
        const info = wsCache.get(appStore.getUserInfo)
        putinData.putin_man_id = info.id
        putinData.putin_man_name = info.resident_name
        //默认入库日期为今天
        putinData.putin_date = getTodayDate()

    }
    else
    {        
        if(currentRoute.value.query.mode == 'info')
        {
            title.value = t('putin.look')
        }
        else
        {
            title.value = t('putin.modify')
        }
        
        
        //查询产品信息 
        const ret = await getOemPutinInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(putinData, ret.data)
            putinData.pdt_list = ret.data.pdt_list;
            currentRoute.value.query.type = ret.data.类型
        }
        nextTick(()=>{
            if(currentRoute.value.query.mode === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }
    
})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})



//显示隐藏选择入库员窗口变量
const showSelPutinUserDlg = ref(false)
//显示选择入库员弹窗
const onSelPutinUser = ()=>{
    showSelPutinUserDlg.value = true
}
//选择入库员回调
const onSelPutinCallback = (id,name)=>{
    console.log(id,name)
    putinData.putin_man_id = id
    putinData.putin_man_name = name
}


//重新校验输入
const recomputeNum = (pdt)=>{
    if(putinData.type == '良品库')
    {
        if(pdt.入库数量>(pdt.良品数量-pdt.已入库良品))
        {
            pdt.入库数量 = pdt.良品数量-pdt.已入库良品
        }        
    }
    else if(putinData.type == '不良品库')
    {
        if(pdt.入库数量>(pdt.不良品数量-pdt.已入库不良品))
        {
            pdt.入库数量 = pdt.不良品数量-pdt.已入库不良品
        }
    }
    if(pdt.入库数量<0)
    {
        pdt.入库数量 = 0
    }
}

//保存
const onSave = async()=>{
    console.log(putinData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //删除putinData.pdt_list中入库数量为0的行
    // putinData.pdt_list = putinData.pdt_list.filter(pdt=>pdt.入库数量!=0)
    const info = wsCache.get(appStore.getUserInfo)
    putinData.fsm_exe_man_name = info.resident_name
    putinData.fsm_exe_trig = '直接入库'

    const tmp = cloneDeep(putinData)
    tmp.pdt_list = tmp.pdt_list.filter(pdt=>pdt.入库数量!=0)
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }
    
    if(tmp.id == undefined)
    {
        const ret = await addOemPutinApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newPutinSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret =await updateOemPutinApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updatePutinSuccess'))
            back()
        }
    }


}
</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <ElButton type="primary" @click="onSave"  v-show="currentRoute.query.mode != 'info' && putinData.fsm_can_trig_data.操作触发.includes('保存')">
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <el-form :rules="rules" :model="putinData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('oemputin.id')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  v-model="putinData.oem_putin_num" :disabled="putinData.id!=undefined" />
                            <ElButton v-if="putinData.id==undefined" type="primary"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('receipt.id')"
                    class="flex">
                    <div>{{ putinData.oem_drawin_num }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('store.store')"
                    class="flex">
                    <el-select v-model="putinData.store_id" placeholder="Select" >
                        <el-option v-for="item in storeCur" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('oem.name')"
                    class="flex">
                    <div>{{ putinData.oem_order_num }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('oem.parter')"
                    class="flex">
                    <div>{{ putinData.parter_nick }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('receipt.user')"
                    class="flex">
                    <div>{{ putinData.drawin_man_name }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('oem.opter')"
                    class="flex">
                    <div>{{ putinData.oem_man_name }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('putin.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ putinData.putin_man_name }}</div> 
                        <ElButton @click="onSelPutinUser" v-if="currentRoute.query.mode != 'info' && putinData.fsm_can_trig_data.操作触发.includes('保存')">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('quality.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="putinData.putin_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>

            </el-descriptions>
        
        </el-form>

        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="putinData.pdt_list" style="width: 100%" border stripe>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="120" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130" />
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.id != undefined"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column v-if="currentRoute.query.type == '良品库'" show-overflow-tooltip  :label="t('quality.good_count')" >
                <template #default="scope">
                    <div>{{ scope.row.良品数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column v-if="currentRoute.query.type != '良品库'" show-overflow-tooltip  :label="t('quality.bad_count')" >
                <template #default="scope">
                    <div>{{ scope.row.不良品数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column v-if="currentRoute.query.type == '良品库'" show-overflow-tooltip  :label="t('putin.putin_good')" min-width="110">
                <template #default="scope">
                    <div>{{ scope.row.已入库良品 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column v-if="currentRoute.query.type != '良品库'" show-overflow-tooltip  :label="t('putin.putin_bad')" min-width="130">
                <template #default="scope">
                    <div>{{ scope.row.已入库不良品 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('putin.count')" >
                <template #default="scope">
                    <el-input v-model="scope.row.入库数量" @input="recomputeNum(scope.row)" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('receipt.price')" >
                <template #default="scope">
                    {{ '￥'+scope.row.buy_price_bef_tax+'/'+scope.row.buy_price_aft_tax }}
                        <div class="ex_text">含税{{ scope.row.发票税率 }}%</div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('receipt.totle_price')" >
                <template #default="scope">
                    {{ '￥'+scope.row.总价 }}
                </template>
            </el-table-column>

            <el-table-column show-overflow-tooltip  prop="委外备注" :label="t('oem.remark')" />
            <el-table-column show-overflow-tooltip  prop="收货备注" :label="t('receipt.remark')" />
            
            <el-table-column show-overflow-tooltip  :label="t('putin.remark')" >
                <template #default="scope">
                    <el-input v-model="scope.row.入库备注" />
                </template>
            </el-table-column>
        </el-table>
    <!-- 显示合计 -->
    <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">委外单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ putinData.order_note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">收货单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ putinData.drawin_note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">入库单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="putinData.note" clearable :autosize="{ minRows: 10, maxRows: 4 }"     type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>

    </div>

        
        




        <!-- <div class="mb-20"></div> -->

        <!-- 选择质检员 -->
        <DialogUser :param="''" v-model:show="showSelPutinUserDlg" :title="t('msg.selectUser')" @on-submit="onSelPutinCallback"/>

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}
//扩展文字
.ex_text{
  font-size: 11px;
  color: #646464;
}
</style>
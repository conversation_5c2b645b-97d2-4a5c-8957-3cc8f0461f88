<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElProgress,  ElButton,  ElMessage, ElMessageBox, ElImage,  ElInput, ElSelect, ElOption, ElCheckbox, ElDropdown, ElDropdownItem,  ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getOemTakeChannelListApi,delOemTakeChannelApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { DialogSaleSel } from '@/components/DialogSaleSel'
import { checkPermissionApi } from '@/api/tool';


const { currentRoute,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

//退料数据源
const channelData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  oem_reback_num:'',
  oem_order_num:'',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getOemTakeChannelList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getOemTakeChannelList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getOemTakeChannelList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
  //编辑产品
  if(type === 'edit' || type === 'info')
  {
    const retPath = (currentRoute.value.name?.indexOf("Stone")>=0)?'/oeminventorymanage/stoneaddoemtakechannel':'/oemmanage/addoemtakechannel'
    push({
      path: retPath,
      query:{
          id:item.id,
          type:type
      }
    })
  }
  else if(type === 'del') 
  {
    ElMessageBox.confirm(t('msg.confirm_del_takechannel')+'--> '+item.sell_takeout_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const ret = await delOemTakeChannelApi({
        ids: [item.id],
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getOemTakeChannelList()
      }
    }
    ).catch(() => {})
  }


}



//查询退料单列表
const getOemTakeChannelList = async (page = 1) => {
  searchCondition.page = page
  const ret = await getOemTakeChannelListApi(searchCondition)
  if(ret)
  {
    channelData.splice(0,channelData.length, ...ret.data)
    console.log(channelData)
    totleCount.value = parseInt( ret.count)
  }
}

onMounted(() => {
    if(currentRoute.value.query.oem_order_num != undefined)
      searchCondition.oem_order_num = currentRoute.value.query.oem_order_num
    if(currentRoute.value.query.pdt_biaoshi != undefined)
      searchCondition.oem_order_sub = currentRoute.value.query.pdt_biaoshi
    getOemTakeChannelList()  

      //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})



</script>

<template>
    <!-- 销售出库单列表 -->
    <div ref="rootRef">
      <div class="bg-white p-7">
        <div class="text-center mb-5 font-bold">{{ t('takechannel.list') }}</div>
        <div  class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
          <!-- 检索条件 -->
          <div class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('takechannel.id') }}</div>
            <el-input    v-model="searchCondition.oem_reback_num" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('oem.name') }}</div>
            <el-input    v-model="searchCondition.oem_order_num" placeholder="" size="small" class="searchItem" />
          </div>
          
          <div  class="text-center mt-5 mb-2">
            <ElButton type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
            <el-checkbox class="ml-5" :label="t('customer.senior')" v-model="senior"/>
          </div>
        </div>
        <!-- 产品列表 -->
        <div>
            <!-- 表头 -->
            <div class="flex header headerBk" style="color: white;">
              <div class="w-[60%] flex">
                <div class="w-[85%] ">产品名称</div>
                <div class="w-[15%] ">退料数量</div>
                <!-- <div class="w-[25%] ">价格</div> -->
              </div>
              <div class="flex flex-grow !p-0">
                <div class="rightcss rightcss_title">受托商</div>
                <div class="rightcss rightcss_title">退料人员</div>
                <div class="rightcss rightcss_title">订单状态</div>
                <div class="rightcss rightcss_title">备注</div>
                <div class="rightcss_title !min-w-[70px]">操作</div>
              </div>

            </div>
            <!-- <div class="flex">
              <div class="flex text-sm items-center mr-3">
                <div class="title_unreceipt w-[24px] h-[14px]"></div>
                未出库
              </div>
              <div class="flex text-sm items-center mr-3">
                <div class="title_partial w-[24px] h-[14px]"></div>
                部分出库
              </div>
              <div class="flex text-sm items-center mr-3">
                <div class="title_ok w-[24px] h-[14px]"></div>
                全部出库
              </div>
              <div class="flex text-sm items-center">
                <div class="title_over w-[24px] h-[14px]"></div>
                超量出库
              </div>
            </div> -->
            <!-- 表内容 -->
            <div class="mt-2" v-for="item in channelData" :key="item.id">
              <!-- 内容头 -->
              <div>
                <div class="p-2 flex flex-nowrap text-[13px] title_ok" :class-="{'title_unreceipt': item.发货状态 === '未发货', 'title_partial': item.发货状态 === '部分发货', 'title_ok': item.发货状态 === '全部发货', 'title_over': item.发货状态 === '超量发货'}">
                  <div class="w-[45%]  flex items-center">
                        <div class="mr-3 font-bold">
                            {{ item.create_date.split(' ')[0] }}
                        </div> 
                        <div class="mr-2">退料单号: {{ item.oem_reback_num }}</div>
                        <div class="cursor-pointer" @click="onNaviSale(item)">委外单号: {{ item.oem_order_num }}</div>
                    </div>
                  <div class="min-w-[200px] flex items-center">
                    <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                    {{ checkPermissionApi('受托商名称显示')? item.parter_nick:'***' }}
                  </div>
                  <div class="flex justify-center items-center min-w-[250px]">
                    <div class="mr-5">操作员:{{ item.reback_man_name }}</div>
                  </div>
                  <!-- 靠右的其他信息 -->
                  <div class="ml-auto flex justify-center items-center min-w-[200px]">
                  </div>
                </div>
              </div>
              <!-- 内容体 -->
              <div class="flex">
                <!-- 左边产品列表 -->
                <div class="w-[60%]  table_self">
                  <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                    <div class="w-[85%] flex-grow ">
                      <div class="flex justify-start items-start w-[100%] p-1">
                        <el-image v-if="pdt.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="pdt.pics[0].url" />
                        <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                        <div class="inline-block text-left max-w-[100%]">
                          <div style="white-space: normal;" class="mb-1 nameStyle" @click="handleOper('info', item)">{{ '['+pdt.name+']'+pdt.nick }}</div>
                        </div>
                      </div>
                    </div>                  
                    <div class="w-[15%] text-center">{{ pdt.退料数量+' '+pdt.base_unit }}</div>
                    <!-- <div class="w-[25%] text-center text-red-400" style="font-size: smaller;">
                      <div>税前:￥{{ parseFloat((pdt.sell_price_bef_tax*pdt.发货数量-pdt.avg_price_bef_tax*pdt.发货数量).toFixed(2)) }}</div>
                      <div>税后:￥{{ parseFloat((pdt.sell_price_aft_tax*pdt.发货数量-pdt.avg_price_aft_tax*pdt.发货数量).toFixed(2)) }}</div>
                    </div> -->
                  </div>
                </div>
                <!-- 右边其他数据 -->
                <div class="flex flex-grow text-center  right">
                  <div class="rightcss" style="text-align: left;">
                        {{ item.parter_nick }}
                  </div>
                  <div class="rightcss">
                    {{ item.pay_type }}
                  </div>
                  <div class="rightcss">
                    <div>已退料</div>
                  </div>
                  <div class="rightcss" style="text-align: left;">{{ item.note }}</div>
                  <div class="!min-w-[70px]">
                    <el-dropdown trigger="click" placement="bottom">
                      <span class="el-dropdown-link">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                      </span>
                      <template #dropdown>
                        <div class="flex flex-wrap w-[140px]">
                          <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.detail') }}</el-dropdown-item>
                          <!-- <el-dropdown-item @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item> -->
                        </div>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>

      <el-pagination
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
    <div class="h-[300px]"></div>
    

    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #ffe48d !important;
}

:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
}

.nameStyle {
  cursor: pointer;
}
.nameStyle:hover{
  color: rgb(130, 130, 255);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
}
.headerBk{
  background-color: #6d92b4 !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: center; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
  }
  .rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}


//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt{ //未收货
  background-color: #BFEFFF;
  border: #d9d9d9 1px solid;
}
.title_partial{ //部分收货
  background-color: #FFF8D7;
  border: #d9d9d9 1px solid;
}
.title_ok{ //完全收货
  background-color: #F2F2F2;
  border: #d9d9d9 1px solid;
}
.title_over{//超量收货
  background-color: #FFC0CB;
  border: #d9d9d9 1px solid;
}
</style>

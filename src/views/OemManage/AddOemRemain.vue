<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard,ElOption,ElSelect,ElTooltip,ElTable,ElPopconfirm,ElTag,ElDatePicker,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogProductSel } from '@/components/DialogProductSel'
import {checkFormRule, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { addOemRemainApi,updateOemRemainApi,getOemLockListApi,getOemRemainInfoApi,getOemRemainNewnumApi,getStoreListApi} from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { DialogSelSaleOutPdt } from '@/components/DialogSelSaleOutPdt'
import { cloneDeep } from 'lodash-es'
import { DialogCheckShow } from '@/components/DialogCheckShow'

const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})




//入库单数据
const remainData = reactive(
    {
    "oem_order_num": "",
    "oem_remain_num": "",
    "store_id": "",
    "remain_man_id": "",
    "remain_date": "", //业务日期
    "pdt_list": [],
    "note": "",
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:['保存']
    }, //审核决策
    fsm_cur_state:'订单创建',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)




//获取最新ID
const onChangeID = async()=>{
    const ret = await getOemRemainNewnumApi()
    if(ret)
    {
        console.log(ret)
        remainData.oem_remain_num = ret.data.new_id
    }
}



onMounted(async () => {    

    await getStoreList()
    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('remain.add')
        onChangeID()

        //新建需要默认可以提审
        const tmp = [...remainData.fsm_can_trig_data.操作触发,'提交审核']
        remainData.fsm_can_trig_data.操作触发 = tmp

        //克隆必要信息
        remainData.oem_order_num  = currentRoute.value.query.oem_order_num as string
        remainData.pdt_biaoshi  = currentRoute.value.query.pdt_biaoshi
        remainData.oem_order_sub = currentRoute.value.query.pdt_biaoshi
        remainData.parter_nick  = currentRoute.value.query.parter_nick
        const info = wsCache.get(appStore.getUserInfo)
        remainData.remain_man_id = info.id
        remainData.remain_man_name = info.resident_name
        remainData.remain_date = getTodayDate()

        updatePdtStock()
    }
    else
    {        
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('remain.look')
        }
        else
        {
            title.value = t('remain.modify')
        }
        
        //查询信息 
        const ret = await getOemRemainInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(remainData, ret.data)
            remainData.pdt_list = ret.data.pdt_list;
        }
        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }



    console.log(remainData)
    
})

//校验输入
const recomputeCount = (pdt)=>{
    
    if(pdt.返料数量>pdt.委外商可用库存)
        pdt.返料数量 = pdt.委外商可用库存
}



//添加出库产品
const showSelOutPdt = ref(false)
const onAddPdt = ()=>{
    showSelOutPdt.value = true
}
//选择产品回调
const onSelOutPdtCallback = (pdt)=>{
    remainData.pdt_list.push(pdt)
}

//删除某一个产品
const onDelPdt = (index)=>{
    remainData.pdt_list.splice(index,1)
}



//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)

    remainData.store_id = storeData[0].id

    //updatePdtStock(remainData.store_id)
  }

}

//产品仓库分布数据
// const detailData  = reactive([])
//更新pdt库存
const updatePdtStock = async () => {

    const ret = await getOemLockListApi({              
                oem_order_num:remainData.oem_order_num,
                pdt_biaoshi:remainData.pdt_biaoshi,
                page: 1,
                count: 1000
            })
    if(ret)
    {
        remainData.pdt_list.splice(0,remainData.pdt_list.length, ...ret.data)
        for(let pdt of remainData.pdt_list)
        {
            pdt.返料数量 = 0
            pdt.返料备注 = ''
        }
    }

    console.log(remainData.pdt_list)
}

// //切换仓库更新库存
// const onChangeStore = async (store_id) => {
//     updatePdtStock(store_id)
// }

//显示隐藏选择出库员窗口变量
const showSelOutUserDlg = ref(false)
//显示选择出库员弹窗
const onSelOutUser = ()=>{
    showSelOutUserDlg.value = true
}
//选择出库员回调
const onSelReturnCallback = (id,name)=>{
    console.log(id,name)
    remainData.remain_man_id = id
    remainData.remain_man_name = name
}


//保存
const onSave = async()=>{
    console.log(remainData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }


    //提交时自动过滤数量为0或者为空的产品
    // remainData.pdt_list = remainData.pdt_list.filter(item=>parseFloat(item.返料数量)>0) 
    const tmp = cloneDeep(remainData)
    tmp.pdt_list = tmp.pdt_list.filter(item=>parseFloat(item.返料数量)>0) 
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }

    console.log('----',tmp)
    
    if(tmp.id == undefined)
    {
        const ret = await addOemRemainApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newReceiptSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret =await updateOemRemainApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateReceiptSuccess'))
            back()
        }
    }

    //用户要求不刷新
    // closeOneTagByPath('/oemmanage/oemorderlist')

}
//校验pdt
const checkPdt = ()=>{
    if(remainData.pdt_list.length <= 0)
    {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}

//提交审核意见
const handleCheck = async(btn)=>{
    console.log(11)
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }


    const info = wsCache.get(appStore.getUserInfo)
    remainData.fsm_exe_man_name = info.resident_name
    remainData.fsm_exe_trig = btn

    const tmp = cloneDeep(remainData)
    tmp.pdt_list = tmp.pdt_list.filter(item=>parseFloat(item.返料数量)>0) 
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }



    if(remainData.id == undefined)
    {
        const ret = await addOemRemainApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newOemOrderSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret =await updateOemRemainApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateOemOrderSuccess'))
            back()
        }
    }
}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = ()=>{
    showCheckHisDlg.value = true
}
</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />任务历史</ElButton>
        </template>
        <template #right>
            <ElButton color="#409EFF" style="color: #fff;" v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && remainData.fsm_can_trig_data.操作触发.includes('保存')" @click="handleCheck('保存')">
                <Icon class="mr-0.5" icon="carbon:save" />
                保存
            </ElButton>
            <el-popconfirm  title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && remainData.fsm_can_trig_data.操作触发.includes('提交审核')"   type="success" >
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        提交审核
                    </ElButton>
                </template>
            </el-popconfirm>
            
            <el-popconfirm  title="是否确认关闭订单?" @confirm="handleCheck('关闭')">
                <template #reference>
                    <ElButton  v-show="currentRoute.query.cmd != '审核' && remainData.fsm_can_trig_data.操作触发.includes('关闭')" type="danger">
                        <Icon class="mr-0.5" icon="carbon:close-outline" />
                        关闭订单
                    </ElButton>
                </template>
            </el-popconfirm>
        </template>
        <el-card id="check" v-if="remainData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%]">
            <template #header>
            <div class="flex items-center">
                <span>当前节点:</span>
                <span class="text-red-500 mr-3">{{ remainData.fsm_cur_state }}</span>
                <!-- <ElButton @click="handleCheck(btn)" v-for="btn in purchaseData.fsm_can_trig_data.审核触发.toReversed()" :key="btn" :type="btn=='同意'?'success':'danger'">{{ btn }}</ElButton> -->
                <ElButton v-show="remainData.fsm_can_trig_data.审核触发.includes('同意')" type="success" @click="handleCheck('同意')" >同意</ElButton>
                <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="remainData.fsm_can_trig_data.审核触发.includes('驳回')" type="danger" >驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="remainData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                    </template>
                </el-popconfirm>

            
            </div>
            </template>
            <el-input v-model="remainData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        </el-card>
        <el-form :rules="rules" :model="remainData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('takeout.id')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input class="mr-2" v-model="remainData.oem_remain_num" :disabled="remainData.id!=undefined" />
                            <ElButton v-if="remainData.id==undefined" type="warning"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="t('oem.name')"
                    class="flex">
                        <div>{{ remainData.oem_order_num }}</div> 
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('parter.nick')"
                    class="flex">
                        {{ remainData.parter_nick }}
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('store.store')"
                    class="flex">
                    <el-select v-model="remainData.store_id" placeholder="Select" >
                        <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('saleout.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="remainData.remain_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('remain.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ remainData.remain_man_name }}</div> 
                        <!-- <ElButton @click="onSelOutUser">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton> -->
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        
        </el-form>
        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="remainData.pdt_list" style="width: 100%" border stripe>
            <el-table-column  :label="t('process.opt')" width="60" >
                <template #default="scope">
                    <div  type="primary">
                        <el-popconfirm v-if="currentRoute.query.type != 'info'" title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>
                        
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index+1 }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div  class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130"/>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip                        
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="base_unit" :label="t('product_manage.b_unit')" />
            <el-table-column show-overflow-tooltip  prop="委外商库存" :label="t('takeout.parter_stone')" />
            <el-table-column show-overflow-tooltip  prop="委外商可用库存" :label="t('remain.parter_val_stone')" />
            <el-table-column show-overflow-tooltip  prop="委外商此单锁定" :label="t('remain.parter_cur_lock')" />

            
            <el-table-column show-overflow-tooltip  :label="t('remain.count')">
                <template #default="scope">
                    <el-input  v-model="scope.row.返料数量" @input="recomputeCount(scope.row)" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  :label="t('saleout.remark')">
                <template #default="scope">
                    <el-input  v-model="scope.row.返料备注" />
                </template>
            </el-table-column>
        </el-table>

        <!-- 显示合计 -->
        <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">发货备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="remainData.note" clearable :autosize="{ minRows: 3, maxRows: 3 }" type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>


    <div class="mb-20"></div>

    <!-- 选择发货领取员 -->
    <DialogUser :param="''" v-model:show="showSelOutUserDlg" :title="t('msg.selectUser')" @on-submit="onSelReturnCallback"/>
        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="remainData.fsm_log_list" />

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
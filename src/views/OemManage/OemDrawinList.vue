<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDatePicker,  ElButton,  ElMessage, ElMessageBox, ElImage,  ElInput, ElSelect, ElOption, ElCheckbox, ElDropdown, ElDropdownItem,  ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getOemDrawinListApi,delOemDrawinApi } from '@/api/product'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { ceilToFixed, checkPermissionApi, downloadFile } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { exportOemReceiptListApi } from '@/api/extra';

const { currentRoute,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//返料数据源
const drawinData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  oem_drawin_num:'',
  oem_order_num: '',
  任务编号:'',
  产品编号:'',
  产品名称:'',
  质检状态:'',
  parter_nick:'',
  drawin_man_name:'',
  良品状态:'',
  不良品状态:'',
  产品规格:'',
  收货备注:'',
  收货时间:['',''],
  质检时间:['',''],
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getOemDrawinList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getOemDrawinList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getOemDrawinList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
  //编辑产品
  if(type === 'edit' ||type == 'info')
  {
    const retPath =currentRoute.value.name === 'QualityOemDrawinList'?'/oemqualitymanage/qualityaddoemdrawin':((currentRoute.value.name?.indexOf("Stone")>=0)?'/oeminventorymanage/stoneaddoemdrawin':'/oemmanage/addoemdrawin')
   
    push({
      path: retPath,
      query:{
          id:item.id,
          type:type
      }
    })
  }
  else if(type === 'del') 
  {
    ElMessageBox.confirm(t('msg.confirm_del_drawin'), t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)

      const ret = await delOemDrawinApi({
        ids: [item.id],
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getOemDrawinList()
      }
    }
    ).catch(() => {})
  }
  else if(type === 'qualitycheck')
  {
    const retPath =currentRoute.value.name === 'QualityOemDrawinList'?'/oemqualitymanage/qualityaddoemqualitycheck':'/oemmanage/addoemqualitycheck'
   
    push({
      path: retPath,
      query:{
        oem_drawin_num:item.oem_drawin_num
        }
    })
  }
  else if(type === 'quality_list')
  {
    const retPath =currentRoute.value.name === 'QualityOemDrawinList'?'/oemqualitymanage/qualityoemqualitychecklist':'/oemmanage/oemqualitychecklist'
   
    push({
      path: retPath,
      query:{
        oem_drawin_num:item.oem_drawin_num
      }
    })
  }
  else if(type === 'good_putin')
  {
    const retPath = (currentRoute.value.name?.indexOf("Stone")>=0)?'/oeminventorymanage/stoneaddoemputin':'/oemmanage/addoemputin'
    
    push({
      path: retPath,
      query:{
          oem_drawin_num:item.oem_drawin_num,
          type:'良品库'
        }
    })
  }
  else if(type === 'bad_putin')
  {
    const retPath = (currentRoute.value.name?.indexOf("Stone")>=0)?'/oeminventorymanage/stoneaddoemputin':'/oemmanage/addoemputin'
    
    push({
      path: retPath,
      query:{
            oem_drawin_num:item.oem_drawin_num,
            type:'不良品库'
        }
    })
  }
}



//查询销售出库单列表
const getOemDrawinList = async (page = 1) => {
  searchCondition.page = page
  const tmp = cloneDeep(searchCondition)
  
  tmp.收货时间 = searchCondition.收货时间[0]+','+searchCondition.收货时间[1]
  tmp.质检时间 = searchCondition.质检时间[0]+','+searchCondition.质检时间[1]
  isLoading.value = true
  const ret = await getOemDrawinListApi(tmp)
  if(ret)
  {
    drawinData.splice(0,drawinData.length, ...ret.data)
    console.log(drawinData)
    for (let one of drawinData) {
      one.checked = false
    }
    totleCount.value = parseInt( ret.count)
  }
  isLoading.value = false
}

onMounted(() => {

  if(currentRoute.value.name == 'QualityOemDrawinList')
  {
    searchCondition.质检状态 = '未质检+部分质检'
  }

    if(currentRoute.value.query.oem_drawin_num != undefined)
      searchCondition.oem_drawin_num = currentRoute.value.query.oem_drawin_num
    if(currentRoute.value.query.oem_order_num != undefined)
      searchCondition.oem_order_num = currentRoute.value.query.oem_order_num
    if(currentRoute.value.query.任务编号 != undefined)
      searchCondition.任务编号 = currentRoute.value.query.任务编号
      getOemDrawinList()  

        //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})

const tableColCss = [
    'w-[85%] ',
    'w-[15%] ',
    'w-[15%] ',
    'w-[15%] ',
    'w-[15%] ',
    'w-[15%] ',
    'w-[15%] ',
    'w-[15%] ',
    'w-[15%] ',
    'w-[15%] ',
    'w-[15%] ',
]



const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: '委外收货单' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}



const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    const tmp = cloneDeep(searchCondition)
  
  tmp.收货时间 = searchCondition.收货时间[0]+','+searchCondition.收货时间[1]
  tmp.质检时间 = searchCondition.质检时间[0]+','+searchCondition.质检时间[1]
    const ret = await exportOemReceiptListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}

//批量打印
const toPrintPageMul = () => {
  //拿到所有勾选
  let arrayAll = drawinData.filter((one) => one.checked)
  if (arrayAll.length == 0)
  {
    ElMessage.warning('请先勾选要打印的单据！')
    return
  }
  console.log(arrayAll)
  let params = []
  for(let one of arrayAll)
  {
    params.push({ ...one, printType: '委外收货单' })
  }

  sessionStorage.setItem('printInfo', JSON.stringify(params))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}

const checkAll = ref(false)
const onCheckedAll = () => {
  for (let one of drawinData)
  {
    one.checked = checkAll.value
  }
}

const isLoading = ref(false)
</script>

<template>
    <!-- 销售出库单列表 -->
    <div ref="rootRef">
      <div class="p-7 pt-0">
        <div  class="pt-7 pr-5 pl-5 pb-1 mb-1 bg-white relative">
          <div class="absolute top-3 left-10">
            <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                <Icon icon="carbon:export" />
                <div class="pl-2">{{ t('button.export') }}</div>
            </ElButton>
          </div>
          <div class="text-center mb-5 font-bold">{{ t('drawin.list') }}</div>
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">收货单号</div>
          <el-input size="small" v-model="searchCondition.oem_drawin_num" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">委外单号</div>
          <el-input size="small" v-model="searchCondition.oem_order_num" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">任务编号</div>
          <el-input size="small" v-model="searchCondition.任务编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('product_manage.id') }}</div>
          <el-input size="small" v-model="searchCondition.产品编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('product_manage.name') }}</div>
          <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
        </div>

        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">质检状态</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.质检状态" placeholder="" >
            <el-option v-for="item in ['未质检+部分质检','未质检','部分质检','完全质检','部分质检+完全质检']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">受托商</div>
          <el-input size="small" v-model="searchCondition.parter_nick" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">收货人员</div>
          <el-input size="small" v-model="searchCondition.drawin_man_name" placeholder="" class="searchItem" />
        </div>

        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">良品状态</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.良品状态" placeholder="" >
            <el-option v-for="item in ['良品未入库+良品部分入库','良品未入库','良品部分入库','良品完全入库']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">不良品状态</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.不良品状态" placeholder="" >
            <el-option v-for="item in ['不良品未入库+不良品部分入库','不良品未入库','不良品部分入库','不良品完全入库']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品规格</div>
          <el-input size="small" v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">收货备注</div>
          <el-input size="small" v-model="searchCondition.收货备注" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">收货时间</div>
          <el-date-picker size="small" class="searchItem" v-model="searchCondition.收货时间" type="daterange"
            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
        <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">质检时间</div>
          <el-date-picker size="small" class="searchItem" v-model="searchCondition.质检时间" type="daterange"
            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
          
          <div  class="flex justify-end items-center mr-6 mt-1 mb-1">
            <el-checkbox :label="t('customer.senior')" v-model="senior" size="small"/>
            <ElButton class="ml-5"  type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <div class="flex mb-2">
          <div class="flex text-sm items-center mr-3">
            <div class="title_unquality w-[14px] rounded-[50%] mr-1 h-[14px]"></div>
            未质检
          </div>
          <div class="flex text-sm items-center mr-3">
            <div class="title_partial w-[14px] rounded-[50%] mr-1 h-[14px]"></div>
            部分质检
          </div>
          <div class="flex text-sm items-center mr-3">
            <div class="title_quality w-[14px] rounded-[50%] mr-1 h-[14px]"></div>
            已质检
          </div>
          <div class="flex text-sm items-center mr-3">
            <div class="title_ok w-[14px] rounded-[50%] mr-1 h-[14px]"></div>
            已入库
          </div>
          <ElButton size="small" type="success" class="ml-auto" plain @click="toPrintPageMul">
              <Icon title="打印" icon="material-symbols:print-outline"/>
              批量打印
            </ElButton>
        </div>
        <!-- 产品列表 -->
        <div v-loading.lock="isLoading">
            <!-- 表头 -->
            <div class="flex header headerBk">      
              <div class="w-[60%] flex">
                <ElCheckbox class="min-w-[14px] !h-[14px] ml-5" v-model="checkAll" @change="onCheckedAll" />        
                <div class="min-w-[50%] text-center">产品名称</div>
                <div class="min-w-[10%] text-center">收货数量</div>
                <div class="min-w-[10%] text-center">质检数量</div>
                <div class="min-w-[10%] text-center">已入库</div>
                <div class="min-w-[10%] text-center">单价</div>
                <div class="min-w-[10%] text-center">金额</div>
              </div>
              <div class="flex flex-grow !p-0">
                <div class="rightcss rightcss_title">质检状态</div>
                <div class="rightcss rightcss_title">入库状态</div>
                <div class="rightcss rightcss_title">备注</div>
                <div class="rightcss_title !min-w-[70px]">操作</div>
              </div>
            </div>
           
            <!-- 表内容 -->
            <div class="mt-3 bg-white" v-for="item in drawinData" :key="item.id" style="box-shadow:var(--el-box-shadow-lighter);">
              <!-- 内容头 -->
              <div>
                <div class="p-2 flex flex-nowrap text-[13px] border-b-1px font-bold">
                  <ElCheckbox v-model="item.checked" />
                  <div class="ml-2 flex items-center mr-1 w-80px flex-grow-0 flex-shrink-0" >
                    <div class="rounded p-1 pl-2 pr-2" style="color: #fff;" :class="{'title_unquality': item.质检状态 === '未质检', 'title_partial': item.质检状态 === '部分质检','title_quality':(item.质检状态 === '完全质检'&&(item.已入库良品+item.已入库不良品)<item.收货数量) , 'title_ok': (item.已入库良品+item.已入库不良品)>=item.收货数量, 'title_over': item.质检状态 === '超量质检'}">
                      {{ item.质检状态 }}
                    </div>
                  </div>
                  <div class="w-[60%] min-w-[300px] flex items-center">
                        <div class="mr-4 font-bold">
                            {{ item.create_date.split(' ')[0] }}
                        </div> 
                        <div class="mr-4">收货单编号: {{item.oem_drawin_num }}</div>
                        <div>委外单编号: <span class="cursor-pointer text-blue-400" @click="onSelOem(item.oem_order_num)">{{ item.oem_order_num }}</span></div>
                    </div>
                  <div class="min-w-[200px] flex items-center">
                    <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                    {{ checkPermissionApi('受托商名称显示')?item.parter_nick:'***' }}
                  </div>
                  <div class="flex justify-center items-center min-w-[250px]">
                    <div class="mr-5">收货人:{{ item.drawin_man_name }}</div>
                  </div>
                  <!-- 靠右的其他信息 -->
                  <div class="ml-auto flex justify-center items-center min-w-[200px]">
                <Icon title="打印" style="scale: 1.5; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;" icon="material-symbols:print-outline" 
                      @click="toPrintPage(item)"
                    />

              </div>
                </div>
              </div>
              <!-- 内容体 -->
              <div class="flex">
                <!-- 左边产品列表 -->
                <div class="w-[60%]  table_self">
                  <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%] " :class="{'bc':pdt.不良品数量>0}"   >
                    <div class="min-w-[40%] text-center flex-grow ">
                      <div class="flex justify-start items-center w-[100%] p-1">
                        <el-image v-if="pdt.pics.length>0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" :src="pdt.pics[0].url" />
                        <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" src="/nopic.jpg" />
                        <div class="inline-block text-left max-w-[100%] ml-2">
                          <div style="white-space: normal;" class="nameStyle" @click="handleOper('info', item)">{{ '['+pdt.name+']'+pdt.nick}}</div>
                        </div>
                      </div>
                    </div>                  
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">{{ pdt.收货数量+' '+pdt.base_unit }}</div>
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px" v-if="pdt.不良品数量<=0">{{ pdt.已质检+' '+pdt.base_unit }}</div>
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px" v-if="pdt.不良品数量>0">{{ pdt.良品数量+pdt.base_unit+'(不良:'+pdt.不良品数量+')' }}
                      <div class="ex_text" :class="{'bc':pdt.不良品数量>0}">{{ pdt.良品率 = ((pdt.良品数量 / pdt.已质检) * 100).toFixed(2) }}%</div>  
                    </div>
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">{{ pdt.已入库良品+pdt.已入库不良品+' '+pdt.base_unit }}</div>
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">                      
                        {{ checkPermissionApi('委外订单价格显示')?('￥'+pdt.oem_price_bef_tax+'/'+pdt.oem_price_aft_tax):'*' }}
                        <div class="ex_text" :class="{'bc':pdt.不良品数量>0}">含税{{ pdt.发票税率 }}%</div>                      
                    </div>
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">{{checkPermissionApi('委外订单价格显示')?('￥'+ceilToFixed(pdt.oem_price_aft_tax*pdt.收货数量,4,0)):'*'}}</div>
                  </div>
                </div>
                <!-- 右边其他数据 -->
                <div class="flex flex-grow text-center  right">
                  <div class="rightcss flex items-center" style="justify-content: start;">
                    <div>{{ item.质检状态 }}</div>
                    <div>{{ item.质检时间 }}</div>
                    <div>{{ item.pdt_list[0].最近质检方式 }}</div>
                  </div>
                  <div class="rightcss flex justify-start" style="justify-content: start;">
                    <div v-if="(item.质检状态 == '完全质检'||item.质检状态 == '超量质检')&&item.良品数量>0">良品入库状态:{{ parseInt(item.已入库良品/item.良品数量*100)+'%' }}</div>
                    <div v-if="(item.质检状态 == '完全质检'||item.质检状态 == '超量质检')&&item.不良品数量>0">不良品入库状态:{{ parseInt(item.已入库不良品/item.不良品数量*100)+'%'}}</div>
                    <div v-if="item.质检状态 != '完全质检'&&item.质检状态 != '超量质检'">等待质检</div>
                  </div>
                  <div class="rightcss flex justify-start" style="justify-content: start;">{{ item.note }}</div>
                  <div>
                    <el-dropdown trigger="click" placement="bottom">
                      <span class="el-dropdown-link">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                      </span>
                      <template #dropdown>
                        <div class="flex flex-wrap w-[280px]">
                          <el-dropdown-item :disabled ="item.质检状态 == '完全质检'" @click="handleOper('qualitycheck', item)">{{ t('quality.check') }}</el-dropdown-item>
                          <el-dropdown-item @click="handleOper('quality_list', item)">{{ t('quality.check_count') +'('+item.check_count+')'}}</el-dropdown-item>
                          <div class="flex flex-wrap w-[280px]" v-show='currentRoute.name != "QualityOemDrawinList"'>
                            <el-dropdown-item v-if="item.质检状态 == '完全质检'" :disabled="item.已入库良品>=item.良品数量" @click="handleOper('good_putin', item)">{{ t('putin.good_putin') }}</el-dropdown-item>
                            <el-dropdown-item v-if="item.质检状态 == '完全质检'" @click="handleOper('good_putin_list', item)">{{ t('putin.good_putin_list')+'('+item.putin_good_count+')' }}</el-dropdown-item>      
                            <el-dropdown-item v-if="item.质检状态 == '完全质检'" :disabled="item.已入库不良品>=item.不良品数量" @click="handleOper('bad_putin', item)">{{ t('putin.bad_putin') }}</el-dropdown-item>                                              
                            <el-dropdown-item v-if="item.质检状态 == '完全质检'" @click="handleOper('bad_putin_list', item)">{{ t('putin.bad_putin_list') +'('+item.putin_bad_count+')'}}</el-dropdown-item>
                          </div>
                          
                          <el-dropdown-item @click="handleOper('info', item)">查看</el-dropdown-item>
                          <el-dropdown-item v-if='(item.已入库良品+item.已入库不良品)<=0 && currentRoute.name.indexOf("Quality")<0' @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                        </div>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
        </div>

      </div>

      <el-pagination class="flex justify-end mb-4"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
    <!-- <div class="h-[300px]"></div> -->
    
    <PrintModal v-model:show="dialogVisible" />
    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  cursor: pointer;
}
.nameStyle:hover{
  color: rgb(130, 130, 255);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  color: #333;
  font-weight: bold;
}
.headerBk{
  background-color: #fff;
}
.content{
  &:extend(.header);
  font-size: 14px;
}
.bc{
  color: #F56C6C !important;
}
.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: center; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}
:deep(.el-checkbox__input){
  border: 1px solid #999;
}

//---------------列表对象标题栏条件颜色-------------------
.title_unquality{ //未收货
  background-color:#79bbff;
}
.title_partial{ //部分收货
  background-color: #eebe77;
}
.title_ok{ //完全收货
  background-color: #b1b3b8;
}
.title_quality{
  background-color: #95d475;
}
.title_over{//超量收货
  background-color: #f89898;
}
</style>

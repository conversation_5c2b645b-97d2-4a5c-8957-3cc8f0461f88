<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElOption,ElSelect,ElTooltip,ElTable,ElPopconfirm,ElTag,ElDatePicker,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogProductSel } from '@/components/DialogProductSel'
import {checkFormRule, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { addOemTakeOutApi,updateOemTakeOutApi,getOemLockListApi,getOemDemandListApi,getOemTakeOutInfoApi,getOemTakeOutNewnumApi,getOemOrderInfoApi,getInventoryListApi,getStoreListApi,updateSaleOutApi,addSaleOutApi,getSaleOutInfoApi,getSaleOutNewnumApi,getSaleInfoApi } from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { DialogSelSaleOutPdt } from '@/components/DialogSelSaleOutPdt'
import { cloneDeep } from 'lodash-es'


const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})




//销售单数据
const saleData = reactive({})

//出库单数据
const outData = reactive(
{
    "oem_order_num": "",
    "oem_takeout_num": "",
    "store_id": "",
    "takeout_man_id": "",
    "takeout_date": "",
    "pdt_list": [],
    "note": "",
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:[]
    }, //审核决策
    fsm_cur_state:'',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)




//获取最新ID
const onChangeID = async()=>{
    const ret = await getOemTakeOutNewnumApi()
    if(ret)
    {
        console.log(ret)
        outData.oem_takeout_num = ret.data.new_id
    }
}



onMounted(async () => {    

    await getStoreList()
    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('oem.add_takeout')
        onChangeID()
        //克隆必要信息
        outData.oem_order_num  = currentRoute.value.query.oem_order_num
        outData.pdt_biaoshi = currentRoute.value.query.pdt_biaoshi
        outData.oem_order_sub = currentRoute.value.query.pdt_biaoshi
        outData.子任务单号    = currentRoute.value.query.子任务单号
        const info = wsCache.get(appStore.getUserInfo)
        outData.takeout_man_id = info.id
        outData.takeout_man_name = info.resident_name
        outData.takeout_date = getTodayDate()

        updatePdtStock(outData.store_id)
    }
    else
    {        
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('oem.modify_takeout_look')
        }
        else
        {
            title.value = t('oem.modify_takeout')
        }


        //查询信息 
        const ret = await getOemTakeOutInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(outData, ret.data)
            outData.pdt_list = ret.data.pdt_list;
        }

        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }



    console.log(outData)
    
})

//校验输入
const recomputeCount = (pdt)=>{

    // const nMin = Math.min(pdt.所需数量,(pdt.仓库可用库存+pdt.仓库此单锁定))

    // if(pdt.发料数量>(pdt.仓库可用库存+pdt.仓库此单锁定)|| pdt.发料数量>pdt.所需数量)
    //     pdt.发料数量 = nMin
    if(pdt.发料数量>(pdt.仓库可用库存+pdt.仓库此单锁定))
    {
        pdt.发料数量 = pdt.仓库可用库存+pdt.仓库此单锁定
    }
}



//添加出库产品
const showSelOutPdt = ref(false)
const onAddPdt = ()=>{
    showSelOutPdt.value = true
}
//选择产品回调
const onSelOutPdtCallback = (pdt)=>{
    outData.pdt_list.push(pdt)
}

//删除某一个产品
const onDelPdt = (index)=>{
    outData.pdt_list.splice(index,1)
}



//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)

    outData.store_id = storeData[0].id

    // updatePdtStock(outData.store_id)
  }

}

//产品仓库分布数据
const detailData  = reactive([])
//更新pdt库存
const updatePdtStock = async (store_id:string) => {

    const ret = await getOemLockListApi({
                store_id:store_id,                
                oem_order_num:outData.oem_order_num,
                pdt_biaoshi:outData.pdt_biaoshi,
                page: 1,
                count: 1000
            })
    if(ret)
    {
        outData.pdt_list.splice(0,outData.pdt_list.length, ...ret.data)
        for(let pdt of outData.pdt_list)
        {
            pdt.发料数量 = Math.floor(Math.min(pdt.仓库可用库存,pdt.所需数量))
            pdt.发料备注 = ''
        }
    }

    console.log(outData.pdt_list)
}

//切换仓库更新库存
const onChangeStore = async (store_id) => {
    updatePdtStock(store_id)
}

//显示隐藏选择出库员窗口变量
const showSelOutUserDlg = ref(false)
//显示选择出库员弹窗
const onSelOutUser = ()=>{
    showSelOutUserDlg.value = true
}
//选择出库员回调
const onSelReturnCallback = (id,name)=>{
    console.log(id,name)
    outData.takeout_man_id = id
    outData.takeout_man_name = name
}


//保存
const onSave = async()=>{
    console.log(outData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //提交时自动过滤数量为0或者为空的产品
    const tmp = cloneDeep(outData)
    tmp.pdt_list = tmp.pdt_list.filter(item=>parseFloat(item.发料数量)>0) 
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }



    //增加审核流程参数
    const info = wsCache.get(appStore.getUserInfo)
    tmp.fsm_exe_man_name = info.resident_name
    tmp.fsm_exe_trig = '直接出库'

    
    if(tmp.id == undefined)
    {
        const ret = await addOemTakeOutApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newReceiptSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret =await updateOemTakeOutApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateReceiptSuccess'))
            back()
        }
    }
    //不刷新OEM列表，用户要求
    // closeOneTagByPath('/oemmanage/oemorderlist')

}
</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <el-popconfirm v-if="outData.id==undefined"  title="是否确认发料?操作将影响库存！" @confirm="onSave">
                <template #reference>
                    <ElButton  type="primary">
                        提交
                    </ElButton>
                </template>
            </el-popconfirm>
        </template>
        <el-form :rules="rules" :model="outData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('takeout.id')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  v-model="outData.oem_takeout_num" :disabled="outData.id!=undefined" />
                            <ElButton v-if="outData.id==undefined" type="warning"  @click="onChangeID">{{ t('button.update') }}</ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="t('oem.name')"
                    class="flex">
                        <div>{{ outData.oem_order_num }}</div> 
                </el-descriptions-item>
<!-- 
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('parter.nick')"
                    class="flex">
                        {{ saleData.buyer_nick }}
                </el-descriptions-item> -->
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('store.store')"
                    class="flex">
                    <el-select v-model="outData.store_id" placeholder="Select" @change="onChangeStore(outData.store_id)">
                        <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('saleout.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="outData.takeout_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('return.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ outData.takeout_man_name }}</div> 
                        <!-- <ElButton @click="onSelOutUser">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton> -->
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        
        </el-form>
        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="outData.pdt_list" style="width: 100%" border stripe>
            <el-table-column  :label="t('process.opt')" width="60" >
                <template #default="scope">
                    <div  type="primary">
                        <el-popconfirm v-if="currentRoute.query.type != 'info'" title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon  icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>
                        
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index+1 }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div  class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" min-width="110"/>
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick}}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip                        
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="base_unit" :label="t('product_manage.b_unit')" />
            <el-table-column show-overflow-tooltip  prop="仓库可用库存" :label="t('inventory.avali_count')" />
            <el-table-column show-overflow-tooltip  prop="单个用量" :label="t('takeout.one_use')" />
            <el-table-column show-overflow-tooltip  prop="计划数量" :label="t('takeout.jh_count')" >
                <template #default="scope">
                    {{ Math.ceil(scope.row.计划数量) }}
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="实际发料数量" :label="t('takeout.actual_count')" />
            <el-table-column show-overflow-tooltip  prop="委外商库存" :label="t('takeout.parter_stone')" />
            <el-table-column show-overflow-tooltip fixed="right" prop="仓库此单锁定" :label="'仓库'+t('takeout.cur_lock')" />
            <el-table-column show-overflow-tooltip fixed="right" prop="所需数量" :label="t('takeout.need')" >
                <template #default="scope">
                    {{ Math.floor(scope.row.所需数量) }}
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed="right"  :label="t('takeout.out')">
                <template #default="scope">
                    <el-input  v-model="scope.row.发料数量" @input="recomputeCount(scope.row)" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed="right"  :label="t('saleout.remark')">
                <template #default="scope">
                    <el-input  v-model="scope.row.发料备注" />
                </template>
            </el-table-column>
        </el-table>

        <!-- 显示合计 -->
        <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">发货备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="outData.note" clearable :autosize="{ minRows: 3, maxRows: 3 }" type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>


    <div class="mb-40"></div>

    <!-- 选择发货领取员 -->
    <DialogUser :param="''" v-model:show="showSelOutUserDlg" :title="t('msg.selectUser')" @on-submit="onSelReturnCallback"/>


    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getStoreListApi,getParterInventoryListApi, addCategApi, updateCategApi, delCategApi,getInventoryListApi, updateProductApi,delProductApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { RightMenu } from '@/components/RightMenu'
import { Dialog } from '@/components/Dialog'
import { computed,nextTick } from 'vue';
import { useCache } from '@/hooks/web/useCache'
import {getParterListApi} from '@/api/customer'
import { checkPermissionApi } from '@/api/tool';
const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

//分类树
const categTree = ref()
//当前选中分类节点
const currentCatg = ref('')
//当前选中分类节点详细信息
const currentCatgData = ref({})
//分类树数据源
const categData = reactive([])
//分类树默认属性
const defaultProps = {
  children: 'list',
  label: 'parter_nick',
}
//分类树默认展开节点
const expandCateg = reactive([])
//分类树右键菜单classname
const categMenuClassName = 'categ_menu'
//分类右键菜单
const menuCateg = reactive([])

//库存数据
const inventorytData = reactive([])

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  parter_id:'',
  page: 1,
  count: 1000
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//查询产品分类树
const getCategList = async () => {
  const ret = await getParterListApi({
    page: 1,
    count: 10000
  })
  if (ret) {
    console.log(ret)
    console.log('当前选中')

    //设置默认选中
    const lastSelCateg = wsCache.get('last_sel_parter') || {}
    console.log(categTree.value)
    console.log(lastSelCateg.id)
    currentCatgData.value = lastSelCateg
    currentCatg.value = lastSelCateg.id
    searchCondition.parter_id = lastSelCateg.id
    if(lastSelCateg.id != undefined)
    {
      nextTick(()=>{
        categTree.value.setCurrentNode(lastSelCateg)
        console.log(categTree.value?.getCurrentNode())
      })
    }

    let param = {
      parter_nick:'所有',
      id:0,
      list:[]
    }
    param.list = ret.data
    categData.splice(0, categData.length,param)
    //设置默认展开
    expandCateg.splice(0, expandCateg.length, 0)
    if (currentCatg.value) {
      expandCateg.push(currentCatg.value)
    }
  }
}
//分类树点击左键
const leftClick = (data) => {
  console.log(data,data.id)
  currentCatgData.value = data
  searchCondition.parter_id = data.id
  //更新最后一次选中
  wsCache.set('last_sel_parter',data)
  console.log(searchCondition)
  getParterInventoryList()
}

//开始查询
const onSearch = () => {
  getParterInventoryList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getParterInventoryList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getParterInventoryList(val)
}


//查询库存列表
const getParterInventoryList = async (page = 1) => {
  const lastSelCateg = wsCache.get('last_sel_parter') || {}
  searchCondition.parter_id = lastSelCateg.id
  searchCondition.page = page
  const ret = await getParterInventoryListApi(searchCondition)
  if(ret)
  {
    inventorytData.splice(0,inventorytData.length, ...ret.data)
    console.log(inventorytData)
    totleCount.value = parseInt(ret.count)
  }
}

//查看库存明细
const onShowDetail = (item)=>{
    const lastSelCateg = wsCache.get('last_sel_parter') || {}
    console.log(item)
    const retPath = (currentRoute.value.name?.indexOf("Stone")>=0)?'/oeminventorymanage/stoneparterstonestatement':'/oemmanage/parterstonestatement'

    push({
        path: retPath,
        query:{
          parter_id:lastSelCateg.id,
          pdt_name:item.产品编码,
          pdt_nick:item.产品名称,
          pdt_id:item.id
        }
      })
}

onMounted(() => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //更新分类树
  getCategList()

  //更新库存列表
  getParterInventoryList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});

</script>

<template>
  <div class="flex">
    <!-- 左侧分类栏 -->
    <div class="w-[25%] max-w-[250px] bg-white p-2">
      <el-tree ref="categTree" :data="categData" :props="defaultProps" :default-expanded-keys="expandCateg" node-key="id"
         @node-click="leftClick" highlight-current :current-node-key="currentCatg"
        :expand-on-click-node="false" :render-after-expand="true">
        <template #default="{ node }">
          <Icon icon="bx:category" />
          <div class="pl-2">{{ checkPermissionApi('受托商名称显示')?node.data.parter_nick:node.data.parter_name  }}
          </div>
        </template>
      </el-tree>
    </div>
    <div class="w-2"></div>
    <!-- 右侧产品列表 -->
    <div ref="rootRef" class="relative w-[100%] !bg-white flex-grow overflow-hidden">
      <div class="h-[100%] bg-white p-7">
        <div class="text-center mb-5 font-bold">{{ t('inventory.list') }}</div>
        <!-- <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
          <div class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('store.nick') }}</div>
            <el-select class="ml-3 searchItem" v-model="searchCondition.store_id" placeholder="选择仓库" size="small" >
              <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
            </el-select>
          </div>
          <div class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input v-model="searchCondition.nick" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input v-model="searchCondition.name" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
            <el-input v-model="searchCondition.specs" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('product_manage.brand') }}</div>
            <el-input v-model="searchCondition.brand" placeholder="" size="small" class="searchItem" />
          </div>

          <div v-if="senior" class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('product_manage.short_name') }}</div>
            <el-input v-model="searchCondition.nick_brif" placeholder="" size="small" class="searchItem" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('product_manage.help_name') }}</div>
            <el-input v-model="searchCondition.note" placeholder="" size="small" class="searchItem" />
          </div>

          <div v-if="senior" class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('product_manage.buy_price') }}</div>
            <el-input v-model="searchCondition.buy_price_begin" placeholder="" size="small" class="!w-[60px]" />
            <div class="searchTitle">到</div>
            <el-input v-model="searchCondition.buy_price_end" placeholder="" size="small" class="!w-[60px]" />
          </div>
          <div  class="text-center mt-5 mb-2">
            <ElButton type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
            <el-checkbox class="ml-5" :label="t('customer.senior')" v-model="senior"/>
          </div>
        </div> -->
        <!-- 产品列表 -->
        <el-table ref="tableRef" :data="inventorytData" style="width: 100%; margin-bottom: 20px" row-key="id" border stripe
          highlight-current-row header-cell-class-name="tableHeader">
          <el-table-column show-overflow-tooltip align="center"  prop="产品编码" :label="t('product_manage.id')" />
          <el-table-column show-overflow-tooltip align="center"  prop="产品名称" :label="t('product_manage.name')" />
          <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip                        
                        class="box-item"
                        effect="dark"
                        :content="scope.row.规格文本"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.规格名称 }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip align="center"  prop="单位" :label="t('product_manage.b_unit')" />
            

          <el-table-column show-overflow-tooltip align="center"  prop="库存数量" :label="t('inventory.count')" /> 
          <el-table-column show-overflow-tooltip align="center"  prop="锁定数量" :label="t('inventory.lock_count')" />
          
          <el-table-column show-overflow-tooltip align="center"  prop="可动用数量" :label="t('inventory.avalid_count')" />
          
          <el-table-column show-overflow-tooltip align="center" prop="货物价值(未税)" :label="t('inventory.price_bef_tax')" />
          <el-table-column show-overflow-tooltip align="center" prop="货物价值(含税)" :label="t('inventory.price_aft_tax')" />
          <el-table-column show-overflow-tooltip align="center"  prop="均价(未税)" :label="t('inventory.price_avr_bef_tax')" />
          <el-table-column show-overflow-tooltip align="center"  prop="均价(含税)" :label="t('inventory.price_avr_aft_tax')" />
          <el-table-column show-overflow-tooltip align="center"  prop="modify_date" :label="t('inventory.last_date')" min-width="140"/>
    
          <el-table-column show-overflow-tooltip fixed="right" align="center" width="80px" prop="name" :label="t('roleTable.opt')" >
            <template #default="scope">
              <ElButton type="primary" size="small" @click="onShowDetail(scope.row)">{{ t('userOpt.info') }}</ElButton>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          :page-sizes="[30, 100, 300]"
          :background="true"
          layout="sizes, prev, pager, next"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
      <div class="mb-[200px]"></div>
      </div>




    </div>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   font-size: 11px;
//   font-weight: 500;
// }

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e1f3d8 !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
}
.headerBk{
  background-color: #6d92b4 !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

</style>

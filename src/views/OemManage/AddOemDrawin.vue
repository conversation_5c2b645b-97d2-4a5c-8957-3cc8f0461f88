<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElInputNumber,ElOption,ElSelect,ElTooltip,ElTable,ElPopconfirm,ElTag,ElDatePicker,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogProductSel } from '@/components/DialogProductSel'
import {checkFormRule, closeOneTagByPath,closeOneTagByName} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getOemOrderInfoApi,addOemDrawinApi,updateOemDrawinApi,getOemLockListApi,getOemDrawinInfoApi,getOemDrawinNewnumApi, getStoreListApi} from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { DialogSelSaleOutPdt } from '@/components/DialogSelSaleOutPdt'
import { cloneDeep } from 'lodash-es'
import { getHashApi } from '@/api/extra'


const { currentRoute,back,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})




//收货单数据
const drawinData = reactive(
    {
    "oem_order_num": "",
    "oem_drawin_num": "",
    "oem_man_id": "",
    "drawin_date": "",
    "pdt_list": [],
    "note": "",
    fsm_can_trig_data:{
        审批触发:[],
        操作触发:['保存']
    }, //审批决策
    fsm_cur_state:'',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[],
    store_data: {'良品库': 0, '不良品库':0}
}
)




//获取最新ID
const onChangeID = async()=>{
    const ret = await getOemDrawinNewnumApi()
    if(ret)
    {
        console.log(ret)
        drawinData.oem_drawin_num = ret.data.new_id
    }
}


//缓存单产品列表
const pdtList = ref([])

onMounted(async () => {    
    await getStoreList()

    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('drawin.add')
        onChangeID()
        //克隆必要信息
        drawinData.oem_order_num  = currentRoute.value.query.oem_order_num as string
        drawinData.pdt_biaoshi  = currentRoute.value.query.pdt_biaoshi
        drawinData.oem_order_sub = currentRoute.value.query.pdt_biaoshi
        drawinData.parter_nick  = currentRoute.value.query.parter_nick
        const info = wsCache.get(appStore.getUserInfo)
        drawinData.drawin_man_id = info.id
        drawinData.drawin_man_name = info.resident_name
        drawinData.drawin_date = getTodayDate()

        //查询产品信息 
        const ret = await getOemOrderInfoApi({
            oem_order_num:currentRoute.value.query.oem_order_num==undefined?'':currentRoute.value.query.oem_order_num,
            page:1,
            count:100,
            realrole:'oem.order_list'
        })
        if (ret) {
            // // updatePdtStock()
            // const arPdts =  wsCache.get('drawin_list')
            for(let item of ret.data.pdt_list)
            {
                item.收货数量 =  (parseFloat(item.委外数量)+parseFloat(item.委外备品数量))-item.已收货+(item.不良退货+item.良品退货)
                if(item.收货数量<0)
                    item.收货数量 = 0
                item.收货备注 = ''
                item.重量 = ''
            }
            drawinData.pdt_list = ret.data.pdt_list
            Object.assign(pdtList.value, drawinData.pdt_list)

            //过滤收货数量大于等于采购数量的pdt
            drawinData.pdt_list = drawinData.pdt_list.filter(item => (item.已收货-(item.不良退货+item.良品退货)) < (item.委外数量+item.委外备品数量)); 
            console.log('112233',drawinData.pdt_list)
        }



    }
    else
    {   
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('drawin.look')
        }
        else
        {
            title.value = t('drawin.modify')
        }

        
        //查询信息 
        const ret = await getOemDrawinInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(drawinData, ret.data)
            drawinData.pdt_list = ret.data.pdt_list;
        }

        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }



    console.log(drawinData)
    
})

//校验输入
const recomputeCount = (pdt)=>{

    let nMax = ((parseFloat(pdt.委外数量)+parseFloat(pdt.委外备品数量)))*1-pdt.已收货+(pdt.不良退货+pdt.良品退货)
    if(pdt.收货数量>nMax)
    {
        pdt.收货数量 = Math.floor(nMax)
        ElMessage.warning('委外不允许超量收货！')
    }

    if(pdt.收货数量<0)
    {
        pdt.收货数量 = 0
    }
}


//添加收货产品
const showSelReceiptPdt = ref(false)
const onAddPdt = ()=>{
    showSelReceiptPdt.value = true
}
//选择产品回调
const onSelReceiptPdtCallback = (pdt)=>{
    //检测是否已经存在了
    const index = drawinData.pdt_list.findIndex(item=>item.标识==pdt.标识)
    console.log(index)
    if(index<0)
        drawinData.pdt_list.push(pdt)
    else
        ElMessage.warning('该产品已经存在')
}

//删除某一个产品
const onDelPdt = (index)=>{
    drawinData.pdt_list.splice(index,1)
}



//显示隐藏选择出库员窗口变量
const showSelOutUserDlg = ref(false)
//显示选择出库员弹窗
const onSelOutUser = ()=>{
    showSelOutUserDlg.value = true
}
//选择出库员回调
const onSelReturnCallback = (id,name)=>{
    console.log(id,name)
    drawinData.drawin_man_id = id
    drawinData.drawin_man_name = name
}


//保存
const onSave = async()=>{
    console.log(drawinData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //提交时自动过滤数量为0或者为空的产品
    // drawinData.pdt_list = drawinData.pdt_list.filter(item=>parseFloat(item.收货数量)>0) 
    const tmp = cloneDeep(drawinData)
    tmp.pdt_list = tmp.pdt_list.filter(item=>parseFloat(item.收货数量)>0) 
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }


    //增加审核流程参数
    const info = wsCache.get(appStore.getUserInfo)
    tmp.fsm_exe_man_name = info.resident_name
    tmp.fsm_exe_trig = '提交'
    
    if(tmp.id == undefined)
    {
        const ret = await addOemDrawinApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newReceiptSuccess'))
            closeOneTagByName(currentRoute.value.meta.title)
            closeOneTagByPath('/oemmanage/oemdrawinlist')
            // back()
            push({
                path: '/oemmanage/oemdrawinlist',
                query: {
                    oem_order_num: drawinData.oem_order_num,
                }
            })
        }
    }
    else //修改
    {
        const ret =await updateOemDrawinApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateReceiptSuccess'))
            closeOneTagByPath('/oemmanage/oemorderlist')
            back()
            
        }
    }


}



//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    storeData.splice(0,storeData.length,...res.data)

    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        //设置默认良品不良品仓库
        let a = storeData.find(item=>item.type=='良品库')
        drawinData.store_data.良品库 = storeData.find(item=>item.type=='良品库').id
        drawinData.store_data.不良品库 = storeData.find(item => item.type == '不良品库').id

        //如果有配置则根据配置设置默认仓库
        const ret = await getHashApi({
            name: '默认入库仓库',
            page: 1,
            count: 10000
        })
        if (ret) {
            if(ret.data.json != undefined)
            {
                drawinData.store_data.良品库 = ret.data.json.默认委外入库仓库
            }
        }
    }

  }

}



//选择某项后
let selRow = []
const handleSelectionChange = (val)=>{
  selRow = val
}
//批量删除选择产品
const delMul = () => {
    ElMessageBox.confirm('是否确认删除所选产品条目？', t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
        console.log(selRow)

        // 获取 selRow 中的所有 id
        const selRowIds = selRow.map(item => item.标识);
        drawinData.pdt_list = drawinData.pdt_list.filter(item => !selRowIds.includes(item.标识));
    }
    ).catch(() => { })
}

</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <ElButton color="#409EFF" style="color: #fff;" @click="onSave"  v-show="currentRoute.query.type != 'info' && drawinData.fsm_can_trig_data.操作触发.includes('保存')">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <el-form :rules="rules" :model="drawinData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('drawin.id')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  class="mr-1" v-model="drawinData.oem_drawin_num" :disabled="drawinData.id!=undefined" />
                            <ElButton v-if="drawinData.id==undefined" type="primary"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="t('oem.name')"
                    class="flex">
                        <div>{{ drawinData.oem_order_num }}</div> 
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('parter.nick')"
                    class="flex">
                        {{ drawinData.parter_nick }}
                </el-descriptions-item>


                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('drawin.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="drawinData.drawin_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('remain.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ drawinData.drawin_man_name }}</div> 
                        <!-- <ElButton @click="onSelOutUser">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton> -->
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'良品入库仓库'"
                    class="flex">
                    <el-select v-model="drawinData.store_data.良品库" placeholder="Select" >
                        <el-option v-for="item in storeData.filter(item => item.type === '良品库')" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'不良入库仓库'"
                    class="flex">
                    <el-select v-model="drawinData.store_data.不良品库" placeholder="Select" >
                        <el-option v-for="item in storeData.filter(item => item.type === '不良品库')" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
            </el-descriptions>
        
        </el-form>
        <div class="mt-2 mb-2">
            <!-- <ElButton type="success" @click="onAddPdt" v-show="currentRoute.query.type != 'info' && drawinData.fsm_can_trig_data.操作触发.includes('保存')">
                <Icon class="mr-0.5" icon="material-symbols:add"/>
                新增
            </ElButton> -->
            <ElButton plain @click="delMul">批量删除</ElButton>
        </div>
        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="drawinData.pdt_list" style="width: 100%" border stripe scrollbar-always-on @selection-change="handleSelectionChange">
            <el-table-column align="center"  type="selection" width="50" />
            <el-table-column  :label="t('process.opt')" width="60" >
                <template #default="scope">
                    <div  type="primary">
                        <el-popconfirm v-if="currentRoute.query.type != 'info' && drawinData.fsm_can_trig_data.操作触发.includes('保存')" title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>
                        
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index+1 }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div  class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" min-width="120" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick}}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip                        
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="base_unit" :label="t('product_manage.b_unit')" />
            <el-table-column show-overflow-tooltip  prop="委外数量" :label="t('oem.count')" />
            <el-table-column show-overflow-tooltip  prop="委外备品数量" label="备品" />
            <el-table-column show-overflow-tooltip  prop="已收货" :label="t('drawin.pre_count')" />
            <el-table-column show-overflow-tooltip  :label="t('drawin.return')" >
                <template #default="scope">
                    {{ scope.row.不良退货+scope.row.良品退货 }}
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="已入库" :label="t('drawin.instone')" />
            <el-table-column show-overflow-tooltip  prop="良品数" :label="t('drawin.good')" />
            <el-table-column show-overflow-tooltip  prop="不良数" :label="t('drawin.bad')" />
            <el-table-column show-overflow-tooltip fixed="right"  :label="t('drawin.count')">
                <template #default="scope">
                    <el-input-number style="width: 100%;" :controls="false" :min="0"  v-model="scope.row.收货数量" @blur="recomputeCount(scope.row)" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed="right"  label="重量"  >
                <template #default="scope">
                        <el-input v-model="scope.row.重量" class="!text-center"/>                  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed="right" :label="t('saleout.remark')">
                <template #default="scope">
                    <el-input  v-model="scope.row.收货备注" />
                </template>
            </el-table-column>
        </el-table>

        <!-- 显示合计 -->
        <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">发货备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="drawinData.note" clearable :autosize="{ minRows: 3, maxRows: 3 }" type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>


    <div class="mb-20"></div>

    <!-- 选择发货领取员 -->
    <DialogUser :param="''" v-model:show="showSelOutUserDlg" :title="t('msg.selectUser')" @on-submit="onSelReturnCallback"/>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}
:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
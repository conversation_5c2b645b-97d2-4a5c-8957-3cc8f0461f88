<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getOemQualityCheckListApi,delOemQualityCheckApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { DialogPurchaseSel } from '@/components/DialogPurchaseSel'
import { useAppStore } from '@/store/modules/app'
import { useCache } from '@/hooks/web/useCache'
import { checkPermissionApi, downloadFile } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { exportOemCheckListApi } from '@/api/extra';


const { currentRoute,push } = useRouter()
const { t } = useI18n()
const appStore = useAppStore()
const { wsCache } = useCache()

//质检单数据源
const qualityData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  oem_drawin_num:'',
  oem_check_num:'',
  oem_order_num:'',
  产品编号:'',
  产品名称:'',
  check_man_name:'',
  parter_nick:'',
  drawin_man_name:'',
  specs:'',
  质检时间:['',''],
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getOemQualityCheckList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getOemQualityCheckList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getOemQualityCheckList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
    console.log(item)
  //编辑
  if(type === 'edit' || type === 'info')
  {
    const retPath =currentRoute.value.name === 'QualityOemQualityCheckList'?'/oemqualitymanage/qualityaddoemqualitycheck':'/oemmanage/addoemqualitycheck'
   console.log(retPath)
    push({
      path: retPath,
      query:{
          id:item.id,
          type:type
      }
    })
  }
  else if(type === 'del')
  {
    ElMessageBox.confirm(t('msg.confirm_del_qualitycheck')+'--> '+item.oem_drawin_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)

      const ret = await delOemQualityCheckApi({
        ids: [item.id],
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getOemQualityCheckList()
      }
    }
    ).catch(() => {})
  }
}


//查询质检单列表
const getOemQualityCheckList = async (page = 1) => {
  searchCondition.page = page
  const tmp = cloneDeep(searchCondition)
  
  tmp.质检时间 = searchCondition.质检时间[0]+','+searchCondition.质检时间[1]

  const ret = await getOemQualityCheckListApi(tmp)
  if(ret)
  {
    qualityData.splice(0,qualityData.length, ...ret.data)
    console.log(qualityData)
    totleCount.value = parseInt(ret.count)
  }
}

onMounted(() => {
    if(currentRoute.value.query.oem_drawin_num != undefined)
        searchCondition.oem_drawin_num = currentRoute.value.query.oem_drawin_num as string
    getOemQualityCheckList()  

      //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})


const loading = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loading.value = true
    const tmp = cloneDeep(searchCondition)
  
    tmp.质检时间 = searchCondition.质检时间[0]+','+searchCondition.质检时间[1]


    const ret = await exportOemCheckListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loading.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
      loading.value = false
    }, 10000)

}

</script>

<template>
    <!-- 列表 -->
    <div ref="rootRef">
      <div class="p-7 pt-0">
        <div  class="pt-7 pr-5 pl-5 pb-7 mb-5 bg-white">
          <div class="absolute top-3 left-10">
          <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loading">
            <Icon icon="carbon:export" />
            <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton>
        </div>
          <div class="text-center mb-5 font-bold">{{ t('qualitycheck.list') }}</div>
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">质检单号</div>
          <el-input size="small" v-model="searchCondition.oem_check_num" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('receipt.id') }}</div>
          <el-input size="small" v-model="searchCondition.oem_drawin_num" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">委外单号</div>
          <el-input size="small" v-model="searchCondition.oem_order_num" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('product_manage.id') }}</div>
          <el-input size="small" v-model="searchCondition.产品编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('product_manage.name') }}</div>
          <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">质检人员</div>
          <el-input size="small" v-model="searchCondition.check_man_name" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">受托商</div>
          <el-input size="small" v-model="searchCondition.parter_nick" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">收货人员</div>
          <el-input size="small" v-model="searchCondition.drawin_man_name" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
          <el-input size="small" v-model="searchCondition.specs" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">质检时间</div>
          <el-date-picker size="small" class="searchItem" v-model="searchCondition.质检时间" type="daterange"
            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>




          <div  class="flex justify-end items-center mr-6 mt-6 mb-2">
            <el-checkbox :label="t('customer.senior')" v-model="senior" size="small"/>
            <ElButton class="ml-5" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 产品列表 -->
        <div>
            <!-- 表头 -->
            <div class="flex header headerBk">              
              <div class="w-[88%] flex">
                <div class="min-w-[35%] text-center border-l-1px">产品名称</div>
                <div class="min-w-[10%] text-center border-l-1px">收货数量</div>
                <div class="min-w-[10%] text-center border-l-1px">质检方式</div>
                <div class="min-w-[10%] text-center border-l-1px">质检数量</div>
                <div class="min-w-[10%] text-center">是否合格</div>
                <div class="min-w-[10%] text-center border-l-1px">不良处理方式</div>
                <div class="min-w-[15%] text-center border-l-1px">情况说明</div>
              </div>
              <div class="flex flex-grow !p-0">
                <div class="rightcss rightcss_title">质检人员</div>
                <div class="rightcss_title !min-w-[70px]">操作</div>
              </div>
            </div>
            <!-- 表内容 -->
            <div class="mt-3 bg-white" v-for="item in qualityData" :key="item.id" style="box-shadow:var(--el-box-shadow-lighter);">
              <!-- 内容头 -->
              <div>
                <div class="bg-light-500 p-2 flex flex-nowrap text-[13px] border-b-1px font-bold">
                    <div class="w-[45%]  flex items-center">
                        <div class="mr-4 font-bold">
                            {{ item.create_date.split(' ')[0] }}
                        </div> 
                        <div class="mr-3">质检单号: {{ item.oem_check_num }}</div>
                        <div class="mr-3">收货单编号: {{ item.oem_drawin_num }}</div>
                        <div>委外单编号: {{ item.oem_order_num }}</div>
                    </div>
                  <div class="min-w-[200px] flex items-center">
                    <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                    {{ checkPermissionApi('受托商名称显示')?item.parter_nick:'***' }}
                  </div>
                  <div class="flex justify-center items-center min-w-[250px]">
                    <div class="mr-5">收货人:{{ item.drawin_man_name }}</div>
                    <!-- <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin-right: 5px;" icon="solar:file-bold" />
                    <div>文件(0)</div>
                    <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin:0 5px 0 50px;" icon="material-symbols:contract" />
                    <div>合同(0)</div> -->
                  </div>
                  <!-- 靠右的其他信息 -->
                  <!-- <div class="ml-auto flex justify-center items-center min-w-[200px]">
                    <Icon style="scale: 1.1; margin:0 5px 0 50px;" icon="ic:baseline-qrcode" />
                    <Icon style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);" icon="material-symbols:print-outline" />
                    <Icon style="scale: 1.1; margin:0 5px 0 10px;" icon="uil:copy" />
                  </div> -->
                </div>
              </div>
              <!-- 内容体 -->
              <div class="flex">
                <!-- 左边产品列表 -->
                <div class="w-[88%]  table_self">
                  <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                    <div class="min-w-[35%] text-center flex-grow ">
                      <div class="flex justify-start items-center w-[100%] p-1">
                        <el-image v-if="pdt.pics.length>0"  class="object-fill w-[50px] h-[50px] min-w-[50px]" :src="pdt.pics[0].url" />
                        <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[50px] h-[50px] min-w-[50px]" src="/nopic.jpg" />
                        <div class="inline-block text-left max-w-[100%]">
                          <div style="white-space: normal;" class="nameStyle" @click="handleOper('info', item)">{{ '['+pdt.name+']'+pdt.nick }}</div>
                        </div>
                      </div>
                    </div>                  
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">{{ pdt.收货数量+' '+pdt.base_unit }}</div>
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">{{ pdt.质检方式 }}</div>
                    <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">
                      <div>{{pdt.良品数量 + pdt.base_unit }}</div>
                      <div>{{ '不良:' +pdt.不良品数量 }}</div>
                      <div class="ex_text">{{ pdt.良品率 }}%</div>
                    </div>
                    <div class="min-w-[10%] text-center border-l-1px">
                      <el-tag :type="pdt.良品率>=90?'success':'danger'">{{ pdt.良品率>=90?'合格':'不合格' }}</el-tag>
                    </div>
                    <div class="min-w-[10%] text-center border-l-1px">{{ pdt.处理方式 }}</div>
                    <div class="min-w-[15%] flex justify-center items-center flex-col border-l-1px">{{ pdt.情况说明 }}</div>

                  </div>
                </div>
                <!-- 右边其他数据 -->
                <div class="flex flex-grow text-center  right">
                  <div class="rightcss flex justify-start items-start">{{ item.check_man_name }}</div>
                  <div class="!min-w-[70px] flex justify-start items-start" style="text-align: center;">
                    <el-dropdown trigger="click" placement="bottom">
                      <span class="el-dropdown-link">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                      </span>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item  @click="handleOper('edit', item)">修改</el-dropdown-item>
                          <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.detail') }}</el-dropdown-item>
                          <!-- <el-dropdown-item v-if="item.fsm_can_trig_data?.操作触发.includes('删除')" @click="handleOper('edit', item)">{{ t('userOpt.edit') }}</el-dropdown-item> -->
                          <el-dropdown-item v-if="item.fsm_can_trig_data?.操作触发.includes('删除')" @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>

      <el-pagination class="flex justify-end mb-4"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
    <!-- <div class="h-[300px]"></div> -->

    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  cursor: pointer;
}
.nameStyle:hover{
  color: rgb(130, 130, 255);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  color: #333;
  font-weight: bold;
}
.headerBk{
  background-color: #fff;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
 // white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: left; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

.rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}
</style>

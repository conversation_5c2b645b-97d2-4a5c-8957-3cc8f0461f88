<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDropdownMenu,  ElButton,  ElMessage, ElMessageBox, ElImage,  ElInput, ElSelect, ElOption, ElCheckbox, ElDropdown, ElDropdownItem,  ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getOemRemainListApi,delOemRemainApi,updateOemRemainApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { DialogSaleSel } from '@/components/DialogSaleSel'
import { useAppStore } from '@/store/modules/app'
import { checkPermissionApi } from '@/api/tool';

const { currentRoute,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//返料数据源
const remainData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state:'',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getOemRemainList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getOemRemainList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getOemRemainList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
  //编辑产品
  if(type === 'edit' || type === 'info')
  {
    const retPath = (currentRoute.value.name?.indexOf("Stone")>=0)?'/oeminventorymanage/stoneaddoemremain':'/oemmanage/addoemremain'
    push({
      path: retPath,
      query:{
          id:item.id,
          type:type,
          cmd:bCheckMode.value?'审核':''
      }
    })
  }
  else if(type === 'del') 
  {
    ElMessageBox.confirm(t('msg.confirm_del_remain'), t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)
      const ret = await delOemRemainApi({
        ids: [item.id],
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getOemRemainList()
      }
    }
    ).catch(() => {})
  }
  else if(type === 'putin')
  {
    ElMessageBox.confirm('是否入库，操作后仓库库存将发生变化！', t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)
      const ret = await updateOemRemainApi({
        id: item.id,
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '入库'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getOemRemainList()
      }
    }
    ).catch(() => {})
  }

}



//查询销售出库单列表
const getOemRemainList = async (page = 1) => {
  searchCondition.page = page
  const ret = await getOemRemainListApi(searchCondition)
  if(ret)
  {
    remainData.splice(0,remainData.length, ...ret.data)
    console.log(remainData)
    totleCount.value = parseInt( ret.count)
  }
}

//审核模式
const bCheckMode = ref(false)

onMounted(() => {
    if(currentRoute.value.name === "OemRemainCheck")
    {
      bCheckMode.value = true
      searchCondition.fsm_cur_state = '等待审核'
    }


    if(currentRoute.value.query.oem_remain_num != undefined)
      searchCondition.oem_remain_num = currentRoute.value.query.oem_remain_num
    if(currentRoute.value.query.oem_order_num != undefined)
      searchCondition.oem_order_num = currentRoute.value.query.oem_order_num
    if(currentRoute.value.query.pdt_biaoshi != undefined)
      searchCondition.oem_order_sub = currentRoute.value.query.pdt_biaoshi
    getOemRemainList()  

      //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})



</script>

<template>
    <!-- 销售出库单列表 -->
    <div ref="rootRef">
      <div class="p-7 pt-0">
        <div  class="pt-7 pr-5 pl-5 pb-7 mb-5 bg-white">
          <div div class="text-center mb-5 font-bold">委外返料单</div>
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.check_status') }}</div>
            <el-select class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择" >
              <el-option v-for="item in ['订单创建', '等待审核', '等待修改', '等待提交', '已审核', '已拒绝', '已关闭']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('remain.id') }}</div>
            <el-input    v-model="searchCondition.oem_remain_num" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('oem.name') }}</div>
            <el-input    v-model="searchCondition.oem_order_num" placeholder="" class="searchItem" />
          </div>
          
          <div  class="flex justify-end items-center mr-6 mt-6 mb-2">
            <el-checkbox :label="t('customer.senior')" v-model="senior" size="small"/>
            <ElButton class="ml-5" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 图例 -->
        <div class="flex mb-2">
          <div class="flex text-sm items-center mr-3">
            <div class="title_create rounded-[50%] mr-1 w-[14px] h-[14px]"></div>
            已创建
          </div>
          <div class="flex text-sm items-center mr-3">
            <div class="title_ok rounded-[50%] mr-1 w-[14px] h-[14px]"></div>
            审核通过
          </div>
          <div class="flex text-sm items-center mr-3">
            <div class="title_wait rounded-[50%] mr-1 w-[14px] h-[14px]"></div>
            驳回
          </div>
        </div>
        <!-- 产品列表 -->
        <div>
            <!-- 表头 -->
            <div class="flex header headerBk">
              <div class="w-[60%] flex">
                <div class="w-[85%] ">产品名称</div>
                <div class="w-[15%] border-l-1px">返料数量</div>
                <!-- <div class="w-[25%] ">价格</div> -->
              </div>
              <div class="flex flex-grow !p-0">
                <div class="rightcss rightcss_title">受托商</div>
                <div class="rightcss rightcss_title">操作人员</div>
                <div class="rightcss rightcss_title">订单状态</div>
                <div class="rightcss rightcss_title">备注</div>
                <div class="rightcss_title !min-w-[70px]">操作</div>
              </div>

            </div>
           
            <!-- 表内容 -->
            <div class="mt-3 bg-white" v-for="item in remainData" :key="item.id" style="box-shadow:var(--el-box-shadow-lighter);">
              <!-- 内容头 -->
              <div>
                <div class="p-2 flex flex-nowrap text-[13px] border-b-1px font-bold">
                  <div class="flex items-center mr-1 w-80px flex-grow-0 flex-shrink-0" >
                    <div class="rounded p-1 pl-2 pr-2" style="color: #fff;" :class="{'title_create': ['订单创建','等待审核','等待修改','等待提交'].includes(item.fsm_cur_state), 'title_checked': item.fsm_cur_state === '审核通过', 'title_ok': ['已入库','已关闭','已拒绝'].includes(item.fsm_cur_state), 'title_wait': item.fsm_cur_state === '等待修改'}">
                      {{ item.fsm_cur_state }}
                    </div>
                  </div>
                  <div class="w-[45%]  flex items-center">
                        <div class="mr-3 font-bold">
                            {{ item.create_date.split(' ')[0] }}
                        </div> 
                        <div class="mr-2">返料单号: {{ item.oem_takeout_num }}</div>
                        <div class="cursor-pointer" @click="onNaviSale(item)">委外单号: {{ item.oem_order_num }}</div>
                    </div>
                  <div class="min-w-[200px] flex items-center">
                    <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                    {{ checkPermissionApi('受托商名称显示')?item.parter_nick:'***' }}
                  </div>
                  <div class="flex justify-center items-center min-w-[250px]">
                    <div class="mr-5">操作员:{{ item.remain_man_name }}</div>

                  </div>
                  <!-- 靠右的其他信息 -->
                  <div class="ml-auto flex justify-center items-center min-w-[200px]">
                    <div>{{ item.store_nick }}</div>
                  </div>
                </div>
              </div>
              <!-- 内容体 -->
              <div class="flex">
                <!-- 左边产品列表 -->
                <div class="w-[60%]  table_self">
                  <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                    <div class="w-[85%] flex-grow ">
                      <div class="flex justify-start items-center w-[100%] p-1">
                        <el-image v-if="pdt.pics.length>0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" :src="pdt.pics[0].url" />
                        <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" src="/nopic.jpg" />
                        <div class="inline-block text-left max-w-[100%] ml-2">
                          <div style="white-space: normal;" class="nameStyle" @click="handleOper('info', item)">{{ '['+pdt.name+']'+pdt.nick }}</div>
                        </div>
                      </div>
                    </div>                  
                    <div class="w-[15%] flex justify-center items-center flex-col border-l-1px">{{ pdt.返料数量+' '+pdt.base_unit }}</div>
                    <!-- <div class="w-[25%] text-center text-red-400" style="font-size: smaller;">
                      <div>税前:￥{{ parseFloat((pdt.sell_price_bef_tax*pdt.发货数量-pdt.avg_price_bef_tax*pdt.发货数量).toFixed(2)) }}</div>
                      <div>税后:￥{{ parseFloat((pdt.sell_price_aft_tax*pdt.发货数量-pdt.avg_price_aft_tax*pdt.发货数量).toFixed(2)) }}</div>
                    </div> -->
                  </div>
                </div>
                <!-- 右边其他数据 -->
                <div class="flex flex-grow text-center  right">
                  <div class="rightcss" style="text-align: left;">
                        {{ item.parter_nick }}
                  </div>
                  <div class="rightcss">
                    {{ item.remain_man_name }}
                  </div>
                  <div class="rightcss">
                      <div class="p-2 pt-0.5 pb-0.5" style="font-size: 14px;font-weight: 900; border-radius: 4px;color: white;" :class="{'title_create': ['订单创建','等待审核','等待修改','等待提交'].includes(item.fsm_cur_state), 'title_checked': item.fsm_cur_state === '已审核', 'title_ok': ['已入库','已关闭','已拒绝'].includes(item.fsm_cur_state), 'title_wait': item.fsm_cur_state === '等待修改'}">{{ item.fsm_cur_state }}</div>
                      <div class="text-red-400 mt-1">{{ item.fsm_log_list.length>0?item.fsm_log_list[0][5]:'' }}</div>  
                  </div>
                  <div class="rightcss" style="text-align: left;">{{ item.note }}</div>
                  <div class="!min-w-[70px] flex justify-center items-center">
                    <ElButton v-if="bCheckMode && item.fsm_can_trig_data.审核触发.length>0" type="success" size="small" @click="handleOper('info',item)">{{ t('cmd.check') }}</ElButton>
                    <el-dropdown v-if="!bCheckMode" trigger="click" placement="bottom">
                      <span class="el-dropdown-link">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                      </span>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.detail') }}</el-dropdown-item>
                          <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('入库')" @click="handleOper('putin', item)">{{ t('cmd.putin') }}</el-dropdown-item>
                          <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('保存')" @click="handleOper('edit', item)">{{ t('userOpt.edit') }}</el-dropdown-item>
                          <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('保存')" @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>

      <el-pagination
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
    <div class="h-[300px]"></div>
    

    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #ffe48d !important;
}

:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
}

.nameStyle {
  cursor: pointer;
}
.nameStyle:hover{
  color: rgb(130, 130, 255);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 200px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  color: #333;
  font-weight: bold;
}
.headerBk{
  background-color: #fff;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: center; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}


//---------------列表对象标题栏条件颜色-------------------
.title_create{ //已创建
  background-color: #79bbff;
}
.title_checked{ //已审核
  background-color: #95d475;
}
.title_ok{ //已入库
  background-color: #b1b3b8;
}
.title_wait{//等待修改
  background-color: #f89898;
}
</style>

<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard,ElPopconfirm,ElTable,ElTag,ElDatePicker,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import { getInventoryListApi,getStoreListApi,getOemReturnNewnumApi,getOemOrderInfoApi,delOemReturnApi,updateOemReturnApi,getOemReturnInfoApi,getOemReturnListApi,addOemReturnApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount } from 'vue'
import {checkFormRule} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { DialogCheckShow } from '@/components/DialogCheckShow'
import { cloneDeep } from 'lodash-es'


const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})

//委外单数据
const oemData = reactive({})

//出库单数据
const returnData = reactive(
    {
    "supplier_id": "",
    "buy_order_num": "",
    "buy_cancel_num": "",
    "cancel_date": "",
    "cancel_man_id": "", //领取人员
    "store_id": "",
    "pdt_list": [],
    "note": "",
    "express_fee": 0.00,
    "other_fee": 0.00,
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:['保存']
    }, //审核决策
    fsm_cur_state:'',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)

//产品仓库分布数据
const detailData  = reactive([])

//获取最新ID
const onChangeID = async()=>{
    const ret = await getOemReturnNewnumApi()
    if(ret)
    {
        console.log(ret)
        returnData.oem_cancel_num = ret.data.new_id
    }
}


//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    type:currentRoute.value.query.type as string,
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)

    returnData.store_id = storeData[0].id

    await updatePdtStock(returnData.store_id)
  }

}

//更新pdt库存
const updatePdtStock = async (store_id:string) => {
    if(returnData.pdt_list.length<=0)
        return
    let arrPdtIDs = []
    for(let one of returnData.pdt_list)
    {
        arrPdtIDs.push(one.id)
    }

    //查询这个产品在那些仓库有库存
    const ret = await getInventoryListApi({
                store_id:store_id,
                pdt_ids:[...arrPdtIDs],
                page: 1,
                count: 500
            })
    if(ret)
    {
        detailData.splice(0,detailData.length, ...ret.data)
        console.log('--------',detailData)
        //更新统计数据到
        for(let item of returnData.pdt_list)
        {
            item.可用数量 = 0
            for(let one of detailData)
            {
                let bFind = false
                if(item.id == one.pdt_id)
                {
                    item.可用数量 = currentRoute.value.query.type=='良品库'?one.良品数量:one.不良品数量
                    bFind = true
                }
                if(bFind)
                    break
            }
        }
    }
    console.log(returnData.pdt_list)
}

//计算输入
const recomputeNum = (row)=>{
    if(row.退货数量<0)
        row.退货数量 = 0
    if(row.退货数量>row.已收货-row.良品退货-row.不良退货)
    {
        row.退货数量 = row.已收货-row.良品退货-row.不良退货
        //提示退货数量不能超过已收货数量减去已退货数量
        ElMessage.warning(t('msg.returnNumTooBig'))
    }
    if(row.退货数量>row.可用数量)
    {
        row.退货数量 = row.可用数量
        if(row.退货数量>row.已收货-row.良品退货-row.不良退货)
        {
            row.退货数量 = row.已收货-row.良品退货-row.不良退货
        }
        //提示退货数量不能超过已入库数量
        ElMessage.warning(t('msg.returnNumTooBig2'))
    }
}


onMounted(async() => {    
    //查询获取关联采购单信息
    const ret = await getOemOrderInfoApi({
        oem_order_num:currentRoute.value.query.oem_order_num as string
    })
    if(!ret)
    {
        return
    }
    console.log(ret)    
    Object.assign(oemData,ret.data)

    // await getStoreList()

    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('oemreturn.add')
        onChangeID()
        //新建需要默认可以提审
        const tmp = [...returnData.fsm_can_trig_data.操作触发,'提交审核']
        returnData.fsm_can_trig_data.操作触发 = tmp


        returnData.oem_order_num  = currentRoute.value.query.oem_order_num as string
        returnData.cancel_date = getTodayDate()
        //所有产品到收货列表
        returnData.pdt_list = oemData.pdt_list
        returnData.oem_order_sub = currentRoute.value.query.pdt_biaoshi
        //设置出库人员
        const info = wsCache.get(appStore.getUserInfo)
        returnData.cancel_man_id = info.id
        returnData.cancel_man_name = info.resident_name

        //追加产品额外属性
        for(let item of returnData.pdt_list)
        {
            item.退货数量 = 0        
        }
        await getStoreList()
    }
    else
    {
        await getStoreList()
        if(currentRoute.value.query.mode == 'info')
        {
            title.value = t('oemreturn.look')
        }
        else
        {
            title.value = t('oemreturn.modify')
        }

        
        //查询产品信息 
        const ret = await getOemReturnInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(returnData, ret.data)
            returnData.pdt_list = ret.data.pdt_list;
        }

        if(currentRoute.value.query.mode === 'info') //查看模式
        {
            let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
            let excludeDiv = document.getElementById('check');
            if(excludeDiv != null)
            {
                // 使用 :not() 伪类排除特定的 div 元素下的子元素
                let filteredComponents = Array.from(components).filter(
                    component => !excludeDiv.contains(component)
                );
                filteredComponents.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }
            else
            {
                components.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }


            components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
            components.forEach((component) => {
                component.classList.add('infomode')
            });
            const suffixElements = document.querySelectorAll('.el-input__suffix');
            suffixElements.forEach(suffixElement => {
                suffixElement.style.display = 'none';
            });
        }
    }
    
 
})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})


//切换仓库更新库存
const onChangeStore = async (store_id) => {
    updatePdtStock(store_id)
}


//显示隐藏选择出库员窗口变量
const showSelReturnUserDlg = ref(false)
//显示选择出库员弹窗
const onSelReturnUser = ()=>{
    showSelReturnUserDlg.value = true
}
//选择出库员回调
const onSelReturnCallback = (id,name)=>{
    console.log(id,name)
    returnData.cancel_man_id = id
    returnData.cancel_man_name = name
}



//保存
const onSave = async()=>{
    console.log(returnData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //删除returnData.pdt_list中入库数量为0的行
    returnData.pdt_list = returnData.pdt_list.filter(pdt=>pdt.退货数量!=0)
    

    if(!checkPdt())
    {
        console.log('???')
        return 
    }

    if(returnData.id == undefined)
    {
        const ret = await addOemReturnApi(returnData)
        if(ret)
        {
            ElMessage.success(t('msg.newReturnSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret =await updateOemReturnApi(returnData)
        if(ret)
        {
            ElMessage.success(t('msg.updateReturnSuccess'))
            back()
        }
    }

}
//校验pdt
const checkPdt = ()=>{
    if(returnData.pdt_list.length <= 0)
    {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}
//提交审核意见
const handleCheck = async(btn)=>{
    console.log(11)

    // console.log(oldPutin)
    // console.log(putinData)
    // return 

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }


    const info = wsCache.get(appStore.getUserInfo)
    returnData.fsm_exe_man_name = info.resident_name
    returnData.fsm_exe_trig = btn
    //删除putinData.pdt_list中入库数量为0的行
    // returnData.pdt_list = returnData.pdt_list.filter(pdt=>pdt.退货数量>0)

    const tmp = cloneDeep(returnData)
    tmp.pdt_list = tmp.pdt_list.filter(pdt=>pdt.退货数量>0)
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }
    

    if(tmp.id == undefined)
    {
        const ret = await addOemReturnApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newPutinSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret =await updateOemReturnApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updatePutinSuccess'))
            back()
        }
    }
}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = ()=>{
    showCheckHisDlg.value = true
}

</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史
            </ElButton>
        </template>
        <template #right>
            <ElButton color="#409EFF" style="color: #fff;"  v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.mode != 'info' && returnData.fsm_can_trig_data.操作触发.includes('保存')" @click="handleCheck('保存')">
                <Icon class="mr-0.5" icon="carbon:save" />
                保存
            </ElButton>
            <el-popconfirm  title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && returnData.fsm_can_trig_data.操作触发.includes('提交审核')"   type="success" >
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        提交审核
                    </ElButton>
                </template>
            </el-popconfirm>
            
            <el-popconfirm  title="是否确认关闭订单?" @confirm="handleCheck('关闭')">
                <template #reference>
                    <ElButton  v-show="currentRoute.query.cmd != '审核' && returnData.fsm_can_trig_data.操作触发.includes('关闭')" type="danger">
                        <Icon class="mr-0.5" icon="carbon:close-outline" />
                        关闭订单
                    </ElButton>
                </template>
            </el-popconfirm>    
        </template>

        <el-card id="check" v-if="returnData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%]">
            <template #header>
            <div class="flex items-center">
                <span>当前节点:</span>
                <span class="text-red-500 mr-3">{{ returnData.fsm_cur_state }}</span>
                <!-- <ElButton @click="handleCheck(btn)" v-for="btn in purchaseData.fsm_can_trig_data.审核触发.toReversed()" :key="btn" :type="btn=='同意'?'success':'danger'">{{ btn }}</ElButton> -->
                <ElButton v-show="returnData.fsm_can_trig_data.审核触发.includes('同意')" type="success" @click="handleCheck('同意')" >同意</ElButton>
                <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="returnData.fsm_can_trig_data.审核触发.includes('驳回')" type="danger" >驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="returnData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                    </template>
                </el-popconfirm>

            
            </div>
            </template>
            <el-input v-model="returnData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        </el-card>
        <el-form :rules="rules" :model="returnData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('return.num')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input class="mr-2" v-model="returnData.oem_cancel_num" :disabled="returnData.id!=undefined" />
                            <ElButton v-if="returnData.id==undefined" type="primary"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('oem.name')"
                    class="flex">
                    <div>{{ oemData.oem_order_num }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('store.store')"
                    class="flex">
                    <el-select v-model="returnData.store_id" placeholder="请选择" @change="onChangeStore(returnData.store_id)">
                        <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('parter.nick')"
                    class="flex">
                    <div>{{ oemData.parter_nick }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('supplier.person')"
                    class="flex">
                    <div>{{ oemData.oem_man_name }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'委外'+t('oem.date')"
                    class="flex">
                    <div>{{ oemData.oem_date }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('return.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ returnData.cancel_man_name }}</div> 
                        <ElButton @click="onSelReturnUser" v-if="currentRoute.query.mode != 'info'">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('return.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="returnData.cancel_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>

            </el-descriptions>
        
        </el-form>

        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="returnData.pdt_list" style="width: 100%" border stripe>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="120" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130" />
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.id != undefined"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('oem.count')" >
                <template #default="scope">
                    <div>{{ scope.row.委外数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('receipt.receipt_count')" >
                <template #default="scope">
                    <div>{{ scope.row.已收货 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('return.count')" >
                <template #default="scope">
                    <div>{{ scope.row.良品退货+scope.row.不良退货 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('inventory.avali_count')" >
                <template #default="scope">
                    <div>{{ scope.row.可用数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('return.count2')" >
                <template #default="scope">
                    <el-input v-model="scope.row.退货数量" @input="recomputeNum(scope.row)" type="number"/>
                </template>
            </el-table-column>            

            <el-table-column show-overflow-tooltip  prop="委外备注" :label="t('purchase.remark')" />
            
            <el-table-column show-overflow-tooltip  :label="t('return.remark')" >
                <template #default="scope">
                    <el-input v-model="scope.row.退货备注" />
                </template>
            </el-table-column>
        </el-table>
    <!-- 显示合计 -->
    <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">委外单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ returnData.order_note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">出库单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="returnData.note" clearable :autosize="{ minRows: 10, maxRows: 4 }"     type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>

    </div>

        
        




        <!-- <div class="mb-20"></div> -->

        <!-- 选择退货员 -->
        <DialogUser :param="''" v-model:show="showSelReturnUserDlg" :title="t('msg.selectUser')" @on-submit="onSelReturnCallback"/>
        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="returnData.fsm_log_list" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
//:deep(.tableHeader) {
//  background-color: #6d92b4 !important;
//  color: #fff;
//  font-weight: 400;
//  white-space: nowrap;
//  text-align: center;
//}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}
//扩展文字
.ex_text{
  font-size: 11px;
  color: #646464;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
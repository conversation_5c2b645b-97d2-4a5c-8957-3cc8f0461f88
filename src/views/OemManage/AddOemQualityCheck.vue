<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,nextTick } from 'vue'
import { getOemQualityCheckInfoApi,addOemQualityCheckApi,updateOemQualityCheckApi, addOemQualityCheckPutinApi, updateOemQualityCheckPutinApi } from '@/api/product'

import type { FormInstance } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogProductSel } from '@/components/DialogProductSel'
import {checkFormRule, closeOneTagByName, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getOemDrawinInfoApi,getOemQualityCheckNewnumApi} from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'



const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//收货单数据
const receiptData = reactive(
    {
    "oem_order_num": "",
    "oem_drawin_num": "",
    "drawin_man_id": "",
    "drawin_date": "",
    "pdt_list": [],
    "note": ""
}
)

//质检单数据
const qualityData = reactive(
    {
    "parter_id": "",
    "oem_drawin_num": "",
    "oem_check_num": "",
    "drawin_type": "",
    "check_man_id": "",
    "check_date": "",
    "pdt_list": [],
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:['保存']
    }, //审核决策
    fsm_cur_state:'',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)

//获取最新ID
const onChangeID = async()=>{
    const ret = await getOemQualityCheckNewnumApi()
    if(ret)
    {
        console.log(ret)
        qualityData.oem_check_num = ret.data.new_id
    }
}


onMounted(async () => {    
    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('quality.add')

        onChangeID()
        //同步收货单数据
        const ret = await getOemDrawinInfoApi({
            oem_drawin_num:currentRoute.value.query.oem_drawin_num,
            page:1,
            count:100
        })
        console.log('---',ret)
         qualityData.oem_order_num = ret.data.oem_order_num
         qualityData.oem_drawin_num = ret.data.oem_drawin_num
         qualityData.oem_order_sub = ret.data.oem_order_sub
         qualityData.parter_name = ret.data.parter_name
         qualityData.parter_nick = ret.data.parter_nick
         qualityData.drawin_man_name = ret.data.drawin_man_name
         qualityData.drawin_date = ret.data.drawin_date
        // qualityData.express_fee = ret.data.express_fee
        // qualityData.other_fee = ret.data.other_fee
        qualityData.note = ret.data.note
        qualityData.order_note = ret.data.order_note

        qualityData.pdt_list = ret.data.pdt_list
        //追加产品额外属性
        for(let item of qualityData.pdt_list)
        {
            item.质检方式 = '全检'
            item.质检数量 = item.收货数量 - item.已质检
            item.良品数量 = item.质检数量
            item.不良品数量 = 0
            item.良品率 = 100
            item.情况说明 = ''
            item.质检结果 = '整单合格'
            item.处理方式 = '无'

            //初始化该单模式
            if(item.最近质检方式 == '全检' || item.最近质检结果 == '升级全检')
            {
                item.质检方式 = '全检'
            }
        }

        //设置默认人员部门
        const info = wsCache.get(appStore.getUserInfo)
        qualityData.check_man_id = info.id
        qualityData.check_man_name = info.resident_name
        //默认质检日期为今天
        qualityData.check_date = getTodayDate()
        qualityData.fsm_can_trig_data.操作触发 = ['提交']
    }
    else
    {        
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('quality.look')
        }
        else
        {
            title.value = t('quality.modify')
        }


        
        //查询产品信息 
        const ret = await getOemQualityCheckInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(qualityData, ret.data)
            qualityData.pdt_list = ret.data.pdt_list;
        }
        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }
})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})



//显示隐藏选择质检员窗口变量
const showSelQualityUserDlg = ref(false)
//显示选择质检员弹窗
const onSelQualityUser = ()=>{
    showSelQualityUserDlg.value = true
}
//选择质检员回调
const onSelQualityCallback = (id,name)=>{
    console.log(id,name)
    qualityData.check_man_id = id
    qualityData.check_man_name = name
}

//重新计算相关数字
const reComputeCount = (row, type) => {
    row.质检数量 = row.质检数量 || 0;
    row.良品数量 = row.良品数量 || 0;
    row.不良品数量 = row.不良品数量 || 0;
    
    //超越最大的数量就复位
    const nMax = row.收货数量 - row.已质检
    if(row.质检数量>nMax)
    {
        row.质检数量 = nMax
    }
    if(row.良品数量>nMax)
    {
        row.良品数量 = nMax
    }
    if(row.不良品数量>nMax)
    {
        row.不良品数量 = nMax
    }

    if(type == '质检数量')
    {
        row.良品数量 = row.质检数量
        row.不良品数量 = 0
        row.良品率 = 100
    }
    else if(type == '良品数量')
    {
        row.不良品数量 = row.质检数量 - row.良品数量
        row.良品率 = ((row.良品数量 / row.质检数量) * 100).toFixed(2)
    }
    else if(type == '不良品数量')
    {
        row.良品数量 = row.质检数量 - row.不良品数量
        row.良品率 = ((row.良品数量 / row.质检数量) * 100).toFixed(2)
    }
}



//保存
const onSave = async()=>{
    console.log(qualityData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    const tmp = cloneDeep(qualityData)

    const info = wsCache.get(appStore.getUserInfo)
    tmp.fsm_exe_man_name = info.resident_name
    tmp.fsm_exe_trig = '提交'

    tmp.pdt_list = tmp.pdt_list.filter(pdt=>pdt.质检数量!=0)
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning('质检数量不能为0')
        return
    }
    
    if(tmp.id == undefined)
    {
        const ret = await addOemQualityCheckPutinApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newQualityCheckSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        const ret =await updateOemQualityCheckPutinApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateQulityCheckSuccess'))
            baskFront()
        }
    }


}

//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
    //刷新上一页
    closeOneTagByPath('/qualitymanage/qualityreceiptlist')
}
</script>

<template>
    <ContentDetailWrap :title="title" @back="baskFront()">
        <template #right>
            <ElButton color="#409EFF" style="color: #fff;" @click="onSave" v-show="currentRoute.query.type != 'info'">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <el-form :rules="rules" :model="qualityData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('qualitycheck.name')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  v-model="qualityData.oem_order_num" :disabled="qualityData.id!=undefined" />
                            <ElButton v-if="qualityData.id==undefined" type="primary"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('receipt.id')"
                    class="flex">
                    <div>{{ qualityData.oem_drawin_num }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('receipt.type')"
                    class="flex">
                    <div>委外收货</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('oem.name')"
                    class="flex">
                    <div>{{ qualityData.oem_order_num }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('parter.name')"
                    class="flex">
                    <div>{{ qualityData.parter_name }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('parter.nick')"
                    class="flex">
                    <div>{{ qualityData.parter_nick }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('receipt.user')"
                    class="flex">
                    <div>{{ qualityData.drawin_man_name }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('receipt.date')"
                    class="flex">
                    <div>{{ qualityData.drawin_date }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('quality.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ qualityData.check_man_name }}</div> 
                        <ElButton @click="onSelQualityUser" v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && qualityData.fsm_can_trig_data.操作触发.includes('保存')">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('quality.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="qualityData.check_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>

            </el-descriptions>
        
        </el-form>

        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="qualityData.pdt_list" style="width: 100%" border stripe>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="120" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130" />
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.id != undefined"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  :label="t('purchase.receipt_count')" >
                <template #default="scope">
                    <div>{{ scope.row.收货数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  :label="t('quality.type')" >
                <template #default="scope">  
                    <!-- v-if="(scope.row.最近质检方式 == '抽检'&&scope.row.最近质检结果!='升级全检') || scope.row.最近质检结果 == undefined || scope.row.最近质检结果 == ''" -->
                    <el-select v-if="((scope.row.最近质检方式 == '抽检'&&scope.row.最近质检结果!='升级全检') || scope.row.最近质检结果 == undefined || scope.row.最近质检结果 == '')"   v-model="scope.row.质检方式" placeholder="Select">
                        <el-option v-for="item in ['全检','抽检']" :key="item" :label="item" :value="item" />
                    </el-select>
                    <div v-if="scope.row.最近质检方式 == '全检' || scope.row.最近质检结果 == '升级全检'">{{ scope.row.质检方式 }}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('quality.count')" >
                <template #default="scope">
                    <el-input v-model="scope.row.质检数量" @blur="reComputeCount(scope.row,'质检数量')" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  :label="t('quality.good_count')" >
                <template #default="scope">
                    <el-input v-model="scope.row.良品数量" @blur="reComputeCount(scope.row,'良品数量')" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('quality.bad_count')" >
                <template #default="scope">
                    <el-input v-model="scope.row.不良品数量" @blur="reComputeCount(scope.row,'不良品数量')" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('quality.good_per')" >
                <template #default="scope">
                    {{ scope.row.良品率+'%' }}
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('receipt.price')" >
                <template #default="scope">
                    {{ '￥'+scope.row.buy_price_bef_tax+'/'+scope.row.buy_price_aft_tax }}
                        <div class="ex_text">含税{{ scope.row.发票税率 }}%</div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('receipt.totle_price')" >
                <template #default="scope">
                    {{ '￥'+scope.row.总价 }}
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="采购备注" :label="t('purchase.remark')" />
            <el-table-column show-overflow-tooltip  prop="收货备注" :label="t('receipt.remark')" />
            <el-table-column   show-overflow-tooltip  :label="t('quality.result')" >
                <template #default="scope">
                    <el-select v-if="scope.row.质检方式 !== '全检'" v-model="scope.row.质检结果" placeholder="Select">
                        <el-option v-for="item in ['整单合格','整单不合格','继续抽检','升级全检']" :key="item" :label="item" :value="item" />
                    </el-select>
                    <div v-if="scope.row.质检方式 == '全检'">{{ scope.row.质检方式 }}</div>
                </template>
            </el-table-column>
            <el-table-column   show-overflow-tooltip  :label="'不良处理'" >
                <template #default="scope">
                    <el-select  v-model="scope.row.处理方式" placeholder="Select">
                        <el-option v-for="item in ['无','特采','退货']" :key="item" :label="item" :value="item" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  :label="t('quality.desc')" >
                <template #default="scope">
                    <el-input v-model="scope.row.情况说明" />
                </template>
            </el-table-column>
        </el-table>
    <!-- 显示合计 -->
    <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">委外单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ qualityData.order_note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">收货单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ qualityData.note }}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

        
        




        <div class="mb-20"></div>

        <!-- 选择质检员 -->
        <DialogUser :param="''" v-model:show="showSelQualityUserDlg" :title="t('msg.selectUser')" @on-submit="onSelQualityCallback"/>

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}
//扩展文字
.ex_text{
  font-size: 11px;
  color: #646464;
}
</style>
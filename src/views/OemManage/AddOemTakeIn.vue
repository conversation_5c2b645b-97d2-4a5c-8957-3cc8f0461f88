<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElOption,ElSelect,ElTooltip,ElTable,ElPopconfirm,ElTag,ElDatePicker,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogProductSel } from '@/components/DialogProductSel'
import {checkFormRule} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { addOemTakeInApi,updateOemTakeInApi,getOemLockListApi,getOemTakeInInfoApi,getOemTakeInNewnumApi,getStoreListApi,updateSaleOutApi,addSaleOutApi,getSaleOutInfoApi,getSaleOutNewnumApi,getSaleInfoApi } from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { DialogSelSaleOutPdt } from '@/components/DialogSelSaleOutPdt'
import { cloneDeep } from 'lodash-es'


const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//l领料单数据
const channelData = reactive(
    {
    "oem_order_num": "",
    "oem_takein_num": "",
    "takein_man_id": "",
    "takein_date": "",
    "pdt_list": [],
    "note": "",
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:[]
    }, //审核决策
    fsm_cur_state:'',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)




//获取最新ID
const onChangeID = async()=>{
    const ret = await getOemTakeInNewnumApi()
    if(ret)
    {
        console.log(ret)
        channelData.oem_takein_num = ret.data.new_id
    }
}


//更新pdt库存
const updatePdtStock = async () => {

    const ret = await getOemLockListApi({               
                oem_order_num:channelData.oem_order_num,
                pdt_biaoshi:channelData.pdt_biaoshi,
                page: 1,
                count: 1000
            })
    if(ret)
    {
        channelData.pdt_list.splice(0,channelData.pdt_list.length, ...ret.data)
        for(let pdt of channelData.pdt_list)
        {
            pdt.领料数量 = Math.min(pdt.委外商可用库存,pdt.所需数量)
            pdt.领料备注 = ''
        }
    }

    console.log(channelData.pdt_list)
}

onMounted(async () => {    

    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('oem.add_takein')
        onChangeID()
        //克隆必要信息
        channelData.oem_order_num  = currentRoute.value.query.oem_order_num as string
        channelData.pdt_biaoshi  = currentRoute.value.query.pdt_biaoshi
        channelData.oem_order_sub = currentRoute.value.query.pdt_biaoshi
        channelData.parter_nick  = currentRoute.value.query.parter_nick
        const info = wsCache.get(appStore.getUserInfo)
        channelData.takein_man_id = info.id
        channelData.takein_man_name = info.resident_name
        channelData.takein_date = getTodayDate()

        updatePdtStock()
    }
    else
    {        
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('oem.modify_takein_look')
        }
        else
        {
            title.value = t('oem.modify_takein')
        }
        
        //查询信息 
        const ret = await getOemTakeInInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(channelData, ret.data)
            channelData.pdt_list = ret.data.pdt_list;
        }
        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }
    console.log(channelData)    
})

//校验输入
const recomputeCount = (pdt)=>{
    
    const nMin = Math.min((pdt.计划数量-pdt.已领料+pdt.已退料),(pdt.委外商可用库存+pdt.委外商此单锁定))

    if(pdt.领料数量>(pdt.委外商可用库存+pdt.委外商此单锁定) || pdt.领料数量>(pdt.计划数量-pdt.已领料+pdt.已退料))
        pdt.领料数量 = nMin
}



//添加出库产品
const showSelOutPdt = ref(false)
const onAddPdt = ()=>{
    showSelOutPdt.value = true
}
//选择产品回调
const onSelOutPdtCallback = (pdt)=>{
    channelData.pdt_list.push(pdt)
}

//删除某一个产品
const onDelPdt = (index)=>{
    channelData.pdt_list.splice(index,1)
}


//显示隐藏选择出库员窗口变量
const showSelOutUserDlg = ref(false)
//显示选择出库员弹窗
const onSelOutUser = ()=>{
    showSelOutUserDlg.value = true
}
//选择出库员回调
const onSelReturnCallback = (id,name)=>{
    console.log(id,name)
    channelData.takein_man_id = id
    channelData.takein_man_name = name
}


//保存
const onSave = async()=>{
    console.log(channelData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //提交时自动过滤数量为0或者为空的产品
    // channelData.pdt_list = channelData.pdt_list.filter(item=>parseFloat(item.领料数量)>0) 

    const tmp = cloneDeep(channelData)
    tmp.pdt_list = tmp.pdt_list.filter(item=>parseFloat(item.领料数量)>0) 
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }
    
    //增加审核流程参数
    const info = wsCache.get(appStore.getUserInfo)
    tmp.fsm_exe_man_name = info.resident_name
    tmp.fsm_exe_trig = '直接领取'

    if(tmp.id == undefined)
    {
        const ret = await addOemTakeInApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newLLSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret =await updateOemTakeInApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateLLSuccess'))
            back()
        }
    }


}
</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <el-popconfirm v-if="channelData.id==undefined"  title="是否确认发料?操作将影响库存！" @confirm="onSave">
                <template #reference>
                    <ElButton  type="primary">
                        提交
                    </ElButton>
                </template>
            </el-popconfirm>
        </template>
        <el-form :rules="rules" :model="channelData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('takein.id')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  v-model="channelData.oem_takein_num" :disabled="channelData.id!=undefined" />
                            <ElButton v-if="channelData.id==undefined" type="warning"  @click="onChangeID">{{ t('button.update') }}</ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="t('oem.name')"
                    class="flex">
                        <div>{{ channelData.oem_order_num }}</div> 
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('parter.nick')"
                    class="flex">
                        {{ channelData.parter_nick }}
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('takein.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="channelData.takein_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('return.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ channelData.takein_man_name }}</div> 
                        <!-- <ElButton @click="onSelOutUser">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton> -->
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        
        </el-form>
        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="channelData.pdt_list" style="width: 100%" border stripe>
            <el-table-column  :label="t('process.opt')" width="60" >
                <template #default="scope">
                    <div  type="primary">
                        <el-popconfirm v-if="currentRoute.query.type != 'info'" title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon  icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>                    
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index+1 }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div  class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" min-width="110" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick}}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip                        
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="base_unit" :label="t('product_manage.b_unit')" />
            <el-table-column show-overflow-tooltip  prop="委外商可用库存" :label="t('takein.out_stone')" />
            <el-table-column show-overflow-tooltip  prop="委外商此单锁定" :label="'委外商此单锁定'" />
            <el-table-column show-overflow-tooltip  prop="计划数量" :label="t('takeout.jh_count')" >
                <template #default="scope">
                    {{ Math.ceil(scope.row.计划数量) }}
                </template>
            </el-table-column>  
            <el-table-column show-overflow-tooltip  prop="已领料" :label="t('takein.actual_count')" />        
            <el-table-column show-overflow-tooltip  prop="已退料" :label="t('takein.back')" />
            <el-table-column show-overflow-tooltip   :label="t('takein.left')" >
                <template #default="scope">
                    {{ Math.ceil(scope.row.计划数量)-scope.row.已领料+scope.row.已退料 }}
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  :label="t('takein.in')">
                <template #default="scope">
                    <el-input  v-model="scope.row.领料数量" @input="recomputeCount(scope.row)" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  :label="t('saleout.remark')">
                <template #default="scope">
                    <el-input  v-model="scope.row.发料备注" />
                </template>
            </el-table-column>
        </el-table>

        <!-- 显示合计 -->
        <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">发货备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="channelData.note" clearable :autosize="{ minRows: 3, maxRows: 3 }" type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>


    <div class="mb-20"></div>

    <!-- 选择发货领取员 -->
    <DialogUser :param="''" v-model:show="showSelOutUserDlg" :title="t('msg.selectUser')" @on-submit="onSelReturnCallback"/>


    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
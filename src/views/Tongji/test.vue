<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElForm, ElFormItem, ElMessage, ElButton, ElInput, ElDatePicker } from 'element-plus';
import { reactive, ref, onMounted, onBeforeMount, nextTick } from 'vue'
import * as VTable from '@visactor/vtable'

const { t } = useI18n()

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//VTable容器引用
const vtableContainer = ref<HTMLElement | null>(null)
//VTable实例
let vtableInstance: VTable.ListTable | null = null
//表对象高度
const tableHeight = ref(400)
//查询条件
//定义搜索条件
const defaultCondition = {
    agent_id :'',
  page: 1,
  count: 20,
  start_date: '',
  end_date: '',
  date: null as any,
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//VTable 列定义
const columns = [
  {
    field: 'id',
    title: 'ID',
    width: 80,
    sort: true
  },
  {
    field: 'name',
    title: '姓名',
    width: 120
  },
  {
    field: 'age',
    title: '年龄',
    width: 80
  },
  {
    field: 'department',
    title: '部门',
    width: 120
  },
  {
    field: 'position',
    title: '职位',
    width: 150
  },
  {
    field: 'salary',
    title: '薪资',
    width: 100
  },
  {
    field: 'status',
    title: '状态',
    width: 100
  },
  {
    field: 'action',
    title: '操作',
    width: 150
  }
]

//数据源 - 写死的测试数据
const tableData = reactive([
  {
    id: 1,
    name: '张三',
    age: 28,
    department: '技术部',
    position: '前端工程师',
    salary: '15000',
    status: '在职',
    action: '编辑 | 删除'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    department: '产品部',
    position: '产品经理',
    salary: '18000',
    status: '在职',
    action: '编辑 | 删除'
  },
  {
    id: 3,
    name: '王五',
    age: 26,
    department: '设计部',
    position: 'UI设计师',
    salary: '12000',
    status: '离职',
    action: '编辑 | 删除'
  },
  {
    id: 4,
    name: '赵六',
    age: 30,
    department: '技术部',
    position: '后端工程师',
    salary: '16000',
    status: '在职',
    action: '编辑 | 删除'
  },
  {
    id: 5,
    name: '钱七',
    age: 35,
    department: '运营部',
    position: '运营专员',
    salary: '10000',
    status: '试用期',
    action: '编辑 | 删除'
  }
])

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  // 这里可以根据搜索条件过滤数据
  initVTable()
}

//清除条件
const onClear = () => {
  searchCondition.reset()
}
//初始化VTable
const initVTable = () => {
  if (!vtableContainer.value) return

  // 如果已存在实例，先销毁
  if (vtableInstance) {
    vtableInstance.release()
  }

  const option = {
    container: vtableContainer.value,
    columns: columns,
    records: tableData,
    widthMode: 'standard' as const,
    heightMode: 'autoHeight' as const,
    autoWrapText: true,
    theme: VTable.themes.DEFAULT.extends({
      headerStyle: {
        bgColor: '#f6f6f6',
        color: '#333',
        fontWeight: 'bold'
      },
      bodyStyle: {
        bgColor: '#fff',
        color: '#333'
      }
    })
  }

  vtableInstance = new VTable.ListTable(option)

  // 监听单元格点击事件
  vtableInstance.on('click_cell', (args: any) => {
    const { col, row } = args
    if (columns[col]?.field === 'action') {
      handleOper('click', tableData[row - 1]) // row-1 因为表头占用了第0行
    }
  })
}

//更新表高度
const updateTableHeight = () => {
  if (vtableContainer.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 200
    // VTable 会自动适应容器高度，不需要手动设置
  }
}

//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}

//处理表格对象操作
const handleOper = async (type: string, row: any) => {
  console.log('操作类型:', type, '行数据:', row)
  if (type === 'click') {
    ElMessage.success(`点击了第${row.id}行的操作列`)
  }
}


onMounted(async () => {
  await nextTick()
  initVTable() // 初始化VTable
  updateTableHeight() // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize)

  //监听键盘事件
  const inputs = document.querySelectorAll('input')
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch()
      }
    })
  })
})

// 在组件销毁前移除窗口大小变化的监听器和销毁VTable实例
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize)
  if (vtableInstance) {
    vtableInstance.release()
  }
})


</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
    <!-- 搜索条件区域 -->
    <div class="mb-4 p-4 bg-white rounded shadow">
      <ElForm :model="searchCondition" inline>
        <ElFormItem label="代理ID:">
          <ElInput
            v-model="searchCondition.agent_id"
            placeholder="请输入代理ID"
            clearable
            style="width: 200px"
          />
        </ElFormItem>
        <ElFormItem label="日期范围:">
          <ElDatePicker
            v-model="searchCondition.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="onSearch">查询</ElButton>
          <ElButton @click="onClear">清除</ElButton>
        </ElFormItem>
      </ElForm>
    </div>

    <!-- VTable 表格区域 -->
    <div class="bg-white rounded shadow p-4">
      <div
        ref="vtableContainer"
        :style="{ height: tableHeight + 'px' }"
        class="vtable-container"
      ></div>
    </div>
  </div>
</template>

<style lang="less" scoped>
// VTable 容器样式
.vtable-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

// 表单样式
:deep(.el-form-item__label) {
  width: 120px;
  text-align: right;
  padding-right: 10px;
}

// 搜索区域样式
.mb-4 {
  margin-bottom: 16px;
}

.p-4 {
  padding: 16px;
}

.bg-white {
  background-color: #fff;
}

.rounded {
  border-radius: 4px;
}

.shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElPopconfirm, ElInputNumber, ElDialog, ElForm, ElFormItem, ElRow, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { addUserScoreApi, deleteCZApi, delUserScoreApi, getCashWater<PERSON>ist<PERSON><PERSON>, getCZList<PERSON>pi, getCZTJApi, getDownloadListApi, getPlayerTongjiListApi, getRegisteListApi, getUserCashWaterInfoApi, getWithdrawListApi, getWithdrawTJApi, modifyUserVipTypeApi, updateCZFailedApi, updateCZSuccessApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';

const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    agent_id :'',
    start_date: '',
    end_date: '',
    date:['',''],
    user_id:'',
    user_name:'',
    未登录时间:'',
    进货大于等于:'',
    进货小于等于:'',
    出货大于等于:'',
    出货小于等于:'',
    page: 1,
    count: 20,
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//数据源
const cashData = reactive([])

//查询数据
const getPlayerTongjiList = async () => {

  let temp = cloneDeep(searchCondition)
  temp.start_date = temp.date[0]
  temp.end_date = temp.date[1]
  delete temp.date

  temp.出货大于等于 = temp.出货大于等于=='' ? '' : parseInt(temp.出货大于等于*100)
  temp.出货小于等于 = temp.出货小于等于==''? '' : parseInt(temp.出货小于等于*100)
  temp.进货大于等于 = temp.进货大于等于==''? '' : parseInt(temp.进货大于等于*100)
  temp.进货小于等于 = temp.进货小于等于==''? '' : parseInt(temp.进货小于等于*100)

  const ret = await getPlayerTongjiListApi(temp)
  if (ret) {
    console.log(ret)
    cashData.splice(0, cashData.length, ...ret.data)
    totleCount.value = parseInt(ret.count)
  }
}


//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getPlayerTongjiList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
    getPlayerTongjiList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getPlayerTongjiList()
}


//处理表格对象操作
const handleOper = async (type, row) => {
  console.log(row)

}


onMounted(() => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  getPlayerTongjiList()
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});



const cellClassName = (cell) => {
  // console.log(cell)
  // if (cell.column.label == '下载时间') {
    if(cell.row.status == '(notfound)')
      return 'font-bold text-red-400 '

    // if(cell.row.reg_source == 'salesmartly'){
    //   return 'font-bold text-blue-400 '
    // }
  // }
  // if (cell.column.label == '状态') {
  //   if (cell.row.status == 2) {
  //     return 'font-bold text-green-500'
  //   }
  //   else if (cell.row.status == 3) {
  //     return 'font-bold text-red-500'
  //   }
  //   else if (cell.row.status == 1) {
  //     return 'font-bold text-yellow-500'
  //   }

  // }
}


</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
    <div ref="rootRef" class="flex relative">
      <!-- <div class="absolute top-8 left-8">
        <ElButton type="success" @click="onAddCustmer">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
      </div> -->
      <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
        <div class="text-center mb-5 font-bold text-[30px] text-green-600">玩家数据明细</div>
        <div style1="border: 1px solid rgb(143, 143, 143);color:#666;" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">代理ID:</div>
            <el-input size="small" class="ml-4" v-model="searchCondition.agent_id " placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">玩家ID:</div>
            <el-input size="small" class="ml-4" v-model="searchCondition.user_id " placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">玩家昵称:</div>
            <el-input size="small" class="ml-4" v-model="searchCondition.user_name " placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">未登录时间:</div>
            <el-select size="small" class="ml-4" v-model="searchCondition.未登录时间" placeholder="">
              <el-option v-for="item in [['从未登录',-1],['当天登录',0],['超过1天未登录',1],['超过3天未登录',3],['超过7天未登录',7],['超过15天未登录',15],['超过30天未登录',30]]" :key="item[1]" :label="item[0]" :value="item[1]" />
            </el-select>
          </div>

          <div class="inline-flex items-center mr-5 ">
            <el-input size="small" class="!w-[80px]" v-model="searchCondition.进货大于等于 " placeholder="进货>=" />
            <el-input size="small" class="!w-[80px]" v-model="searchCondition.进货小于等于 " placeholder="进货<=" />
          </div>
          <div class="inline-flex items-center mr-5 ">
            <el-input size="small" class="!w-[80px]" v-model="searchCondition.出货大于等于 " placeholder="出货>=" />
            <el-input size="small" class="!w-[80px]" v-model="searchCondition.出货小于等于 " placeholder="出货<=" />
          </div>
          <div class="inline-flex items-center mr-5">
              <div class="w-20 flex-none flex justify-end text-sm">注册时间:</div>
              <el-date-picker size="small" class="ml-4 w-53" v-model="searchCondition.date"
                  type="daterange" range-separator="To" start-placeholder="开始时间"
                  end-placeholder="结束时间" :clearable="false" value-format="YYYY-MM-DD" />
          </div>
        </div>
        <div class="w-[100%] inline-flex items-center  mr-5 mb-2">
            <ElButton class="ml-auto" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton class="mr-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
        </div>
        <div>
            总人数：<span class="font-bold text-red-500">{{totleCount}}</span>
        </div>
        <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="cashData"
          style="width: 100%;color: #666666;" :height1="tableHeight" border stripe :cell-class-name="cellClassName">
          <el-table-column show-overflow-tooltip prop="玩家ID" label="玩家ID"  width="90"/>
          <el-table-column show-overflow-tooltip prop="玩家名" label="玩家名"  />
          <el-table-column show-overflow-tooltip prop="总盈亏" label="总盈亏"  width="70">
            <template #default="scope">
              <span v-if="scope.row.总盈亏>0" class="">{{scope.row.总盈亏/100}}</span>
              <span v-if="scope.row.总盈亏<0" class="font-bold text-green-500">{{scope.row.总盈亏/100}}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="总进货" label="总进货"  width="70">
            <template #default="scope">
              <span >{{scope.row.总进货/100}}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="总出货" label="总出货"  width="70">
            <template #default="scope">
              <span >{{scope.row.总出货/100}}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="注册时间" label="注册时间"  />
          <el-table-column show-overflow-tooltip prop="注册IP" label="注册IP"  />
          <el-table-column show-overflow-tooltip prop="登录次数" label="登录次数"  width="80"/>
          <el-table-column show-overflow-tooltip prop="最后登录时间" label="最后登录时间"  />
          <el-table-column show-overflow-tooltip prop="最后登录IP" label="最后登录IP"  />
          <el-table-column show-overflow-tooltip prop="经理" label="经理"  />

        </el-table>
        <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />

      </div>
    </div>


  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
  color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
  /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
}

:deep(.el-form-item__label) {
  width: 120px;
  /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
  text-align: right;
  /* 让文本右对齐，使标题看起来更整齐 */
  padding-right: 10px;
  /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

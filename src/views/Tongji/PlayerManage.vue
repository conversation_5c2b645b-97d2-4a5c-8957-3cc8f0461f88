<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElCard, ElForm, ElFormItem, ElRow, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { ceilToFixed, checkPermissionApi, decodeString, getTodayDate } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { getActiveOneDayApi, getOtherDateInfo<PERSON>pi, getTongjiFishPlayerListApi, getTongjiPlayerListApi, getTongjiRegisterApi, getWithdrawListApi, getWithdrawTJApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';
import { Howl } from 'howler';
import { EChartsOption } from 'echarts'
import { pieOptions, barOptions, lineOptions, lineOptionsHis } from './echarts-data'
import { Echart } from '@/components/Echart'
import { set } from 'lodash-es'
const lineOptionsData = reactive<EChartsOption>(lineOptions) as EChartsOption
const lineOptionsDataHis = reactive<EChartsOption>(lineOptionsHis) as EChartsOption
const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    user_id: '',
    status: '',
    page: 1,
    count: 1000
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})

//客户数据源
const tongjiData = reactive([])

//查询客户数据
const getTongjiPlayer = async () => {
    let ret = await getTongjiPlayerListApi(searchCondition)
    if (ret) {
        console.log(ret)
        let tmp = [...ret.data]
        const msg = decodeURIComponent(decodeString(ret.player_count_list, true))
        let arrD = JSON.parse(msg)
        console.log('--', arrD)
        tongjiData.splice(0, tongjiData.length, ...tmp)
        set(
            lineOptionsData,
            'xAxis.data',
            arrD.map((v) => v[0]),
        )
        set(lineOptionsData, 'series', [
            {
                name: '今日活跃',
                smooth: true,
                type: 'line',
                data: arrD.map((v) => v[1]),
                animationDuration: 1500,
                animationEasing: 'cubicInOut'
            },
            {
                name: '昨日活跃',
                smooth: true,
                type: 'line',
                data: arrD.map((v) => v[2]),
                animationDuration: 1500,
                animationEasing: 'cubicInOut'
            },
        ])


        //如果发现停止服务的则报警
        for (let one of tongjiData) {
            if (one.game_status == undefined)
                continue;
            if (one.game_status.indexOf('停止') >= 0) {
                playAudio()
                break
            }
        }

        // ret = await getTongjiFishPlayerListApi({
        //     page: 1,
        //     count: 2000
        // })
        // if (ret) {
        //     //在原有tongjiData数据基础上追加ret.data
        //     tmp.push(...ret.data)
        //     tongjiData.splice(0, tongjiData.length, ...tmp)


        // }

        totleCount.value = parseInt(ret.count)
    }
    await getTongjiRegister()
}

//查询客户数据
const getTongjiRegister = async () => {
    const ret = await getTongjiRegisterApi({
        start_date: searchDateRound.value[0],
        end_date: searchDateRound.value[1],
    })
    if (ret) {
        console.log(ret)

        // tongjiData.splice(0, tongjiData.length, ...tmp)
        Object.assign(tjZC, ret.data)
    }
}

//查询指定日期
const searchDateRound = ref(['', ''])
// const hisRegCount = ref(0)
// const getOtherDateInfo = async () => {
//     let ret = await getOtherDateInfoApi({
//         start_date: searchDateRound.value[0],
//         end_date: searchDateRound.value[1],
//         page: 1,
//         count: 2000
//     })
//     if (ret) {
//         hisRegCount.value = ret.data.other_days
//     }
// }


//开始查询
const onSearch = () => {
    getTongjiPlayer()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
    if (userTableRef.value && rootRef.value) {
        tableHeight.value = rootRef.value.clientHeight - 400
    }
}
//浏览器大小变化
const handleWindowResize = () => {
    updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
    getTongjiPlayer()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getTongjiPlayer()
}
//创建新客户
const onAddCustmer = () => {

}

//处理表格对象操作
const handleOper = (type, row) => {

}

let timmer
onMounted(() => {
    updateTableHeight(); // 首次设置表格高度
    window.addEventListener('resize', handleWindowResize);

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    searchDateRound.value[0] = getTodayDate()
    searchDateRound.value[1] = getTodayDate()
    getTongjiPlayer()
    //启动定时刷新
    timmer = setInterval(() => {
        getTongjiPlayer()
    }, 4000);


    searchActiveHisDate.value = getTodayDate()
    getActiveOneDay()
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
    window.removeEventListener('resize', handleWindowResize);
});

// 在组件销毁前移除窗口大小变化的监听器
onBeforeUnmount(() => {
    window.removeEventListener('resize', handleWindowResize);
    clearInterval(timmer)
});

const cellClassName = (cell) => {
    if (cell.column.label == '游戏状态') {
        if (cell.row.game_status == undefined)
            return ''
        let tt = cell.row.game_status
        if (tt.indexOf('停止') >= 0) {
            return 'font-bold bg-red-500 text-white'
        }
        else if (tt.indexOf('运行') >= 0)
            return 'font-bold text-green-500'
        else
            return ''

    }
}

const gameNameMap = {
    'da ting': '游戏大厅',
    'lingdangyouxi': '铃铛游戏',
    'shuihuzhuan': '水浒传',
    'zuan shi': '777',
    'ai ji zhen bao': '法老王',
    'nezhanaohai': '哪吒闹海',
    'taiji panda': '太极熊猫',
    'shuiguoxiaomali': '水果小玛丽',
    'black jack': '二十一点',
    'sen_lin_wu_hui': '森林舞会',
    'fish_1': '捕鱼1',
    'attlhp': 'ATT',
    'lian huan duo bao': '连环夺宝',
    'te lang pu': '特朗普',
    'sai ma': '皇家赛马',
    'fish_kuailebuyu_1':'快乐捕鱼',
    'fish_leitingzhanji_1':'雷霆战机',
    'fish_shenhaibuyu_1':'深海捕鱼',
};
const translateGameName = (name) => {
    return gameNameMap[name] || name;
}

const tjZC = reactive({
    'total_count': 0,
    'today': 0,
    'seven_days': 0,
    'thirty_days': 0,
    'ninety_days': 0
})


//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        console.log('1111111111111')
        if (['player_count'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return prev + curr;
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })

    return sums;
}


const sound = ref(null);
const playAudio = () => {
    console.log('播放')
    sound.value = new Howl({
        src: ['服务异常.mp3'],
        volume: 1.0,
    });
    sound.value.play();
};

const searchActiveHisDate = ref('')
const getActiveOneDay = async () => {
    let ret = await getActiveOneDayApi({
        other_date: searchActiveHisDate.value,
        page: 1,
        count: 2000
    })
    if (ret) {
        console.log(ret)
        const msg = decodeURIComponent(decodeString(ret.player_count_list, true))
        let arrD = JSON.parse(msg)
        set(
            lineOptionsDataHis,
            'xAxis.data',
            arrD.map((v) => v[0]),
        )
        set(lineOptionsDataHis, 'series', [
            {
                name: '当日活跃',
                smooth: true,
                type: 'line',
                data: arrD.map((v) => v[1]),
                animationDuration: 1500,
                animationEasing: 'cubicInOut'
            },
            {
                name: '上日活跃',
                smooth: true,
                type: 'line',
                data: arrD.map((v) => v[2]),
                animationDuration: 1500,
                animationEasing: 'cubicInOut'
            },
        ])

    }
}
</script>

<template>
    <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
        <div ref="rootRef" class="flex relative">
            <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
                <div class="text-center mb-5 font-bold text-red-500 text-[30px]">用户统计</div>
                <div class="text-center mb-5">
                    <!-- <div class="inline-flex items-center mr-5 mb-2">
                        <div class="searchTitle">时间范围</div>
                        <el-date-picker size="small" class="searchItem" v-model="searchDateRound" type="daterange"
                            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" :clearable="false"
                            value-format="YYYY-MM-DD" />
                        <ElButton type="primary" class="mr-5" @click="getTongjiRegister">查询</ElButton>
                    </div> -->
                    <el-row class="text-center lineOut1 mb-2" justify="center">
                        <el-col :span="3" class="lineOut">
                            <el-statistic title="注册总人数" :value="tjZC.total_count" />
                        </el-col>
                        <el-col :span="3" class="lineOut ">
                            <el-statistic title="当日注册" :value="tjZC.today" />
                        </el-col>
                        <el-col :span="3" class="lineOut ">
                            <el-statistic title="昨日注册" :value="tjZC.yesterday_count" />
                        </el-col>
                        <el-col :span="3" class="lineOut">
                            <el-statistic title="最近7天" :value="tjZC.seven_days" />
                        </el-col>
                        <el-col :span="3" class="lineOut">
                            <el-statistic title="最近30天" :value="tjZC.thirty_days" />
                        </el-col>
                        <el-col :span="3" class="lineOut">
                            <el-statistic title="最近90天" :value="tjZC.ninety_days" />
                        </el-col>
                    </el-row>
                </div>





                <Echart :options="lineOptionsData" :height="250" v-if="checkPermissionApi('显示活跃用户统计')" />

                <div class="flex">
                    <ElCard class="mb-10 mt-5 text-center !w-[50%] mr-2">
                        <div class="text-center mb-5 font-bold text-green-500 text-[20px]">历史注册</div>
                        <div class="flex items-center">

                            <div class="inline-flex items-center mr-5">
                                <div class="searchTitle">时间范围</div>
                                <el-date-picker size="small" class="searchItem" v-model="searchDateRound"
                                    type="daterange" range-separator="To" start-placeholder="开始时间"
                                    end-placeholder="结束时间" :clearable="false" value-format="YYYY-MM-DD" />
                            </div>
                            <ElButton type="primary" class="mr-5" @click="getTongjiRegister">查询</ElButton>

                        </div>
                        <div class="flex justify-center items-center h-[200px]">
                            <div class="text-center  font-bold text-[20px]">
                                总注册人数:
                            </div>
                            <span class="text-center  font-bold text-red-500 text-[20px]">{{ tjZC.other_days }}人</span>
                        </div>


                    </ElCard>
                    <ElCard class="mb-10 mt-5 text-center !w-[50%]">
                        <div class="text-center mb-5 font-bold text-purple-500 text-[20px]">历史活跃</div>
                        <div class="flex items-center">

                            <div class="inline-flex items-center mr-5">
                                <div class="searchTitle">日期</div>
                                <el-date-picker v-model="searchActiveHisDate" type="date" placeholder=""
                                    format="YYYY/MM/DD" value-format="YYYY-MM-DD" :clearable="false" />
                            </div>
                            <ElButton type="primary" class="mr-5" @click="getActiveOneDay">查询</ElButton>
                        </div>
                        <Echart :options="lineOptionsDataHis" :height="250" v-if="checkPermissionApi('显示活跃用户统计')" />

                    </ElCard>
                </div>



                <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="tongjiData"
                    v-if="checkPermissionApi('显示游戏在线统计')" style="width: 100%;color: #666666;" :height1="tableHeight"
                    border show-summary :summary-method="getSummaries" :cell-class-name="cellClassName">
                    <el-table-column show-overflow-tooltip prop="game_id" label="游戏编号" width="200" />
                    <el-table-column show-overflow-tooltip prop="game_name" label="游戏名称">
                        <template #default="scope">
                            {{ translateGameName(scope.row.game_name) }}
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="player_count" label="在线人数" />
                    <el-table-column show-overflow-tooltip prop="enter_count" label="进入次数" />
                    <el-table-column show-overflow-tooltip prop="online_time" label="在线时长" />
                    <el-table-column show-overflow-tooltip align="center" prop="game_status" label="游戏状态" width="200">
                        <template #default="scope">
                            {{ scope.row.game_status }}
                        </template>
                    </el-table-column>

                </el-table>
                <!-- <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
                    v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                    layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" /> -->


            </div>
        </div>


    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    cursor: pointer;
    color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
    /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
}

:deep(.el-form-item__label) {
    width: 120px;
    /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
    text-align: right;
    /* 让文本右对齐，使标题看起来更整齐 */
    padding-right: 10px;
    /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

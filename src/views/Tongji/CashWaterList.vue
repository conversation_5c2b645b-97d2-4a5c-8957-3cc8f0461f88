<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElPopconfirm, ElInputNumber, ElDialog, ElForm, ElFormItem, ElRow, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { addUserScoreApi, deleteCZA<PERSON>, delUserScoreApi, getCashWater<PERSON>ist<PERSON><PERSON>, getCZList<PERSON>pi, getCZTJA<PERSON>, getUserCashWaterInfoApi, getWithdrawListApi, getWithdrawTJApi, modifyUserVipTypeApi, updateCZFailedApi, updateCZSuccessApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';

const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  user_id: '',
  call_type: '',
  call_loc: '',
  no_call_type:'',
  note: '',
  page: 1,
  count: 20,
  start_date: '',
  end_date: '',
  date:['',''],
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//数据源
const cashData = reactive([])

//查询数据
const getCashWaterList = async () => {
  if (!checkPermissionApi('允许查看游戏数据')) {
    searchCondition.no_call_type = '游戏'
  }

  let temp = cloneDeep(searchCondition)
  temp.start_date = temp.date[0]
  temp.end_date = temp.date[1]
  delete temp.date


  const ret = await getCashWaterListApi(temp)
  if (ret) {
    console.log(ret)
    cashData.splice(0, cashData.length, ...ret.data)
    totleCount.value = parseInt(ret.count)
  }
}


//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getCashWaterList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getCashWaterList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getCashWaterList()
}
//创建新客户
const onAddCustmer = () => {

}

//处理表格对象操作
const handleOper = async (type, row) => {
  console.log(row)

}


onMounted(() => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  getCashWaterList()
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});



const cellClassName = (cell) => {
  if (cell.column.label == '支付链接') {
    return 'font-bold text-green-400'
  }
  if (cell.column.label == '状态') {
    if (cell.row.status == 2) {
      return 'font-bold text-green-500'
    }
    else if (cell.row.status == 3) {
      return 'font-bold text-red-500'
    }
    else if (cell.row.status == 1) {
      return 'font-bold text-yellow-500'
    }

  }
}

const showInfo = ref(false)
const selItemdef = {
  user_id: '',
  名字: '',
  VIP: '',
  临时金币: '',
  账户金币: '',
  补分: 0,
  消分: 0,
}
const selItem = reactive({
  ...selItemdef,
  reset() {
    for (let key in this) {
      if (this.hasOwnProperty(key) && !(key in selItemdef) && key != 'reset') {
        delete this[key];
      }
    }
    Object.assign(this, selItemdef)
  }
})

const showUserInfo = async (row) => {
  showInfo.value = true;
  selItem.reset()
  selItem.user_id = row.user_id
  selItem.名字 = row.user_name
  let ret = await getUserCashWaterInfoApi({
    user_id: row.user_id
  })
  if (ret) {
    console.log(ret)
    Object.assign(selItem, ret.data)
  }
}

const onAddScore = async (item) => {
  console.log(item)
  let ret = await addUserScoreApi({
    user_id: item.user_id,
    coin: item.补分
  })
  if (ret) {
    ElMessage.success('操作成功')
    showInfo.value = false;
    getCashWaterList()
  }
}
const onDelScore = async (item) => {
  console.log(item)
  let ret = await delUserScoreApi({
    user_id: item.user_id,
    coin: item.消分
  })
  if (ret) {
    ElMessage.success('操作成功')
    showInfo.value = false;
    getCashWaterList()
  }
}

const onModifyUserVipType = async (item) => {
  let ret = await modifyUserVipTypeApi({
    VIP:item.VIP,
    user_id: item.user_id
  })
  if (ret) {
    ElMessage.success('操作成功')
    let ret = await getUserCashWaterInfoApi({
      user_id: item.user_id
    })
    if (ret) {
      console.log(ret)
      Object.assign(selItem, ret.data)
    }
  }
}

const placeData = { '16006':'皇家赛马','15201': '森林舞会','13101':'老捕鱼','13111':'海王捕鱼','13121':'深海捕鱼','13131':'雷霆战机','13141':'快乐捕鱼','13301':'疯狂捕鱼', '13901': '21点', '15010': '铃铛', '15016': '水浒传', '15035': '777钻石', '15036': '埃及珍宝', '15047': '哪吒闹海', '15050': '太极熊猫', '15060': '水果玛丽', '16007': '连环炮', '15200': '连环夺宝', '25036': '特朗普',
'15901':'Fire777',
'15902':'Deluxe777',
'15903':'Rush777',
'15904':'Grand777',
'15905':'Super',
 }
</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
    <div ref="rootRef" class="flex relative">
      <!-- <div class="absolute top-8 left-8">
        <ElButton type="success" @click="onAddCustmer">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
      </div> -->
      <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
        <div class="text-center mb-5 font-bold text-[30px] text-green-600">资金流水管理</div>
        <div style1="border: 1px solid rgb(143, 143, 143);color:#666;" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">玩家ID:</div>
            <el-input size="small" class="ml-4" v-model="searchCondition.user_id" placeholder="" />
          </div>
          <div class="inline-flex items-center  mr-5 mb-2" v-if="checkPermissionApi('允许查看游戏数据')">
            <div class="w-20 flex-none flex justify-end text-sm">类型:</div>
            <el-select size="small" class="ml-4 w-53" v-model="searchCondition.call_type" placeholder="">
              <el-option v-for="item in ['邀请','游戏', '赠送', '充值', '提现']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center  mr-5 mb-2" v-if="!checkPermissionApi('允许查看游戏数据')">
            <div class="w-20 flex-none flex justify-end text-sm">类型:</div>
            <el-select size="small" class="ml-4 w-53" v-model="searchCondition.call_type" placeholder="">
              <el-option v-for="item in ['邀请','赠送', '充值', '提现']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center  mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">地点:</div>
            <el-select size="small" class="ml-4 w-53" v-model="searchCondition.call_loc" placeholder="">
              <el-option v-for="(label, value) in placeData" :key="value" :label="label" :value="value" />
            </el-select>
          </div>
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">备注:</div>
            <el-select size="small" class="ml-4 w-53" v-model="searchCondition.note" placeholder="">
              <el-option v-for="item in ['VIP']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center mr-5">
              <div class="w-20 flex-none flex justify-end text-sm">时间范围:</div>
              <el-date-picker size="small" class="ml-4 w-53" v-model="searchCondition.date"
                  type="daterange" range-separator="To" start-placeholder="开始时间"
                  end-placeholder="结束时间" :clearable="false" value-format="YYYY-MM-DD" />
          </div>
          <div class="inline-flex items-center  mr-5 mb-2">
            <ElButton class="ml-4" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton class="mr-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="cashData"
          style="width: 100%;color: #666666;" :height1="tableHeight" border stripe :cell-class-name="cellClassName">
          <el-table-column show-overflow-tooltip prop="create_time" label="时间" width="160" />
          <el-table-column show-overflow-tooltip prop="user_id" label="玩家ID" />
          <el-table-column show-overflow-tooltip prop="user_name" label="昵称" width="160">
            <template #default="scope">
              <div class="nameStyle" @click="showUserInfo(scope.row)">{{ scope.row.user_name }}</div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="user_loc" label="位于" />
          <el-table-column show-overflow-tooltip prop="user_status" label="状态" />
          <el-table-column show-overflow-tooltip prop="call_loc" label="地点" width="160" />
          <el-table-column show-overflow-tooltip prop="call_type" label="类型" />
          <el-table-column show-overflow-tooltip prop="chg_money" label="预期变化" />
          <el-table-column show-overflow-tooltip prop="add_money" label="实际变化" />
          <el-table-column show-overflow-tooltip prop="old_money" label="变化前" />
          <el-table-column show-overflow-tooltip prop="new_money" label="变化后" />
          <el-table-column show-overflow-tooltip prop="tmp_money" label="累积" />
          <el-table-column show-overflow-tooltip prop="note" label="备注" />
          <el-table-column fixed="right" :label="t('userTable.operate')" width="100" v-if="checkPermissionApi('允许查看游戏数据')">
            <template #default="scope">
              <ElButton type="success" size="small" @click="showUserInfo(scope.row)">查询</ElButton>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />


        <el-dialog title="玩家信息" v-model="showInfo" width="500" align-center destroy-on-close>
          <el-form>
            <el-form-item class="titleShow" label="用户ID:">
              {{ selItem.user_id }}
            </el-form-item>
            <el-form-item class="titleShow" label="用户昵称:">
              {{ selItem.名字 }}
            </el-form-item>
            <el-form-item class="titleShow" label="VIP:">
              <!-- {{ selItem.VIP }} -->
              <el-select  class="w-23 mr-5" v-model="selItem.VIP" placeholder="">
                <el-option v-for="item in ['是','否']" :key="item" :label="item" :value="item" />
              </el-select>
              <el-popconfirm title="是否确定切换用户类型?" @confirm="onModifyUserVipType(selItem)">
                  <template #reference>
                    <ElButton type="primary">切换</ElButton>
                  </template>
                </el-popconfirm>
            </el-form-item>
            <el-form-item class="titleShow" label="账户金币:">
              {{ selItem.账户金币 }}
            </el-form-item>
            <el-form-item class="titleShow" label="临时金币:">
              {{ selItem.临时金币 }}
            </el-form-item>
            <el-form-item class="titleShow" label="补分:">
              <div class="flex">
                <ElInputNumber class="mr-2" :min="0" v-model="selItem.补分" placeholder="请输入补分" />
                <el-popconfirm title="确定是否给改玩家补分?" @confirm="onAddScore(selItem)">
                  <template #reference>
                    <ElButton type="primary">确定补分</ElButton>
                  </template>
                </el-popconfirm>
              </div>
            </el-form-item>
            <el-form-item class="titleShow" label="消分:">
              <div class="flex">
                <ElInputNumber class="mr-2" :min="0" v-model="selItem.消分" placeholder="请输入消分" />
                <el-popconfirm title="确定是否给改玩家消分?" @confirm="onDelScore(selItem)">
                  <template #reference>
                    <ElButton type="danger">确定消分</ElButton>
                  </template>
                </el-popconfirm>
              </div>
            </el-form-item>

          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="showInfo = false">关闭</el-button>
              <!-- <el-button type="danger" @click="handleOper('确定修改', selItem)">{{ selItem.id == undefined ? '新增' : '修改' }}</el-button> -->
            </div>
          </template>
        </el-dialog>
      </div>
    </div>


  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
  color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
  /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
}

:deep(.el-form-item__label) {
  width: 120px;
  /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
  text-align: right;
  /* 让文本右对齐，使标题看起来更整齐 */
  padding-right: 10px;
  /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

import { EChartsOption } from 'echarts';
import { useI18n } from '@/hooks/web/useI18n';

const { t } = useI18n();

// // 生成一天24小时每分钟对应的时间字符串数组，格式示例："00:00"、"00:01"等
// const generateTimeDataFor24Hours = () => {
//     const timeData = [];
//     for (let hour = 0; hour < 24; hour++) {
//         for (let minute = 0; minute < 60; minute++) {
//             const hourStr = hour < 10? `0${hour}` : `${hour}`;
//             const minuteStr = minute < 10? `0${minute}` : `${minute}`;
//             timeData.push(`${hourStr}:${minuteStr}`);
//         }
//     }
//     return timeData;
// };

export const lineOptions: EChartsOption = {
    title: {
        text: '活跃用户统计',
        left: 'center'
    },
    xAxis: {
        data: [],  // 使用生成的24小时每分钟时间数据
        boundaryGap: false,
        axisTick: {
            show: false
        }
    },
    grid: {
        left: 20,
        right: 20,
        bottom: 20,
        top: 80,
        containLabel: true
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross'
        },
        padding: [5, 10]
    },
    yAxis: {
        axisTick: {
            show: false
        }
    },
    legend: {
        data: ['今日活跃', '昨日活跃'],
        top: 50
    },
    series: [
        {
            name: '今日',
            smooth: true,
            type: 'line',
            // 这里需要替换为真实的一天内每分钟对应的数据，示例用随机数模拟（实际应用中需获取正确数据）
            data: [],
            animationDuration: 1500,
            animationEasing: 'cubicInOut'
        },
        // {
        //     name: '昨日',
        //     smooth: true,
        //     type: 'line',
        //     itemStyle: {},
        //     // 同样替换为真实的昨日每分钟对应的数据，示例用随机数模拟（实际应用中需获取正确数据）
        //     data: Array(24 * 60).fill(0).map(() => Math.floor(Math.random() * 1000)),
        //     animationDuration: 2800,
        //     animationEasing: 'quadraticOut'
        // }
    ]
};


export const lineOptionsHis: EChartsOption = {
    title: {
        text: '历史活跃用户统计',
        left: 'center'
    },
    xAxis: {
        data: [],  // 使用生成的24小时每分钟时间数据
        boundaryGap: false,
        axisTick: {
            show: false
        }
    },
    grid: {
        left: 20,
        right: 20,
        bottom: 20,
        top: 80,
        containLabel: true
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross'
        },
        padding: [5, 10]
    },
    yAxis: {
        axisTick: {
            show: false
        }
    },
    legend: {
        data: ['当日活跃', '上日活跃'],
        top: 50
    },
    series: [
        {
            name: '今日',
            smooth: true,
            type: 'line',
            // 这里需要替换为真实的一天内每分钟对应的数据，示例用随机数模拟（实际应用中需获取正确数据）
            data: [],
            animationDuration: 1500,
            animationEasing: 'cubicInOut'
        },
        // {
        //     name: '昨日',
        //     smooth: true,
        //     type: 'line',
        //     itemStyle: {},
        //     // 同样替换为真实的昨日每分钟对应的数据，示例用随机数模拟（实际应用中需获取正确数据）
        //     data: Array(24 * 60).fill(0).map(() => Math.floor(Math.random() * 1000)),
        //     animationDuration: 2800,
        //     animationEasing: 'quadraticOut'
        // }
    ]
};
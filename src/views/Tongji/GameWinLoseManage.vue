<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElPopconfirm, ElInputNumber, ElDialog, ElForm, ElFormItem, ElRow, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi, getTodayDate } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { addUserScoreApi, deleteCZ<PERSON><PERSON>, delUserScore<PERSON><PERSON>, getCZ<PERSON>ist<PERSON><PERSON>, getCZ<PERSON>J<PERSON><PERSON>, getGameWinLoseListApi, getUserCashWaterInfoApi, getWithdrawListApi, getWithdrawTJApi, updateCZFailedApi, updateCZSuccessApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';

const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    start_date: '',
    end_date: '',
    date: ['', ''],
    page: 1,
    count: 2000,
    game_id:'',
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
const id2name_data = {
                    '13101':'捕鱼',
                    '13901':'21点',
                    '15010':'铃铛',
                    '15016':'水浒传',
                    '15035':'777钻石',
                    '15036':'埃及珍宝',
                    '15047':'哪吒闹海',
                    '15050':'太极熊猫',
                    '15060':'水果玛丽',
                    '16007':'连环炮',
                    '15200':'连环夺宝',
                    '25036':'特朗普',
                    '16006':'赛马',
                    '13131':'雷霆战机',
                    '13141':'快乐捕鱼',
                    '13301':'疯狂捕鱼',
                    '13121':'深海捕鱼',
                    '13111':'海王2',
                    '15901':'Fire777',
                    '15902':'Deluxe777',
                    '15903':'Rush777',
                    '15904':'Grand777',
                    '15905':'超级钻石',
                    }
//数据源
const tongjiData = reactive([])

const getGameWinLoseList = async (param = '') => {

 //查询前先复位一下数据
 tongjiData.splice(0, tongjiData.length)
    // for(let item in id2name_data){
    //     tongjiData.push([id2name_data[item],[],[],[],[],[],[]])
    // }
    console.log('重新排序')
    // 提取对象的键
    const keys = Object.keys(id2name_data);
    // 对键进行从大到小排序
    keys.sort((a, b) => parseInt(b) - parseInt(a));
    // 遍历排序后的键
    keys.forEach(key => {
        tongjiData.push([id2name_data[key], [], [], [], []]);
    });


    for(let key of keys){
        let tmp = cloneDeep(searchCondition)
        tmp.start_date = searchCondition.date[0]
        tmp.end_date = searchCondition.date[1]
        tmp.game_id = key
        delete tmp.date
        await getGameWinLoseListApi(tmp).then((ret)=>{
            console.log(ret)
            // tongjiData.splice(0, tongjiData.length, ...ret.data)
            // totleCount.value = parseInt(ret.count)
            for(let i=0;i<tongjiData.length;i++){
                if(tongjiData[i][0]==ret.data[0][0]){
                    tongjiData[i].splice(0, tongjiData[i].length-1,...ret.data[0])
                }
            }
        })

        // const ret = await getGameWinLoseListApi(tmp)
        // if (ret) {
        //     console.log(ret)
        //     // tongjiData.splice(0, tongjiData.length, ...ret.data)
        //     // totleCount.value = parseInt(ret.count)
        //     for(let i=0;i<tongjiData.length;i++){
        //         if(tongjiData[i][0]==ret.data[0][0]){
        //             tongjiData[i].splice(0, tongjiData[i].length-1,...ret.data[0])
        //         }
        //     }
        // }  
    }

}

//查询数据
const getGameWinLoseListFun = async (param = '') => {
    let tmp = cloneDeep(searchCondition)
    tmp.start_date = searchCondition.date[0]
    tmp.end_date = searchCondition.date[1]
    tmp.game_id = param
    delete tmp.date
    const ret = await getGameWinLoseListApi(tmp)
    if (ret) {
        console.log(ret)
        // tongjiData.splice(0, tongjiData.length, ...ret.data)
        // totleCount.value = parseInt(ret.count)
    }
}


//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getGameWinLoseList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
    if (userTableRef.value && rootRef.value) {
        tableHeight.value = rootRef.value.clientHeight - 400
    }
}
//浏览器大小变化
const handleWindowResize = () => {
    updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
    getGameWinLoseList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getGameWinLoseList()
}

//处理表格对象操作
const handleOper = async (type, row) => {
    console.log(row)

}


onMounted(() => {
    updateTableHeight(); // 首次设置表格高度
    window.addEventListener('resize', handleWindowResize);

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });
    searchCondition.date[0] = getTodayDate()
    searchCondition.date[1] = getTodayDate()

    //初始化tongjiData
    for(let item in id2name_data){
        tongjiData.push([id2name_data[item],[],[],[],[],[],[]])
    }
    getGameWinLoseList()
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
    window.removeEventListener('resize', handleWindowResize);
});



const cellClassName = ({ row, column, rowIndex, columnIndex }) => {
    let css = ''
    if (column.label == '总数') {
        css += 'font-bold  bg-[#f6f6f6] '
    }

    if (row[0] == '总计') {
        css = 'font-bold bg-[#f6f6f6] '
    }

    if (column.property && column.property.includes('[') && column.property.includes(']')) {
        // 解析prop属性，例如"[1][0]"
        let props = column.property.match(/\[(.*?)\]/g);
        let value = row;
        for (let prop of props) {
            prop = prop.replace(/[\[\]]/g, '');
            value = value[prop];
        }
        console.log('????')
        if(value != undefined){
            if (value.indexOf('%')>=0 && value.indexOf('-')<0 && value.indexOf('=0%')<0) {
                css += ' text-green-500 ';
            }
        }

    }

    return css
}

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        console.log('1111')
        if (['[1]', '[2]', '[3]', '[4]', '[5]'].includes(column.property)) {
            const values = data.map(item => Number(item[Number(column.property.replace('[', '').replace(']', ''))]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return prev + curr;
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })

    return sums;
}
</script>

<template>
    <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
        <div ref="rootRef" class="flex relative">
            <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
                <div class="text-center mb-5 font-bold text-[30px] text-green-600">游戏输赢统计</div>
                <div class="flex items-center mb-4">

                    <div class="inline-flex items-center mr-5">
                        <div class="searchTitle">时间范围</div>
                        <el-date-picker size="small" class="searchItem" v-model="searchCondition.date" type="daterange"
                            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" :clearable="false"
                            value-format="YYYY-MM-DD" />
                    </div>
                    <ElButton type="primary" class="mr-5" @click="onSearch">查询</ElButton>
                </div>
                <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="tongjiData"
                    style="width: 100%;color: #666666;" :height1="tableHeight" border :cell-class-name="cellClassName">
                    <el-table-column align="center" show-overflow-tooltip prop="[0]" label="游戏名" />
                    <el-table-column align="center" show-overflow-tooltip prop="[4]" label="查询日输赢">
                        <el-table-column align="center" show-overflow-tooltip prop="[4][0]" label="免费" >
                            <template #default="scope">
                            <div >{{ scope.row[4][0] }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" show-overflow-tooltip prop="[4][1]" label="VIP" ></el-table-column>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="[1]" label="今日输赢">
                        <el-table-column align="center" show-overflow-tooltip prop="[1][0]" label="免费" />
                        <el-table-column align="center" show-overflow-tooltip prop="[1][1]" label="VIP" />
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="[2]" label="昨日输赢">
                        <el-table-column align="center" show-overflow-tooltip prop="[2][0]" label="免费" />
                        <el-table-column align="center" show-overflow-tooltip prop="[2][1]" label="VIP" />
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="[3]" label="近7日输赢">
                        <el-table-column align="center" show-overflow-tooltip prop="[3][0]" label="免费" />
                        <el-table-column align="center" show-overflow-tooltip prop="[3][1]" label="VIP" />
                    </el-table-column>

                </el-table>
                <!-- <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" /> -->

            </div>
        </div>


    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    cursor: pointer;
    color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
    /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
}

:deep(.el-form-item__label) {
    width: 120px;
    /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
    text-align: right;
    /* 让文本右对齐，使标题看起来更整齐 */
    padding-right: 10px;
    /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

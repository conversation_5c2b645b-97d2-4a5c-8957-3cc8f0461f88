<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDialog, ElForm, ElFormItem, ElRow, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { ceilToFixed, checkPermissionApi, getTodayDate } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { getTongjiChannelListApi, getTongjiFishPlayerListApi, getTongjiPlayerListApi, getTongjiRegisterApi, getWithdrawListApi, getWithdrawTJApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';

const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    start_date: '',
    end_date: '',
    date: ['', ''],
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})

//客户数据源
const tongjiData = reactive([])

//查询客户数据
const getTongjiChannel = async () => {
    let tmp = cloneDeep(searchCondition)
    tmp.start_date = searchCondition.date[0]
    tmp.end_date = searchCondition.date[1]
    delete tmp.date
    let ret = await getTongjiChannelListApi(tmp)
    if (ret) {
        console.log(ret)

        tongjiData.splice(0, tongjiData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }

}


//开始查询
const onSearch = () => {
    getTongjiChannel()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
    if (userTableRef.value && rootRef.value) {
        tableHeight.value = rootRef.value.clientHeight - 400
    }
}
//浏览器大小变化
const handleWindowResize = () => {
    updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
    getTongjiChannel()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getTongjiChannel()
}
//创建新客户
const onAddCustmer = () => {

}

//处理表格对象操作
const handleOper = (type, row) => {

}

let timmer
onMounted(() => {
    updateTableHeight(); // 首次设置表格高度
    window.addEventListener('resize', handleWindowResize);

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    searchCondition.date[0] = getTodayDate()
    searchCondition.date[1] = getTodayDate()
    getTongjiChannel()
    //启动定时刷新
    timmer = setInterval(() => {
        getTongjiChannel()
    }, 2000);
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
    window.removeEventListener('resize', handleWindowResize);
});

// 在组件销毁前移除窗口大小变化的监听器
onBeforeUnmount(() => {
    window.removeEventListener('resize', handleWindowResize);
    clearInterval(timmer)
});

const cellClassName = (cell) => {
    if (cell.column.label == '到账金额') {
        return 'font-bold text-green-400'
    }
    if (cell.column.label == '状态') {
        if (cell.row.status == 'Successful Withdrawal') {
            return 'font-bold text-green-500'
        }
        else if (cell.row.status == 'Withdrawal Failed') {
            return 'font-bold text-red-500'
        }
        else if (cell.row.status == 'Processing Payment') {
            return 'font-bold text-yellow-500'
        }

    }
}

const gameNameMap = {
    'da ting': '游戏大厅',
    'lingdangyouxi': '铃铛游戏',
    'shuihuzhuan': '水浒传',
    'zuan shi': '777',
    'ai ji zhen bao': '法老王',
    'nezhanaohai': '哪吒闹海',
    'taiji panda': '太极熊猫',
    'shuiguoxiaomali': '水果小玛丽',
    'black jack': '二十一点',
    'sen_lin_wu_hui': '森林舞会',
    'fish_1': '捕鱼1',
};
const translateGameName = (name) => {
    return gameNameMap[name] || name;
}

const tjZC = reactive({
    'total_count': 0,
    'today': 0,
    'seven_days': 0,
    'thirty_days': 0,
    'ninety_days': 0
})


//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        console.log('1111111111111')
        if (['全部其它日下载数量','安卓其它日下载数量','苹果其它日下载数量','全部今日下载数量', '安卓今日下载数量', '苹果今日下载数量', '全部昨日下载数量', '安卓昨日下载数量', '苹果昨日下载数量', '全部7日下载数量', '安卓7日下载数量', '苹果7日下载数量', '全部30日下载数量', '安卓30日下载数量', '苹果30日下载数量', '全部90日下载数量', '安卓90日下载数量', '苹果90日下载数量', '全部下载数量', '安卓下载数量', '苹果下载数量'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return prev + curr;
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })

    return sums;
}
</script>

<template>
    <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
        <div ref="rootRef" class="flex relative">
            <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
                <div class="text-center mb-5 font-bold text-blue-500 text-[30px]">渠道数据统计</div>
                <div class="flex items-center mb-4">

                    <div class="inline-flex items-center mr-5">
                        <div class="searchTitle">时间范围</div>
                        <el-date-picker size="small" class="searchItem" v-model="searchCondition.date" type="daterange"
                            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" :clearable="false"
                            value-format="YYYY-MM-DD" />
                    </div>
                    <ElButton type="primary" class="mr-5" @click="getTongjiChannel">查询</ElButton>

                </div>
                <div class="flex">
                    <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="tongjiData"
                        style="width: 100%;color: #666666;" :height1="tableHeight" border stripe show-summary
                        :summary-method="getSummaries" :cell-class-name="cellClassName" class="mb-5 mr-5">
                        <el-table-column show-overflow-tooltip prop="渠道" label="渠道" width="200" />

                        <el-table-column show-overflow-tooltip prop="全部其它日下载数量" label="全部其它日下载数量" />
                        <el-table-column show-overflow-tooltip prop="安卓其它日下载数量" label="安卓其它日下载数量" />
                        <el-table-column show-overflow-tooltip prop="苹果其它日下载数量" label="苹果其它日下载数量" />
                    </el-table>
                    <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="tongjiData"
                        style="width: 100%;color: #666666;" :height1="tableHeight" border stripe show-summary
                        :summary-method="getSummaries" :cell-class-name="cellClassName" class="mb-5">
                        <el-table-column show-overflow-tooltip prop="渠道" label="渠道" width="200" />

                        <el-table-column show-overflow-tooltip prop="全部今日下载数量" label="全部今日下载数量" />
                        <el-table-column show-overflow-tooltip prop="安卓今日下载数量" label="安卓今日下载数量" />
                        <el-table-column show-overflow-tooltip prop="苹果今日下载数量" label="苹果今日下载数量" />
                    </el-table>
                </div>

                <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="tongjiData"
                    style="width: 100%;color: #666666;" :height1="tableHeight" border stripe show-summary
                    :summary-method="getSummaries" :cell-class-name="cellClassName" class="mb-5">
                    <el-table-column show-overflow-tooltip prop="渠道" label="渠道" width="200" />

                    <el-table-column show-overflow-tooltip prop="全部昨日下载数量" label="全部昨日下载数量" />
                    <el-table-column show-overflow-tooltip prop="安卓昨日下载数量" label="安卓昨日下载数量" />
                    <el-table-column show-overflow-tooltip prop="苹果昨日下载数量" label="苹果昨日下载数量" />
                </el-table>
                <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="tongjiData"
                    style="width: 100%;color: #666666;" :height1="tableHeight" border stripe show-summary
                    :summary-method="getSummaries" :cell-class-name="cellClassName" class="mb-5">
                    <el-table-column show-overflow-tooltip prop="渠道" label="渠道" width="200" />

                    <el-table-column show-overflow-tooltip prop="全部7日下载数量" label="全部7日下载数量" />
                    <el-table-column show-overflow-tooltip prop="安卓7日下载数量" label="安卓7日下载数量" />
                    <el-table-column show-overflow-tooltip prop="苹果7日下载数量" label="苹果7日下载数量" />
                </el-table>
                <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="tongjiData"
                    style="width: 100%;color: #666666;" :height1="tableHeight" border stripe show-summary
                    :summary-method="getSummaries" :cell-class-name="cellClassName" class="mb-5">
                    <el-table-column show-overflow-tooltip prop="渠道" label="渠道" width="200" />

                    <el-table-column show-overflow-tooltip prop="全部30日下载数量" label="全部30日下载数量" />
                    <el-table-column show-overflow-tooltip prop="安卓30日下载数量" label="安卓30日下载数量" />
                    <el-table-column show-overflow-tooltip prop="苹果30日下载数量" label="苹果30日下载数量" />
                </el-table>
                <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="tongjiData"
                    style="width: 100%;color: #666666;" :height1="tableHeight" border stripe show-summary
                    :summary-method="getSummaries" :cell-class-name="cellClassName" class="mb-5">
                    <el-table-column show-overflow-tooltip prop="渠道" label="渠道" width="200" />

                    <el-table-column show-overflow-tooltip prop="全部90日下载数量" label="全部90日下载数量" />
                    <el-table-column show-overflow-tooltip prop="安卓90日下载数量" label="安卓90日下载数量" />
                    <el-table-column show-overflow-tooltip prop="苹果90日下载数量" label="苹果90日下载数量" />
                </el-table>
                <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="tongjiData"
                    style="width: 100%;color: #666666;" :height1="tableHeight" border stripe show-summary
                    :summary-method="getSummaries" :cell-class-name="cellClassName" class="mb-5">
                    <el-table-column show-overflow-tooltip prop="渠道" label="渠道" width="200" />
                    <el-table-column show-overflow-tooltip prop="全部下载数量" label="全部下载数量" />
                    <el-table-column show-overflow-tooltip prop="安卓下载数量" label="安卓下载数量" />
                    <el-table-column show-overflow-tooltip prop="苹果下载数量" label="苹果下载数量" />
                </el-table>
            </div>
        </div>


    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    cursor: pointer;
    color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
    /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
}

:deep(.el-form-item__label) {
    width: 120px;
    /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
    text-align: right;
    /* 让文本右对齐，使标题看起来更整齐 */
    padding-right: 10px;
    /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

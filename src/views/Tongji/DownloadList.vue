<script setup lang="ts">
import { ElButton, ElTable, ElTableColumn, ElInput, ElDatePicker, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { Icon } from '@/components/Icon'
import { onBeforeMount } from 'vue';
import { getDownloadListApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';

// 移除未使用的导入


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    agent_id :'',
  page: 1,
  count: 20,
  start_date: '',
  end_date: '',
  date: ['',''] as [string, string],
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//数据源
const cashData = reactive<any[]>([])

//查询数据
const getDownloadList = async () => {

  let temp = cloneDeep(searchCondition)
  temp.start_date = temp.date[0]
  temp.end_date = temp.date[1]
  // 移除 date 属性
  const { date, ...tempWithoutDate } = temp
  temp = tempWithoutDate as typeof temp
  const ret = await getDownloadListApi(temp)
  if (ret) {
    console.log(ret)
    cashData.splice(0, cashData.length, ...ret.data)
    totleCount.value = typeof ret.count === 'string' ? parseInt(ret.count) : ret.count
  }
}


//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getDownloadList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (_val: number) => {
    getDownloadList()
}
//page控件发生切换
const handleCurrentChange = (_val: number) => {
    getDownloadList()
}


onMounted(() => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  getDownloadList()
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});



const cellClassName = (cell: any) => {
  if (cell.column.label == '支付链接') {
    return 'font-bold text-green-400'
  }
  if (cell.column.label == '状态') {
    if (cell.row.status == 2) {
      return 'font-bold text-green-500'
    }
    else if (cell.row.status == 3) {
      return 'font-bold text-red-500'
    }
    else if (cell.row.status == 1) {
      return 'font-bold text-yellow-500'
    }

  }
}

</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
    <div ref="rootRef" class="flex relative">
      <!-- <div class="absolute top-8 left-8">
        <ElButton type="success" @click="onAddCustmer">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
      </div> -->
      <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
        <div class="text-center mb-5 font-bold text-[30px] text-green-600">下载统计明细</div>
        <div style1="border: 1px solid rgb(143, 143, 143);color:#666;" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">代理ID:</div>
            <el-input size="small" class="ml-4" v-model="searchCondition.agent_id " placeholder="" />
          </div>

          <div class="inline-flex items-center mr-5">
              <div class="w-20 flex-none flex justify-end text-sm">时间范围:</div>
              <el-date-picker size="small" class="ml-4 w-53" v-model="searchCondition.date"
                  type="daterange" range-separator="To" start-placeholder="开始时间"
                  end-placeholder="结束时间" :clearable="false" value-format="YYYY-MM-DD" />
          </div>
          <div class="inline-flex items-center  mr-5 mb-2">
            <ElButton class="ml-4" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton class="mr-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="cashData"
          style="width: 100%;color: #666666;" :height1="tableHeight" border stripe :cell-class-name="cellClassName">
          <el-table-column show-overflow-tooltip prop="create_time" label="下载时间"  />
          <el-table-column show-overflow-tooltip prop="platform" label="平台" />
          <el-table-column show-overflow-tooltip prop="client_ip" label="玩家IP" />
          <el-table-column show-overflow-tooltip prop="agent_id" label="代理ID" >
            <template #default="scope">
                <div v-if="scope.row.agent_id!=''">{{ scope.row.agent_id+' ('+scope.row.agent_name+')' }}</div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="url" label="推广来源地址" />

          <!-- <el-table-column fixed="right" :label="t('userTable.operate')" width="100" v-if="checkPermissionApi('允许查看游戏数据')">
            <template #default="scope">
              <ElButton type="success" size="small" @click="showUserInfo(scope.row)">查询</ElButton>
            </template>
          </el-table-column> -->
        </el-table>
        <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />

      </div>
    </div>


  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
  color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
  /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
}

:deep(.el-form-item__label) {
  width: 120px;
  /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
  text-align: right;
  /* 让文本右对齐，使标题看起来更整齐 */
  padding-right: 10px;
  /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

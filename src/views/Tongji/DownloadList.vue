<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElPopconfirm, ElInputNumber, ElDialog, ElForm, ElFormItem, ElRow, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted, nextTick } from 'vue'
import * as VTable from '@visactor/vtable'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { addUserScoreApi, deleteC<PERSON><PERSON><PERSON>, delUserScore<PERSON><PERSON>, getCashWater<PERSON>ist<PERSON>pi, getCZList<PERSON><PERSON>, getCZTJA<PERSON>, getDownloadListApi, getUserCashWaterInfoApi, getWithdrawListApi, getWithdrawTJApi, modifyUserVipTypeApi, updateCZFailedApi, updateCZSuccessApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';

const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//VTable容器引用
const vtableContainer = ref<HTMLElement | null>(null)
//VTable实例
let vtableInstance: VTable.ListTable | null = null
//表对象高度
const tableHeight = ref(100)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    agent_id :'',
  page: 1,
  count: 20,
  start_date: '',
  end_date: '',
  date: null as any,
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//VTable 列定义
const columns = [
  {
    field: 'create_time',
    title: '下载时间',
    width: 180
  },
  {
    field: 'platform',
    title: '平台',
    width: 120
  },
  {
    field: 'client_ip',
    title: '玩家IP',
    width: 150
  },
  {
    field: 'agent_display',
    title: '代理ID',
    width: 250
  },
  {
    field: 'url',
    title: '推广来源地址',
    minWidth: 300
  }
]

//数据源
const cashData = reactive([] as any[])

//查询数据
const getDownloadList = async () => {
  let temp = cloneDeep(searchCondition) as any
  // 安全处理日期数组
  if (temp.date && Array.isArray(temp.date) && temp.date.length >= 2) {
    temp.start_date = temp.date[0]
    temp.end_date = temp.date[1]
  } else {
    temp.start_date = ''
    temp.end_date = ''
  }
  delete temp.date

  const ret = await getDownloadListApi(temp)
  if (ret) {
    console.log(ret)
    // 处理数据，为代理ID列添加格式化显示
    const processedData = ret.data.map((item: any) => ({
      ...item,
      agent_display: item.agent_id !== '' ? `${item.agent_id} (${item.agent_name})` : ''
    }))
    cashData.splice(0, cashData.length, ...processedData)
    totleCount.value = typeof ret.count === 'string' ? parseInt(ret.count) : ret.count
    // 重新初始化VTable
    initVTable()
  }
}


//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getDownloadList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//初始化VTable
const initVTable = () => {
  if (!vtableContainer.value) return

  // 如果已存在实例，先销毁
  if (vtableInstance) {
    vtableInstance.release()
  }

  const option = {
    container: vtableContainer.value,
    columns: columns,
    records: cashData,
    widthMode: 'adaptive' as const, // 自适应宽度，横向最大化
    heightMode: 'autoHeight' as const,
    autoWrapText: true,
    stripe: true, // 斑马纹
    hover: {
      highlightMode: 'row' as const
    }, // 悬停效果
    select: {
      disableSelect: true
    },
    theme: VTable.themes.DEFAULT.extends({
      headerStyle: {
        bgColor: '#f8f9fa',
        color: '#333',
        fontWeight: 'bold',
        fontSize: 14,
        textAlign: 'center',
        borderColor: '#e9ecef',
        borderLineWidth: 1
      },
      bodyStyle: {
        bgColor: '#ffffff',
        color: '#333',
        fontSize: 13,
        borderColor: '#e9ecef',
        borderLineWidth: 1
      },
      frameStyle: {
        borderColor: '#e9ecef',
        borderLineWidth: 1
      },
      selectionStyle: {
        cellBgColor: 'rgba(33, 150, 243, 0.1)',
        cellBorderColor: '#2196f3'
      }
    }),
    // 自定义样式函数
    defaultRowHeight: 40,
    defaultHeaderRowHeight: 45
  }

  vtableInstance = new VTable.ListTable(option)
}

//更新表高度
const updateTableHeight = () => {
  if (vtableContainer.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
    getDownloadList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getDownloadList()
}
//创建新客户
const onAddCustmer = () => {

}

//处理表格对象操作
const handleOper = async (type, row) => {
  console.log(row)

}


onMounted(async () => {
  await nextTick()
  updateTableHeight() // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize)

  //监听键盘事件
  const inputs = document.querySelectorAll('input')
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch()
      }
    })
  })

  getDownloadList()
})

// 在组件销毁前移除窗口大小变化的监听器和销毁VTable实例
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize)
  if (vtableInstance) {
    vtableInstance.release()
  }
})



// 移除了 cellClassName 函数，因为 VTable 使用不同的样式配置方式

</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
    <div ref="rootRef" class="flex relative">
      <!-- <div class="absolute top-8 left-8">
        <ElButton type="success" @click="onAddCustmer">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
      </div> -->
      <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
        <div class="text-center mb-5 font-bold text-[30px] text-green-600">下载统计明细</div>
        <div style1="border: 1px solid rgb(143, 143, 143);color:#666;" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">代理ID:</div>
            <el-input size="small" class="ml-4" v-model="searchCondition.agent_id " placeholder="" />
          </div>

          <div class="inline-flex items-center mr-5">
              <div class="w-20 flex-none flex justify-end text-sm">时间范围:</div>
              <el-date-picker size="small" class="ml-4 w-53" v-model="searchCondition.date"
                  type="daterange" range-separator="To" start-placeholder="开始时间"
                  end-placeholder="结束时间" :clearable="false" value-format="YYYY-MM-DD" />
          </div>
          <div class="inline-flex items-center  mr-5 mb-2">
            <ElButton class="ml-4" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton class="mr-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- VTable 表格区域 -->
        <div
          ref="vtableContainer"
          :style="{ height: tableHeight + 'px', width: '100%' }"
          class="vtable-container"
        ></div>
        <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />

      </div>
    </div>


  </div>
</template>

<style lang="less" scoped>
// VTable 容器样式
.vtable-container {
  width: 100% !important;
  overflow: hidden;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

// 确保 VTable 内部也是全宽
:deep(.vtable) {
  width: 100% !important;
}

// 优化 VTable 内部样式
:deep(.vtable-container canvas) {
  border-radius: 6px;
}

// 确保所有状态下文字都可见
:deep(.vtable) {
  canvas {
    // 确保 canvas 中的文字渲染正常
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
}

// 通过全局样式确保文字可见性
:deep(.vtable-container) {
  * {
    color: #333 !important;
  }
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
  color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
  /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
}

:deep(.el-form-item__label) {
  width: 120px;
  /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
  text-align: right;
  /* 让文本右对齐，使标题看起来更整齐 */
  padding-right: 10px;
  /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}

// 优化搜索区域样式
.bg-light-200 {
  background-color: #f8f9fa !important;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

// 优化按钮样式
:deep(.el-button) {
  border-radius: 4px;
}

// 优化分页样式
:deep(.el-pagination) {
  margin-top: 16px;
  padding: 16px 0;
}
</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElPopconfirm, ElInputNumber, ElDialog, ElForm, ElFormItem, ElRow, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted, nextTick } from 'vue'
import * as VTable from '@visactor/vtable'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { addUserScoreApi, deleteC<PERSON><PERSON><PERSON>, delUserScore<PERSON><PERSON>, getCashWater<PERSON>ist<PERSON>pi, getCZList<PERSON><PERSON>, getCZTJA<PERSON>, getDownloadListApi, getUserCashWaterInfoApi, getWithdrawListApi, getWithdrawTJApi, modifyUserVipTypeApi, updateCZFailedApi, updateCZSuccessApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';

const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//VTable容器引用
const vtableContainer = ref<HTMLElement | null>(null)
//VTable实例
let vtableInstance: VTable.ListTable | null = null
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    agent_id :'',
  page: 1,
  count: 20,
  start_date: '',
  end_date: '',
  date: null as any,
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//VTable 列定义
const columns = [
  {
    field: 'create_time',
    title: '下载时间',
    width: 150
  },
  {
    field: 'platform',
    title: '平台',
    width: 100
  },
  {
    field: 'client_ip',
    title: '玩家IP',
    width: 120
  },
  {
    field: 'agent_display',
    title: '代理ID',
    width: 200
  },
  {
    field: 'url',
    title: '推广来源地址',
    width: 300
  }
]

//数据源
const cashData = reactive([] as any[])

//查询数据
const getDownloadList = async () => {
  let temp = cloneDeep(searchCondition) as any
  temp.start_date = temp.date[0]
  temp.end_date = temp.date[1]
  delete temp.date
  const ret = await getDownloadListApi(temp)
  if (ret) {
    console.log(ret)
    // 处理数据，为代理ID列添加格式化显示
    const processedData = ret.data.map((item: any) => ({
      ...item,
      agent_display: item.agent_id !== '' ? `${item.agent_id} (${item.agent_name})` : ''
    }))
    cashData.splice(0, cashData.length, ...processedData)
    totleCount.value = parseInt(ret.count)
    // 重新初始化VTable
    initVTable()
  }
}


//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getDownloadList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//初始化VTable
const initVTable = () => {
  if (!vtableContainer.value) return

  // 如果已存在实例，先销毁
  if (vtableInstance) {
    vtableInstance.release()
  }

  const option = {
    container: vtableContainer.value,
    columns: columns,
    records: cashData,
    widthMode: 'standard' as const,
    heightMode: 'autoHeight' as const,
    autoWrapText: true,
    theme: VTable.themes.DEFAULT.extends({
      headerStyle: {
        bgColor: '#f6f6f6',
        color: '#333',
        fontWeight: 'bold'
      },
      bodyStyle: {
        bgColor: 'transparent',
        color: '#333'
      }
    })
  }

  vtableInstance = new VTable.ListTable(option)
}

//更新表高度
const updateTableHeight = () => {
  if (vtableContainer.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
    getDownloadList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getDownloadList()
}
//创建新客户
const onAddCustmer = () => {

}

//处理表格对象操作
const handleOper = async (type, row) => {
  console.log(row)

}


onMounted(async () => {
  await nextTick()
  updateTableHeight() // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize)

  //监听键盘事件
  const inputs = document.querySelectorAll('input')
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch()
      }
    })
  })

  getDownloadList()
})

// 在组件销毁前移除窗口大小变化的监听器和销毁VTable实例
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize)
  if (vtableInstance) {
    vtableInstance.release()
  }
})



// 移除了 cellClassName 函数，因为 VTable 使用不同的样式配置方式

</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
    <div ref="rootRef" class="flex relative">
      <!-- <div class="absolute top-8 left-8">
        <ElButton type="success" @click="onAddCustmer">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
      </div> -->
      <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
        <div class="text-center mb-5 font-bold text-[30px] text-green-600">下载统计明细</div>
        <div style1="border: 1px solid rgb(143, 143, 143);color:#666;" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">代理ID:</div>
            <el-input size="small" class="ml-4" v-model="searchCondition.agent_id " placeholder="" />
          </div>

          <div class="inline-flex items-center mr-5">
              <div class="w-20 flex-none flex justify-end text-sm">时间范围:</div>
              <el-date-picker size="small" class="ml-4 w-53" v-model="searchCondition.date"
                  type="daterange" range-separator="To" start-placeholder="开始时间"
                  end-placeholder="结束时间" :clearable="false" value-format="YYYY-MM-DD" />
          </div>
          <div class="inline-flex items-center  mr-5 mb-2">
            <ElButton class="ml-4" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton class="mr-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- VTable 表格区域 -->
        <div
          ref="vtableContainer"
          :style="{ height: tableHeight + 'px' }"
          class="vtable-container"
        ></div>
        <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />

      </div>
    </div>


  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
  color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
  /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
}

:deep(.el-form-item__label) {
  width: 120px;
  /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
  text-align: right;
  /* 让文本右对齐，使标题看起来更整齐 */
  padding-right: 10px;
  /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

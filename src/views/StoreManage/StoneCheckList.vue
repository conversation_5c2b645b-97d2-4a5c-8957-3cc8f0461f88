<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElProgress, ElDialog,ElForm,ElFormItem, ElButton, ElMessage, ElMessageBox, ElImage, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElPagination } from 'element-plus';
import { reactive, ref, onMounted, onActivated, onBeforeUnmount, watch, onBeforeMount } from 'vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { useCache } from '@/hooks/web/useCache'
import { DialogProduct } from '@/components/DialogProduct';
import { useAppStore } from '@/store/modules/app'
import { checkPermissionApi, checkRouterPermissionApi, closeOneTagByPath, downloadFile, getMoneyFlag, getTodayDate } from '@/api/tool';
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { DialogUser } from '@/components/DialogUser'
import { getOssSignApi, ossUpload } from '@/api/oss';
import { DialogImportTask } from '@/components/DialogImportTask'
import { importSellOrderApi } from '@/api/task'
import { cloneDeep } from 'lodash-es';
import { exportSellListApi } from '@/api/extra';
import { clearDefer, useDefer } from '@/hooks/web/useDefer';

import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { DialogProductLite } from '@/components/DialogProductLite';
import { addStoreCheckApi, delStoreCheckApi, getStoreCheckListApi,getStoreCheckNewnumApi } from '@/api/product'
import { getStoreListApi } from '@/api/product';

const defer = useDefer()

const { currentRoute, push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//盘点数据源
const checkData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    pdt_check_num: '',
    产品编码: '',
    产品名称: '',
    盘点人员: '',
    store_id: '',
    盘点日期: ['', ''],
    page: 1,
    count: 10
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
    getStoreCheckList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getStoreCheckList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getStoreCheckList(val)
}

//处理表格对象操作
const handleOper = (type, item, param = null) => {

    //编辑产品
    if (type === 'edit' || type === 'info' || type === 'check') {
        push({
            path: '/stonecheck/stoneaddcheck',
            query: {
                id: '',
                pdt_check_num:item.pdt_check_num,
                type: type === 'check' ? 'info' : type,
                cmd: type === 'check' ? '审核' : ''
            }
        })
    }
    else if (type === 'del') {
        ElMessageBox.confirm('是否确认删除该盘点单？', t('msg.notify'), {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'error',
        }
        ).then(async () => {
            console.log(item)
            const info = wsCache.get(appStore.getUserInfo)

            const ret = await delStoreCheckApi({
                ids: [item.id],
                fsm_exe_man_name: info.resident_name,
                fsm_exe_trig: '删除'
            })
            if (ret) {
                ElMessage({
                    type: 'success',
                    message: t('msg.success'),
                })
                getStoreCheckList()
            }
        }
        ).catch((err) => {
            console.log(err)
        })
    }
}

//显示创建窗口
const showCreateDlg = ref(false)
//进入新增界面
const onAddCheck = async() => {
    showCreateDlg.value = true
    await onChangeID()
    //设置入库人员
    const info = wsCache.get(appStore.getUserInfo)
    preCreateData.check_man_id = info.id
    preCreateData.check_man_name = info.resident_name
    preCreateData.check_date = getTodayDate()
    
}

const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
    let printInfo = { ...item, printType: '盘点单' }
    sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
    dialogVisible.value = true
    setTimeout(() => { dialogVisible.value = false })
}

//查询销售单列表
const getStoreCheckList = async (page = 1) => {
    clearDefer()
    searchCondition.page = page
    let tmp = cloneDeep(searchCondition)

    tmp.盘点日期 = searchCondition.盘点日期[0] + ',' + searchCondition.盘点日期[1]
    isLoading.value = true
    checkData.splice(0, checkData.length)
    const ret = await getStoreCheckListApi(tmp)
    if (ret) {
        checkData.splice(0, checkData.length, ...ret.data)
        console.log(checkData)
        totleCount.value = parseInt(ret.count)
    }
    isLoading.value = false
}

onMounted(async () => {
    await getStoreList()
    if (currentRoute.value.query.pdt_check_num != undefined)
        searchCondition.pdt_check_num = currentRoute.value.query.pdt_check_num

    getStoreCheckList()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    adjustScrollerHeight()
    window.addEventListener('resize', adjustScrollerHeight)
})

//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
    const res = await getStoreListApi({
        page: 1,
        count: 1000
    })
    if (res) {
        console.log(res.data)
        storeData.splice(0, storeData.length, ...res.data)
    }

}


//显示产品信息
const curSelItem = ref({ id: '0', file_list: [], hetong_list: [] })
const showPdtInfo = ref(false)
const onShowPdtInfo = (item) => {
    showPdtInfo.value = true
    curSelItem.value = item
}


//页面切换后恢复滚动位置
let scrollY = ref(0);
onBeforeRouteLeave((to, from, next) => {
    scrollY.value = document.getElementById('mainscroll')?.scrollTop
    console.log('离开了', scrollY)
    next()
})
onActivated(() => {
    document.getElementById('mainscroll')?.scrollTo(0, scrollY.value)
})


const isLoading = ref(false)
const loadingExport = ref(false)
//导出列表
const onExport = async () => {
    return
    //确认是否导出当前质检单列表
    ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
    }
    ).then(async () => {
        loadingExport.value = true
        let tmp = cloneDeep(searchCondition)
        tmp.盘点日期 = searchCondition.盘点日期[0] + ',' + searchCondition.盘点日期[1]
        const ret = await exportSellListApi(tmp)
        if (ret) {
            
            ElMessage.success('导出成功，等待下载！')
            setTimeout(() => {
                loadingExport.value = false
                downloadFile(ret.data.download, ret.data.filename)
            }, 4000)
        }
    })

    setTimeout(() => {
        loadingExport.value = false
    }, 10000)

}

// 调整 DynamicScroller 高度
const adjustScrollerHeight = () => {
    const height = document.getElementById('mainscroll')?.clientHeight
    const scroller = document.getElementById('dynamicScroller');
    if (scroller) {
        scroller.style.height = `${height}px`;
    }
}
onBeforeUnmount(() => {
    window.removeEventListener('resize', adjustScrollerHeight)
})

watch(checkData, () => {
    adjustScrollerHeight()
})

//盘点预创建结构
const preCreateData = reactive({
    store_id:'',
    pdt_check_num:'',
    check_man_id: '',
    check_man_name:'',
    check_date: '',
    fsm_exe_trig:'提交审核'
})
//获取最新ID
const onChangeID = async()=>{
    const ret = await getStoreCheckNewnumApi()
    if(ret)
    {
        console.log(ret)
        preCreateData.pdt_check_num = ret.data.new_id
    }
}

//创建盘点单
const onCreateCheck = async () => {
    if(preCreateData.pdt_check_num == '')
    {
        ElMessage.error('请输入盘点单号')
        return
    }
    if(preCreateData.store_id == '')
    {
        ElMessage.error('请选择盘点仓库')
        return
    }

    showCreateDlg.value = false
    preCreateData.fsm_exe_trig = '提交审核'
    let ret = await addStoreCheckApi(preCreateData)
    if (ret) {
        ElMessage.success('创建成功')
        // getStoreCheckList()
        push({
            path: '/stonecheck/stoneaddcheck',
            query: {
                id: '',
                pdt_check_num:ret.data.pdt_check_num,
                type: 'edit',
                cmd: ''
            }
        })
    }
}
</script>

<template>
    <!-- 销售单列表 -->
    <div ref="rootRef" class11="absolute top-[0px] right-[0px] left-[0px] bottom-[0px] overflow-auto">
        <div class11="p-7 pt-0 relative " style="background-color: var(--app-content-bg-color);">

            <div class="absolute top-8 left-15 flex w-[90%]">
                <ElButton class="mr-2" type="success" @click="onAddCheck">
                    <Icon icon="carbon:document-add" />
                    <div class="pl-1">{{ t('button.add') }}</div>
                </ElButton>
                <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                    <Icon icon="carbon:export" />
                    <div class="pl-2">{{ t('button.export') }}</div>
                </ElButton>
            </div>

            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-3 pb-2 mb-1 bg-white">
                <div class="text-center mb-5 font-bold">库存盘点</div>
                <!-- 检索条件 -->
                <div class="inline-flex items-center ml-1">
                    <div class="searchTitle">订单状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.fsm_cur_state"
                        placeholder="请选择">
                        <el-option v-for="item in ['等待提交', '等待审核', '等待修改', '已入库', '已拒绝']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">盘点单号</div>
                    <el-input size="small" v-model="searchCondition.pdt_check_num" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">{{ t('product_manage.id') }}</div>
                    <el-input size="small" v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">{{ t('product_manage.name') }}</div>
                    <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">盘点人员</div>
                    <el-input size="small" v-model="searchCondition.盘点人员" placeholder="" class="searchItem" />
                </div>

                <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
                    <div class="searchTitle">盘点日期</div>
                    <el-date-picker size="small" class="searchItem" v-model="searchCondition.盘点日期" type="daterange"
                        range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间"
                        value-format="YYYY-MM-DD" />
                </div>


                <div class="flex justify-end items-center mr-6 mt-2 mb-1">
                    <!-- <el-checkbox size="small" :label="t('customer.senior')" v-model="senior"/> -->
                    <ElButton type="primary" class="ml-4" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="pl-2">查询</div>
                    </ElButton>
                    <ElButton type="warning" class="ml-4" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="pl-2">清除</div>
                    </ElButton>
                </div>
            </div>

            <!-- 产品列表 -->
            <div v-loading.lock="isLoading">
                <!-- 表头 -->
                <div class="flex header headerBk">
                    <div class="w-[80%] flex">
                        <div class="w-[50%] flex-grow">产品名称</div>
                        <div class="w-[10%] ">库存数量</div>
                        <div class="w-[10%] ">实际数量</div>
                        <div class="w-[10%] ">盈亏数量</div>
                        <!-- <div class="w-[10%] " v-show="checkPermissionApi('销售订单价格显示')">单价</div> -->
                        <div class="w-[10%] " v-show="checkPermissionApi('销售订单价格显示')">价格</div>
                        <div class="w-[10%] ">备注</div>
                    </div>
                    <div class="w-[20%] flex !p-0">
                        <div class="!max-w-[100px] rightcss rightcss_title">仓库</div>
                        <div class="!max-w-[200px] rightcss rightcss_title">盘点人员</div>
                        <div class="!max-w-[200px] rightcss rightcss_title">状态</div>                        
                        <!-- <div class="rightcss rightcss_title">备注</div> -->
                        <div class="rightcss_title !min-w-[70px]">操作</div>
                    </div>
                </div>
                <!-- 表内容 -->
                <DynamicScroller :items="checkData" :min-item-size="82.53" key-field="id" class="scroller"
                    id="dynamicScroller">
                    <template #default="{ item, index, active }">
                        <DynamicScrollerItem :item="item" :size-dependencies="[item.new_list.length]" :data-index="index" :active="active">
                            <div>
                                <!-- 内容头 -->
                                <div class="font-bold  border-b-1px border-gray-300">
                                    <div class="p-1 pl-4 pr-4 flex flex-nowrap text-[12px] justify-between"
                                        style="background-color: #BFEFFF;">
                                        <div class="w-[25%] min-w-[200px] flex items-center">
                                            <div class="mr-4  flex items-center">
                                                 <div class="flex items-center mr-4 w-80px">
                                                    <div class="mr-3 rounded p-1 pl-2 pr-2" style="color: #fff;"
                                                        :class="{ 'title_unreceipt': item.fsm_cur_state === '等待审核', 'title_ok': item.fsm_cur_state === '已入库'}">
                                                        {{ item.fsm_cur_state }}
                                                    </div>
                                                </div>
                                                <div>{{ item.create_date.split(' ')[0] }}</div>
                                            </div>
                                            盘点单号: {{ item.pdt_check_num }}
                                        </div>
                                        <div class="min-w-[200px] flex items-center">
                                            <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;"
                                                icon="mdi:company" />
                                            {{ item.store_nick+'  ('+item.new_list_len+'/'+item.photo_list_len+')' }}
                                        </div>
                                        <div class="flex items-center min-w-[100px]">
                                            <div class="mr-5">盘点人员:{{ item.check_man_name }}</div>
                                        </div>
                                        <div class="flex justify-end items-center">
                                            <div class="mr-5">盘点时间:{{ item.check_date }}</div>
                                        </div>
                                        <!-- 靠右的其他信息 -->
                                        <div class="flex justify-end items-center min-w-[200px]">
                                            <Icon title="打印"
                                                style="scale: 1.5; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;"
                                                icon="material-symbols:print-outline" @click="toPrintPage(item)" />
                                        </div>
                                    </div>
                                </div>
                                <!-- 内容体 -->
                                <div class="flex ">
                                    <!-- 左边产品列表 -->
                                    <div class="w-[84%]  table_self">
                                        <div v-for="pdt in item.new_list" :key="pdt.id" class="flex w-[100%] border-b-1px h-[70px]">
                                            <div class="w-[50%] flex-grow !min-w-[300px] h-[100%]">
                                                <div class="flex justify-start items-center w-[100%] p-1">
                                                    <el-image v-if="pdt.pics.length > 0"
                                                        class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer"
                                                        :src="pdt.pics[0].url" @click="onShowPdtInfo(pdt)" />
                                                    <el-image v-if="pdt.pics.length <= 0"
                                                        class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer"
                                                        src="/nopic.jpg" @click="onShowPdtInfo(pdt)" />
                                                    <div class="ml-2 inline-block text-left max-w-[100%]">
                                                        <div style="white-space: normal;font-size:'12px'"
                                                            class="nameStyle" @click="onShowPdtInfo(pdt)">{{'[' + pdt.name + ']' + pdt.nick }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="w-[10%] flex-col flex justify-center items-center  border-left-1px">
                                                <div>{{ pdt.账面数量 }}</div>
                                            </div>
                                            <div class="w-[10%] flex-col flex justify-center items-center  border-left-1px">
                                                {{ pdt.实际数量 }}
                                            </div>
                                            <div class="w-[10%] flex-col flex justify-center items-center  border-left-1px">
                                                <span :class="{'text-red-500': pdt.盈亏数量 > 0, 'text-green-500': pdt.盈亏数量 < 0,'text-black': pdt.盈亏数量 === 0}">
                                                     {{ pdt.盈亏数量 }}
                                                </span>
                                            </div>
                                            <div class="w-[10%] flex-col flex justify-center border-left-1px">
                                                <div>
                                                    单价:{{ pdt.avg_price_aft_tax }}
                                                </div>
                                                <div :class="{'text-red-500': pdt.盈亏数量 > 0, 'text-green-500': pdt.盈亏数量 < 0,'text-black': pdt.盈亏数量 === 0}">
                                                    总盈亏:{{ parseFloat((pdt.avg_price_aft_tax*pdt.盈亏数量).toFixed(2)) }}
                                                </div>
                                            </div>
                                            <div class="w-[10%] flex-col flex justify-center items-center  border-left-1px">
                                                {{ pdt.备注 }}
                                            </div>
                                        </div>
                                        <div v-if="item.new_list_len == 0" class="h-[90px] text-center flex justify-center items-center">
                                            暂无盘点产品，等待盘点
                                        </div>
                                        <div v-if="item.new_list_len>5" class="h-[40px] text-blue-500 text-center h-[100%] flex justify-center items-center cursor-pointer" @click="handleOper('info', item)">
                                            总变更个数:{{ item.new_list_len +'   '}},更多请点击查看详情
                                        </div>
                                    </div>
                                    <!-- 右边其他数据 -->
                                    <div class="w-[20%] flex  text-center  right">
                                        <div class="!max-w-[100px] rightcss flex justify-center items-center border-l-0.5px">
                                            {{ item.store_nick }}
                                        </div>
                                        <div class="!max-w-[200px] rightcss flex flex-col justify-center items-center border-l-0.5px">
                                            {{ item.check_man_name }}
                                        </div>

                                        <div class="rightcss border-l-0.5px !max-w-[400px] " style="text-align: left;">
                                            {{ item.fsm_cur_state }}
                                        </div>
                                        <div class="!min-w-[70px] flex justify-center items-center border-l-0.5px">
                                            <el-dropdown trigger="click" placement="bottom">
                                                <span class="el-dropdown-link">
                                                    <ElButton type="primary" size="small">{{ t('userTable.operate') }}
                                                    </ElButton>
                                                </span>
                                                <template #dropdown>
                                                    <div class="flex flex-wrap w-[140px]">
                                                        <el-dropdown-item v-if="item.fsm_cur_state.indexOf('审核') >= 0" @click="handleOper('check', item)">{{ '审核' }}</el-dropdown-item>
                                                        <el-dropdown-item @click="handleOper('info', item)">查看</el-dropdown-item>
                                                        <el-dropdown-item v-if="item.fsm_cur_state != '已入库'" @click="handleOper('edit', item)">{{ t('userOpt.edit') }}</el-dropdown-item>
                                                        <el-dropdown-item v-if="item.fsm_cur_state != '已入库'" @click="handleOper('del', item)">{{ t('userOpt.del')}}</el-dropdown-item>

                                                    </div>
                                                </template>
                                            </el-dropdown>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </DynamicScrollerItem>
                    </template>
                </DynamicScroller>
            </div>

            <el-pagination v-model:current-page="searchCondition.page" v-model:page-size="searchCondition.count"
                :page-sizes="[10, 50, 100, 300]" :background="true" layout="sizes, prev, pager, next"
                :total="totleCount" @size-change="handleSizeChange" @current-change="handleCurrentChange" />



        </div>



        <DialogProductLite :id="curSelItem.id" type="info" categ="" :exinfo="{}"
            :title="t('product_manage.add_product')" v-model:show="showPdtInfo" @on-submit="()=>{}" />
        <PrintModal v-model:show="dialogVisible" />

        <el-dialog title="创建盘点单" v-model="showCreateDlg" width="500" align-center destroy-on-close>
            <el-form>
                <div class='flex'>
                    <el-form-item class="mr-2 w-[50%] flex" label="盘点单号:">
                        <el-input class='!w-[60%]' size='small' v-model="preCreateData.pdt_check_num" placeholder="请输入盘点单号" />
                        <ElButton size='small' type="warning"  @click="onChangeID">{{ t('button.update') }}</ElButton>
                    </el-form-item>                
                    <el-form-item label="盘点仓库:">
                        <el-select size='small' v-model="preCreateData.store_id" placeholder="请选择盘点仓库" >
                            <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                        </el-select>
                    </el-form-item>
                </div>
                <div class='flex'>
                    <el-form-item class="mr-2 w-[50%]" label="盘点人员:">
                        {{ preCreateData.check_man_name }}
                    </el-form-item>                
                    <el-form-item class="w-[50%]" label="盘点日期:">
                        <el-date-picker size='small' v-model="preCreateData.check_date" type="date" placeholder="" format="YYYY/MM/DD"
                        value-format="YYYY-MM-DD"  :clearable="false"/>
                    </el-form-item>
                </div>

            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="showCreateDlg = false">取消</el-button>
                    <el-button type="primary" @click="onCreateCheck">创建</el-button>
                </div>
            </template>

        </el-dialog>

    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
    background-color: #f0f9eb !important;
}

:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #fff;
    font-weight: 400;
}

.nameStyle {
    cursor: pointer;
}

.nameStyle:hover {
    color: rgb(130, 130, 255);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #ffe48d !important;
}

.searchItem {
    width: 150px;
}

//自定义表格
.header {
    display: flex;
    border: 1px solid #e2e2e2;
    border-collapse: collapse;
    width: 100%;
    color: var(--el-text-color-regular);
    color: #333;
    font-size: 15px;
}

.headerBk {
    background-color: #fff !important;
}

.content {
    &:extend(.header);
    font-size: 14px;
}

.header>div,
.content>div {
    display: flex;
    border-right: 1px solid #e2e2e2;
    padding: 10px;
    // max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    padding: 5px;
    justify-content: center;
    min-width: 50px;
    max-height: 90px;
    font-size: 14px;
    font-weight: bold;
}

.header>div:last-child,
.content>div:last-child {
    border-right: none;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.table_self {
    font-size: 14px;
}

.table_self>div>div,
.right>div {
    // border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}

.test {
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}

.ex_text {
    font-size: 11px;
    color: #646464;
}

.ex_text_danger {
    &:extend(.ex_text);
    color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0;
    /* 设置最小宽度，防止内容撑大 */
    text-align: center;
    /* 文字居中对齐 */
    word-wrap: break-word;
    /* 文字超长时换行处理 */
    font-size: 11px;
}

.rightcss_title {
    display: flex;
    border-right: 1px solid #e2e2e2;
    padding: 10px;
    // max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    padding: 5px;
    justify-content: center;
    min-width: 50px;
    max-height: 90px;
    align-items: center;
    font-size: 13px;
}


//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt {
    //未收货
    background-color: #79bbff;
    // border: #d9d9d9 1px solid;
}

.title_partial {
    //部分收货
    background-color: #eebe77;
}

.title_ok {
    //完全收货
    background-color: #95d475;
}

.title_over {
    //超量收货
    background-color: #FFC0CB;
}

.title_create {
    //已创建
    color: #409EFF;
    background-color: color-mix(in oklch, #409EFF, transparent 80%);
}

.title_checked {
    //已审核
    color: #67C23A;
    background-color: color-mix(in oklch, #67C23A, transparent 80%);
}

.title_wait {
    //等待修改
    background-color: #FFC0CB;
    background-color: color-mix(in oklch, #FFC0CB, transparent 80%);
}

.title_ok2 {
    //完全收货
    background-color: #95d475;
    background-color: color-mix(in oklch, #95d475, transparent 80%);
}

:deep(.el-progress__text) {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-left: 5px;
    min-width: auto;
    line-height: 1;
}

:deep(.el-progress__text) {
    font-size: 12px !important;
}

// div{
//   font-size: 12px;
// }</style>

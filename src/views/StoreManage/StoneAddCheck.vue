<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive,nextTick } from 'vue'
import { ElPagination,ElCard,ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,set } from 'vue'
import { getProductListApi, getStoreCheckInfoApi, updateStoreCheckApi,getStoreCheckPdtListApi, updateStoreCheckPdtApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelCustomer } from '@/components/DialogSelCustomer'
import { DialogProductSel } from '@/components/DialogProductSel'
import {checkFormRule, checkPermissionApi, closeOneTagByName, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { getGUID } from '@/api/tool'
import { getSupplierListApi } from '@/api/customer'
import { saleDef } from '@/types/logic'
import { DialogCheckShow } from '@/components/DialogCheckShow'

const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    buyer_nick: [{ required: true, message: t('msg.noCustom'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//盘点单数据
const checkData = reactive(
{
        store_id: '',
        store_nick:'',
        pdt_check_num:'',
        check_man_id: '',
        check_man_name:'',
        check_date: '', 
        pdt_list:[],   
        fsm_can_trig_data:{
            审核触发:[],
            操作触发:['保存']
        }, //审核决策
        fsm_cur_state:'订单创建',    //当前节点状态
        fsm_exe_man_name:'',
        fsm_exe_log:'',
        fsm_exe_trig:'提交审核',//决策内容
        fsm_log_list:[],
        deliver_date:''
})



onMounted(async () => {    
 
    if(currentRoute.value.query.pdt_check_num == '' || currentRoute.value.query.pdt_check_num == undefined)
    {
        ElMessage.error('没有带单号查询!')
        back()
        return
    }
        
    if(currentRoute.value.query.type == 'info')
    {
        title.value = '查看盘点单'
    }
    else
    {
        title.value = '修改盘点单'
    }


    
    //查询产品信息 
    const ret = await getStoreCheckInfoApi({
        pdt_check_num:currentRoute.value.query.pdt_check_num,
        page:1,
        count:100
    })
    if(ret)
    {
        console.log(ret)
        delete ret.data.pdt_list
        Object.assign(checkData, ret.data)

        await getPdtList()
    }

    nextTick(()=>{
        if(currentRoute.value.query.type === 'info') //查看模式
        {
            let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
            let excludeDiv = document.getElementById('check');
            let searchDiv = document.getElementById('nono');
            if(excludeDiv != null || searchDiv != null)
            {
                // 使用 :not() 伪类排除特定的 div 元素下的子元素
                let filteredComponents = Array.from(components).filter(
                    component => !excludeDiv.contains(component)
                );
                filteredComponents = Array.from(components).filter(
                    component => !searchDiv.contains(component)
                );
                filteredComponents.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }
            else
            {
                components.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }


            components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
            components.forEach((component) => {
                component.classList.add('infomode')
            });
            const suffixElements = document.querySelectorAll('.el-input__suffix');
            suffixElements.forEach(suffixElement => {
                suffixElement.style.display = 'none';
            });
        }
    })
    
})

const totleCount = ref(0)
const pdeSearchCondition = reactive({
    pdt_check_num: '',
    inputTxt: '',
    产品编码: '',
    产品名称: '',
    _or:true,
    page: 1,
    count: 30
})
 //获取产品列表
const getPdtList = async () => {   
    isLoading.value = true
    pdeSearchCondition.pdt_check_num = checkData.pdt_check_num
    pdeSearchCondition.产品编码 = pdeSearchCondition.inputTxt
    pdeSearchCondition.产品名称 = pdeSearchCondition.inputTxt
    let ret = await getStoreCheckPdtListApi(pdeSearchCondition)
    if(ret)
    {
        checkData.pdt_list.splice(0, checkData.pdt_list.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
    isLoading.value = false
}

//提交审核意见
const handleCheck = async(btn)=>{
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    const info = wsCache.get(appStore.getUserInfo)
    checkData.fsm_exe_man_name = info.resident_name
    checkData.fsm_exe_trig = btn
    const data = cloneDeep(checkData)

    delete data.pdt_list
    if(checkData.id == undefined)
    {
        ElMessage.error('盘点单不存在')
        return
    }
    else //修改
    {
        const ret =await updateStoreCheckApi(data)
        if(ret)
        {
            ElMessage.success('盘点单更新成功')
            baskFront()                        
        }
    }
}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = ()=>{
    showCheckHisDlg.value = true
}

//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
    //刷新上一页
    closeOneTagByPath('/stonecheck/stonechecklist')
}

const baskFrontNoFreshFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}
const isLoading = ref(false)
const onModifyPdt = async(row) => {
    let ret = await updateStoreCheckPdtApi({
        pdt_check_num:checkData.pdt_check_num,
        pdt_id:row.产品ID,
        实际数量: row.实际数量,
        备注:row.备注,
        fsm_exe_trig: '保存'
    })
    if(ret)
    {
        ElMessage.success('修改成功')
        getPdtList()
    }
}
</script>

<template>
    <ContentDetailWrap :title="title+'-'+checkData.fsm_cur_state" @back="baskFrontNoFreshFront()">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史
            </ElButton>
        </template>
        <template #right>
            <ElButton type="primary"  @click="getPdtList">
                <Icon class="mr-0.5" icon="mingcute:send-line" />
                刷新
            </ElButton>
            <el-popconfirm v  title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && checkData.fsm_can_trig_data.操作触发.includes('提交审核') && currentRoute.query.type!='info'"   type="success" >
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        提交审核
                    </ElButton>
                </template>
            </el-popconfirm>            
        </template>
        <el-card id="check" shadow="never" v-show="checkData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%] mt-4">
            审核原因：
            <el-input v-model="checkData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
            <div class="mt-4 flex justify-end">
                <div class="flex items-center text-12px">
                    <span>当前节点:</span>
                    <span class="text-red-500 mr-3">{{ checkData.fsm_cur_state }}</span>                
                </div>
                <ElButton v-show="checkData.fsm_can_trig_data.审核触发.includes('同意')" type="success" @click="handleCheck('同意')" >同意并入库</ElButton>
                <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="checkData.fsm_can_trig_data.审核触发.includes('驳回')" type="warning" >驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="checkData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                    </template>
                </el-popconfirm>  
            </div>
        </el-card>   

        <el-form :rules="rules" :model="checkData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="盘点单号" class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  v-model="checkData.pdt_check_num" :disabled="checkData.pdt_check_num!=undefined" />
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="盘点仓库" class="flex">
                    {{ checkData.store_nick }}
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="盘点人员" class="flex">
                    {{ checkData.check_man_name }}
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="盘点日期" class="flex">
                    {{ checkData.check_date }}
                </el-descriptions-item>
            </el-descriptions>
        </el-form>

        <div class="mt-2 flex justify-center items-center">
            <el-input id="nono" style="width: 300px;margin-right: 10px;" :placeholder="t('msg.pleaseInputNameOrID')" v-model="pdeSearchCondition.inputTxt" @keyup.enter="getPdtList()" clearable/>
            <el-button type="primary" @click="getPdtList">{{ t('button.search') }}</el-button>
        </div>

         <el-table v-loading.lock="isLoading"  class="mt-4" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="checkData.pdt_list" style="width: 100%" border stripe>
            <el-table-column  label="序号" width="60" >
                <template #default="scope">
                    {{ scope.$index + 1}}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="60" >
                <template #default="scope">
                    <div class="flex items-start w-[30px] h-[30px]">
                        <el-image v-if="scope.row.产品图片.length>0"  class="object-fill w-[30px] h-[30px] min-w-[30px]" :src="scope.row.产品图片[0].url" />
                        <el-image v-if="scope.row.产品图片.length<=0"  class="object-fill w-[30px] h-[30px] min-w-[30px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="产品编码" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip  prop="产品名称" :label="t('product_manage.name')" min-width="160">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.产品名称}}</div>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tag>{{ scope.row规格 }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="账面数量" label="账面数量"  />            
            <el-table-column show-overflow-tooltip  prop="实际数量" label="实际数量">
                <template #default="scope">
                    <el-input  v-model="scope.row.实际数量" type="number" @blur="onModifyPdt(scope.row)"/>
                </template>
            </el-table-column>
            

            <el-table-column prop="单价(税前)"  :label="t('sale.bef_tax')"  />
            <el-table-column prop="单价"  :label="t('sale.after_tax')"  />
          
            <el-table-column  label="账面总价值"  >
                <template #default="scope">   
                    <div  v-show="checkPermissionApi('销售订单价格显示')">
                        {{  parseFloat((scope.row.账面数量*scope.row.单价).toFixed(2)) }}
                    </div>              
                </template>
            </el-table-column>
            <el-table-column  label="实盘总价值"  >
                <template #default="scope">   
                    <div  v-show="checkPermissionApi('销售订单价格显示')">
                        {{ parseFloat((scope.row.实际数量*scope.row.单价).toFixed(2)) }}
                    </div>              
                </template>
            </el-table-column>
            <el-table-column  label="备注"  >
                <template #default="scope">
                    <div>
                        <el-input v-model="scope.row.备注" class="!text-center" @blur="onModifyPdt(scope.row)"/>
                    </div>                    
                </template>
            </el-table-column>
        </el-table>
        <el-pagination 
            v-model:current-page="pdeSearchCondition.page"
            v-model:page-size="pdeSearchCondition.count"
            :page-sizes="[30,50, 100, 300]"
            :background="true"
            layout="sizes, prev, pager, next"
            :total="totleCount"
            @size-change="getPdtList"
            @current-change="getPdtList"
          />
        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="checkData.fsm_log_list" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
  color: #333;
 // white-space: nowrap;
  text-align: center;
  font-weight: 500;
  font-size: 13px;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    font-size: 13px;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 13px;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
:deep(.input-search){
    .el-input__inner {
    text-align:start;
}
}


</style>
<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import { getInventoryStatementListApi  } from '@/api/product'


const { currentRoute,back,push } = useRouter()
const { t } = useI18n()

//标题
const title = ref('')

//定义搜索条件
const defaultCondition = {
    pdt_id:'',
    page: 1,
    count: 10000
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//库存明细列表
const statementData = reactive([])


//查询库存流水列表
const getInventoryStatementList = async (page = 1) => {
  searchCondition.page = page
  const ret = await getInventoryStatementListApi(searchCondition)
  if(ret)
  {
    statementData.splice(0,statementData.length, ...ret.data)
    console.log(statementData)
  }
}

//切换流水页面
const onShowInfo = (item)=>{
//   push({
//       path: '/inventorymanage/inventorystatement',
//       query:{
//         pdt_id:item.pdt_id
//       }
//     })
}

onMounted(async () => {    
    searchCondition.pdt_id = currentRoute.value.query.pdt_id as string
    searchCondition.store_id = currentRoute.value.query.store_id as string
    title.value = '['+currentRoute.value.query.pdt_name+']'+currentRoute.value.query.pdt_nick+' 进出流水'
    getInventoryStatementList()
})

</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <el-table ref="tableRef" :data="statementData" style="width: 100%; margin-bottom: 20px" row-key="id" border stripe
           header-cell-class-name="tableHeader">
          <el-table-column show-overflow-tooltip align="center"  prop="modify_date" :label="t('inventory.time')" min-width="140"/>
          <el-table-column show-overflow-tooltip align="center"  prop="pdt_nick" :label="t('product_manage.name')" />
          <el-table-column show-overflow-tooltip align="center"  prop="进出类型" label="进/出库单号" min-width="150">
            <template #default="scope">
              {{ scope.row.source+'['+scope.row.src_attr_value+']' }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"  prop="关联订单" label="来源单号" min-width="150">
            <template #default="scope">
              {{ scope.row.关联订单 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"  prop="关联名称" label="关联名称" />
          <el-table-column show-overflow-tooltip align="center"  prop="before_all_amount" :label="t('inventory.old_count')" />
          <el-table-column show-overflow-tooltip align="center"  prop="avail_amount" :label="t('inventory.span_count')" />
          <el-table-column show-overflow-tooltip align="center"   :label="'库存结余数量'" >
            <template #default="scope">
              {{ scope.row.before_all_amount+ scope.row.avail_amount }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"  prop="库存结余金额" :label="'库存结余金额'" />
          <el-table-column show-overflow-tooltip align="center"  prop="pdt_base_unit" :label="t('product_manage.b_unit')" />
          <el-table-column show-overflow-tooltip align="center"  prop="未出库数量" label="未出库数量" />
          <el-table-column show-overflow-tooltip align="center"  prop="cur_price_bef_tax" label="当前(未税)" />
          <el-table-column show-overflow-tooltip align="center"  prop="cur_price_aft_tax" label="当前(含税)" />
          <!-- <el-table-column show-overflow-tooltip align="center"  prop="avg_price_bef_tax" :label="t('inventory.price_avr_bef_tax')" />
          <el-table-column show-overflow-tooltip align="center"  prop="avg_price_aft_tax" :label="t('inventory.price_avr_aft_tax')" /> -->
          <el-table-column show-overflow-tooltip align="center"  :label="t('inventory.price_bef_tax')" >
            <template #default="scope">
              {{  scope.row.cur_price_aft_tax == '--'?'--':Math.abs(parseFloat(((scope.row.未出库数量)*scope.row.cur_price_bef_tax).toFixed(4))) }}              
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"  :label="t('inventory.price_aft_tax')" >
            <template #default="scope">
              {{ scope.row.cur_price_aft_tax == '--'?'--':Math.abs(parseFloat(((scope.row.未出库数量)*scope.row.cur_price_aft_tax).toFixed(4))) }}
            </template>
          </el-table-column>
          
          <!-- <el-table-column show-overflow-tooltip fixed="right" align="center" width="100px" prop="name" :label="t('roleTable.opt')" >
            <template #default="scope">
              <ElButton type="primary" size="small" @click="onShowStatement(scope.row)">{{ t('inventory.look') }}</ElButton>
            </template>
          </el-table-column> -->
        </el-table>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}



</style>
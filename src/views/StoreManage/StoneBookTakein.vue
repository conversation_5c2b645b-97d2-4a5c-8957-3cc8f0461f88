<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDatePicker, ElTag, ElButton, ElMessage, ElMessageBox, ElImage, ElInput, ElSelect, ElOption, ElCheckbox, ElDropdown, ElDropdownItem, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getBookTakeinListApi, delBookTakeinApi } from '@/api/stone'
import { onBeforeMount } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { DialogSaleSel } from '@/components/DialogSaleSel'
import { checkPermissionApi, getMoneyFlag } from '@/api/tool';
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { useAppStore } from '@/store/modules/app'
import { cloneDeep } from 'lodash-es';

const appStore = useAppStore()

const { currentRoute, push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

//数据源
const takeinData = reactive([])


//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    领料单号: '',
    产品编码: '',
    产品名称: '',
    领料人员: '',
    领料备注: '',
    领料日期: ['', ''],
    page: 1,
    count: 10
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
    getBookTakeinList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getBookTakeinList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getBookTakeinList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
    //编辑产品
    if (type === 'info' || type === 'edit') {
        push({
            path: '/inventorymanage/stoneaddbooktakein',
            query: {
                id: item.id,
                type: type,
            }
        })
    }
    else if (type === 'del') {
        ElMessageBox.confirm('是否确认删除该领料单', t('msg.notify'), {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'error',
        }
        ).then(async () => {
            console.log(item)
            const ret = await delBookTakeinApi({
                ids: [item.id],
                fsm_exe_trig:'删除'
            })
            if (ret) {
                ElMessage({
                    type: 'success',
                    message: t('msg.success'),
                })
                getBookTakeinList()
            }
        }
        ).catch(() => { })
    }
}


//查询销售出库单列表
const getBookTakeinList = async (page = 1) => {
    searchCondition.page = page
    let tmp = cloneDeep(searchCondition)

    const ret = await getBookTakeinListApi(tmp)
    if (ret) {
        takeinData.splice(0, takeinData.length, ...ret.data)
        console.log(takeinData)
        totleCount.value = parseInt(ret.count)
    }
}

const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
    let printInfo = { ...item, printType: '领料单' }
    sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
    dialogVisible.value = true
    setTimeout(() => { dialogVisible.value = false })
}

onMounted(async () => {
    getBookTakeinList()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });
})

//选择销售订单弹窗
const onAddTakeIn = () => {
    push({
        path: '/inventorymanage/stoneaddbooktakein',
        query: {
            id: '',
        }
    })
}
</script>

<template>
    <!-- 销售出库单列表 -->
    <div ref="rootRef">
        <div class="absolute top-3 left-10">
            <ElButton type="success" @click="onAddTakeIn">
                <Icon icon="fluent-mdl2:people-add" />
                <div class="pl-2">{{ t('button.add') }}</div>
            </ElButton>
        </div>
        <div class="p-7">
            <div class="text-center mb-5 font-bold">领料申请单</div>
            <div class="p-5 bg-white">
                <!-- 检索条件 -->
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">领料单号</div>
                    <el-input size="small" v-model="searchCondition.领料单号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">{{ t('product_manage.id') }}</div>
                    <el-input size="small" v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">{{ t('product_manage.name') }}</div>
                    <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">领料人员</div>
                    <el-input size="small" v-model="searchCondition.领料人员" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">领料备注</div>
                    <el-input size="small" v-model="searchCondition.领料备注" placeholder="" class="searchItem" />
                </div>

                <div class="inline-flex items-center mr-5 mb-2">
                    <div class="searchTitle">领料日期</div>
                    <el-date-picker size="small" class="searchItem" v-model="searchCondition.领料日期" type="daterange"
                        range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间"
                        value-format="YYYY-MM-DD" />
                </div>



                <div class="text-center mt-5 mb-2 flex justify-end items-center">
                    <el-checkbox size="small" :label="t('customer.senior')" v-model="senior" />
                    <ElButton class="ml-4" type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="pl-2">查询</div>
                    </ElButton>
                    <ElButton type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="pl-2">清除</div>
                    </ElButton>
                </div>
            </div>
            <!-- 产品列表 -->
            <div class="mt-6">
                <!-- 表头 -->
                <div class="flex header headerBk">
                    <div class="w-[60%] flex">
                        <div class="w-[70%] ">产品名称</div>
                        <div class="w-[10%] ">领料数量</div>
                        <div class="w-[20%] ">价格</div>
                    </div>
                    <div class="flex flex-grow !p-0">
                        <div class="rightcss rightcss_title">领料人员</div>
                        <div class="rightcss rightcss_title">订单状态</div>
                        <div class="rightcss rightcss_title">备注</div>
                        <div class="rightcss_title !min-w-[100px]">操作</div>
                    </div>

                </div>

                <!-- 表内容 -->
                <div class="mt-4 bg-white" style="box-shadow: 0 1px 8px 1px rgba(165,165,165,0.2)"
                    v-for="item in takeinData" :key="item.id">
                    <!-- 内容头 -->
                    <div>
                        <div class="p-2 pl-4 pr-4 flex flex-nowrap items-center text-[13px] title_ok font-bold"
                            style="background: #ECF4FF;border: 1px solid #AACAF5;">
                            <div class="w-[45%]  flex">
                                <div class="mr-4 font-bold">
                                    {{ item.create_date.split(' ')[0] }}
                                </div>
                                <div>
                                    <span>领料单号</span>
                                    <span>{{ item.book_takein_num }}</span>
                                </div>
                            </div>
                            <div class="flex justify-center items-center min-w-[250px]">
                                <div class="mr-5">仓库:{{ item.store_nick }}</div>
                            </div>
                            <!-- 靠右的其他信息 -->
                            <div class="ml-auto flex justify-center items-center min-w-[200px]">
                                <Icon style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;"
                                    icon="material-symbols:print-outline" @click="toPrintPage(item)" />
                                <!-- <Icon style="scale: 1.1; margin:0 5px 0 10px;" icon="uil:copy" /> -->
                            </div>
                        </div>
                    </div>
                    <!-- 内容体 -->
                    <div class="flex">
                        <!-- 左边产品列表 -->
                        <div class="w-[60%]  table_self">
                            <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                                <div class="w-[70%] flex-grow p-1">
                                    <div class="flex justify-start items-center w-[100%] p-2">
                                        <el-image class="object-fill w-[50px] h-[50px] mr-1" src="/nopic.jpg" />
                                        <div class="inline-block text-left max-w-[100%]">
                                            <div style="white-space: normal;" class='nameStyle'>{{'[' + pdt.name + ']' + pdt.nick }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-[10%] p-2 border-left-1 flex items-center justify-center">{{ pdt.领料数量 + '            '+pdt.base_unit }}</div>
                                <div class="w-[20%] p-2 border-left-1 flex items-center justify-center flex-col">
                                    {{ getMoneyFlag(item.money_type) +pdt.avg_price_aft_tax + 'x' + pdt.领料数量 + '=' + parseFloat((pdt.avg_price_aft_tax * pdt.领料数量).toFixed(2)) }}
                                </div>
                            </div>
                        </div>
                        <!-- 右边其他数据 -->
                        <div class="flex flex-grow text-center  right">
                            <div class="rightcss" style="text-align: left;">
                                <div>{{ item.takein_man_name }}</div>
                            </div>
                            <div class="rightcss">
                                已申请
                            </div>
                            <div class="rightcss" style="text-align: left;">{{ item.note }}</div>
                            <div class="!min-w-[100px] flex items-center justify-center">
                                <el-dropdown trigger="click" placement="bottom">
                                    <span class="el-dropdown-link">
                                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                                    </span>
                                    <template #dropdown>
                                        <div class="flex flex-wrap w-[140px]">
                                            <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.info')}}</el-dropdown-item>
                                            <el-dropdown-item @click="handleOper('edit', item)">编辑</el-dropdown-item>
                                           <el-dropdown-item v-if="checkPermissionApi('删除领料单')" @click="handleOper('del', item)">删除</el-dropdown-item>
                                        </div>
                                    </template>
                                </el-dropdown>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <el-pagination class="mt-8 flex justify-end mb-8" v-model:current-page="searchCondition.page"
            v-model:page-size="searchCondition.count" :page-sizes="[10, 50, 100, 300]" :background="true"
            layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        <PrintModal v-model:show="dialogVisible" />
    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
    background-color: #ffe48d !important;
}

:deep(.tableHeader) {
    background-color: #6d92b4 !important;
    color: #fff;
    font-weight: 400;
}

.nameStyle {
    color: rgb(60, 60, 255);
    cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #ffe48d !important;
}

.searchItem {
    width: 140px;
}

//自定义表格
.header {
    display: flex;
    border: 1px solid #e2e2e2;
    border-collapse: collapse;
    width: 100%;
    color: var(--el-text-color-regular);
    color: #333;
    font-size: 15px;
    font-weight: bold;
}

.headerBk {
    background-color: #fff !important;
}

.content {
    &:extend(.header);
    font-size: 14px;
}

.header>div,
.content>div {
    display: flex;
    border-right: 1px solid #e2e2e2;
    padding: 10px;
    // max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    padding: 5px;
    justify-content: center;
    min-width: 50px;
    max-height: 90px;
    font-size: 13px;
}

.header>div:last-child,
.content>div:last-child {
    border-right: none;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.table_self {
    font-size: 14px;
}

.table_self>div,
.right>div {
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    // padding: 7px;
}

.test {
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}

.ex_text {
    font-size: 11px;
    color: #646464;
}

.ex_text_danger {
    &:extend(.ex_text);
    color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0;
    /* 设置最小宽度，防止内容撑大 */
    text-align: center;
    /* 文字居中对齐 */
    word-wrap: break-word;
    /* 文字超长时换行处理 */
    font-size: 11px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.rightcss_title {
    display: flex;
    border-right: 1px solid #e2e2e2;
    padding: 10px;
    // max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    padding: 5px;
    justify-content: center;
    min-width: 50px;
    max-height: 90px;
    align-items: center;
    font-size: 13px;
}


//---------------列表对象标题栏条件颜色-------------------
.title_wait {
    background-color: #eebe77;
}

.title_ok {
    background-color: #3fb106
}

.title_end {
    background-color: #646464;
}

.nameStyle {
    cursor: pointer;
}

.nameStyle:hover {
    color: #3fb106
}
</style>

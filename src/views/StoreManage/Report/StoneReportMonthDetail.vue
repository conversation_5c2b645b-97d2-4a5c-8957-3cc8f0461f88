<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElInput,ElButton, ElTable, ElTableColumn, ElDatePicker, ElSelect, ElOption, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { checkPermissionApi, closeOneTagByName, closeOneTagByPath } from '@/api/tool';
import { getBuyReportAllApi, getSellBayBillTJApi, getSellReportAllApi, getStoneReportMonthApi, getStoneReportMonthDetailApi } from '@/api/tj';
import { getStoreListApi } from '@/api/product';
import { ContentDetailWrap } from '@/components/ContentDetailWrap'

const { currentRoute, back, push } = useRouter()
const { t } = useI18n()


//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    月份: '',
    仓库: '',
    产品编码: '',
    产品名称: '',
    排序:'',
    page: 1,
    count: 50
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

const reportData = reactive([])
//当前选中行
const currentRow = ref(null)


const getStoneReportMonthDetail = async () => {
    loading.value = true
    const ret = await getStoneReportMonthDetailApi(searchCondition)
    if (ret) {
        console.log(ret)
        reportData.splice(0, reportData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
    loading.value = false
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getStoneReportMonthDetail()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getStoneReportMonthDetail()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getStoneReportMonthDetail()
}

//处理表格对象操作
const handleOper = (type, row) => {
    if (type == 'info') {

    }

}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}


onMounted(() => {

    searchCondition.月份 = currentRoute.value.query.month as string
    searchCondition.仓库 = currentRoute.value.query.stone as string
    searchCondition.产品编码 = currentRoute.value.query.pdt_name as string
    searchCondition.产品名称 = currentRoute.value.query.pdt_nick as string

    //刷新表格
    getStoneReportMonthDetail()

   // tableHeight.value = document.getElementById('mainscroll')?.clientHeight - 20
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if ([''].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                const sum = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
                sums[index] = sum
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}

const tableHeight = ref(60)
const loading = ref(false)

//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}
</script>

<template>
    <ContentDetailWrap :title=" searchCondition.产品名称+ '明细'" @back="baskFront()">
        <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">
            <div class="inline-flex items-center ml-1 mb-1">
                <div class="searchTitle">月份</div>
                {{ searchCondition.月份 }}
            </div>
            <div class="inline-flex items-center ml-1 mb-1">
                <div class="searchTitle">仓库</div>
                 {{ searchCondition.仓库 }}
            </div>
            <div class="inline-flex items-center ml-1 mb-1">
                <div class="searchTitle">{{ t('product_manage.id') }}</div>
                {{ searchCondition.产品编码 }}
            </div>
            <div class="inline-flex items-center ml-1 mb-1">
                <div class="searchTitle">{{ t('product_manage.name') }}</div>
                {{ searchCondition.产品名称 }}
            </div>
            <el-table v-loading="loading"  header-cell-class-name="tableHeader"
                :data="reportData" style="width: 100%;color: #666666;"
                border stripe>

                <el-table-column align="center" show-overflow-tooltip prop="序号" :label="'序号'" width="50">
                    <template #default="scope">
                        <div>{{ scope.$index + 1 }}</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="类型" :label="'类型'" />
                <el-table-column align="center" show-overflow-tooltip prop="订单单号" :label="'订单单号'" />
                <el-table-column align="center" show-overflow-tooltip prop="来源单号" :label="'来源单号'" />
                <el-table-column align="center" show-overflow-tooltip prop="操作类型" :label="'操作类型'" />
                <el-table-column align="center" show-overflow-tooltip prop="数量" :label="'数量'" />
               
            </el-table>
            <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>

<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { ElButton, ElTable, ElTableColumn,ElTag,ElDropdown,ElDropdownItem,ElDropdownMenu, ElMessageBox, ElMessage } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getStoreListApi,delStoreApi } from '@/api/product'
import { DialogStore } from '@/components/DialogStore'

const { push } = useRouter()
const { t } = useI18n()
//权限数据源
const storeData = reactive([])

//当前选中仓库
const curSelStore = ref({'aa':123})
//显示隐藏弹窗
const showStoreEdit = ref(false)

//新增仓库
const onAddStore = () => {
  showStoreEdit.value = true
}

//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)
  }

}

//编辑
const onEditStore = (row)=>{
  console.log(row)
  showStoreEdit.value = true
  curSelStore.value = row
}
//删除
const onDelStore = (row)=>{
  ElMessageBox.confirm(
      t('msg.delStore'),
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        const ret = await delStoreApi({ "ids": [row.id] })
        if (ret) {
          getStoreList()

          ElMessage({
            type: 'success',
            message: t('msg.delOK'),
          })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })
}

onMounted(() => {
  getStoreList()
})




</script>

<template>
  <ContentWrap :title="t('router.storeRouterMap')">
    <div class="mb-2">
      <ElButton type="success" @click="onAddStore">
        <Icon icon="carbon:document-add" />
        <div class="pl-2">新增</div>
      </ElButton>
      <ElButton color="#409EFF"  plain>
        <Icon icon="clarity:import-line" />
        <div class="pl-2">导入</div>
      </ElButton>
      <ElButton color="#409EFF"  plain>
        <Icon icon="carbon:export" />
        <div class="pl-2">导出</div>
      </ElButton>
    </div>
    <el-table ref="tableRef" :data="storeData" style="width: 100%;min-height: 60vh; margin-bottom: 20px" row-key="guuid" border stripe
      highlight-current-row header-cell-class-name="tableHeader">
      <el-table-column show-overflow-tooltip align="center" width="150px" prop="name" :label="t('store.name')" />
      <el-table-column show-overflow-tooltip align="center" width="150px" prop="nick" :label="t('store.nick')" >
        <template #default="scope">
            <div class="nameStyle" @click="onEditStore(scope.row)">{{ scope.row.nick }}</div>
          </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip align="center" width="150px" prop="type" :label="t('store.prop')" />
      <el-table-column show-overflow-tooltip align="center" width="150px" prop="mainer_name" :label="t('store.manager')" />
      <el-table-column show-overflow-tooltip align="center" min-width="150px"  :label="t('store.alarm_user')" >
        <template #default="scope">          
          <el-tag class="mr-1 mb-2" effect="dark" type="success" v-for="item in scope.row.warnning_name" :key="item">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip  align="center" width="150px" prop="linkman" :label="t('store.linkman')" />
      <el-table-column show-overflow-tooltip align="center" width="150px" prop="telephone" :label="t('store.phone')" />
      <el-table-column show-overflow-tooltip align="center" width="150px" prop="email" :label="t('store.email')" />
      <el-table-column show-overflow-tooltip align="center" width="150px" prop="address" :label="t('store.address')" />
      <el-table-column show-overflow-tooltip fixed="right" align="center" width="100px" prop="name" :label="t('roleTable.opt')" >
        <template #default="scope">
          <el-dropdown trigger="click" placement="left">
              <span class="el-dropdown-link">
                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
              </span>
              <template #dropdown>
                <el-dropdown-menu>                  
                  <el-dropdown-item @click="onEditStore(scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                  <el-dropdown-item @click="onDelStore(scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <DialogStore v-model:show="showStoreEdit" v-model:data="curSelStore" :title="t('store.manage')" :bReturnData = "false" @on-submit="getStoreList"/>
    
  </ContentWrap>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}


</style>

<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard, ElTable, ElPopconfirm, ElTag, ElCheckbox, ElDatePicker, ElTreeSelect, ElSelect, ElOption, ElTooltip, ElTableColumn, ElButton, ElForm, ElFormItem, FormRules, ElDescriptions, ElDescriptionsItem, ElInput, ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted, set } from 'vue'
import { getInventoryListApi, getStoneMoveInfoApi, updateStoneMoveApi, getStoreListApi, getStoneMoveNewnumApi, getProductListApi, addStoneMoveApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount, watch } from 'vue'
import { checkFormRule, checkPermission<PERSON><PERSON>, closeOneTagByName, closeOneTagByPath } from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { DialogCheckShow } from '@/components/DialogCheckShow'


const { currentRoute, back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//调拨单数据
const moveData = reactive(
    {
        'hand_move_num': '',
        "src_store_id": "", //仓库ID
        tgt_store_id: '',
        "pdt_list": [],
        "note": "", //调拨单备注
        //调拨方式（无用）
        move_man_id: '',
        move_man_name: '',
        move_date: '',
        fsm_can_trig_data: {
            审核触发: [],
            操作触发: []
        }, //审核决策
        fsm_cur_state: '订单创建',    //当前节点状态
        fsm_exe_man_name: '',
        fsm_exe_log: '',
        fsm_exe_trig: '',//决策内容
        fsm_log_list: []
    }
)


//获取最新ID
const onChangeID = async () => {
    const ret = await getStoneMoveNewnumApi()
    if (ret) {
        console.log(ret)
        moveData.hand_move_num = ret.data.new_id
    }
}


//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
    const res = await getStoreListApi({
        page: 1,
        count: 1000
    })
    if (res) {
        console.log(res.data)
        storeData.splice(0, storeData.length, ...res.data)
    }

}



onMounted(async () => {
    if (currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined) {
        await getStoreList()
        title.value = '手动调拨单'

        onChangeID()
        //新建需要默认可以提审
        const tmp = [...moveData.fsm_can_trig_data.操作触发, '提交审核']
        moveData.fsm_can_trig_data.操作触发 = tmp

        //默仓库
        moveData.src_store_id = storeData[0].id
        moveData.tgt_store_id = storeData[0].id

        //设置调拨人员
        const info = wsCache.get(appStore.getUserInfo)
        moveData.move_man_id = info.id
        moveData.move_man_name = info.resident_name

        moveData.move_date = getTodayDate()
        //追加默认行
        moveData.pdt_list.push({ 总价: '0' })
    }
    else {
        if (currentRoute.value.query.mode == 'info') {
            title.value = '调拨单详情'
        }
        else {
            title.value = '调拨单编辑'
        }




        //查询产品信息 
        const ret = await getStoneMoveInfoApi({
            id: currentRoute.value.query.id,
            page: 1,
            count: 100
        })
        if (ret) {
            console.log(ret)
            Object.assign(moveData, ret.data)
            moveData.pdt_list = ret.data.pdt_list;
            currentRoute.value.query.type = ret.data.type
            await getStoreList()
        }

        if (currentRoute.value.query.mode === 'info') //查看模式
        {
            let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
            let excludeDiv = document.getElementById('check');
            if (excludeDiv != null) {
                // 使用 :not() 伪类排除特定的 div 元素下的子元素
                let filteredComponents = Array.from(components).filter(
                    component => !excludeDiv.contains(component)
                );
                filteredComponents.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }
            else {
                components.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }


            components = document.querySelectorAll('.el-input__wrapper,.el-textarea');

            let i = 0
            components.forEach((component) => {
                if (i > 0)
                    component.classList.add('infomode')
                i++
            });
            const suffixElements = document.querySelectorAll('.el-input__suffix');
            suffixElements.forEach(suffixElement => {
                suffixElement.style.display = 'none';
            });
        }
    }

})



//显示隐藏选择调拨员窗口变量
const showSelPutoutUserDlg = ref(false)
//显示选择调拨员弹窗
const onSelPutoutUser = () => {
    showSelPutoutUserDlg.value = true
}
//选择调拨员回调
const onSelPutoutCallback = (id, name) => {
    console.log(id, name)
    moveData.move_man_id = id
    moveData.move_man_name = name
}


//校验pdt
const checkPdt = () => {
    if (moveData.pdt_list.length <= 0) {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}

//提交审核意见
const handleCheck = async (btn) => {
    console.log(11)
    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    if (moveData.src_store_id == "" || moveData.tgt_store_id == null) {
        ElMessage.error('请选择仓库！')
        return
    }
    if (moveData.src_store_id == moveData.tgt_store_id) {
        ElMessage.error('请选择不同仓库进行调拨！')
        return
    }


    const info = wsCache.get(appStore.getUserInfo)
    moveData.fsm_exe_man_name = info.resident_name
    moveData.fsm_exe_trig = btn

    const data = cloneDeep(moveData)
    if(currentRoute.value.query.mode != 'info')
        data.pdt_list.splice(-1, 1)

    if (!checkPdt()) {
        return
    }

    //删除moveData.pdt_list中调拨数量为0的行
    data.pdt_list = data.pdt_list.filter(pdt => pdt.调拨数量 != 0)

    if(data.pdt_list.length <=0)
    {
        ElMessage.error('调拨数量不能为0')
        return
    }
    // console.log(moveData.pdt_list)
    // return

    if (data.id == undefined) {
        const ret = await addStoneMoveApi(data)
        if (ret) {
            ElMessage.success('创建调拨单成功！')
            baskFront()
        }
    }
    else //修改
    {
        //特殊处理同意
        if (btn === '同意') {
            const ret = await updateStoneMoveApi({
                id: data.id,
                move_date: data.move_date,
                fsm_exe_trig: data.fsm_exe_trig,
                fsm_exe_man_name: data.fsm_exe_man_name,
                fsm_exe_log: data.fsm_exe_log,
            })
            if (ret) {
                ElMessage.success('操作成功!')
                baskFront()
            }
        }
        else {
            const ret = await updateStoneMoveApi(data)
            if (ret) {
                ElMessage.success('操作成功!')
                baskFront()
            }
        }

    }
    // closeOneTagByPath('')
}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = () => {
    showCheckHisDlg.value = true
}


//查询产品绑定的查询结果
const searchPdtData = reactive<any>([])
//查询产品列表根据编号
const getProductList = async (val = '') => {
    const ret = await getProductListApi({
        name: val,
        nick: val,
        _or: true,
        status: '正常',
        page: 1,
        count: 30,
    })
    if (ret) {

        searchPdtData.splice(0, searchPdtData.length, ...ret.data)
        if (searchPdtData.length > 0) {
            setCurrent(searchPdtData[0])
        }
    }
}


//产品查找的输入
const txtSearch = ref('')
//点击了查找产品input
const onClickSearchPdt = () => {
    console.log('点了')
    getProductList(txtSearch.value)
    showProductSel.value = true
    // 添加全局点击事件监听器
    window.addEventListener('click', handleGlobalClick);
}
const handleGlobalClick = (event) => {

    const elTableContainers = document.querySelectorAll('.el-table-container');
    let clickedInsideTable = false;
    for (const container of elTableContainers) {
        if (container.contains(event.target)) {
            // 点击的元素在 el-table 容器内部
            clickedInsideTable = true;
            break;
        }
    }

    if (!clickedInsideTable) {
        // 判断点击位置是否在输入框和浮动窗口外部
        // const isOutsideClick = !inputElement.contains(event.target) && !floatWindowElement.contains(event.target);
        const bIn = (event.target as HTMLElement).classList.contains('el-input__inner')
        // 根据判断结果来决定是否隐藏浮动窗口
        if (!bIn) {
            showProductSel.value = false
        }
    }
}
//处理输入
const delaytime = ref<NodeJS.Timeout | null>(null);
const onInputSearch = (val) => {
    showProductSel.value = true
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)

    delaytime.value = setTimeout(() => {
        // 输入停止后触发的方法
        getProductList(val)
    }, 200)
}


//键盘上下只切换不选择
const nCurSelPdtID = ref('')
const bKeyDown = ref(true)
const onKeyDownOnePdt = (event) => {
    if (event.keyCode === 38 || event.keyCode === 40) {
        // 阻止默认行为，以保持光标位置不变
        event.preventDefault();
    }
    //esc按键关闭table
    if (event.keyCode === 27) {
        showProductSel.value = false
        return
    }
    bKeyDown.value = true
    if (nCurSelPdtID.value == '') {
        setCurrent(searchPdtData[0])
    }
    else {


        for (let i = 0; i < searchPdtData.length; i++) {
            if (searchPdtData[i].id == nCurSelPdtID.value) {
                if (event.keyCode === 38 && i > 0)
                    setCurrent(searchPdtData[i - 1])
                else if (event.keyCode === 40 && i < searchPdtData.length - 1)
                    setCurrent(searchPdtData[i + 1])
                //如果是回车，直接选择
                else if (event.keyCode === 13) {
                    onRowClick(searchPdtData[i])
                    return
                }
                return
            }
        }
    }

}
const searchRef = ref<InstanceType<typeof ElTable>>()

const setCurrent = (row?) => {
    if (row == undefined)
        nCurSelPdtID.value = ''
    console.log('setCurrent', searchRef)
    searchRef.value!.setCurrentRow(row)
}
const onRowClick = (row) => {
    console.log('xuanle', row)
    handleCurrentSelectPdt(row, null, true)
}

//显示隐藏选择产品浮窗
const showProductSel = ref(false)
//选择了某一个产品
const handleCurrentSelectPdt = (item, old, fource = false) => {
    if (item == undefined) {
        return
    }

    nCurSelPdtID.value = item.id
    if (!fource) {
        return
    }
    txtSearch.value = ''

    setTimeout(() => {
        const inputElement = document.querySelector('.input-search input');
        inputElement?.focus()
        onClickSearchPdt()
    }, 500)


    item.调拨备注 = ''
    item.调拨数量 = 0


    //更新产品价格
    reComputePdtInfo(item, '调拨数量')

    //构造一行产品
    moveData.pdt_list.splice(-1, 0, item)
    //隐藏浮窗
    showProductSel.value = false

    console.log(moveData)
    onUpdateKC(moveData.src_store_id)

}

//删除某一个产品
const onDelPdt = (index) => {
    moveData.pdt_list.splice(index, 1)
}


const reComputePdtInfo = (pdt) => {
    pdt.调拨数量 = pdt.调拨数量 == '' ? 0 : pdt.调拨数量
    pdt.调拨数量 = parseFloat(pdt.调拨数量, 10)

    //如果调拨数量不能大于库存
    if(pdt.调拨数量>pdt.可用数量)
    {
        pdt.调拨数量=pdt.可用数量
    }
    if(pdt.调拨数量<0)
    {
        pdt.调拨数量=0
    }
}



const totlePutout = computed(() => {

    let totle = 0
    for (const item of moveData.pdt_list) {
        if (item.调拨数量 == undefined)
            continue
        totle += parseFloat(item.调拨数量)
    }
    return totle
})


//返回上一页
const baskFront = () => {
    back()
    closeOneTagByName(currentRoute.value.meta.title)
    //刷新上一页
    closeOneTagByPath('/inventorymanage/stonemovelist')
}

//产品仓库分布数据
const detailData = reactive([])
//更新库存显示
const onUpdateKC = async (store_id) => {
    if (moveData.pdt_list.length <= 0)
        return
    let arrPdtIDs = []
    for (let one of moveData.pdt_list) {
        arrPdtIDs.push(one.id)
    }

    //查询这个产品在那些仓库有库存
    const ret = await getInventoryListApi({
        store_id: store_id,
        pdt_ids: [...arrPdtIDs],
        page: 1,
        count: 100
    })
    if (ret) {
        detailData.splice(0, detailData.length, ...ret.data)
        //更新统计数据到
        for (let item of moveData.pdt_list) {
            item.可用数量 = 0
            item.锁定数量 = 0
            for (let one of detailData) {
                let bFind = false
                if (item.id == one.pdt_id) {
                    item.可用数量 = one.良品数量 + one.不良品数量 - one.锁定数量
                    item.锁定数量 = one.锁定数量
                    bFind = true
                }
                if (bFind)
                    break
            }
        }
    }
    console.log(moveData.pdt_list)
}

</script>

<template>
    <ContentDetailWrap :title="'调拨单-' + moveData.fsm_cur_state" @back="baskFront()">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史
            </ElButton>
        </template>
        <template #right>
            <ElButton
                v-show="currentRoute.query.cmd != '审核' && currentRoute.query.mode != 'info' && moveData.fsm_can_trig_data.操作触发.includes('保存')"
                type="primary" @click="handleCheck('保存')">保存</ElButton>
            <el-popconfirm title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton
                        v-show="currentRoute.query.cmd != '审核' && moveData.fsm_can_trig_data.操作触发.includes('提交审核')"
                        type="success">提交审核</ElButton>
                </template>
            </el-popconfirm>

            <el-popconfirm title="是否确认关闭订单?" @confirm="handleCheck('关闭')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && moveData.fsm_can_trig_data.操作触发.includes('关闭')"
                        type="danger">关闭订单</ElButton>
                </template>
            </el-popconfirm>
        </template>
        <el-card id="check" v-if="moveData.fsm_can_trig_data.审核触发.length > 0 && currentRoute.query.cmd == '审核'"
            class="w-[100%]">
            <template #header>
                <div class="flex items-center">
                    <span>当前节点:</span>
                    <span class="text-red-500 mr-3">{{ moveData.fsm_cur_state }}</span>
                    <!-- <ElButton @click="handleCheck(btn)" v-for="btn in moveData.fsm_can_trig_data.审核触发.toReversed()" :key="btn" :type="btn=='同意'?'success':'danger'">{{ btn }}</ElButton> -->
                    <ElButton v-show="moveData.fsm_can_trig_data.审核触发.includes('同意')" type="success"
                        @click="handleCheck('同意')">同意</ElButton>
                    <el-popconfirm title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                        <template #reference>
                            <ElButton v-show="moveData.fsm_can_trig_data.审核触发.includes('驳回')" type="danger">驳回
                            </ElButton>
                        </template>
                    </el-popconfirm>
                    <el-popconfirm title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                        <template #reference>
                            <ElButton v-show="moveData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger">拒绝
                            </ElButton>
                        </template>
                    </el-popconfirm>

                    <div class="flex ml-4 items-center">
                        <div class="mr-2">调拨日期:</div>
                        <el-date-picker v-model="moveData.move_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </div>


                </div>
            </template>
            <el-input v-model="moveData.fsm_exe_log" class="mt-3" :autosize="{ minRows: 5, maxRows: 2 }"
                type="textarea" />
        </el-card>
        <el-form :rules="rules" :model="moveData" ref="ruleFormRef">
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'调拨单号'"
                    class="flex">
                    <el-form-item prop="name">
                        <div class="flex">
                            <el-input v-model="moveData.hand_move_num" :disabled="moveData.id != undefined" />
                            <ElButton v-if="moveData.id == undefined" type="warning" @click="onChangeID">{{
                                t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name='conentStyle' :label="'调出仓库'"
                    class='flex'>
                    <el-select v-model="moveData.src_store_id" placeholder='Select' @change="onUpdateKC">
                        <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name='conentStyle' :label="'调入仓库'"
                    class='flex'>
                    <el-select v-model="moveData.tgt_store_id" placeholder='Select'>
                        <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'调拨人'"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ moveData.move_man_name }}</div>
                        <ElButton @click="onSelPutoutUser">
                            <Icon icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name='conentStyle' :label="'入库时间'"
                        class='flex'>
                        <el-date-picker class="!w-[160px]" v-model="moveData.move_date" type="date" placeholder="" format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD" />
                </el-descriptions-item>
            </el-descriptions>

        </el-form>

        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell"
            :data="moveData.pdt_list" style="width: 100%" border stripe>
            <el-table-column :label="t('process.opt')" width="60">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined && currentRoute.query.type != 'info'" type="primary">
                        <el-popconfirm title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon icon="material-symbols:delete-outline" class=" cursor-pointer"
                                    style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60" />
            <el-table-column :label="t('sale.img')" width="80">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length > 0" class="object-fill w-[60px] h-[60px] min-w-[60px]"
                            :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length <= 0" class="object-fill w-[60px] h-[60px] min-w-[60px]"
                            src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip prop="nick" :label="t('product_manage.name')" min-width="160">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick }}</div>
                    <el-input class="input-search" @keydown='onKeyDownOnePdt' @keyup='bKeyDown = false'
                        v-model="txtSearch" placeholder="请点击输入搜索" @input="onInputSearch" @click="onClickSearchPdt"
                        v-if="scope.row.nick == '' || scope.row.nick == undefined">{{ scope.row.id }}</el-input>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.specs')">
                <template #default="scope">
                    <el-tooltip v-if="scope.row.id != undefined && scope.row.specs != ''" class="box-item" effect="dark"
                        :content="scope.row.specs_text" placement="bottom">
                        <el-tag>{{ scope.row.specs_name == '自定义规格' ? scope.row.specs_text : scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column :label="'可用库存'">
                <template #default="scope">
                    {{ scope.row.可用数量 }}
                </template>
            </el-table-column>
            <el-table-column :label="'锁定库存'">
                <template #default="scope">
                    {{ scope.row.锁定数量 }}
                </template>
            </el-table-column>

            <el-table-column show-overflow-tooltip :label="'调拨数量'" width="140">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex justify-center items-center">
                        <el-input v-model="scope.row.调拨数量" class="!text-center mr-1 !w-[80%] flex" type="number"
                            @blur="reComputePdtInfo(scope.row, '调拨数量')" />
                        <div style="font-size: smaller;">({{ scope.row.base_unit }})</div>
                    </div>
                    <div v-if="moveData.pdt_list.length > 1 && scope.row.id == undefined">
                        {{ totlePutout }}
                    </div>
                </template>
            </el-table-column>

            <el-table-column :label="'备注'">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.调拨备注" class="!text-center" />
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div v-if="showProductSel"
            class=" bg-light-600 w-[90%] max-h-[400px] absolute z-99 p-1 shadow el-table-container">
            <el-table ref="searchRef" :data="searchPdtData" style="width: 100%;" row-key="id" border
                highlight-current-row @current-change="handleCurrentSelectPdt" header-cell-class-name="tableHeader"
                height="300" @row-click='onRowClick'>
                <el-table-column show-overflow-tooltip fixed prop="name" :label="t('product_manage.id')" width="130" />
                <el-table-column show-overflow-tooltip fixed prop="nick" :label="t('product_manage.name')"
                    width="600" />
                <el-table-column show-overflow-tooltip fixed prop="brand" :label="t('product_manage.brand')" />
                <el-table-column show-overflow-tooltip fixed prop="specs_text"
                    :label="t('product_manage.specify_info')" />
                <el-table-column show-overflow-tooltip fixed prop="nick_brif" :label="t('product_manage.short_name')" />
                <el-table-column show-overflow-tooltip fixed prop="note" :label="t('product_manage.help_name')" />
            </el-table>
        </div>

        <!-- 显示合计 -->
        <div class="flex">
            <!-- 左边部分 -->
            <div class="w-[100%] text-center">
                <div class="flex">
                    <table class="table-auto border-collapse table_self w-[100%]">

                        <tr>
                            <td class="table_self_title min-w-[100px] p-2">调拨单备注:</td>
                            <td class="table_self !w-[100%] p-3">
                                <el-input v-model="moveData.note" clearable :autosize="{ minRows: 10, maxRows: 4 }"
                                    type="textarea" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

        </div>







        <!-- <div class="mb-60"></div> -->

        <!-- 选择质检员 -->
        <DialogUser :param="''" v-model:show="showSelPutoutUserDlg" :title="t('msg.selectUser')"
            @on-submit="onSelPutoutCallback" />
        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="moveData.fsm_log_list" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
    margin-bottom: 10px;
}

//设置表单元格属性
:deep(.table_cell .cell) {
    padding-left: 3px;
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
    /* 添加你的样式 */
    text-align: center;
}

:deep(.bakinput .el-input__wrapper) {
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self {
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}

//下半部分表格标题
.table_self_title {
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner) {
    text-align: center;
    font-size: 18px;
}

//扩展文字
.ex_text {
    font-size: 11px;
    color: #646464;
}

:deep(.infomode) {
    border: none;
    /* 设置边框为 none */
    border-radius: 0;
    /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none;
    /* 如果有阴影，也可以设置为 none */
}
</style>
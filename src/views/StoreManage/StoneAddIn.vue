<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard,ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,set } from 'vue'
import { getOtherPutinInfoApi,updateOtherPutinApi,getStoreListApi,getOtherPutinNewnumApi,getProductListApi,addOtherPutinApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount,watch } from 'vue'
import {checkFormRule, checkPermission<PERSON><PERSON>, closeOneTagByName, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { DialogCheckShow } from '@/components/DialogCheckShow'


const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//入库单数据
const putinData = reactive(
{
    'hand_putin_num':'',
    "store_id": "", //仓库ID
    "pdt_list": [],
    "note": "", //入库单备注
    //入库方式（无用）
    putin_man_id:'',
    putin_man_name:'',
    putin_date:'',
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:[]
    }, //审核决策
    fsm_cur_state:'订单创建',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)


//获取最新ID
const onChangeID = async()=>{
    const ret = await getOtherPutinNewnumApi()
    if(ret)
    {
        console.log(ret)
        putinData.hand_putin_num = ret.data.new_id
    }
}


//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)
  }

}

const oldPutin = reactive({})

onMounted(async () => {    
    console.log('???',currentRoute.value.matched[currentRoute.value.matched.length - 2]?.path || '/')
    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        await getStoreList()
        title.value = '手动入库单'

        onChangeID()
        //新建需要默认可以提审
        const tmp = [...putinData.fsm_can_trig_data.操作触发,'提交审核']
        putinData.fsm_can_trig_data.操作触发 = tmp

        //默仓库
        putinData.store_id = storeData[0].id
        putinData.putin_date = getTodayDate()
        //设置入库人员
        const info = wsCache.get(appStore.getUserInfo)
        putinData.putin_man_id = info.id
        putinData.putin_man_name = info.resident_name

        //追加默认行
        putinData.pdt_list.push({总价:'0'}) 
    }
    else
    {        
        if(currentRoute.value.query.mode == 'info')
        {
            title.value = '入库单详情'
        }
        else
        {
            title.value = '入库单编辑'
        }



        
        //查询产品信息 
        const ret = await getOtherPutinInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(putinData, ret.data)
            putinData.pdt_list = ret.data.pdt_list;
            //追加默认行
            putinData.pdt_list.push({总价:'0'}) 
            currentRoute.value.query.type = ret.data.type
            await getStoreList()
        }

        if(currentRoute.value.query.mode === 'info') //查看模式
        {
            let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
            let excludeDiv = document.getElementById('check');
            if(excludeDiv != null)
            {
                // 使用 :not() 伪类排除特定的 div 元素下的子元素
                let filteredComponents = Array.from(components).filter(
                    component => !excludeDiv.contains(component)
                );
                filteredComponents.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }
            else
            {
                components.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }


            components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
  
            let i = 0
            components.forEach((component) => {
                if(i>0)
                    component.classList.add('infomode')
                i++
            });
            const suffixElements = document.querySelectorAll('.el-input__suffix');
            suffixElements.forEach(suffixElement => {
                suffixElement.style.display = 'none';
            });
        }
    }

    //先备份下旧对象
    Object.assign(oldPutin,putinData)
})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})



//显示隐藏选择入库员窗口变量
const showSelPutinUserDlg = ref(false)
//显示选择入库员弹窗
const onSelPutinUser = ()=>{
    showSelPutinUserDlg.value = true
}
//选择入库员回调
const onSelPutinCallback = (id,name)=>{
    console.log(id,name)
    putinData.putin_man_id = id
    putinData.putin_man_name = name
}


//校验pdt
const checkPdt = ()=>{
    if(putinData.pdt_list.length <= 0)
    {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}

//提交审核意见
const handleCheck = async(btn)=>{
    console.log(11)
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    if(putinData.store_id == "")
    {
        ElMessage.error('请选择仓库！')
        return
    }
    

    const info = wsCache.get(appStore.getUserInfo)
    putinData.fsm_exe_man_name = info.resident_name
    putinData.fsm_exe_trig = btn

    const data = cloneDeep(putinData)
    if(currentRoute.value.query.mode != 'info')
        data.pdt_list.splice(-1,1)

    if(!checkPdt())
    {
        return 
    }

    //删除putinData.pdt_list中入库数量为0的行
    data.pdt_list = data.pdt_list.filter(pdt=>pdt.入库数量!=0)

    if(data.pdt_list.length <= 0)
    {
        ElMessage.warning('没有产品，或者入库数量为0？')
        return
    }

    // console.log(putinData.pdt_list)
    // return

    if(data.id == undefined)
    {
        const ret = await addOtherPutinApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.newPutinSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        //特殊处理同意
        if(btn === '同意')
        {
            const ret =await updateOtherPutinApi({
                id:data.id,
                putin_date:data.putin_date,
                fsm_exe_trig:data.fsm_exe_trig,
                fsm_exe_man_name:data.fsm_exe_man_name,
                fsm_exe_log:data.fsm_exe_log,
            })
            if(ret)
            {
                ElMessage.success(t('msg.updatePutinSuccess'))
                baskFront()
            }
        }
        else    
        {
            const ret =await updateOtherPutinApi(data)
            if(ret)
            {
                ElMessage.success(t('msg.updatePutinSuccess'))
                baskFront()
            }
        }

    }
    // closeOneTagByPath('')
}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = ()=>{
    showCheckHisDlg.value = true
}


//查询产品绑定的查询结果
const searchPdtData = reactive<any>([])
//查询产品列表根据编号
const getProductList = async (val='') => {
  const ret = await getProductListApi({
    name:val,
    nick:val,
    _or:true,
    status:'正常',
    page: 1,
    count:30,
  })
  if(ret)
  {
    
    searchPdtData.splice(0,searchPdtData.length, ...ret.data)
    if(searchPdtData.length >0)
    {
        setCurrent(searchPdtData[0])
    }
  }
}


//产品查找的输入
const txtSearch = ref('')
//点击了查找产品input
const onClickSearchPdt = ()=>{
    console.log('点了')
    getProductList(txtSearch.value)
    showProductSel.value = true
    // 添加全局点击事件监听器
    window.addEventListener('click', handleGlobalClick);
}
const handleGlobalClick = (event)=>{

    const elTableContainers = document.querySelectorAll('.el-table-container');      
    let clickedInsideTable = false;
    for (const container of elTableContainers) {
        if (container.contains(event.target)) {
            // 点击的元素在 el-table 容器内部
            clickedInsideTable = true;
            break;
        }
    }
        
    if (!clickedInsideTable) {
        // 判断点击位置是否在输入框和浮动窗口外部
        // const isOutsideClick = !inputElement.contains(event.target) && !floatWindowElement.contains(event.target);
        const bIn = (event.target as HTMLElement).classList.contains('el-input__inner')
        // 根据判断结果来决定是否隐藏浮动窗口
        if(!bIn)
        {
            showProductSel.value = false
        }
    }
}
//处理输入
const delaytime =ref<NodeJS.Timeout | null>(null); 
const onInputSearch =(val)=>{
    showProductSel.value = true
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)
    
    delaytime.value = setTimeout(() => {
      // 输入停止后触发的方法
      getProductList(val)
    }, 200) 
}


//键盘上下只切换不选择
const nCurSelPdtID = ref('')
const bKeyDown = ref(true)
const onKeyDownOnePdt = (event)=>{
    if (event.keyCode === 38 || event.keyCode === 40) {
        // 阻止默认行为，以保持光标位置不变
        event.preventDefault();
    }
    //esc按键关闭table
    if(event.keyCode === 27)
    {
        showProductSel.value = false  
        return
    }
    bKeyDown.value = true
    if(nCurSelPdtID.value == '')
    {
        setCurrent(searchPdtData[0])
    }
    else  
    {


        for(let i = 0;i<searchPdtData.length;i++)
        {
            if(searchPdtData[i].id == nCurSelPdtID.value)
            {
                if(event.keyCode === 38 && i>0)
                    setCurrent(searchPdtData[i-1])
                else if(event.keyCode === 40 && i<searchPdtData.length-1)
                    setCurrent(searchPdtData[i+1])
                //如果是回车，直接选择
                else if(event.keyCode === 13)
                {
                    onRowClick(searchPdtData[i])
                    return
                }
                return
            }
        }
    }

}
const searchRef = ref<InstanceType<typeof ElTable>>()

const setCurrent = (row?) => {
    if(row == undefined)
        nCurSelPdtID.value = ''
    console.log('setCurrent',searchRef)
    searchRef.value!.setCurrentRow(row)
}
const onRowClick = (row)=>{
    console.log('xuanle',row)
    handleCurrentSelectPdt(row,null,true)
}

//显示隐藏选择产品浮窗
const showProductSel = ref(false)
//选择了某一个产品
const handleCurrentSelectPdt = (item,old,fource = false) =>{
    if( item == undefined)
    {
        return
    }

    nCurSelPdtID.value = item.id
    if(!fource){
        return
    }
    txtSearch.value = ''
    
    setTimeout(() => {
        const inputElement = document.querySelector('.input-search input');
        inputElement?.focus()
        onClickSearchPdt()
    },500)
       

    item.入库备注 = ''
    item.入库数量 = 0
    item.hand_price_bef_tax = 0
    item.hand_price_aft_tax = 0
    item.发票类型 = '专票'
    item.发票税率 = 0


    //更新产品价格
    reComputePdtInfo(item,'入库数量')

    //构造一行产品
    putinData.pdt_list.splice(-1,0,item)
    //隐藏浮窗
    showProductSel.value = false    

    console.log(putinData)
    
}

//删除某一个产品
const onDelPdt = (index)=>{
    putinData.pdt_list.splice(index,1)
}


const reComputePdtInfo = (pdt,mode) => {
    pdt.入库数量 = pdt.入库数量 == ''?0:pdt.入库数量
    pdt.hand_price_bef_tax = pdt.hand_price_bef_tax == ''?0:pdt.hand_price_bef_tax
    pdt.hand_price_aft_tax = pdt.hand_price_aft_tax == ''?0:pdt.hand_price_aft_tax

    if(mode == '入库数量')
    {
        //根据入库数量计算总价 总价等于税后单价乘以入库数量
        //重新计算税后价格
        pdt.hand_price_aft_tax = parseFloat(pdt.hand_price_bef_tax)+ parseFloat(pdt.hand_price_bef_tax)*pdt.发票税率/100
        pdt.总价 = parseFloat((pdt.hand_price_aft_tax*pdt.入库数量).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '税前单价')
    {
        //根据税前单价和税率计算出税后单价         
        pdt.hand_price_aft_tax = parseFloat(pdt.hand_price_bef_tax)+ parseFloat(pdt.hand_price_bef_tax)*pdt.发票税率/100
        //计算出总价
        pdt.总价 = parseFloat((pdt.hand_price_aft_tax*pdt.入库数量).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '税后单价')
    {
        //根据税后单价和税率计算出税前单价         
        pdt.hand_price_bef_tax = parseFloat((pdt.hand_price_aft_tax/(1+pdt.发票税率/100)).toFixed(pdt.sell_buy_digit))
        //计算出总价
        pdt.总价 = parseFloat((pdt.hand_price_aft_tax*pdt.入库数量).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '折扣')
    {
        //根据折扣重新计算总价
        pdt.总价 = parseFloat((pdt.hand_price_aft_tax*pdt.入库数量).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '发票税率')
    {
        //根据发票类型调整税率数
        if(!['普票','专票','电子票'].includes(pdt.发票类型))
        {
            pdt.发票税率 = 0
            //重新调整税后价格
            pdt.hand_price_aft_tax = pdt.hand_price_bef_tax
        }
        //重新计算税后价格
        pdt.hand_price_aft_tax = parseFloat(pdt.hand_price_bef_tax)+ parseFloat(pdt.hand_price_bef_tax)*pdt.发票税率/100
        //重新计算总价
        pdt.总价 = parseFloat((pdt.hand_price_aft_tax*pdt.入库数量).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '总价')
    {
        //根据总价重新调整税前税后价格
        pdt.hand_price_aft_tax = parseFloat(((pdt.总价)/pdt.入库数量).toFixed(pdt.sell_buy_digit))
        pdt.hand_price_bef_tax = parseFloat(((pdt.总价)/((1+pdt.发票税率/100)*pdt.入库数量)).toFixed(pdt.sell_buy_digit))
    }

    pdt.入库数量 = pdt.入库数量 == ''?0:parseFloat(pdt.入库数量,10)
    pdt.hand_price_bef_tax = pdt.hand_price_bef_tax == ''?0:pdt.hand_price_bef_tax
    pdt.hand_price_aft_tax = pdt.hand_price_aft_tax == ''?0:pdt.hand_price_aft_tax

 
}



const totlePutin = computed(() => {

    let totle = 0
    for(const item of putinData.pdt_list)
    {
        if(item.入库数量 == undefined)
            continue
        totle += parseFloat(item.入库数量)
    }
    return totle
})

//根据产品列表计算销售 总价
const totleSalePrice = computed(() => {

    let totle = 0
    let span = 0
    for(const item of putinData.pdt_list)
    {
        if(item.总价 == undefined || item.id == undefined)
            continue
        if(item.sell_buy_digit != undefined)
            span = parseInt(item.sell_buy_digit)
        totle += parseFloat(item.总价)
    }

    return  parseFloat(totle.toFixed(span))
})

//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
    //刷新上一页
    closeOneTagByPath('/inventorymanage/stoneinlist')
}

const baskFrontNoFreshFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}
</script>

<template>
    <ContentDetailWrap :title="'入库单-'+putinData.fsm_cur_state" @back="baskFront()">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史
            </ElButton>
        </template>
        <template #right>
            <ElButton  v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.mode != 'info' && putinData.fsm_can_trig_data.操作触发.includes('保存')" type="primary" @click="handleCheck('保存')">保存</ElButton>
            <el-popconfirm  title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && putinData.fsm_can_trig_data.操作触发.includes('提交审核')"   type="success" >提交审核</ElButton>
                </template>
            </el-popconfirm>
            
            <el-popconfirm  title="是否确认关闭订单?" @confirm="handleCheck('关闭')">
                <template #reference>
                    <ElButton  v-show="currentRoute.query.cmd != '审核' && putinData.fsm_can_trig_data.操作触发.includes('关闭')" type="danger">关闭订单</ElButton>
                </template>
            </el-popconfirm>    
            <el-popconfirm  title="确定是否修改订单?" @confirm="handleCheck('修改')">
                <template #reference>
                    <ElButton  v-show="currentRoute.query.cmd != '审核' && currentRoute.query.mode == 'edit' " type="danger">修改</ElButton>
                </template>
            </el-popconfirm>   
        </template>
        <el-card id="check" v-if="putinData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%]">
            <template #header>
            <div class="flex items-center">
                <span>当前节点:</span>
                <span class="text-red-500 mr-3">{{ putinData.fsm_cur_state }}</span>
                <!-- <ElButton @click="handleCheck(btn)" v-for="btn in putinData.fsm_can_trig_data.审核触发.toReversed()" :key="btn" :type="btn=='同意'?'success':'danger'">{{ btn }}</ElButton> -->
                <ElButton v-show="putinData.fsm_can_trig_data.审核触发.includes('同意')" type="success" @click="handleCheck('同意')" >同意</ElButton>
                <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="putinData.fsm_can_trig_data.审核触发.includes('驳回')" type="danger" >驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="putinData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                    </template>
                </el-popconfirm>

                <div class="flex ml-4 items-center">
                    <div class="mr-2">入库日期:</div>
                    <el-date-picker v-model="putinData.putin_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                </div>
                
                
            </div>
            </template>
            <el-input v-model="putinData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        </el-card>
        <el-form :rules="rules" :model="putinData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="'入库单号'"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  v-model="putinData.hand_putin_num" :disabled="putinData.id!=undefined" />
                            <ElButton v-if="putinData.id==undefined" type="warning"  @click="onChangeID">{{ t('button.update') }}</ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('store.store')"
                    class="flex">
                    <el-select v-model="putinData.store_id" placeholder="Select" >
                        <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('putin.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ putinData.putin_man_name }}</div> 
                        <ElButton @click="onSelPutinUser">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name='conentStyle' :label="'入库时间'"
                        class='flex'>
                        <el-date-picker class="!w-[160px]" v-model="putinData.putin_date" type="date" placeholder="" format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD" />
                </el-descriptions-item>
            </el-descriptions>
        
        </el-form>

        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="putinData.pdt_list" style="width: 100%" border stripe>
            <el-table-column  :label="t('process.opt')" width="60" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined && currentRoute.query.type != 'info'"  type="primary">
                        <el-popconfirm title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon  icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" />
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="160">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick}}</div>
                    <el-input class="input-search" @keydown='onKeyDownOnePdt' @keyup='bKeyDown = false'  v-model="txtSearch" placeholder="请点击输入搜索" @input="onInputSearch" @click="onClickSearchPdt" v-if="scope.row.nick=='' || scope.row.nick == undefined">{{ scope.row.id }}</el-input>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.id != undefined && scope.row.specs != ''"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name=='自定义规格'?scope.row.specs_text:scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>

            <el-table-column show-overflow-tooltip :label="'入库数量'"  width="140">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex justify-center items-center">
                        <el-input :disabled="currentRoute.query.mode === 'edit'"  v-model="scope.row.入库数量" class="!text-center mr-1 !w-[80%] flex"  type="number" @blur="reComputePdtInfo(scope.row,'入库数量')"/>
                        <div style="font-size: smaller;">({{ scope.row.base_unit }})</div>
                    </div>  
                    <div v-if="putinData.pdt_list.length>1 && scope.row.id == undefined">
                        {{ totlePutin }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.bef_tax')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.hand_price_bef_tax" class="!text-center" @blur="reComputePdtInfo(scope.row,'税前单价')" type="number"/>
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.after_tax')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.hand_price_aft_tax" class="!text-center" @blur="reComputePdtInfo(scope.row,'税后单价')" type="number" />
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.tax_rate')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class1="flex">
                        <el-select v-if="scope.row.id != undefined"  v-model="scope.row.发票类型" placeholder="请选择" size="small"  @change="reComputePdtInfo(scope.row,'发票税率')">
                            <el-option  v-for="item in ['普票','专票','收据','不开票','电子票']" :key="item" :label="item" :value="item" />
                        </el-select>
                        <div class="flex mt-1" v-if="['普票','专票','电子票'].includes(scope.row.发票类型) ">
                            <el-select v-if="scope.row.id != undefined"  v-model="scope.row.发票税率" placeholder="Select" size="small" @change="reComputePdtInfo(scope.row,'发票税率')">
                                <el-option  v-for="item in [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]" :key="item" :label="item" :value="item" />
                            </el-select>
                            %
                        </div>      
                    </div>   
                    <div v-if="putinData.pdt_list.length>1 && scope.row.id == undefined" class=" font-bold">
                        总价合计:
                    </div>                 
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.totle_price')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.总价" class="!text-center" @blur="reComputePdtInfo(scope.row,'总价')" type="number" v-show="checkPermissionApi('销售订单价格显示')"/>
                    </div>      
                    <div v-if="putinData.pdt_list.length>1 && scope.row.id == undefined" v-show="checkPermissionApi('销售订单价格显示')">
                        {{ totleSalePrice }}
                    </div>              
                </template>
            </el-table-column>
            <el-table-column  :label="'备注'"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.入库备注" class="!text-center"/>
                    </div>                    
                </template>
            </el-table-column>
        </el-table>
        <div v-if="showProductSel"  class=" bg-light-600 w-[90%] max-h-[400px] absolute z-99 p-1 shadow el-table-container">
            <el-table ref="searchRef"  :data="searchPdtData" style="width: 100%;" row-key="id" border
                highlight-current-row @current-change="handleCurrentSelectPdt"
                header-cell-class-name="tableHeader" height="300" @row-click='onRowClick'>
                <el-table-column show-overflow-tooltip fixed prop="name" :label="t('product_manage.id')" width="130" />
                <el-table-column show-overflow-tooltip fixed prop="nick" :label="t('product_manage.name')"  width="600"/>
                <el-table-column show-overflow-tooltip fixed prop="brand" :label="t('product_manage.brand')"  />                
                <el-table-column show-overflow-tooltip fixed prop="specs_text" :label="t('product_manage.specify_info')"  />
                <el-table-column show-overflow-tooltip fixed prop="nick_brif" :label="t('product_manage.short_name')"  />
                <el-table-column show-overflow-tooltip fixed prop="note" :label="t('product_manage.help_name')"  />
            </el-table>
        </div> 

    <!-- 显示合计 -->
    <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">入库单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="putinData.note" clearable :autosize="{ minRows: 10, maxRows: 4 }"     type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>

    </div>

        
        




        <!-- <div class="mb-60"></div> -->

        <!-- 选择质检员 -->
        <DialogUser :param="''" v-model:show="showSelPutinUserDlg" :title="t('msg.selectUser')" @on-submit="onSelPutinCallback"/>
        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="putinData.fsm_log_list" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}
//扩展文字
.ex_text{
  font-size: 11px;
  color: #646464;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
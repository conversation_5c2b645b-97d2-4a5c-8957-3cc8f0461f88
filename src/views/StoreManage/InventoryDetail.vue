<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import type { FormInstance } from 'element-plus'
import {checkFormRule} from '@/api/tool'
import { getInventoryDetailListApi  } from '@/api/product'
import { getTodayDate } from '@/api/tool'
import { DialogSelReceiptPdt } from '@/components/DialogSelReceiptPdt'


const { currentRoute,back,push } = useRouter()
const { t } = useI18n()

//标题
const title = ref('')

//定义搜索条件
const defaultCondition = {
    pdt_id:'',
    page: 1,
    count: 100
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//库存明细列表
const detailData = reactive([])


//查询库存明细列表
const getInventoryDetailList = async (page = 1) => {
  searchCondition.page = page
  const ret = await getInventoryDetailListApi(searchCondition)
  if(ret)
  {
    detailData.splice(0,detailData.length, ...ret.data)
    console.log(detailData)
  }
}

//切换流水页面
const onShowStatement = (item)=>{
  push({
      path: '/inventorymanage/inventorystatement',
      query:{
        pdt_id:currentRoute.value.query.pdt_id,
        pdt_name:item.pdt_name,
        pdt_nick:item.pdt_nick,
        store_id:item.store_id
      }
    })
}

onMounted(async () => {    
    searchCondition.pdt_id = currentRoute.value.query.pdt_id as string
    title.value = '['+currentRoute.value.query.pdt_name+']'+currentRoute.value.query.pdt_nick+' 库存分布'
    getInventoryDetailList()
})

</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <el-table ref="tableRef" :data="detailData" style="width: 100%; margin-bottom: 20px" row-key="id" border stripe
          highlight-current-row header-cell-class-name="tableHeader">
          <el-table-column show-overflow-tooltip align="center"  prop="store_name" :label="t('store.name')" />
          <el-table-column show-overflow-tooltip align="center"  prop="store_nick" :label="t('store.nick')" />
          <el-table-column show-overflow-tooltip align="center"  prop="库存数量" :label="t('inventory.cur_count')" />
          <el-table-column show-overflow-tooltip align="center"  prop="锁定数量" :label="t('inventory.lock_count')" />
          <el-table-column show-overflow-tooltip align="center"  prop="pdt_base_unit" :label="t('product_manage.b_unit')" />
          <el-table-column show-overflow-tooltip align="center"  prop="avg_price_bef_tax" :label="t('inventory.price_avr_bef_tax')" />
          <el-table-column show-overflow-tooltip align="center"  prop="avg_price_aft_tax" :label="t('inventory.price_avr_aft_tax')" />
          <el-table-column show-overflow-tooltip align="center"  :label="t('inventory.price_bef_tax')" >
            <template #default="scope">
              {{ (scope.row.库存数量*scope.row.avg_price_bef_tax).toFixed(2).replace(/\.?0+$/, '') }}              
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"  :label="t('inventory.price_aft_tax')" >
            <template #default="scope">
              {{ (scope.row.库存数量*scope.row.avg_price_aft_tax).toFixed(2).replace(/\.?0+$/, '') }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"  prop="modify_date" :label="t('inventory.last_date')" min-width="140"/>
    
          <el-table-column show-overflow-tooltip fixed="right" align="center" width="100px" prop="name" :label="t('roleTable.opt')" >
            <template #default="scope">
              <ElButton type="primary" size="small" @click="onShowStatement(scope.row)">{{ t('inventory.detail_list') }}</ElButton>
            </template>
          </el-table-column>
        </el-table>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}



</style>
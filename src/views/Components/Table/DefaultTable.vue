<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { Table } from '@/components/Table'
import { getTableListApi } from '@/api/table'
import { TableData } from '@/api/table/types'
import { ref, h } from 'vue'
import { ElTag, ElButton } from 'element-plus'
import { TableColumn, TableSlotDefault } from '@/types/table'

interface Params {
  pageIndex?: number
  pageSize?: number
}

const { t } = useI18n()

const columns: TableColumn[] = [
  {
    field: 'index',
    label: t('tableDemo.index'),
    type: 'index'
  },
  {
    field: 'title',
    label: t('tableDemo.title')
  },
  {
    field: 'author',
    label: t('tableDemo.author')
  },
  {
    field: 'display_time',
    label: t('tableDemo.displayTime'),
    sortable: true
  },
  {
    field: 'importance',
    label: t('tableDemo.importance'),
    formatter: (_: Recordable, __: TableColumn, cellValue: number) => {
      return h(
        ElTag,
        {
          type: cellValue === 1 ? 'success' : cellValue === 2 ? 'warning' : 'danger'
        },
        () =>
          cellValue === 1
            ? t('tableDemo.important')
            : cellValue === 2
            ? t('tableDemo.good')
            : t('tableDemo.commonly')
      )
    }
  },
  {
    field: 'pageviews',
    label: t('tableDemo.pageviews')
  },
  {
    field: 'action',
    label: t('tableDemo.action')
  }
]

const loading = ref(true)

let tableDataList = ref<TableData[]>([])

const getTableList = async (params?: Params) => {
  const res = await getTableListApi(
    params || {
      pageIndex: 1,
      pageSize: 10
    }
  )
    .catch(() => {})
    .finally(() => {
      loading.value = false
    })
  if (res) {
    tableDataList.value = res.data.list
  }
}

getTableList()

const actionFn = (data: TableSlotDefault) => {
  console.log(data)
}

const rightClick = (row,column,event)=>{
  let menu = document.querySelector("#menu");
  if(menu == undefined)
  {
    return
  }
  //阻止元素发生默认的行为
  event.preventDefault();
  // 根据事件对象中鼠标点击的位置，进行定位
  menu.style.left = event.clientX - 200 + "px"; 
  menu.style.top = event.clientY - 200 + "px";
  // 改变自定义菜单的隐藏与显示
  menu.style.display = "block";
  menu.style.zIndex = 1000;
}
const  menus = [
     { name: '编辑webshell', operType: 1 },
     { name: '删除webshell', operType: 2 },
     { name: '虚拟终端', operType: 3 },
     { name: '文件管理', operType: 4 }
   ]

const clickTableRow = (row, column, event)=> {
  let menu = document.querySelector("#menu");
  menu.style.display = "none";
}
</script>

<template>
  <ContentWrap :title="t('tableDemo.table')" :message="t('tableDemo.tableDes')" @row-click="clickTableRow">
    <Table
      :columns="columns"
      :data="tableDataList"
      :loading="loading"
      :defaultSort="{ prop: 'display_time', order: 'descending' }"
      @row-contextmenu="rightClick"
      @row-click="clickTableRow"
    >
      <template #action="data">
        <ElButton type="primary" @click="actionFn(data as TableSlotDefault)">
          {{ t('tableDemo.action') }}
        </ElButton>
      </template>
    </Table>
    <!-- 右键菜单 -->
    <div id="menu" class="menuDiv">
      <ul class="menuUl">
        <li
            v-for="(item, index) in menus"
            :key="index"
            @click.stop="infoClick(index)"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
  </ContentWrap>
</template>

<style lang="less">
.menuDiv {
  display: none;
  position: absolute;

  .menuUl {
    height: auto;
    width: auto;
    font-size: 14px;
    text-align: left;
    border-radius: 3px;
    border: none;
    background-color: #c4c4c4;
    color: #fff;
    list-style: none;
    padding: 0 10px;

    li {
      width: 140px;
      height: 35px;
      line-height: 35px;
      cursor: pointer;
      border-bottom: 1px solid rgba(255, 255, 255, 0.47);

      &:hover {
        // background-color: rgb(26, 117, 158);
        color: rgb(54, 138, 175);
      }
    }
  }
}
</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { checkPermissionApi, closeOneTagByName, closeOneTagByPath } from '@/api/tool';
import { getBuyBayBillTJApi, getSellBayBillTJApi } from '@/api/tj';


const { currentRoute, back, push } = useRouter()
const { t } = useI18n()


//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    page: 1,
    count: 200
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//对账单数据源
const checkData = reactive([])
//当前选中行
const currentRow = ref(null)


const getBuyBayBillTJ = async () => {
    const ret = await getBuyBayBillTJApi(searchCondition)
    if(ret)
    {
        console.log(ret)
        checkData.splice(0,checkData.length,...ret.data)
        totleCount.value =  parseInt(ret.count)
    }
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getBuyBayBillTJ()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getBuyBayBillTJ()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getBuyBayBillTJ()
}

//处理表格对象操作
const handleOper = (type, row) => {
    if (type == 'info') {
        const tmp = currentRoute.value.name?.indexOf('BBB')>=0?'/bbmonthlycheck/bbbuymonthlycheck':'/monthlycheck/buymonthlycheck'
 
        closeOneTagByPath(tmp)
        push({
            path: tmp,
            query: {
                month: row.月份,
            }
        })
    }
    
}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}

onMounted(() => {
    //刷新表格
    getBuyBayBillTJ()
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if(['金额'].includes(column.property))
        {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}
</script>

<template>
  <div ref="rootRef">
      <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">
        <div class="text-center pt-3 mb-5 font-bold" style="color:#333">销售月结统计表</div>
        
        <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="checkData"
          style="width: 100%;color: #666666;" show-summary :summary-method="getSummaries"  border stripe>

          <el-table-column align="center" show-overflow-tooltip prop="月份" :label="'月份'" />
          <el-table-column align="center" show-overflow-tooltip prop="月结个数" :label="'月结个数'" />
          <el-table-column align="center" show-overflow-tooltip prop="金额" :label="'金额'" />
         
          
          <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
            <template #default="scope">
                <ElButton type="primary" size="small" @click="handleOper('info', scope.row)">详情</ElButton>
            </template>
          </el-table-column>
        </el-table>
        <!-- <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" /> -->
      </div>
  </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  color: #00BA80;
  cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.searchItem {
  width: 150px !important;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper)
{
  width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}

</style>

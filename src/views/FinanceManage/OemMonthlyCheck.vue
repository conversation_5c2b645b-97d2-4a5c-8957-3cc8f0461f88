<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillOemListApi, delPayBillOemApi, updatePayBillOemApi } from '@/api/product'
import { closeOneTagByPath, downloadFile } from '@/api/tool';
import { cloneDeep, isNumber } from 'lodash-es';
import { DialogOemOutMoney } from '@/components/DialogOemOutMoney'
import { exportOemReportMonthApi, exportOemReportMonthInfoApi } from '@/api/extra';

const { push,currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  受托方编号:'',
  受托方名称:'',
  月份:'',
  付款状态:'',
  对账状态:'',
  已付金额0:'',
  已付金额1:'',
  已收发票0:'',
  已收发票1:'',
  开票状态:'',
  付款日期:['',''],
  业务日期:['',''],
  page: 1,
  count: 20
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//对账单数据源
const checkData = reactive([])
//当前选中行
const currentRow = ref(null)

const loading = ref(false)
//查询受托方数据
const getOemMonthlyCheckListData = async () => {
  let tmp = cloneDeep(searchCondition)
  delete tmp.已付金额0
  delete tmp.已付金额1
  delete tmp.已收发票0
  delete tmp.已收发票1

  tmp.已付金额 = searchCondition.已付金额0+','+searchCondition.已付金额1
  tmp.已收发票 = searchCondition.已收发票0+','+searchCondition.已收发票1
  tmp.付款日期 = searchCondition.付款日期[0]+','+searchCondition.付款日期[1]
  tmp.业务日期 = searchCondition.业务日期[0] + ',' + searchCondition.业务日期[1]
  loading.value = true
  const ret = await getPayBillOemListApi(tmp)
  if(ret)
  {
    checkData.splice(0,checkData.length,...ret.data)
    totleCount.value =  parseInt(ret.count)
  }
  loading.value = false
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getOemMonthlyCheckListData()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getOemMonthlyCheckListData()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getOemMonthlyCheckListData()
}
//创建新受托方月结
const onAddCheck = () => {
  const tmp = currentRoute.value.name?.indexOf('OOO')>=0?'/oomonthlycheck/oooemmonthlycuslist':'/monthlycheck/oemmonthlycuslist'
 
    push({
      path: tmp,
      query:{
        id:''
      }
    })
}

//处理表格对象操作
const handleOper = (type, row) => {
  if (type === 'edit' || type == 'info') {
    const tmp = currentRoute.value.name?.indexOf('OOO')>=0?'/oomonthlycheck/oooemmonthlycheckinfo':'/monthlycheck/oemmonthlycheckinfo'
 
    closeOneTagByPath(tmp)
    push({
      path: tmp,
      query: {
        id: row.id,
        type: type
      }
    })
  }
  else if (type === 'del') {

    if(row.已付金额>0)
    {
      ElMessage.error('该对账单已付款，不能删除！')
      return
    }

    ElMessageBox.confirm(
      '确定是否删除该对账单？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        const ret = await delPayBillOemApi({ 
          "ids": [row.id] ,
          fsm_exe_trig : '删除'
        })
        if (ret) {
            getOemMonthlyCheckListData()

          ElMessage({
            type: 'success',
            message: t('msg.delOK'),
          })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })
  }
  else if(type === '修改备注')
  {
    ElMessageBox.prompt(
      '请输入备注',
      '修改备注',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }
    )
      .then(async({ value }) => {
        console.log('111111')
        //修改备注
        row.fsm_exe_trig = '保存'
        row.note = value
        const ret = await updatePayBillOemApi(row)
        if (ret) {
          getOemMonthlyCheckListData()
          ElMessage({
            type: 'success',
            message: '更新成功',
          })
        }

      })
      .catch(() => {

      })
  }
  else if(type === '更改发票金额')
  {
    ElMessageBox.prompt(
      '请输入金额,原始金额:'+row.应收发票,
      '修改金额',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[0-9]*$/,
        inputErrorMessage: '请输入有效的数字',
      }
    )
      .then(async ({ value }) => {
        if(value == null || value == '')
        {
          ElMessage.error('请输入有效的数字')
          return
        }
        
        //修改发票金额
        row.fsm_exe_trig = '保存'
        row.need_fp_fee = value
        const ret = await updatePayBillOemApi(row)
        if (ret) {
          getOemMonthlyCheckListData()
          ElMessage({
            type: 'success',
            message: '更新成功',
          })
        }

      })
      .catch(() => {

      })
  }
  else if(type == '付款申请')
  {
    let one = {
          "供应商": row.parter_nick,
          "关联类型": "委外月结单",
          "关联单号": row.oem_paybill_num,
          "月份": row.paybill_date,
          "申请金额": row.应付金额-row.已付金额
    }
        
    push({
      path: '/oomonthlycheck/oempaymentapplicationadd',
      query:{
        id: '',
        type: '委外',
        num:row.oem_paybill_num,
        parter_id: row.parter_id,
        param:JSON.stringify(one)
      }
    })
  }
  else if(type == '申请明细')
  {
    push({
      path: '/oomonthlycheck/oempaymentapplication',
      query: {
        type: '委外月结单',
        num:row.oem_paybill_num
      }
    })
  }
  else if(type == '导出excel')
  {
     //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前月结单？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true

    const ret = await exportOemReportMonthInfoApi({
        id: row.id
    })
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

  }
}
//设置当前选中行
const setCurrentRow = (value) => {
  currentRow.value = value
}


onMounted(() => {
  if(currentRoute.value.query.month != undefined)
  {
    searchCondition.月份 = currentRoute.value.query.month as string
  }

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  //刷新表格
  getOemMonthlyCheckListData()
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if(['应付金额','已付金额', '应收发票','已收发票'].includes(column.property))
        {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}

const curCusID = ref('')
const curCusNick = ref('')
const curMoneyType = ref('')
const showGetMoney = ref(false)
const onShowGetMoney = (row)=>{
  showGetMoney.value = true
  curCusID.value = row.parter_id
  curCusNick.value = row.parter_nick+'('+row.币种+')'
  curMoneyType.value = row.币种
}




const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    delete tmp.已付金额0
    delete tmp.已付金额1
    delete tmp.已收发票0
    delete tmp.已收发票1

    tmp.已付金额 = searchCondition.已付金额0+','+searchCondition.已付金额1
    tmp.已收发票 = searchCondition.已收发票0+','+searchCondition.已收发票1
    tmp.付款日期 = searchCondition.付款日期[0]+','+searchCondition.付款日期[1]
    tmp.业务日期 = searchCondition.业务日期[0]+','+searchCondition.业务日期[1]
    const ret = await exportOemReportMonthApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}
</script>

<template>
  <div ref="rootRef">
      <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">
        <div class="absolute top-5 left-8">
          <ElButton type="success" @click="onAddCheck">
            <Icon icon="fluent-mdl2:people-add" />
            <div class="pl-2">新增对账单</div>
          </ElButton>
          <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
              <Icon icon="carbon:export" />
              <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton>
        </div>
        <div class="text-center mb-5 font-bold" style="color:#333">委外月结对账单</div>
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">受托方编号</div>
            <el-input size="small" v-model="searchCondition.受托方编号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">受托方名称</div>
            <el-input size="small" v-model="searchCondition.受托方名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">月份</div>
            <!-- <el-date-picker size="small" v-model="searchCondition.月份" type="date" placeholder="选择月份" class="searchItem"/> -->
            <el-input size="small" v-model="searchCondition.月份" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">付款状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.付款状态" placeholder="" >
              <el-option v-for="item in ['未付款','部分付款','完全付款','未付款+部分付款']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">对账状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.对账状态" placeholder="" >
              <el-option v-for="item in ['账单生成中','对账中','已对账']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">已付金额</div>
            <el-input size="small"  v-model="searchCondition.已付金额0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.已付金额1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">已收发票</div>
            <el-input size="small"  v-model="searchCondition.已收发票0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.已收发票1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">开票状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.开票状态" placeholder="" >
              <el-option v-for="item in ['未开票','部分开票','完全开票','未开票+部分开票']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">付款日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.付款日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">业务日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.业务日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>


          <div class="flex justify-end items-center mr-6 mt-4">
            <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" />
            <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
              <Icon icon="tabler:search" />
              <div class="ml-1">查询</div>
            </ElButton>
            <ElButton class="ml-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="ml-1">清除</div>
            </ElButton>
          </div>
        </div>

        <el-table v-loading.lock="loading" ref="userTableRef11" header-cell-class-name="tableHeader" :data="checkData"
          style="width: 100%;color: #666666;" show-summary :summary-method="getSummaries" @current-change="setCurrentRow" border stripe>
          <el-table-column align="center" show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60" >
            <template #default="scope">
              {{ scope.$index+1 }}
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="parter_name" :label="'受托方编号'" />
          <el-table-column align="center" show-overflow-tooltip prop="parter_nick" :label="'受托方名称'" />
          <el-table-column align="center" show-overflow-tooltip prop="paybill_date" :label="'月份'" />
          <el-table-column align="center" show-overflow-tooltip prop="币种" :label="'币种'" />
          <el-table-column align="center"  show-overflow-tooltip prop="应付金额" :label="'应付金额'" />
          <!-- <el-table-column align="center" show-overflow-tooltip prop="预存款金额" :label="'预存款'" /> -->
          <el-table-column align="center" show-overflow-tooltip prop="已付金额" :label="'已付金额'" />
          <el-table-column align="center" show-overflow-tooltip prop="应收发票" :label="'应收发票'" />
          <el-table-column align="center" show-overflow-tooltip prop="已收发票" :label="'已收发票'" />
          <el-table-column align="center" show-overflow-tooltip prop="fsm_cur_state" :label="'状态'" />
          <el-table-column align="center" show-overflow-tooltip prop="create_man_name" :label="'创建人'" />
          <el-table-column align="center" show-overflow-tooltip prop="prepaid_date" :label="'预付款日期'" />
          <el-table-column align="center" show-overflow-tooltip prop="note" :label="'备注'" />
          
          <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
            <template #default="scope">
              <el-dropdown trigger="click" placement="bottom">
                <span class="el-dropdown-link">
                  <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                </span>
                <template #dropdown>
                  <div class="flex flex-wrap w-[200px]">
                    <el-dropdown-item @click="handleOper('info', scope.row)">查看</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('导出excel', scope.row)">导出excel</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('发票', scope.row)">发票({{ 0 }})</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('更改发票金额', scope.row)">更改发票金额</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('修改备注', scope.row)">修改备注</el-dropdown-item>
                    <el-dropdown-item @click="onShowGetMoney(scope.row)">账户付款</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('付款申请',scope.row)">付款申请</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('申请明细',scope.row)">申请明细({{ scope.row.book_payment_list.length }})</el-dropdown-item>
                    
                    <!-- <el-dropdown-item @click="handleOper('get_mo预存款抵扣ney_from_pre', scope.row)">预存款抵扣</el-dropdown-item> -->
                    <el-dropdown-item v-if='scope.row.已付金额==0' @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                  </div>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>

    <DialogOemOutMoney v-model:show="showGetMoney" :cus_id="curCusID" :cus_nick="curCusNick" :money_type="curMoneyType" @on-submit="getOemMonthlyCheckListData"/>
  </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  color: #00BA80;
  cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.searchItem {
  width: 150px !important;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper)
{
  width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}

</style>

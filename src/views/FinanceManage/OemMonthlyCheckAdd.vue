<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElInputNumber, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted,nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillOemListApi, getOemPayBillListDrawinApi, getPayBillOemNewnumApi, addPayBillOemApi, getPayBillOemInfoApi, updatePayBillOemApi, updateOemDrawinPdtApi, updateOemCancelPdtApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, closeOneTagByName, closeOneTagByPath, getLastDayOfMonth, getCurMonth, ceilToFixed } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { ContentDetailWrap } from '@/components/ContentDetailWrap'


const { currentRoute, back, push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    收货日期: ['',''],
    对账状态: '未对账+部分对账',
    收货单号: '',
    退货单号:'',
    委外单号: '',
    受托方产品编码: '',
    收货状态: '',
    结算币种: '',
    产品编码: '',
    产品名称: '',
    产品规格: '',
    税前单价0: '',
    税前单价1: '',
    page: 1,
    count: 50
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//对账单数据源
const checkData = reactive({
    buyer_id: '',
    oem_paybill_num: '',
    prepaid_date: '',
    paybill_date: '',
    paybill_man_id: '',
    pdt_list: [],
    note: ''
})

//对账单数据源
const takeoutData = reactive([])
//当前选中行
const currentRow = ref(null)

const isRefreshing = ref(false)

//查询受托方数据
const getPayBillListDrawin = async () => {
    isRefreshing.value = true // 开始刷新

    let tmp = cloneDeep(searchCondition)
    delete tmp.税前单价0
    delete tmp.税前单价1

    tmp.受托方ID = currentRoute.value.query.cus_id
    tmp.税前单价 = searchCondition.税前单价0 + ',' + searchCondition.税前单价0
    tmp.收货日期 = searchCondition.收货日期[0] + ',' + searchCondition.收货日期[1]
    // tmp.收货日期 = ','
    loading.value = true
    const ret = await getOemPayBillListDrawinApi(tmp)
    if (ret) {
        console.log(ret)
        takeoutData.splice(0, takeoutData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
        //更新所有价格
        for (const one of takeoutData) {
            if (modifiedRows[(one.收货单号+one.标识)]) {
                Object.assign(one, modifiedRows[(one.收货单号 + one.标识)])
                console.log('还原', modifiedRows[(one.出库单号 + one.标识)])
            }
            //特殊处理退货相关数据
            if (one.类型.indexOf('退货') >= 0 && one.对账数量>0) {
                one.收货数量 = one.收货数量*-1
                one.对账数量 = one.对账数量*-1
                one.已对账 = one.已对账*-1
                one.未对账 = one.未对账*-1
                one.本次对账 = one.本次对账 * -1
                one.含税总价 = one.含税总价 * -1
                one.不含税总价 = one.不含税总价*-1
            }

            recomputeShow(one, '本次对账',false)
            if (selectedRows.value.includes((one.收货单号+one.标识))) {
                multipleTableRef.value!.toggleRowSelection(one, true)
            }
        }
    }
    loading.value = false
    tableHeight.value = document.getElementById('mainscroll')?.clientHeight - 100

    nextTick(() => {
        isRefreshing.value = false // 刷新完成
    })
    console.log('选中',selectedRows.value)
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getPayBillListDrawin()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getPayBillListDrawin()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getPayBillListDrawin()
}


//处理表格对象操作
const handleOper = (type, row) => {
    if (type === 'edit' || type == 'info') {
 
    }
    else if (type === 'del') {
        ElMessageBox.confirm(
            '确定是否删除该对账单？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {

                // const ret = await delOemerApi({ "ids": [row.id] })
                // if (ret) {
                //     getPayBillListParter()

                //   ElMessage({
                //     type: 'success',
                //     message: t('msg.delOK'),
                //   })
                // }


            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: t('msg.delChannel'),
                })
            })
    }
}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}

const initTime = () => {
    let currentDate = new Date();
    // 计算两个月前的日期并设置为第一天
    let twoMonthsAgo = new Date(currentDate);
    twoMonthsAgo.setMonth(currentDate.getMonth() - 2);
    twoMonthsAgo.setDate(1);

    // 格式化两个月前的日期
    let year = twoMonthsAgo.getFullYear();
    let month = String(twoMonthsAgo.getMonth() + 1).padStart(2, '0');
    let day = String(twoMonthsAgo.getDate()).padStart(2, '0');

    let twoMonthsAgoFormatted = `${year}-${month}-${day}`;

    // 格式化当前日期
    year = currentDate.getFullYear();
    month = String(currentDate.getMonth() + 1).padStart(2, '0');
    day = String(currentDate.getDate()).padStart(2, '0');

    let formattedDate = `${year}-${month}-${day}`;
    if (currentRoute.value.query.date != undefined) {
        let tmp = currentRoute.value.query.date.split(',')
        defaultCondition.收货日期 = []
        defaultCondition.收货日期.push(tmp[0])
        defaultCondition.收货日期.push(tmp[1])
    }
    else {
        defaultCondition.收货日期 = []
        defaultCondition.收货日期.push(twoMonthsAgoFormatted);  // 添加两个月前的第一天
        defaultCondition.收货日期.push(formattedDate);          // 添加今天的日期
    }

    searchCondition.reset()
}


onMounted(async () => {
    initTime()
    if (currentRoute.value.query.id == undefined) {
        onChangeID()
        checkData.prepaid_date = getLastDayOfMonth()
        checkData.paybill_date = getCurMonth()
        checkData.parter_id = currentRoute.value.query.cus_id
    }
    else {
        const ret = await getPayBillOemInfoApi({
            id: currentRoute.value.query.id
        })
        if (ret) {
            Object.assign(checkData, ret.data)
        }
    }



    getOemMonthlyCheckListData()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    //刷新表格
    getPayBillListDrawin()
    tableHeight.value = document.getElementById('mainscroll')?.clientHeight-100
})

const baskFront = () => {
    back()
    closeOneTagByName(currentRoute.value.meta.title)
    // //刷新上一页
    // closeOneTagByPath('/salemanage/salemanage')
}

//当前选中条数
const curSelCount = ref(0)

//缓存最后修改以及最后勾选
const modifiedRows = reactive<Record<string, any>>({})
const selectedRows = ref<any[]>([])

//实时更新数据
const recomputeShow = (row, type, modify = true) => {
    console.log('1111')
    row.本次对账 = row.本次对账 == '' ? 0 : parseFloat(row.本次对账)
    row.含税总价 = row.含税总价 == '' ? 0 : parseFloat(row.含税总价)
    row.不含税总价 = row.不含税总价 == '' ? 0 : parseFloat(row.不含税总价)

    if (type == '本次对账') {      
        if (row.类型.indexOf('退货') >= 0) {
            if (Math.abs(row.本次对账)>Math.abs(row.未对账)) {
                row.本次对账 = row.未对账
            }
            if( row.本次对账 > 0) {
                row.本次对账 = row.本次对账 * -1
            }
        }
        else {
            if (row.本次对账 > row.未对账) {
                row.本次对账 = row.未对账
            }
            if (row.本次对账 < 0) {
                row.本次对账 = 0
            }
        }
  
        row.含税总价 = ceilToFixed((row.本次对账 * row.含税单价 * row.折扣),6,row.含税总价)
        row.不含税总价 = ceilToFixed((row.本次对账 * row.不含税单价 * row.折扣),6,row.不含税总价)
        if (row.本次对账 == 0) {
            multipleTableRef.value!.toggleRowSelection(row, false)
        }
    }
    else if (type == '含税单价') {
        row.不含税单价 = ceilToFixed((row.含税单价 / (1 + row.税率 / 100)),8,row.不含税单价)
        row.含税总价 = ceilToFixed((row.含税单价 * row.本次对账),6,row.含税总价)
        row.不含税总价 = ceilToFixed((row.不含税单价 * row.本次对账),6,row.不含税总价)
    }
    else if (type == '不含税单价') {
        row.含税单价 = ceilToFixed(parseFloat(row.不含税单价) + parseFloat(row.不含税单价) * row.税率 / 100,8,row.含税单价) //true
        row.含税总价 = ceilToFixed((row.含税单价 * row.本次对账),6,row.含税总价)
        row.不含税总价 = ceilToFixed((row.不含税单价 * row.本次对账),6,row.不含税总价)
    }

    // 保存修改的行数据
    if(modify)
        modifiedRows[(row.收货单号+row.标识)] = cloneDeep(row)
}
//所有当前选择
const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const arraySel = reactive([])
const handleSelectionChange = (value) => {
    // 获取当前选中的所有行的收货单号
    const currentSelectedIds = value.map(row => (row.收货单号+row.标识));

    // 如果不是刷新导致的变化
    if (!isRefreshing.value) {
        // 遍历当前选中的行
        currentSelectedIds.forEach(单号 => {
            // 如果 id 不在 selectedRows 中，则添加
            if (!selectedRows.value.includes(单号)) {
                selectedRows.value.push(单号);
            }
        });

        // 遍历 selectedRows
        selectedRows.value = selectedRows.value.filter(单号 => {
            // 仅当单号存在于 takeoutData 中且不在当前选中的行中时，才从 selectedRows 中移除
            const existsInTakeoutData = takeoutData.some(row => (row.收货单号+row.标识) === 单号);
            const notInCurrentSelected = !currentSelectedIds.includes(单号);
            return !(existsInTakeoutData && notInCurrentSelected);
        });
    }


    arraySel.splice(0, arraySel.length, ...value)

    //如果arraysel某一行对账状态为未对账则取消multipleTableRef对应行的选中标志
    for (const one of arraySel) {
        if (one.对账状态 == '已对账' || one.本次对账 == 0) {
            multipleTableRef.value!.toggleRowSelection(one, false)
        }
    }
    curSelCount.value = arraySel.length
}

//获取最新ID
const onChangeID = async () => {
    const ret = await getPayBillOemNewnumApi()
    if (ret) {
        console.log(ret)
        checkData.oem_paybill_num = ret.data.new_id
    }
}

//创建对账
const onCreateCheck = async () => {
    const arrTmp = arraySel.filter(item => item.本次对账 != 0)
    
    if (arrTmp.length == 0 && currentRoute.value.query.id == undefined) {
        ElMessage({
            type: 'warning',
            message: '请先选择需要对账的单据'
        })
        return
    }

    //处理一下所有退货单为正数
    for (const one of arrTmp) {
        if (one.类型.indexOf('退货')>=0) {
            one.收货数量 = Math.abs(one.收货数量)
            one.对账数量 = Math.abs(one.对账数量)
            one.已对账 = Math.abs(one.已对账)
            one.未对账 = Math.abs(one.未对账)
            one.本次对账 = Math.abs(one.本次对账)
            one.含税总价 = Math.abs(one.含税总价)
            one.不含税总价 = Math.abs(one.不含税总价)
        }
    }


    //新增
    if(currentRoute.value.query.id == undefined)
    {
        checkData.pdt_list = [...arrTmp]
        console.log(checkData)
        checkData.fsm_exe_trig = '保存'
        const ret = await addPayBillOemApi(checkData)
        if (ret) {
            const tmp = currentRoute.value.name?.indexOf('OOO')>=0?'/oomonthlycheck/oooemmonthlycheck':'/monthlycheck/oemmonthlycheck'
            closeOneTagByPath('/oomonthlycheck/oooemmonthlycuslist')
            closeOneTagByPath('/monthlycheck/oemmonthlycuslist')
            closeOneTagByName(currentRoute.value.meta.title)
            push({
                path: tmp
            })
        }
    }
    else //修改
    {
        checkData.pdt_list.push(...arrTmp)
        console.log(checkData)
        checkData.fsm_exe_trig = '保存'
        const ret = await updatePayBillOemApi(checkData)
        if (ret) {
            closeOneTagByName(currentRoute.value.meta.title)
            closeOneTagByPath('/monthlycheck/oemmonthlycheckinfo')
            back()
        }
    }

   
}

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if (['收货数量', '含税总价', '不含税总价'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return ceilToFixed((prev + curr),6,0);
                    } else {
                        return ceilToFixed((prev),6,0);
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}

//历史对账单数据
const hisDate = reactive([])
//查询受托方数据
const getOemMonthlyCheckListData = async () => {
    const ret = await getPayBillOemListApi({
        受托方编号: currentRoute.value.query.cus_id,
        page: 1,
        count: 10000
    })
    if (ret) {
        hisDate.splice(0, hisDate.length, ...ret.data)
    }
}

const loading = ref(false)
const tableHeight = ref(600)


const onModifyItem = (row) => {
    ElMessageBox.prompt('请输入对账总数', '修改对账总数',
      {
        confirmButtonText: t('button.ok'),
        cancelButtonText: t('button.cancel'),
        inputErrorMessage: '请输入对账总数',
        inputValidator: (val) => {
          if (val === null ) {
            return false;
          }
        }
      })
        .then(async ({ value }) => {
            let param = {
                pdt_param: {
                对账数量:  Math.abs(value),
              },                  


              pdt_biaoshi:row.标识
            }
            let ret;
            if(row.类型 == '委外收货')
            {
                param.drawin_num = row.收货单号
                ret = await updateOemDrawinPdtApi(param)
            }
            else {
                param.cancel_num = row.收货单号
                ret = await updateOemCancelPdtApi(param)
            }
          
            if(ret)
            {
                ElMessage({
                    type: 'success',
                    message: '修改成功'
                })
                delete modifiedRows[row.收货单号+row.标识]
                getPayBillListDrawin()
            }
      })
}
const tableRowClassName = (row, index) => {
  if(row.row.类型.indexOf('退货')>=0)
  {
    console.log('tuihuo !!!!!')
      return 'error-row'
  }
}
</script>

<template>
    <ContentDetailWrap :title="currentRoute.query.cus_name + ' 收货月结选择'" @back="baskFront()">
        <div class="h-[100%] bg-white p-1 relative w-[100%] !bg-white flex-grow " style="color:#666666">
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-1 pb-1 mb-2 pl-12 bg-light-200">

                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">对账状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.对账状态" placeholder="">
                        <el-option v-for="item in ['未对账+部分对账', '未对账', '部分对账', '完全对账', '超量对账']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">收货单号</div>
                    <el-input size="small" v-model="searchCondition.收货单号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">退货单号</div>
                    <el-input size="small" v-model="searchCondition.退货单号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">委外单号</div>
                    <el-input size="small" v-model="searchCondition.委外单号" placeholder="" class="searchItem" />
                </div>
                <!-- <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">受托方订单号</div>
                    <el-input size="small" v-model="searchCondition.受托方订单号" placeholder="" class="searchItem" />
                </div> -->
                <!-- <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">受托方产品编码</div>
                    <el-input size="small" v-model="searchCondition.受托方产品编码" placeholder="" class="searchItem" />
                </div> -->
                <!-- <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">收货状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.收货状态" placeholder="">
                        <el-option v-for="item in ['未收货', '部分收货', '完全收货', '超量收货']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div> -->
                <!-- <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">结算币种</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.结算币种" placeholder="">
                        <el-option v-for="item in moneyTypeData" :key="item" :label="item" :value="item" />
                    </el-select>
                </div> -->
                <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">产品编码</div>
                    <el-input size="small" v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
                </div>
                <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">产品名称</div>
                    <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
                </div>
                <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">产品规格</div>
                    <el-input size="small" v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
                </div>
                <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">税前单价</div>
                    <el-input size="small" v-model="searchCondition.税前单价0" placeholder="" class="!w-[60px]"
                        type="number" />
                    <div class="searchTitle !w-32px">到</div>
                    <el-input size="small" v-model="searchCondition.税前单价1" placeholder="" class="!w-[60px]"
                        type="number" />
                </div>
                <div v-show="senior" class="inline-flex items-center mr-1 mb-1">
                    <div class="searchTitle">收货日期</div>
                    <el-date-picker :clearable="false" size="small" class="searchItem" v-model="searchCondition.收货日期" type="daterange"
                        range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
                </div>


                <div class="flex justify-end items-center mr-6 mt-4">
                    <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" />
                    <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="ml-1">查询</div>
                    </ElButton>
                    <ElButton class="ml-4" type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="ml-1">清除</div>
                    </ElButton>
                </div>
            </div>
            <div class=" text-red-600 "> {{ '[已选中 ' + curSelCount + ' 个]' }}</div>
            <el-table scrollbar-always-on v-loading="loading" :height="tableHeight" ref="multipleTableRef" header-cell-class-name="tableHeader" :data="takeoutData"
                style="width: 100%;color: #666666;" border stripe show-summary :summary-method="getSummaries" :row-class-name="tableRowClassName"
                @selection-change="handleSelectionChange">
                <el-table-column align="center" type="selection" width="40" />
                <el-table-column align="center" show-overflow-tooltip prop="序号" :label="'序号'" width="40" fixed />
                <el-table-column align="center" show-overflow-tooltip prop="类型" :label="'类型'" width="55" />
                <el-table-column align="center" show-overflow-tooltip prop="对账状态" :label="'对账状态'" width="58" />
                <el-table-column align="center" show-overflow-tooltip prop="收货单号" :label="'收/退货单号'" />
                <el-table-column align="center" show-overflow-tooltip prop="收货时间" :label="'收/退时间'" />
                <el-table-column align="center" show-overflow-tooltip prop="委外订单号" :label="'委外单号'" width="100"/>
                <!-- <el-table-column align="center" show-overflow-tooltip prop="受托方订单号" :label="'受托方订单号'" /> -->
                <!-- <el-table-column align="center" show-overflow-tooltip prop="收货状态" :label="'收货状态'" width="55" /> -->
                <el-table-column align="center" show-overflow-tooltip prop="产品编码" :label="'产品编码'" width="150"/>
                <el-table-column align="center" show-overflow-tooltip prop="产品名称" :label="'产品名称'" width="150"/>
                <el-table-column align="center" show-overflow-tooltip prop="规格" :label="'规格'" />
                <el-table-column align="center" show-overflow-tooltip prop="收货数量" :label="'总数量'" />
                <el-table-column align="center" show-overflow-tooltip prop="对账数量" :label="'对账数量'" />
                <el-table-column align="center" show-overflow-tooltip prop="已对账" :label="'已对账'" />
                <el-table-column align="center" show-overflow-tooltip prop="未对账" :label="'未对账'" />
                <el-table-column align="center" prop="本次对账" :label="'本次对账'">
                    <template #default="scope">
                        <el-input-number :controls="false" size="small" v-model="scope.row.本次对账"  :min="(scope.row.类型.indexOf('退货')<0)?0:scope.row.本次对账"
                             @blur="recomputeShow(scope.row, '本次对账')" />
                    </template>

                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="单位" :label="'单位'" />
                <el-table-column align="center" show-overflow-tooltip prop="币种" :label="'币种'" />
                <el-table-column align="center" show-overflow-tooltip prop="含税单价" :label="'含税单价'">
                    <template #default="scope">
                        <el-input-number :controls="false" size="small" v-model="scope.row.含税单价" :min="0"
                            @blur="recomputeShow(scope.row, '含税单价')" />
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="不含税单价" :label="'不含税单价'">
                    <template #default="scope">
                        <el-input-number :controls="false" size="small" v-model="scope.row.不含税单价" :min="0"
                            @blur="recomputeShow(scope.row, '不含税单价')" />
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="税率" :label="'税率'">
                    <template #default="scope">
                        {{ scope.row.税率 + '%' }}
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="折扣" :label="'折扣'" />
                <el-table-column align="center" show-overflow-tooltip prop="不含税总价" :label="'不含税总价'" />
                <el-table-column align="center" show-overflow-tooltip prop="含税总价" :label="'含税总价'" />
                <el-table-column align="center" fixed="right" show-overflow-tooltip  :label="'操作'" >
                    <template #default="scope">
                        <ElButton type="danger" size="small" @click="onModifyItem(scope.row)">修改总数</ElButton>
                    </template>               
                </el-table-column>

            </el-table>
            <el-pagination class="justify-end mt-3" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[50, 100, 200, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
            <div class="flex justify-center items-center mt-3">
                <div>
                    预付款日期
                    <el-date-picker  :clearable="false" class="ml-2" v-model="checkData.prepaid_date" type="date" placeholder="选择日期"
                        value-format="YYYY-MM-DD" />
                </div>
                <div class="ml-2">
                    结账月份
                    <!-- <el-date-picker :disabled="currentRoute.query.id != undefined" :clearable="false" class="ml-2" v-model="checkData.paybill_date" type="month" placeholder="选择日期"
                        value-format="YYYY-MM" /> -->
                    <el-input :disabled="currentRoute.query.id != undefined" :clearable="false" v-model="checkData.paybill_date" placeholder="" class="!w-[150px]"/>
                </div>
                <ElButton class="ml-2" type="success" @click="onCreateCheck">提交</ElButton>
            </div>
            <div class="mt-5 font-bold text-2xl text-green-600 text-center">
                历史对账单
            </div>
            <el-table ref="multipleTableRef222" header-cell-class-name="tableHeader" :data="hisDate"
                style="width: 100%;color: #666666;" border stripe>
                <el-table-column align="center" show-overflow-tooltip prop="paybill_date" :label="'月份'" />
                <el-table-column align="center" show-overflow-tooltip prop="币种" :label="'币种'" />
                <el-table-column align="center" show-overflow-tooltip prop="应付金额" :label="'金额'" />
                <el-table-column align="center" show-overflow-tooltip prop="fsm_cur_state" :label="'状态'" />
                <el-table-column align="center" show-overflow-tooltip prop="prepaid_date" :label="'预付款日期'" />
                <el-table-column align="center" show-overflow-tooltip prop="设置" :label="'设置'">
                    <template #default="scope">
                        <!-- <el-dropdown trigger="click" placement="bottom">
                            <span class="el-dropdown-link">
                                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                            </span>
                            <template #dropdown>
                                <div class="flex flex-wrap w-[200px]">
                                    <el-dropdown-item @click="handleOper('info', scope.row)">查看</el-dropdown-item>
                                    <el-dropdown-item
                                        @click="handleOper('设置预付款日期', scope.row)">设置预付款日期</el-dropdown-item>
                                    <el-dropdown-item
                                        @click="handleOper('导出excel', scope.row)">导出excel</el-dropdown-item>
                                </div>
                            </template>
                        </el-dropdown> -->
                    </template>
                </el-table-column>

            </el-table>
        </div>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}

:deep(.el-input-number--small) {
    width: 100%;
}

:deep(.error-row .el-table__cell > div){
//   background-color:#ffc2c2;
color:#F56C6C !important;
}
</style>

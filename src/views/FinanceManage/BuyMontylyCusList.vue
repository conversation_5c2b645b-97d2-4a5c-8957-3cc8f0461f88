<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillListBuyerApi } from '@/api/product'
import {  closeOneTagByName, closeOneTagByPath } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { ContentDetailWrap } from '@/components/ContentDetailWrap'


const { currentRoute, back, push } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    收货日期: [],
    对账状态: '未对账+部分对账',
    供应商:'',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//对账单数据源
const checkData = reactive([])
//当前选中行
const currentRow = ref(null)

const loading = ref(false)
//查询供应商数据
const getPayBillListBuyer = async () => {
    let tmp = cloneDeep(searchCondition)
    tmp.收货日期 = searchCondition.收货日期[0] + ',' + searchCondition.收货日期[1]
    loading.value = true
    const ret = await getPayBillListBuyerApi(tmp)
    if(ret)
    {
        console.log(ret)
        checkData.splice(0,checkData.length,...ret.data)
        totleCount.value =  parseInt(ret.count)
    }
    loading.value = false
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getPayBillListBuyer()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getPayBillListBuyer()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getPayBillListBuyer()
}


//处理表格对象操作
const handleOper = (type, row) => {
    if (type === 'edit' || type == 'info') {

    }
    else if (type === 'del') {
        ElMessageBox.confirm(
            '确定是否删除该对账单？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {

                // const ret = await delBuyerApi({ "ids": [row.id] })
                // if (ret) {
                //     getPayBillListBuyer()

                //   ElMessage({
                //     type: 'success',
                //     message: t('msg.delOK'),
                //   })
                // }


            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: t('msg.delChannel'),
                })
            })
    }
}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}

const initTime =  ()=>{
    let currentDate = new Date();
    // 计算两个月前的日期并设置为第一天
    let twoMonthsAgo = new Date(currentDate);
    twoMonthsAgo.setMonth(currentDate.getMonth() - 2);
    twoMonthsAgo.setDate(1);

    // 格式化两个月前的日期
    let year = twoMonthsAgo.getFullYear();
    let month = String(twoMonthsAgo.getMonth() + 1).padStart(2, '0');
    let day = String(twoMonthsAgo.getDate()).padStart(2, '0');

    let twoMonthsAgoFormatted = `${year}-${month}-${day}`;

    // 格式化当前日期
    year = currentDate.getFullYear();
    month = String(currentDate.getMonth() + 1).padStart(2, '0');
    day = String(currentDate.getDate()).padStart(2, '0');

    let formattedDate = `${year}-${month}-${day}`;
    defaultCondition.收货日期 = []
    defaultCondition.收货日期.push(twoMonthsAgoFormatted);  // 添加两个月前的第一天
    defaultCondition.收货日期.push(formattedDate);          // 添加今天的日期

    searchCondition.reset()
}


onMounted(() => {
   initTime()


    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    //刷新表格
    getPayBillListBuyer()
})

//创建对账单
const onCreateCheck = (row) => {
    const tmp = currentRoute.value.name?.indexOf('BBB')>=0?'/bbmonthlycheck/bbbuymonthlycheckadd':'/monthlycheck/buymonthlycheckadd'
 
    push({
        path: tmp,
        query: {
            cus_id:row.供应商ID,
            cus_name: row.供应商名称,
            date:searchCondition.收货日期[0] + ',' + searchCondition.收货日期[1]
        }
    })
}


const baskFront = () => {
    back()
    closeOneTagByName(currentRoute.value.meta.title)
    //刷新上一页
    closeOneTagByPath('/monthlycheck/buymonthlycheck')
}

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if(['收货单个数','未税金额(人民币)', '含税金额(人民币)'].includes(column.property))
        {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}
</script>

<template>
    <ContentDetailWrap :title="'采购收货月结单新增'" @back="baskFront()">
        <div class="h-[100%] bg-white p-1 relative w-[100%] !bg-white flex-grow " style="color:#666666">
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-1 pb-1 mb-2 pl-12 bg-light-200">
                <div class="inline-flex items-center mr-1 mb-1">
                    <div class="searchTitle">收货日期</div>
                    <el-date-picker size="small" class="searchItem" v-model="searchCondition.收货日期" type="daterange"
                        range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">对账状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.对账状态" placeholder="">
                        <el-option v-for="item in ['未对账+部分对账', '未对账', '部分对账', '完全对账', '超量对账']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">供应商</div>
                    <el-input size="small" v-model="searchCondition.供应商" placeholder="" class="searchItem" />
                </div>


                <div class="flex justify-end items-center mr-6 mt-4">
                    <!-- <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" /> -->
                    <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="ml-1">查询</div>
                    </ElButton>
                    <ElButton class="ml-4" type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="ml-1">清除</div>
                    </ElButton>
                </div>
            </div>

            <el-table v-loading.lock="loading" ref="userTableRef11" header-cell-class-name="tableHeader" :data="checkData"
                style="width: 100%;color: #666666;" show-summary :summary-method="getSummaries" @current-change="setCurrentRow" border stripe>
                <el-table-column align="center" show-overflow-tooltip prop="供应商编号" :label="'供应商编号'" />
                <el-table-column align="center" show-overflow-tooltip prop="供应商名称" :label="'供应商名称'" min-width="200" />
                <!-- <el-table-column align="center" show-overflow-tooltip prop="对账日期" :label="'对账日期'" /> -->
                <el-table-column align="center" show-overflow-tooltip prop="负责人" :label="'负责人'" />
                <el-table-column align="center" show-overflow-tooltip prop="收货单个数" :label="'收货单个数'" />
                <el-table-column align="center" show-overflow-tooltip prop="未税金额(人民币)" :label="'未税金额(人民币)'" width="130"/>
                <el-table-column align="center" show-overflow-tooltip prop="含税金额(人民币)" :label="'含税金额(人民币)'" width="130"/>
                <el-table-column align="center" show-overflow-tooltip prop="最新对账月份" :label="'最新对账月份'" width="130"/>
                <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="110">
                    <template #default="scope">
                        <ElButton type="primary" size="small" @click="onCreateCheck(scope.row)">创建对账单</ElButton>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}
</style>

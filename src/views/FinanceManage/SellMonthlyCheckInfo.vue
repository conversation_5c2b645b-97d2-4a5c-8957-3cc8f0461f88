<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElForm,ElFormItem,ElDialog,ElInputNumber, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillListTakeOutApi, getPayBillSellNewnumApi, addPayBillSellApi, getPayBillSellInfoApi, updatePayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, closeOneTagByName, closeOneTagByPath, getLastDayOfMonth, getCurMonth, ceilToFixed } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { ContentDetailWrap } from '@/components/ContentDetailWrap'


const { currentRoute, back, push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)


//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    出库日期: [],
    对账状态: '未对账+部分对账',
    出库单号: '',
    销售单号: '',
    客户订单号: '',
    客户产品编码: '',
    出库状态: '',
    结算币种: '',
    产品编码: '',
    产品名称: '',
    产品规格: '',
    税前单价0: '',
    税前单价1: '',
    page: 1,
    count: 50
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//对账单数据源
const checkData = reactive({
    buyer_id: '',
    sell_paybill_num: '',
    prepaid_date: '',
    paybill_date: '',
    paybill_man_id: '',
    pdt_list: [],
    note: '',
    fsm_can_trig_data: {
        操作触发: []
    },
    discount_mode:'直接扣减',  //折扣方式
    discount_money:0,       //直接扣减金额
    discount_rate:1,        //折扣率
    discount_note:''
})


//查询客户数据
const getSellMonthlyCheckInfo = async () => {
    const ret = await getPayBillSellInfoApi({
        id: currentRoute.value.query.id
    })
    if (ret) {
        Object.assign(checkData, ret.data)
        for (let one of checkData.pdt_list) {
            one.税额单价 = ceilToFixed((one.含税单价 - one.不含税单价), 8, one.税额单价)
            //特殊处理退货相关数据
            if (one.类型.indexOf('退货') >= 0 && one.对账数量>0) {
                one.出库数量 = one.出库数量*-1
                one.对账数量 = one.对账数量*-1
                one.已对账 = one.已对账*-1
                one.未对账 = one.未对账*-1
                one.本次对账 = one.本次对账 * -1
                one.含税总价 = one.含税总价 * -1
                one.不含税总价 = one.不含税总价*-1
            }
        }
        checkData.discount_mode = checkData.discount_mode==''?'直接扣减':checkData.discount_mode
        checkData.discount_money = Number(checkData.discount_money)
        checkData.discount_rate = Number(checkData.discount_rate)
        onSearch()
    }
}
// //根据条件创建显示数据
// const createShowData = ()=>{
//     //根据条件创建显示数据
//     takeoutDetailShow.splice(0,takeoutDetailShow.length, ...checkData.pdt_list)

// }


const onSearch = () => {
    // 先全部隐藏
    for (let one of checkData.pdt_list) {
        one.show = false;
    }

    let bAllConditionsEmpty = true;

    // 检查所有条件是否都为空
    if (searchCondition.产品编码.length > 0 || 
        searchCondition.产品名称.length > 0 || 
        searchCondition.出库单号.length > 0 || 
        searchCondition.销售单号.length > 0 || 
        searchCondition.客户订单号.length > 0) {
        bAllConditionsEmpty = false;
    }

    // 如果所有条件都为空，则全部显示
    if (bAllConditionsEmpty) {
        for (let one of checkData.pdt_list) {
            one.show = true;
        }
    } else {
        // 遍历所有产品
        for (let one of checkData.pdt_list) {
            let match = true;

            // 根据条件逐个判断
            if (searchCondition.产品编码.length > 0 && one.产品编码.indexOf(searchCondition.产品编码) < 0) {
                match = false;
            }
            if (searchCondition.产品名称.length > 0 && one.产品名称.indexOf(searchCondition.产品名称) < 0) {
                match = false;
            }
            if (searchCondition.出库单号.length > 0 && one.出库单号.indexOf(searchCondition.出库单号) < 0) {
                match = false;
            }
            if (searchCondition.销售单号.length > 0 && one.销售订单号.indexOf(searchCondition.销售单号) < 0) {
                match = false;
            }
            if (searchCondition.客户订单号.length > 0 && one.客户订单号.indexOf(searchCondition.客户订单号) < 0) {
                match = false;
            }

            // 如果所有条件都满足，则显示
            if (match) {
                one.show = true;
            }
        }
    }

    tableHeight.value = document.getElementById('mainscroll')?.clientHeight - 130;
};

//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {

}
//page控件发生切换
const handleCurrentChange = (val: number) => {

}


//处理表格对象操作
const handleOper = (type, row) => {
    if (type === 'edit' || type == 'info') {
    }
    else if (type === 'del') {
        ElMessageBox.confirm(
            '确定是否删除该对账单？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {

                // const ret = await delBuyerApi({ "ids": [row.id] })
                // if (ret) {
                //     getPayBillListBuyer()

                //   ElMessage({
                //     type: 'success',
                //     message: t('msg.delOK'),
                //   })
                // }


            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: t('msg.delChannel'),
                })
            })
    }
}



onMounted(async() => {
    await getSellMonthlyCheckInfo()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

})

const baskFront = () => {
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}

//当前选中条数
const curSelCount = ref(0)

//实时更新数据
const recomputeShow = async (row, type) => {
    row.本次对账 = row.本次对账 == '' ? 0 : parseFloat(row.本次对账)
    row.含税总价 = row.含税总价 == '' ? 0 : parseFloat(row.含税总价)
    row.不含税总价 = row.不含税总价 == '' ? 0 : parseFloat(row.不含税总价)

    if (type == '本次对账') {
        row.含税总价 = ceilToFixed(row.本次对账 * row.含税单价 * row.折扣,6,row.含税总价)
        row.不含税总价 = ceilToFixed(row.本次对账 * row.不含税单价 * row.折扣,6,row.不含税总价)
        row.税额总价 = ceilToFixed((row.本次对账 * row.税额单价 * row.折扣),6,row.税额总价)
    }
    else if (type == '含税单价') {
        row.不含税单价 = ceilToFixed((row.含税单价 / (1 + row.税率 / 100)),8, row.不含税单价)
        row.含税总价 = ceilToFixed((row.含税单价 * row.本次对账),6, row.含税总价)
        row.不含税总价 = ceilToFixed((row.不含税单价 * row.本次对账),6, row.不含税总价)
        row.税额单价 = ceilToFixed((row.含税单价 - row.不含税单价),8,row.税额单价)
    }
    else if (type == '不含税单价') {
        row.含税单价 = ceilToFixed(parseFloat(row.不含税单价) + parseFloat(row.不含税单价) * row.税率 / 100,8, row.含税单价)
        row.含税总价 = ceilToFixed((row.含税单价 * row.本次对账),6, row.含税总价)
        row.不含税总价 = ceilToFixed((row.不含税单价 * row.本次对账),6, row.不含税总价)
        row.税额单价 = ceilToFixed((row.含税单价 - row.不含税单价),8,row.税额单价)
    }

    

    //更新
    updateData()

}
const updateData = async () => {
    checkData.fsm_exe_trig = '保存'
    for (let one of checkData.pdt_list) {
        //特殊处理退货相关数据
        if (one.类型.indexOf('退货')>=0) {
            one.出库数量 = Math.abs(one.出库数量)
            one.对账数量 = Math.abs(one.对账数量)
            one.已对账 = Math.abs(one.已对账)
            one.未对账 = Math.abs(one.未对账)
            one.本次对账 = Math.abs(one.本次对账)
            one.含税总价 = Math.abs(one.含税总价)
            one.不含税总价 = Math.abs(one.不含税总价)
        }
    }
    const ret = await updatePayBillSellApi(checkData)
    if (ret) {
        ElMessage.success('更新成功')
        getSellMonthlyCheckInfo()
    }
}
//所有当前选择
const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const arraySel = reactive([])
const handleSelectionChange = (value) => {
    arraySel.splice(0, arraySel.length, ...value)

    //如果arraysel某一行对账状态为未对账则取消multipleTableRef对应行的选中标志
    for (const one of arraySel) {
        if (one.对账状态 == '已对账' || one.本次对账 == 0) {
            multipleTableRef.value!.toggleRowSelection(one, false)
        }
    }
    curSelCount.value = arraySel.length
}


//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if (['本次对账', '含税总价', '不含税总价'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return prev + curr;
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })

    if (checkData.discount_mode === '直接扣减') {
        const discountAmount = checkData.discount_money;
        const 含税总价Index = columns.findIndex(column => column.property === '含税总价');
        sums[含税总价Index] -= discountAmount;
    } else if (checkData.discount_mode === '整单折扣') {
        const discountRate = checkData.discount_rate;
        const 含税总价Index = columns.findIndex(column => column.property === '含税总价');
        sums[含税总价Index] *= discountRate;
    } 
    return sums;
}

//删除pdt_list中某一个对象
const onDelPdt = (row) => {
    ElMessageBox.confirm(
        '确定是否删除该记录？',
        t('msg.warn'),
        {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'warning',
        }
    )
        .then(async () => {
            for (let i = 0; i < checkData.pdt_list.length; i++) {
                if (checkData.pdt_list[i] == row) {
                    checkData.pdt_list.splice(i, 1)
                    break
                }
            }
            updateData()
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: t('msg.delChannel'),
            })
        })
}

//继续添加入
const onAddOhter = () => {
    const tmp = currentRoute.value.name?.indexOf('SSS')>=0?'/ssmonthlycheck/sssellmonthlycheckadd':'/monthlycheck/sellmonthlycheckadd'
    closeOneTagByPath(tmp)
    
    push({
        path: tmp,
        query: {
            cus_id: checkData.buyer_id,
            cus_name: checkData.buyer_nick,
            id: checkData.id
        }
    })
}

//添加完成
const onEdit = (trig) => {
    ElMessageBox.confirm(
        '确定是否执行操作 ' + trig + ' ？',
        t('msg.warn'),
        {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'warning',
        }
    )
        .then(async () => {
            checkData.fsm_exe_trig = trig
            const ret = updatePayBillSellApi(checkData)
            if (ret) {
                ElMessage.success('更新成功')
                getSellMonthlyCheckInfo()
            }
        })
        .catch(() => {

        })



}

const loading = ref(false)
const tableHeight = ref(600)

const tmpData = reactive({})

const showZK = ref(false)
const onShowZK = () =>{
    showZK.value = true
    Object.assign(tmpData,checkData)
}
const onSetZK = () => {
    showZK.value = false
    tmpData.fsm_exe_trig = '保存'
    const ret = updatePayBillSellApi(tmpData)
    if (ret) {
        ElMessage.success('更新成功')
        getSellMonthlyCheckInfo()
    }
    
}


const tableRowClassName = (row, index) => {
  if(row.row.类型.indexOf('退货')>=0)
  {
    console.log('tuihuo !!!!!')
      return 'error-row'
  }
}
</script>

<template>
    <ContentDetailWrap :title="checkData.paybill_date + ' ' + checkData.buyer_nick + ' 出库月结单'" @back="baskFront()">
        <div class="h-[100%] bg-white p-1 relative w-[100%] !bg-white flex-grow " style="color:#666666">
            <div class="text-center flex justify-center items-center mt-[-20px] mb-1">
                <span class="text-1xl">状态:</span>
                <span class="text-1xl">{{ checkData.fsm_cur_state }}</span>
                <ElButton v-if="checkData.fsm_can_trig_data.操作触发.includes('继续添加')" class="ml-4" type="primary" plain
                    @click="onAddOhter">继续添加</ElButton>
                <ElButton v-if="checkData.fsm_can_trig_data.操作触发.includes('添加完成')" class="ml-2" type="primary" plain
                    @click="onEdit('添加完成')">添加完成</ElButton>
                <ElButton v-if="checkData.fsm_can_trig_data.操作触发.includes('对账已平')" class="ml-2" type="primary" plain
                    @click="onEdit('对账已平')">对账已平</ElButton>
                <ElButton v-if="checkData.fsm_can_trig_data.操作触发.includes('反对账')" class="ml-2" type="primary" plain
                    @click="onEdit('反对账')">反对账</ElButton>
            </div>
            <div v-if='checkData.fsm_cur_state != "已对账"' style1="border: 1px solid rgb(143, 143, 143);" class="pt-1 pb-1 mb-2 pl-12 bg-light-200">
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">产品编码</div>
                    <el-input size="small" v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">产品名称</div>
                    <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">出库单号</div>
                    <el-input size="small" v-model="searchCondition.出库单号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">销售单号</div>
                    <el-input size="small" v-model="searchCondition.销售单号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">客户订单号</div>
                    <el-input size="small" v-model="searchCondition.客户订单号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">税前单价</div>
                    <el-input size="small" v-model="searchCondition.税前单价0" placeholder="" class="!w-[60px]"
                        type="number" />
                    <div class="searchTitle !w-32px">到</div>
                    <el-input size="small" v-model="searchCondition.税前单价1" placeholder="" class="!w-[60px]"
                        type="number" />
                </div>

                <div class="flex justify-end items-center mr-6 mt-1">

                    <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="ml-1">查询</div>
                    </ElButton>
                    <ElButton class="ml-4" type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="ml-1">清除</div>
                    </ElButton>
                </div>
            </div>
            <el-table scrollbar-always-on v-loading="loading" :height="tableHeight" ref="multipleTableRef" header-cell-class-name="tableHeader"
                :data="checkData.pdt_list.filter(item => item.show === true)" style="width: 100%;color: #666666;" border :row-class-name="tableRowClassName"
                stripe show-summary :summary-method="getSummaries" @selection-change="handleSelectionChange">
                <!-- <el-table-column align="center" type="selection" width="40" /> -->
                <el-table-column align="center" show-overflow-tooltip prop="序号" :label="'序号'" width="40" fixed >
                    <template #default="scope">
                        <span>{{ scope.$index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="类型" :label="'类型'" />
                <el-table-column align="center" show-overflow-tooltip prop="出库时间" :label="'时间'" />
                <el-table-column align="center" show-overflow-tooltip prop="出库单号" :label="'单号'" />
                <el-table-column align="center" show-overflow-tooltip prop="销售订单号" :label="'销售单号'" width="120"/>
                <el-table-column align="center" show-overflow-tooltip prop="客户订单号" :label="'客户订单号'" />
                <el-table-column align="center" show-overflow-tooltip prop="产品编码" :label="'产品编码'" width="120"/>
                <el-table-column align="center" show-overflow-tooltip prop="产品名称" :label="'产品名称'" width="150"/>
                <el-table-column align="center" show-overflow-tooltip prop="规格" :label="'规格'" />
                <el-table-column align="center" prop="本次对账" :label="'数量'">
                    <template #default="scope">
                        <el-input-number  :disabled='checkData.fsm_cur_state == "已对账"' v-model="scope.row.本次对账" :min="(scope.row.类型.indexOf('退货')<0)?0:-999999999" :controls="false" size="small"
                            @blur="recomputeShow(scope.row, '本次对账')" />
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="发票类型" :label="'发票类型'" />
                <el-table-column align="center" show-overflow-tooltip prop="含税单价" :label="'含税单价'">
                    <template #default="scope">
                        <el-input-number :disabled='checkData.fsm_cur_state == "已对账"' v-model="scope.row.含税单价" :min="0" :controls="false" size="small"
                            @blur="recomputeShow(scope.row, '含税单价')" />
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="不含税单价" :label="'不含税单价'" />
                <el-table-column align="center" show-overflow-tooltip prop="税额单价" :label="'税额单价'">
                    <template #default="scope">
                        {{ scope.row.税额单价 }}
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="税率" :label="'税率'">
                    <template #default="scope">
                        {{ scope.row.税率 + '%' }}
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="折扣" :label="'折扣'" width="40" />
                <el-table-column align="center" show-overflow-tooltip prop="不含税总价" :label="'不含税总价'" />
                <el-table-column align="center" show-overflow-tooltip prop="税额总价" :label="'税额总价'">
                    <template #default="scope">
                        {{ ceilToFixed(((scope.row.含税单价 - scope.row.不含税单价) * scope.row.本次对账),6,0) }}
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="含税总价" :label="'含税总价'" />
                <el-table-column align="center" show-overflow-tooltip prop="付款方式" :label="'付款方式'" />
                <el-table-column align="center" fixed="right" show-overflow-tooltip prop="操作" :label="'操作'">
                    <template #default="scope">
                        <ElButton v-if='checkData.fsm_cur_state != "已对账"' type="danger" size="small" plain @click="onDelPdt(scope.row)">删除</ElButton>
                       
                    </template>
                </el-table-column>

            </el-table>
            <div class="flex items-center">
                <span class="ml-auto mr-5">{{ checkData.discount_mode+':'+(checkData.discount_mode=='直接扣减'?checkData.discount_money:checkData.discount_rate) }}</span>
                <ElButton type="primary"  @click="onShowZK">设置折扣</ElButton>
            </div>
            <div class="flex items-center">
                <span class="ml-auto mr-5">{{ checkData.discount_note }}</span>
            </div>
            <!-- <el-pagination class="justify-end mt-3" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[50, 100, 200, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
             -->
        </div>
        <el-dialog title="创建盘点单" v-model="showZK" width="500" align-center destroy-on-close>
            <el-form>
                <el-form-item class="mr-2  flex" label="总金额:">
                    {{ tmpData.应收金额 }}
                </el-form-item>       
                <el-form-item class="mr-2  flex" label="折扣方式:">
                    <el-select size="small"  v-model="tmpData.discount_mode" placeholder="Select" class="searchItem">
                        <el-option v-for="item in ['直接扣减','整单折扣']" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item> 
                <el-form-item v-if="tmpData.discount_mode == '直接扣减'" class="mr-2  flex" label="扣减金额:">
                    <el-input-number size="small" v-model="tmpData.discount_money" placeholder="" class="searchItem" :min="0" :max="tmpData.应收金额"/>
                </el-form-item> 
                <el-form-item v-if="tmpData.discount_mode == '整单折扣'" class="mr-2  flex" label="折扣率:">
                    <el-input-number size="small" :controls="false" v-model="tmpData.discount_rate" class="searchItem" :min="0" :max="1" />
                </el-form-item>     
                <el-form-item class="mr-2  flex" label="备注:">
                    <el-input size="small" v-model="tmpData.discount_note" placeholder="" />
                </el-form-item>       
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="showZK = false">取消</el-button>
                    <el-button type="primary" @click="onSetZK">修改</el-button>
                </div>
            </template>

        </el-dialog>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}

:deep(.el-input-number--small) {
    width: 100%;
}

:deep(.hidden-row) {
    display: none;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}

:deep(.error-row .el-table__cell > div){
//   background-color:#ffc2c2;
color:#F56C6C !important;
}
</style>

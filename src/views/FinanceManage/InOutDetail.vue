<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElRadioGroup, ElRadioButton, ElTabs, ElTabPane, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { delFinanceWaterApi, getFinanceAccountListApi, getFinanceWaterListApi } from '@/api/finance';
import type { TabsInstance } from 'element-plus'
import { DialogMoneyDetail } from '@/components/DialogMoneyDetail'

const { push,currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  年份: '2024',
  月份: '01月',
  account_nick: '',
  客户编号: '',
  关联类型: '',
  关联单号: '',
  金额0: '',
  金额1: '',
  经手人: '',
  录入人: '',
  摘要: '',
  公司: '',
  page: 1,
  count: 20
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//对账单数据源
const checkData = reactive([])
//当前选中行
const currentRow = ref(null)

const activeMonth = ref('01月')
const activeTab = ref('全部')
const accountData = reactive([])
//获取账号列表
const getFinanceAccountList = async () => {
  const ret = await getFinanceAccountListApi({
    page: 1,
    count: 1000
  })
  if (ret) {
    accountData.splice(0, accountData.length, ...ret.data)
  }
}

//获取收支明细
const getFinanceWaterList = async (page = 1) => {
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  delete tmp.金额0
  delete tmp.金额1

  tmp.金额 = searchCondition.金额0 + ',' + searchCondition.金额1
  const ret = await getFinanceWaterListApi(tmp)
  if (ret) {
    checkData.splice(0, checkData.length, ...ret.data)
    totleCount.value = ret.count
  }
}


//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getFinanceWaterList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getFinanceWaterList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getFinanceWaterList(val)
}
//创建新客户月结
const onAddCheck = () => {
  // push({
  //   path: '/monthlycheck/sellmonthlycuslist',
  //   query: {
  //     id: ''
  //   }
  // })
}

//处理表格对象操作
const handleOper = (type, row) => {
  if (type === 'edit' || type == 'info') {
    // push({
    //   path: '/monthlycheck/sellmonthlycheckinfo',
    //   query: {
    //     id: row.id,
    //     type: type
    //   }
    // })
  }
  else if (type === '删除') {

    ElMessageBox.confirm(
        '确定是否删除该流水？',
        t('msg.warn'),
        {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'warning',
        }
    )
    .then(async () => {

        const ret = await delFinanceWaterApi({ "ids": [row.id] })
        if (ret) {
            getFinanceWaterList()

            ElMessage({
                type: 'success',
                message: t('msg.delOK'),
            })
        }


    })
    .catch(() => {
        ElMessage({
            type: 'info',
            message: t('msg.delChannel'),
        })
    })
  }
  else if(type === '流水明细')
  {
      showDetail.value = true
    curDetailData.value = row
      showMode.value = 'info'
  }
  else if(type === '修改')
  {
    ElMessageBox.confirm(
        '确定是否修改该流水？',
        t('msg.warn'),
        {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'warning',
        }
    )
    .then(async () => {

      showDetail.value = true
      curDetailData.value = row
      showMode.value = 'edit'

    })
    .catch(() => {
        ElMessage({
            type: 'info',
            message: '取消修改',
        })
    })



  }
}
const showDetail = ref(false)
const curDetailData = ref({})
const showMode = ref('info')
//设置当前选中行
const setCurrentRow = (value) => {
  currentRow.value = value
}


onMounted(async() => {
  await getFinanceAccountList()
  const currentDate = new Date();
  const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
  searchCondition.月份 = `${currentMonth}月`;

  if(currentRoute.value.query.account_id != undefined)
  {
    const find = accountData.find(item=>item.id == currentRoute.value.query.account_id)
    if(find != undefined)
    {
      activeTab.value = find.nick
      searchCondition.account_nick = find.nick
    }
  }
  

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  //刷新表格
  getFinanceWaterList()
})

//显示合计
const getSummaries = (param) => {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['应收金额', '已收金额', '应开发票', '已开发票'].includes(column.property)) {
      const values = data.map(item => Number(item[column.property]));
      if (!values.every(value => isNaN(value))) {
        sums[index] = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0);
      } else {
        sums[index] = '/';
      }
    }
  })
  return sums;
}

const onChangeAccount = (value) => {
  console.log(value)
  searchCondition.account_nick = value == '全部' ? '' : value
  getFinanceWaterList()
}
</script>

<template>
  <div ref="rootRef" class="">
    <div class="pb-[100px] w-[100%] !bg-white flex-grow " style="color:#666666">
      <div class="absolute top-2 left-8">
        <!-- <ElButton type="success" @click="onAddCheck">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">新增</div>
        </ElButton> -->
      </div>
      <div class="text-center mb-5 font-bold" style="color:#333">收支明细{{ searchCondition.年份 + '年' + searchCondition.月份
        }}的记录
      </div>
      <div class="flex flex-col justify-center items-center">
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">年份</div>
          <el-date-picker @change="getFinanceWaterList" @calendar-change="getFinanceWaterList" :clearable="false" v-model="searchCondition.年份"
            type="year" placeholder="Pick a year" format="YYYY" value-format="YYYY" />
        </div>

        <el-radio-group @change="(value) => { getFinanceWaterList() }" v-model="searchCondition.月份">
          <el-radio-button v-for="month in 12" :key="month" :label="`${String(month).padStart(2, '0')}月`"
            :value="month" />
        </el-radio-group>
      </div>

      <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-1 pl-5 bg-light-200">
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">类型</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.对账状态" placeholder="">
            <el-option v-for="item in ['收入', '支出']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">关联类型</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.关联类型" placeholder="">
            <el-option v-for="item in ['销售订单', '采购订单', '委外订单']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">关联单号</div>
          <el-input size="small" v-model="searchCondition.客户编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">金额</div>
          <el-input size="small" v-model="searchCondition.金额0" placeholder="" class="!w-[60px]" type="number" />
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small" v-model="searchCondition.金额1" placeholder="" class="!w-[60px]" type="number" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">录入人</div>
          <el-input size="small" v-model="searchCondition.录入人" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">摘要</div>
          <el-input size="small" v-model="searchCondition.摘要" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">公司</div>
          <el-input size="small" v-model="searchCondition.公司" placeholder="" class="searchItem" />
        </div>

        <div class="flex justify-end items-center mr-6 mt-4">
          <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" />
          <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
            <Icon icon="tabler:search" />
            <div class="ml-1">查询</div>
          </ElButton>
          <ElButton class="ml-4" type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="ml-1">清除</div>
          </ElButton>
        </div>
      </div>

      <el-tabs @tab-change="onChangeAccount" v-model="activeTab" class="mb-2 ">
        <el-tab-pane label="全部" name="全部">
        </el-tab-pane>
        <el-tab-pane v-for="item in accountData" :key="item.id" :label="item.nick" :name="item.nick">
        </el-tab-pane>
      </el-tabs>

      <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="checkData"
        style="width: 100%;color: #666666;" show-summary :summary-method="getSummaries" @current-change="setCurrentRow"
        border stripe>
        <el-table-column align="center" show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60" />
        <el-table-column align="center" show-overflow-tooltip prop="create_date" :label="'日期'">
          <template #default="scope">
            <div>{{ scope.row.create_date.split(' ')[0] }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" show-overflow-tooltip prop="account_nick" :label="'收款账户'" />
        <el-table-column align="center" show-overflow-tooltip prop="corp_nick" :label="'公司'" />
        <el-table-column align="center" show-overflow-tooltip prop="money_type" :label="'币种'" />
        <el-table-column align="center" show-overflow-tooltip prop="money_in" :label="'收入'" />
        <el-table-column align="center" show-overflow-tooltip prop="money_out" :label="'支出'" />
        <el-table-column align="center" show-overflow-tooltip prop="money_bal" :label="'账户余额'" />
        <el-table-column align="center" show-overflow-tooltip prop="pay_type" :label="'支付方式'" />
        <el-table-column align="center" show-overflow-tooltip prop="use_type" :label="'用途'" />
        <el-table-column align="center" show-overflow-tooltip prop="name" :label="'关联类型'" />
        <el-table-column align="center" show-overflow-tooltip prop="create_man_name" :label="'录入人'" />
        <el-table-column align="center" show-overflow-tooltip prop="note" :label="'摘要'" />
        <el-table-column align="center" show-overflow-tooltip prop="account_status" :label="'状态'" />



        <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
          <template #default="scope">
            <el-dropdown trigger="click" placement="bottom">
              <span class="el-dropdown-link">
                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
              </span>
              <template #dropdown>
                <div class="flex flex-wrap w-[200px]">
                  <el-dropdown-item @click="handleOper('流水明细', scope.row)">流水明细</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('修改', scope.row)">修改</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('删除', scope.row)">删除</el-dropdown-item>
                </div>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination class="justify-end mt-8 mb-[200px]" v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
        layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
 
    </div>
    <DialogMoneyDetail v-model:show="showDetail" :data="curDetailData" :type="showMode" @on-submit="getFinanceWaterList"/>
  </div>

</template>

<style lang="less" scoped>
:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  color: #00BA80;
  cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.searchItem {
  width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
  width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
  font-size: 12px;
  white-space: normal;
  color: black;
  padding-left: 1px;
  padding-right: 1px;
}
</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi } from '@/api/product'
import { cloneDeep } from 'lodash-es';
import { delFinanceAccountApi, getFinanceAccountListApi, updateFinanceAccountApi } from '@/api/finance';
import { DialogBandAccount } from '@/components/DialogBandAccount'

const { push } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//支付方式
const payTypeData = reactive([
    '现金',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])
//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    账户状态: '',
    账户名称: '',
    支付方式: '',
    账户币种: '',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//对账单数据源
const accountData = reactive([])


//查询数据
const getFinanceAccountList = async () => {
    const ret = await getFinanceAccountListApi(searchCondition)
    if (ret) {
        accountData.splice(0, accountData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getFinanceAccountList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getFinanceAccountList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getFinanceAccountList()
}


//处理表格对象操作
const handleOper = async(type, row) => {
    if (type === '修改') {
        showAccount.value = true
        curAccountID.value = row.id
    }
    else if(type === '禁用账户' || type === '启用账户')
    {
        ElMessageBox.confirm(
            '确定是否'+type,
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
        .then(async () => {
            row.status = type === '禁用账户'?'已禁用':'已启用'
            const ret = await updateFinanceAccountApi(row)
            if(ret)
            {
                ElMessage.success('修改成功')
                getFinanceAccountList()
            }
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: t('msg.delChannel'),
            })
        })



    }
    else if (type === '删除') {

        ElMessageBox.confirm(
            '确定是否删除该账号？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
        .then(async () => {

            const ret = await delFinanceAccountApi({ "ids": [row.id] })
            if (ret) {
                getFinanceAccountList()

                ElMessage({
                    type: 'success',
                    message: t('msg.delOK'),
                })
            }


        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: t('msg.delChannel'),
            })
        })
    }
    else if(type === '账户余额明细')
    {
        push({
            path: '/inoutdetail/inoutdetail',
            query: {
                account_id:row.id
            }
        })
    }
}


onMounted(() => {

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    //刷新表格
    getFinanceAccountList()
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if (['余额(人民币)','当前余额'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}

const curAccountID = ref('')
const showAccount = ref(false)
const onShowEditAccount = (row)=>{
    showAccount.value = true
    curAccountID.value = row.id
}
//创建新客户月结
const onAddAccount = () => {
    showAccount.value = true
    curAccountID.value = ''
}

const tableRowClassName = (row,index)=>{
  if(row.row.status == '已禁用')
  {
    return 'error-row'
  }
  else
    return ''
}
</script>

<template>
    <div ref="rootRef" class="absolute top-[10px] right-[20px] left-[20px] bottom-[100px]">
        <div class="flex h-[100%]">
            <div class="h-[100%] bg-white p-1 relative w-[100%] !bg-white flex-grow " style="color:#666666">
                <div class="absolute top-5 left-8">
                    <ElButton type="success" @click="onAddAccount">
                        <Icon icon="fluent-mdl2:people-add" />
                        <div class="pl-2">新增账户</div>
                    </ElButton>
                </div>
                <div class="text-center mb-5 font-bold" style="color:#333">账户维护</div>
                <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-7 pl-12 bg-light-200">
                    <div class="inline-flex items-center ml-1 mb-1">
                        <div class="searchTitle">账户状态</div>
                        <el-select size="small" class="searchItem" v-model="searchCondition.账户状态" placeholder="">
                            <el-option v-for="item in ['', '已启用', '已禁用']" :key="item" :label="item" :value="item" />
                        </el-select>
                    </div>
                    <div class="inline-flex items-center ml-1 mb-1">
                        <div class="searchTitle">账户名称</div>
                        <el-input size="small" v-model="searchCondition.账户名称" placeholder="" class="searchItem" />
                    </div>
                    <div class="inline-flex items-center ml-1 mb-1">
                        <div class="searchTitle">支付方式</div>
                        <el-select size="small" class="searchItem" v-model="searchCondition.支付方式" placeholder="">
                            <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
                        </el-select>
                    </div>
                    <div class="inline-flex items-center ml-1 mb-1">
                        <div class="searchTitle">账户币种</div>
                        <el-select size="small" class="searchItem" v-model="searchCondition.账户币种" placeholder="">
                            <el-option v-for="item in moneyTypeData" :key="item" :label="item" :value="item" />
                        </el-select>
                    </div>

                    <div class="flex justify-end items-center mr-6 mt-4">
                        <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" />
                        <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
                            <Icon icon="tabler:search" />
                            <div class="ml-1">查询</div>
                        </ElButton>
                        <ElButton class="ml-4" type="warning" @click="onClear">
                            <Icon icon="ant-design:clear-outlined" />
                            <div class="ml-1">清除</div>
                        </ElButton>
                    </div>
                </div>

                <el-table header-cell-class-name="tableHeader" :data="accountData" style="width: 100%;color: #666666;"
                    show-summary :summary-method="getSummaries" border :row-class-name="tableRowClassName">
                    <el-table-column align="center" show-overflow-tooltip prop="id" :label="t('userTable.id')"
                        width="60" />
                    <el-table-column align="center" show-overflow-tooltip prop="nick" :label="'账户名称'" />
                    <el-table-column align="center" show-overflow-tooltip prop="bank_account" :label="'银行账号'" />
                    <el-table-column align="center" show-overflow-tooltip prop="money_type" :label="'账户币种'" />
                    <el-table-column align="center" show-overflow-tooltip prop="当前余额" :label="'当前余额'" />
                    <el-table-column align="center" show-overflow-tooltip prop="汇率" :label="'汇率'" />
                    <el-table-column align="center" show-overflow-tooltip prop="余额(人民币)" :label="'余额（人民币）'" />
                    <el-table-column align="center" show-overflow-tooltip prop="明细条数" :label="'明细条数'" />
                    <el-table-column align="center" show-overflow-tooltip prop="pay_type" :label="'支付方式'" />
                    <!-- <el-table-column align="center" show-overflow-tooltip prop="凭证科目" :label="'凭证科目'" /> -->
                    <el-table-column align="center" show-overflow-tooltip prop="status" :label="'账户状态'" />
                    <el-table-column align="center" show-overflow-tooltip prop="main_man_name" :label="'负责人'" />
                    <!-- <el-table-column align="center" show-overflow-tooltip prop="排序" :label="'排序'" /> -->

                    <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
                        <template #default="scope">
                            <el-dropdown trigger="click" placement="bottom">
                                <span class="el-dropdown-link">
                                    <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                                </span>
                                <template #dropdown>
                                    <div class="flex flex-wrap w-[200px]">
                                        <el-dropdown-item @click="handleOper('修改', scope.row)">修改</el-dropdown-item>
                                        <el-dropdown-item @click="handleOper('账户余额明细', scope.row)">账户余额明细</el-dropdown-item>
                                        <el-dropdown-item v-if="scope.row.status == '已启用'" @click="handleOper('禁用账户', scope.row)">禁用账户</el-dropdown-item>
                                        <el-dropdown-item v-if="scope.row.status == '已禁用'" @click="handleOper('启用账户', scope.row)">启用账户</el-dropdown-item>
                                        <el-dropdown-item @click="handleOper('删除', scope.row)">删除</el-dropdown-item>
                                    </div>
                                </template>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
                    v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                    layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </div>
        <DialogBandAccount v-model:show="showAccount" :id="curAccountID" @on-submit="getFinanceAccountList"/>
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}

:deep(.error-row){
   background-color: rgb(214, 214, 214)!important;
  --el-table-border: 1px solid #dedfe0;
}

</style>

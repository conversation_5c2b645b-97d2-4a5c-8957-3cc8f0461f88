<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTooltip,ElTag,ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPaymentApplicationListApi, delPaymentApplicationApi, updatePaymentApplicationApi } from '@/api/product'
import { checkPermissionApi, closeOneTagByPath, downloadFile } from '@/api/tool';
import { cloneDeep, isNumber } from 'lodash-es';
import { DialogOemOutMoney } from '@/components/DialogOemOutMoney'
import { exportPaymentApplicationApi } from '@/api/extra';
import PrintModal from '@/views/PrintManage/PrintModal.vue'


const { push,currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    付款申请单号: '',
    收款单位名称: '',
  付款状态: '',
  关联类型:'',
    关联单号: '',
    款项用途: '',
    申请人:'',
    付款日期:['',''],
    page: 1,
    count: 30
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//数据源
const paymentData = reactive([])
//当前选中行
const currentRow = ref(null)

//查询受托方数据
const getPaymentApplicationListData = async () => {
  let tmp = cloneDeep(searchCondition)
    tmp.付款日期 = searchCondition.付款日期[0] + ',' + searchCondition.付款日期[1]
    isLoading.value = true
  const ret = await getPaymentApplicationListApi(tmp)
  if(ret)
  {
    paymentData.splice(0,paymentData.length,...ret.data)
    totleCount.value =  parseInt(ret.count)
    }
    isLoading.value = false
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getPaymentApplicationListData()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getPaymentApplicationListData()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getPaymentApplicationListData()
}
//创建新付款申请
const onAddPayment = () => {
   
    push({
      path: '/arap/paymentapplicationadd',
      query:{
          id: '',
      }
    })
}

//处理表格对象操作
const handleOper = (type, row) => {
  if (type === 'edit' || type == 'info' || type == 'check') {

    
    let path = '';

    if(currentRoute.value.name == 'OemPaymentApplication')
    {
      path = '/oomonthlycheck/oempaymentapplicationadd'
    }
    else if(currentRoute.value.name == 'BuyPaymentApplication')
    {
      path = '/bbmonthlycheck/buypaymentapplicationadd'
    }
    else
    {
      path = '/arap/paymentapplicationadd'
    }


    console.log(path)
    closeOneTagByPath(path)
    push({
      path: path,
      query: {
        id: row.id,
        type: type == 'check' ? 'info' : type,
        cmd:type=='check'?'审核':''
      }
    })
  }
  else if (type === 'del') {
    ElMessageBox.confirm(
      '确定是否删除该申请单？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {
        const ret = await delPaymentApplicationApi({ 
          "ids": [row.id] ,
          fsm_exe_trig : '删除'
        })
        if (ret) {
            getPaymentApplicationListData()

            ElMessage({
                type: 'success',
                message: t('msg.delOK'),
            })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })
  }
}
//设置当前选中行
const setCurrentRow = (value) => {
  currentRow.value = value
}


onMounted(() => {

  if (currentRoute.value.query.type != undefined)
  {
    searchCondition.关联类型 = currentRoute.value.query.type
  }
  if(currentRoute.value.query.num != undefined)
  {
    searchCondition.关联单号 = currentRoute.value.query.num
  }

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  //刷新表格
  getPaymentApplicationListData()
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if(['付款金额','已支付金额'].includes(column.property))
        {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return prev + curr;
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}


const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    tmp.付款日期 = searchCondition.付款日期[0]+','+searchCondition.付款日期[1]
    const ret = await exportPaymentApplicationApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          downloadFile(ret.data.download,ret.data.filename)
          loadingExport.value = false
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}

const isLoading = ref(false)


//去打印
const dialogVisible = ref(false)
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: '付款申请单' }
  printInfo.pdt_list = []
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(() => { dialogVisible.value = false })
}

</script>

<template>
  <div ref="rootRef">
      <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">
        <div class="absolute top-5 left-8">
          <ElButton type="success" @click="onAddPayment">
            <Icon icon="fluent-mdl2:people-add" />
            <div class="pl-2">新增申请单</div>
          </ElButton>
          <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
              <Icon icon="carbon:export" />
              <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton>
        </div>
        <div class="text-center mb-5 font-bold" style="color:#333">付款申请单</div>
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">申请单号</div>
            <el-input size="small" v-model="searchCondition.付款申请单号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">收款单位</div>
            <el-input size="small" v-model="searchCondition.收款单位名称" placeholder="" class="searchItem" />
          </div>
          <!-- <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">付款状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.付款状态" placeholder="" >
              <el-option v-for="item in ['']" :key="item" :label="item" :value="item" />
            </el-select>
          </div> -->
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">关联类型</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.关联类型" placeholder="" >
              <el-option v-for="item in ['销售月结单'  , '采购月结单' , '委外月结单'  , '采购单']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">关联单号</div>
            <el-input size="small" v-model="searchCondition.关联单号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">款项用途</div>
            <el-input size="small" v-model="searchCondition.款项用途" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">申请人</div>
            <el-input size="small" v-model="searchCondition.申请人" placeholder="" class="searchItem" />
          </div>
          <div  class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">付款日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.付款日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>


          <div class="flex justify-end items-center mr-6">
            <!-- <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" /> -->
            <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
              <Icon icon="tabler:search" />
              <div class="ml-1">查询</div>
            </ElButton>
            <ElButton class="ml-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="ml-1">清除</div>
            </ElButton>
          </div>
        </div>

        <el-table v-loading.lock="isLoading" ref="userTableRef11" header-cell-class-name="tableHeader" :data="paymentData"
          style="width: 100%;color: #666666;" show-summary :summary-method="getSummaries"  border stripe>
          <el-table-column align="center" show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60" >
            <template #default="scope">
              {{ scope.$index+1 }}
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="book_payment_num" :label="'申请单号'" />
          <el-table-column align="center" show-overflow-tooltip prop="payee_nick" :label="'收款单位名称'" width="250"/>
          <el-table-column align="center" show-overflow-tooltip prop="payment_date" :label="'付款日期'" />
          <el-table-column align="center" show-overflow-tooltip prop="money_amount" :label="'付款金额'" />
          <el-table-column align="center" show-overflow-tooltip prop="币种" :label="'已支付金额'" />
          <el-table-column align="center" show-overflow-tooltip prop="money_use" :label="'款项用途'" width="250"/>
          <el-table-column align="center" show-overflow-tooltip prop="币种" :label="'关联单据'" >
              <template #default="scope">
                
                <div class='flex flex-col'>
                  <div v-if="scope.row.order_list.length == 1" class="mr-2">{{ scope.row.order_list[0].关联类型 }}</div>
                  <div v-if="scope.row.order_list.length == 1" class="mr-2 text-blue-400">{{ '('+scope.row.order_list[0].关联单号+')' }}</div>
                </div>

                <el-tooltip
                        v-if="scope.row.order_list.length>1"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.order_list.map(item => item.关联类型 + '(' + item.关联单号 + ')').join(',')"
                        placement="bottom"
                    >
                    <el-tag>组合</el-tag>
                </el-tooltip>
              </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="payment_man_name" :label="'申请人'" />
          <el-table-column align="center" show-overflow-tooltip prop="payment_man_depts_name" :label="'部门'" />
          <el-table-column align="center" show-overflow-tooltip prop="fsm_cur_state" :label="'状态'" />
                    
          <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
            <template #default="scope">
              <el-dropdown trigger="click" placement="bottom">
                <span class="el-dropdown-link">
                  <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                </span>
                <template #dropdown>
                  <ElDropdownMenu>
                    <el-dropdown-item @click="toPrintPage(scope.row)">打印</el-dropdown-item>
                    <el-dropdown-item v-if="checkPermissionApi('审核付款申请') && scope.row.fsm_cur_state == '等待审核'" @click="handleOper('check', scope.row)">审核</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('info', scope.row)">查看</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('edit', scope.row)">编辑</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('del', scope.row)">删除</el-dropdown-item>
                  </ElDropdownMenu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[30, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
      <PrintModal v-model:show="dialogVisible" />
  </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  color: #00BA80;
  cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.searchItem {
  width: 150px !important;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper)
{
  width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}

</style>

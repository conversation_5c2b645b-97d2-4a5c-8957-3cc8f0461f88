<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElInputNumber,ElSwitch,ElDialog, ElForm, ElFormItem, ElRow, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { getWithdrawListApi, getWithdrawTJApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';
import { getHashApi, getHashSysListApi, getJiangchiListApi, setHashApi, setHashSyspi, updateJiangchiApi, updateJiangchiChangeApi, updateJiangchiPoolApi } from '@/api/extra';

const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  // user_id:'',
  // status:'',
  page: 1,
  count: 2000
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//配置列表
const hashConfig = reactive([])

const getJiangchiList = async () => {
  let ret = await getJiangchiListApi(searchCondition)
  if (ret) {
    console.log(ret)
    if (ret.data) {
      Object.assign(hashConfig, ret.data)
            //给hashconfig中每一个原生增加old_line,old_pool 分别等于line pool
      ret.data.forEach((item) => {
        item.low_nGamblingBalanceGold = item.nGamblingBalanceGold%100
        item.low_nGamblingWinPool = item.nGamblingWinPool%100
        item.nGamblingBalanceGold = Math.floor(item.nGamblingBalanceGold / 100)
        item.nGamblingWinPool = Math.floor(item.nGamblingWinPool / 100)
      })
      totleCount.value =  parseInt(ret.count)
    }
  }
}


//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {

}
//page控件发生切换
const handleCurrentChange = (val: number) => {

}
//创建新客户
const onAddCustmer = () => {

}

//处理表格对象操作
const handleOper = (type, row) => {
  console.log(row)
  if (type == '确定修改奖池') {
    ElMessageBox.confirm(
      '是否确认修改该奖池设置？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

          let tmp = {
            nGameID: row.nGameID,
            nGamblingWaterLevelGold: Number(row.nGamblingWaterLevelGold),
            nGamblingBalanceGold:Number(row.nGamblingBalanceGold)*100,
            nGamblingWinPool:Number(row.nGamblingWinPool)*100,
        }
        const ret = await updateJiangchiPoolApi(tmp)
        if (ret) {
          getJiangchiList()

          ElMessage({
            type: 'success',
            message: '操作成功',
          })
          showEdit.value = false

        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '操作取消',
        })

      })
  }
  else if (type == '确定修改难度') {
    ElMessageBox.confirm(
      '是否确认修改该难度设置？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

          let tmp = {
            nGameID: row.nGameID,
            nGamblingChance:row.nGamblingChance,
            nGamblingFreeChance:row.nGamblingFreeChance,
        }
        const ret = await updateJiangchiChangeApi(tmp)
        if (ret) {
          getJiangchiList()

          ElMessage({
            type: 'success',
            message: '操作成功',
          })
          showEdit.value = false

        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '操作取消',
        })

      })
  }
}

let timmer;
onMounted(() => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);
  getJiangchiList()

      //启动定时刷新
  timmer = setInterval(() => {
    getJiangchiList()
  }, 1000);
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});
// 在组件销毁前移除窗口大小变化的监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize);
  clearInterval(timmer)
});


const showEdit = ref(false)
const selItemdef = {
  name: '',
  text: ''
}
const selItem = reactive({
  ...selItemdef,
  reset() {
    for (let key in this) {
      if (this.hasOwnProperty(key) && !(key in selItemdef) && key != 'reset') {
        delete this[key];
      }
    }
    Object.assign(this, selItemdef)
  }
})

const onAddConfig = () => {
  showEdit.value = true
  selItem.reset()
}

//自动刷新
const autoFresh = ref(true)
const autoFreshChange = () => {
  if (autoFresh.value) {
    timmer = setInterval(() => {
      getJiangchiList()
    }, 1000);
  } else {
    clearInterval(timmer)
  }
}
</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
    <div ref="rootRef" class="flex relative">
      <div class="absolute top-8 left-8">
        <ElButton type="success" @click="getJiangchiList">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">刷新</div>
        </ElButton>
        <el-switch
        class="ml-10"
          v-model="autoFresh"
          size="large"
          active-text="自动刷新"
          inactive-text="关闭自动"
          @change="autoFreshChange"
        />
      </div>
      <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
        <div class="text-center mb-5 font-bold" style="color:#333">LABA奖池配置管理</div>
        <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="hashConfig"
          style="width: 100%;color: #666666;" :height1="tableHeight" border stripe>
          <el-table-column show-overflow-tooltip prop="nGameID" label="游戏ID" />
          <el-table-column show-overflow-tooltip prop="strGameName" label="游戏名称" />
          <el-table-column show-overflow-tooltip prop="nGameType" label="游戏类型" />
          <el-table-column show-overflow-tooltip prop="nGamblingWaterLevelGold" label="抽水百分率%" >
            <template #default="scope">
              <el-input :disabled="autoFresh" v-model="scope.row.nGamblingWaterLevelGold" class="!w-[90%]"></el-input>
              %
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="nGamblingBalanceGold" label="库存" >
            <template #default="scope">
              <el-input :disabled="autoFresh" v-model="scope.row.nGamblingBalanceGold" class="!w-[70%]"></el-input>
              {{'.'+scope.row.low_nGamblingBalanceGold }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="nGamblingWinPool" label="奖池" >
            <template #default="scope">
              <el-input :disabled="autoFresh" v-model="scope.row.nGamblingWinPool" class="!w-[70%]"></el-input>
              {{'.'+scope.row.low_nGamblingWinPool }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button :disabled="autoFresh" type="primary" @click="handleOper('确定修改奖池', scope.row)">修改奖池</el-button>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="line" label="难度" min-width="180">
            <template #default="scope">
              <!-- <el-select :disabled="autoFresh || scope.row.nGamblingChance == 0" class="!w-[50%] mr-2" v-model="scope.row.nGamblingChance" placeholder="">
                <el-option v-for="item in [{ key: 0, name: '不支持' },{ key: 1, name: '简单' },{ key: 2, name: '普通' },{ key: 3, name: '困难' },{ key: 4, name: '噩梦' },{ key: 5, name: '地狱' },{ key: 6, name: '封神' },{ key: 7, name: '超神' },{ key: 8, name: '归一' }]" :key="item.key" :label="item.name" :value="item.key" />
              </el-select> -->
              <ElInputNumber :disabled="autoFresh" :min="1" :max="100"  v-model="scope.row.nGamblingChance" class="!w-[35%]" />
              <ElInputNumber :disabled="autoFresh" :min="1" :max="100"  v-model="scope.row.nGamblingFreeChance" class="!w-[35%]" />
              <el-button :disabled="autoFresh || scope.row.nGamblingChance == 0" type="danger" @click="handleOper('确定修改难度', scope.row)">修改难度</el-button>
            </template>
          </el-table-column>
        </el-table>

      </div>
    </div>


    <el-dialog title="提现" v-model="showEdit" width="700" align-center destroy-on-close>
      <el-form>
        <el-form-item class="titleShow" label="key:">
          <el-input v-model="selItem.name" placeholder="请输入key"></el-input>
        </el-form-item>
        <el-form-item class="titleShow" label="value:">
          <el-input v-model="selItem.text" placeholder="请输入value"></el-input>
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEdit = false">取消</el-button>
          <el-button type="danger" @click="handleOper('确定修改', selItem)">新增</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
  color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
  /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
}

:deep(.el-form-item__label) {
  width: 120px;
  /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
  text-align: right;
  /* 让文本右对齐，使标题看起来更整齐 */
  padding-right: 10px;
  /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElOption,ElSelect,ElSwitch,ElCard,ElButton,ElInput,ElMessage,ElTag,ElProgress } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { onBeforeMount } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { getHashApi, notifyMsg, setHashApi } from '../../api/extra';
import { DialogUser } from '@/components/DialogUser'
import { addCategApi, getStoreListApi } from '@/api/product'

const { currentRoute,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

const notifyUsers = ref([])
const showSelNotifyUser = ref(false)
const onSelNotifyUser = ()=>{
    showSelNotifyUser.value = true
}
const onSelNotifyUserCallback = (id,name)=>{
    notifyUsers.value.push({
        id:id,
        name:name
    })
}
const notifyData = reactive({
    all:false,
    ids:[], //通知用户列表
    note:'' //通知内容
})

const onSendNotify = async()=>{
    
    if(notifyData.note == '')
    {
        ElMessage.warning('请输入通知内容！')
        return
    }
    notifyData.ids = []
    if(!notifyData.all)
    {
        if(notifyUsers.value.length == 0)
        {
            ElMessage.warning('请选择通知用户！')
            return
        }
        for(let one of notifyUsers.value)
        {
            notifyData.ids.push(one.id)
        }
    }

    const ret = await notifyMsg(notifyData)
    if(ret)
    {
        ElMessage.success('发送成功！')
        notifyUsers.value = []
        notifyData.ids = []
        notifyData.note = ''
    }
}

onMounted(async() => {
    await getStoreList()
    await getDefaultStore()
})

//目录导入进度条
const nDirImportPer = ref(0)
const nDirCur = ref(0)
const nDirTotle = ref(0)
//选择目录导入配置文件
const onSelDirImportConfig = async (event: Event)=>{
    const input = event.target as HTMLInputElement;
  const file = input.files?.[0];

  if (file) {
    try {
      const fileContent = await readFileContent(file);
     // console.log(fileContent);

     nDirCur.value = 0
     nDirTotle.value = 0
     getImportDirTotle(JSON.parse(fileContent))
     console.log(nDirTotle.value)
    //开始处理
    deelImportDir(JSON.parse(fileContent),0)

    } catch (error) {
      console.error('Error reading file:', error);
    }
  }
}

//获取等待导入总数
const getImportDirTotle = (item)=>{
    nDirTotle.value++
    for(let child of item.children)
    {
        getImportDirTotle(child)
    }
}

const deelImportDir = async(item,id)=>{
    if(item.name == '所有') //根目录不用创建
    {
        nDirCur.value++
        console.log('处理:'+item.name+'  '+nDirCur.value+'/'+nDirTotle.value)
        await sleep(10);

        for(let child of item.children)
        {
            await deelImportDir(child,id)
        }
    }
    else
    {
        const ret = await addCategApi({
            parent_id: id,
            name: item.name,
            note: ''
          })
        if (ret) {
            nDirCur.value++
            console.log('处理:'+item.name+'  '+nDirCur.value+'/'+nDirTotle.value)
            await sleep(10);

            for(let child of item.children)
            {
                await deelImportDir(child,ret.data.id)
            }
        }
    }


}
function sleep(ms) {
  // 返回一个 Promise，经过指定的时间(ms)后，Promise将被解决（resolve）
  return new Promise(resolve => setTimeout(resolve, ms));
}


const readFileContent = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      const result = event.target?.result as string;
      resolve(result);
    };

    reader.onerror = (event) => {
      reject(event.target?.error);
    };

    reader.readAsText(file);
  });
};

//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    storeData.splice(0,storeData.length,...res.data)
  }

}
//查默认仓库
const getDefaultStore = async() => {
    const ret = await getHashApi({
        name: '默认入库仓库',
        page: 1,
        count: 10000
    })
    if (ret) {
        if(ret.data.json != undefined)
        {
            默认委外入库仓库.value = ret.data.json.默认委外入库仓库
            默认采购入库仓库.value = ret.data.json.默认采购入库仓库
        }
        else
        {
            默认委外入库仓库.value = storeData[0].id
            默认委外入库仓库.value = storeData[0].id
        }
    }
}
const onChangeDefStore = async() => {
    const ret = await setHashApi({
    name: '默认入库仓库',
        json: {
            默认委外入库仓库: 默认委外入库仓库.value,
            默认采购入库仓库: 默认采购入库仓库.value
        }
    })
    if (ret) {
        getDefaultStore()
    }
}

const 默认委外入库仓库 = ref(-1)
const 默认采购入库仓库 = ref(-1)
</script>

<template>
    <div class="flex flex-wrap">      
        <el-card class="w-[300px]">
            <template #header>
            <div class="flex">
                <span>全局通知</span>
                <el-switch class="ml-5 "
                    v-model="notifyData.all"
                    inline-prompt
                    active-text="所有人"
                    inactive-text="指定人"
                />
                <ElButton v-show="!notifyData.all" class="ml-auto" type="success" @click="onSelNotifyUser">选人</ElButton>
            </div>
            </template>
            <div class="flex flex-wrap" >
                <ElTag class="mr-2 mb-2" v-for="item in notifyUsers" :key="item.id" >{{ item.name }}</ElTag>
            </div>
            <el-input class="mt-3" v-model="notifyData.note"  :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
            <ElButton class="mt-5 w-[50%]" type="primary" @click="onSendNotify">发送</ElButton>
        </el-card>
        <el-card class="w-[300px] ml-5">
            <template #header>
            <div class="flex">
                <div class="mr-5 w-[130px]">导入目录</div>
                <!-- <ElButton class="ml-auto" type="success" @click="onSelDirImportConfig">选择配置</ElButton> -->
                <input type="file" @change="onSelDirImportConfig" accept=".json" />
            </div>
            </template>
            <el-progress :percentage="parseFloat((nDirCur/nDirTotle*100).toFixed(2))" :status="'warning'"/>{{ parseFloat((nDirCur/nDirTotle*100).toFixed(2))+'%' }}
        </el-card>
        <el-card class="w-[300px] ml-5">
            <template #header>
            <div class="flex">
                <div class="mr-5 w-[130px]">默认仓库设置</div>
            </div>
            </template>
            <div>
                <div>
                    默认委外入库仓库:
                </div>
                <el-select v-model="默认委外入库仓库" placeholder="Select" >
                    <el-option v-for="item in storeData.filter(item => item.type === '良品库')" :key="item.id" :label="item.nick" :value="item.id" />
                </el-select>
            </div>
            <div>
                <div>
                    默认采购入库仓库:
                </div>
                <el-select v-model="默认采购入库仓库" placeholder="Select" >
                    <el-option v-for="item in storeData.filter(item => item.type === '良品库')" :key="item.id" :label="item.nick" :value="item.id" />
                </el-select>
            </div>
            <ElButton type="primary" @click="onChangeDefStore">修改</ELbutton>
        </el-card>
        <div class="h-[300px]"></div>          
    </div>

    <!-- 选择质检员 -->
    <DialogUser :param="''" v-model:show="showSelNotifyUser" :title="t('msg.selectUser')" @on-submit="onSelNotifyUserCallback"/>
</template>

<style lang="less" scoped>
:deep(.el-card__body)
{
    text-align: center;
}
</style>

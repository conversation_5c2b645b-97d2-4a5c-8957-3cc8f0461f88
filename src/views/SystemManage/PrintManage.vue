<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElSwitch, ElPopconfirm, ElButton, ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio, ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getCategListApi, addCategApi, updateCategApi, delCategApi, getProductListApi, updateProductApi, delProductApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { RightMenu } from '@/components/RightMenu'
import { Dialog } from '@/components/Dialog'
import { checkPermissionApi, decodeString } from '@/api/tool'
import { computed, nextTick } from 'vue';
import { useCache } from '@/hooks/web/useCache'
import { DialogProduct } from '@/components/DialogProduct';
import { DialogFileList } from "@/components/DialogFileList";
import { DialogBomStruct } from '@/components/DialogBomStruct'
import { getOssSignApi, ossUpload } from '@/api/oss';
import { useAppStore } from '@/store/modules/app'
import { importPdtApi } from '@/api/task'
import { getPrintTmpListApi, getPrintTmpInfoApi, delPrintTmpApi, updatePrintTmpApi } from '@/api/print'
import AddPrintTmp from '@/views/PrintManage/AddPrintTmp.vue'

const appStore = useAppStore()
const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
//定义搜索条件
const defaultCondition = {
    type: '',
    page: 1,
    count: 100
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})

//分类树
const categTree = ref()
//当前选中分类节点
const currentCatg = ref('')
//当前选中分类节点详细信息
const currentCatgData = ref({})
//分类树数据源
const categData = reactive(
    [
        {
            "name": "客户模板",
            "sub_categ": [
                {
                    "name": "个人客户"
                },
                {
                    "name": "公司客户"
                }
            ]
        },
        {
            "name": "销售模板",
            "sub_categ": [
                {
                    "name": "销售订单"
                },
                {
                    "name": "销售月结单"
                },
                {
                    "name": "销售出库单"
                },
                {
                    "name": "销售出库单明细"
                },
                {
                    "name": "销售入库单"
                },
                {
                    "name": "销售报表"
                },
                {
                    "name": "报价单"
                },
                {
                    "name": "报价单(精细)"
                },
                {
                    "name": "样品单"
                },
                {
                    "name": "租赁产品打印"
                }
            ]
        },
        {
            "name": "采购模板",
            "sub_categ": [
                {
                    "name": "采购订单"
                },
                {
                    "name": "采购月结单"
                },
                {
                    "name": "采购收货单"
                },
                {
                    "name": "采购入库"
                },
                {
                    "name": "采购入库明细"
                },
                {
                    "name": "采购退货出库单"
                },

            ]
        },
        {
            "name": "仓库模板",
            "sub_categ": [
                {
                    "name": "出库单"
                },
                {
                    "name": "入库单"
                },
                {
                    "name": "仓库调拨"
                },
                {
                    "name": "报废单"
                },
                {
                    "name": "仓位信息打印"
                },
                {
                    "name": "备货单打印"
                },
                {
                    "name": "盘点单"
                }
                ,
                {
                    "name": "领料单"
                }
                
            ]
        },
        {
            "name": "财务模板",
            "sub_categ": [
                {
                    "name": "收支明细"
                },
                {
                    "name": "财务凭证"
                },
                {
                    "name": "固定资产领用"
                },
                {
                    "name": "固定资产管理"
                },
                {
                    "name": "费用报销单"
                },
                {
                    "name": "差旅费报销单"
                },
                {
                    "name": "发票申请单"
                },
                {
                    "name": "已开发票单"
                },
                {
                    "name": "已收发票单"
                },
                {
                    "name": "付款申请单"
                },
                {
                    "name": "借款单"
                },
                {
                    "name": "备用金收支明细"
                }
            ]
        },
        {
            "name": "委外模板",
            "sub_categ": [
                {
                    "name": "委外单"
                },
                {
                    'name': '委外发料单'
                },
                {
                    'name': '委外领料单'
                },
                {
                    'name': '委外返料单'
                },
                {
                    'name': '委外退料单'
                },
                {
                    'name': '委外收货单'
                },
                {
                    'name': '委外质检单'
                },
                {
                    'name': '委外入库单'
                },
                {
                    'name': '委外退货单'
                }
            ]
        }
    ]


)
//分类树默认属性
const defaultProps = {
    children: 'sub_categ',
    label: 'name',
}
//分类树默认展开节点
const expandCateg = reactive([])
const printTmpData = reactive([])
//查询模板列表
const getPrintTmpList = async (page = 1) => {
    const ret = await getPrintTmpListApi(searchCondition)
    if (ret) {
        printTmpData.splice(0, printTmpData.length, ...ret.data)
    }
}

//处理表格对象操作
const handleOper = (type, item) => {
    //编辑产品
    if (type === 'edit' || type === 'info') {

    }

}

onMounted(() => {


})

//分类树点击左键
const leftClick = (data) => {
    console.log(data)
    searchCondition.type = data.name
    // currentCatg.value = data.id
    getPrintTmpList()
}


//--------------中间拖拽调整大小-------------
const isResizing = ref(false);
const startX = ref(0);
const leftWidth = ref(20);
const rightWidth = ref(80);

const startResize = (event: MouseEvent) => {
    event.preventDefault(); // 阻止默认行为
    isResizing.value = true;
    startX.value = event.clientX;

    document.addEventListener('mousemove', resize);
    document.addEventListener('mouseup', stopResize);
};

const resize = (event: MouseEvent) => {
    if (isResizing.value) {
        event.preventDefault(); // 阻止默认行为
        const deltaX = event.clientX - startX.value;
        leftWidth.value += (deltaX / window.innerWidth) * 100;
        rightWidth.value -= (deltaX / window.innerWidth) * 100;
        startX.value = event.clientX;
    }
};

const stopResize = () => {
    isResizing.value = false;
    document.removeEventListener('mousemove', resize);
    document.removeEventListener('mouseup', stopResize);
};


//新增,编辑模板
const curSelID = ref('')
const curCopyData = ref('') //拷贝内容
const bShowModifyPrintTmp = ref(false)
const onAddNewPrintTmp = () => {
    bShowModifyPrintTmp.value = true
    curSelID.value = ''
    curCopyData.value = ''
}
//编辑模板
const onEditTmp = (row) => {
    curSelID.value = row.id
    bShowModifyPrintTmp.value = true
}
//删除模板
const onDeleteTmp = async (row) => {
    const ret = await delPrintTmpApi({ "ids": [row.id] })
    if (ret) {
        getPrintTmpList()

        ElMessage({
            type: 'success',
            message: t('msg.delOK'),
        })
    }

}
//复制模板
const onCopyTmp = async (row) => {
    curSelID.value = ''
    bShowModifyPrintTmp.value = true
    const ret = await getPrintTmpInfoApi({
        id: row.id
    })
    if (ret) {
        curCopyData.value = ret.data
        bShowModifyPrintTmp.value = true
    }


}

//禁用模板
const onDisableTmp = async (event, row) => {
    // console.log(event)
    // return
    const ret = await updatePrintTmpApi({
        ids: [row.id],
        enable: event
    })
    if (ret) {
        getPrintTmpList()
        ElMessage.success('修改成功！')
    }
}

//切换默认模板
const onChangeDef = async (event, row) => {
    const ret = await updatePrintTmpApi({
        ids: [row.id],
        preset: event
    })
    if (ret) {
        getPrintTmpList()
        ElMessage.success('修改成功！')
    }
}

</script>

<template>
    <div ref="rootRef" class="flex absolute top-5 right-5 left-5 bottom-5">
        <!-- 左侧分类栏 -->
        <div class="bg-white p-2 overflow-y-auto" :style="{ width: leftWidth + '%' }">
            <el-tree class="h-[100%] overflow-x-scroll" ref="categTree" :data="categData" :props="defaultProps"
                :default-expanded-keys="expandCateg" node-key="id" @node-click="leftClick" highlight-current
                :current-node-key="currentCatg" :expand-on-click-node="false" :render-after-expand="true">
                <template #default="{ node }">
                    <Icon icon="bx:category" />
                    <div class="pl-2">{{ node.data.name }}
                    </div>
                </template>
            </el-tree>
        </div>
        <!-- <div class="w-2"></div> -->
        <div class="drag w-2  bg-gray-100 " style="cursor: col-resize;" @mousedown="startResize"></div>
        <!-- 右侧产品列表 -->
        <div class="relative !bg-white overflow-y-auto" :style="{ width: rightWidth + '%' }">
            <div class="absolute top-6 left-10 flex z-1">
                <ElButton class="mr-3" type="success" @click="onAddNewPrintTmp">
                    <Icon icon="fluent-mdl2:people-add" />
                    <div class="pl-2">{{ t('button.add') }}</div>
                </ElButton>
            </div>
            <div class="h-[100%] bg-white p-7" style="overflow-x:hidden;">
                <div class="text-center mb-5 font-bold">{{ t('product_manage.product_manage') }}</div>
                <el-table ref="tableRef" :data="printTmpData" style="width: 100%; margin-bottom: 20px" row-key="guuid"
                    border header-cell-class-name="tableHeader">
                    <el-table-column show-overflow-tooltip align="center" fixed prop="index" label="序号">
                        <template #default="scope">
                            {{ scope.$index }}
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip align="center" fixed prop="template" label="名称" />
                    <el-table-column show-overflow-tooltip align="center" fixed prop="type" label="类型" />
                    <el-table-column show-overflow-tooltip align="center" fixed prop="preset" label="是否默认">
                        <template #default="scope">
                            <el-checkbox v-model="scope.row.preset" label="" size="large" trueLabel="1" falseLabel="0"
                                @change="onChangeDef($event, scope.row)" />
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip align="center" fixed prop="enable" label="是否启用">
                        <template #default="scope">
                            <el-switch v-model="scope.row.enable" class="ml-2" inline-prompt
                                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949" active-text="是"
                                inactive-text="否" active-value="1" inactive-value="0"
                                @change="onDisableTmp($event, scope.row)" />
                        </template>
                    </el-table-column>
                    <el-table-column align="center" fixed label="操作" width="200px">
                        <template #default="scope">
                            <div class="flex">
                                <ElButton size="small" type="success" @click="onEditTmp(scope.row)">编辑</ElButton>
                                <ElButton size="small" type="warning" @click="onCopyTmp(scope.row)">复制</ElButton>
                                <el-popconfirm title="是否确认删除该模板?" @confirm="onDeleteTmp(scope.row)">
                                    <template #reference>
                                        <ElButton size="small" type="danger">删除</ElButton>
                                    </template>
                                </el-popconfirm>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- <div class="h-[300px]"></div> -->
            </div>

        </div>
        <AddPrintTmp v-model:show="bShowModifyPrintTmp" :id="curSelID" :type="searchCondition.type"
            :copydata="curCopyData" @on-submit="getPrintTmpList" />
    </div>
</template>

<style lang="less" scoped>
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
    background-color: #80e948;
}
</style>

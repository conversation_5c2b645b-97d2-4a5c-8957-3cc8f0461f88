<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { ElButton, ElTable, ElTableColumn, ElDivider, ElDescriptions, ElDescriptionsItem, ElInput, ElMessage, ElMessageBox, ElCheckbox } from 'element-plus'
import { ref, watch } from 'vue'
import { reactive,onMounted } from 'vue'
import { getDepartmentListApi, addDepApi, modifyDepApi, delDepApi, updateUserApi, delUserApi } from '@/api/usermanage'
import { Dialog } from '@/components/Dialog'
import { cloneDeep } from 'lodash-es'
import { DialogDept } from '@/components/DialogDept'
import { useRouter } from 'vue-router'
import { Infotip } from '@/components/Infotip'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
const { wsCache } = useCache()
const appStore = useAppStore()

const { push } = useRouter()
const { t } = useI18n()
const tableName = t('userTable.name')
const tableRole = t('userTable.role')
const tableStatus = t('userTable.status')
const tableTime = t('userTable.lastlogintime')
const loading = ref(false)
const showModifyDep = ref(false) //是否显示编辑部门对话框
const modifyDepTitle = ref('') //编辑部门对话框标题
const dlgSelDept = ref(null)
const showDeptSel = ref(false)
const curSelectUserParam = reactive([]) //当前选中需要操作的人

//部门树
const depData = reactive([])
//当前输入的部门名称
const inputDeptName = ref('')
//当前需要展开的部门树节点
const arrExpandRowKeys = reactive([])
//部门右键菜单
const menuDepartment = reactive([
  {
    icon: "streamline:interface-edit-write-2-change-document-edit-modify-paper-pencil-write-writing",
    name: 'modifyDep',
    title: t('userTable.modifyDep')
  },
  {
    icon: "icon-park-outline:add",
    name: 'addDep',
    title: t('userTable.addDep')
  },
  {
    icon: "fluent:delete-32-regular",
    name: 'delDep',
    title: t('userTable.delDep')
  }
])
//人员右键菜单
const menuUser = reactive([
  {
    icon: "streamline:interface-edit-write-2-change-document-edit-modify-paper-pencil-write-writing",
    name: 'modifyUser',
    title: t('userTable.modifyUser')
  },
  // {
  //   icon: "fluent:delete-32-regular",
  //   name: 'delUser',
  //   title: t('userTable.delUser')
  // },
  {
    icon: "system-uicons:move",
    name: 'moveUser',
    title: t('userTable.moveUser')
  }
])
//人员批量菜单
const menuUserMulti = reactive([
  // {
  //   icon: "fluent:delete-32-regular",
  //   name: 'delUser',
  //   title: t('userTable.delUserMul')
  // },
  // {
  //   icon: "system-uicons:move",
  //   name: 'moveUserMul',
  //   title: t('userTable.moveUserMul')
  // }
])

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}
//设置当前选中行
const tableRef = ref<InstanceType<typeof ElTable>>()
const setCurrent = (row) => {
  tableRef.value!.setCurrentRow(row)
}

//打开右键菜单
const rightClick = (row, column, event) => {
  //隐藏之前的菜单
  hideRightMenu()
  //更新当前选中行
  setCurrent(row)
  //查询是否触发多选
  const arrSel = getAllSelectUser()
  console.log(arrSel)

  //如果是未分组人员行不允许弹出菜单
  if (row.type == 'dept' && (row.name == '未分组人员' && arrSel.length == 0)) {
    event.preventDefault();
    return
  }
  let menuclass = row.type == 'dept' ? '#contextmenu' : '#contextmenuUser'
  //如果是多选则肯定是批量处理人员
  if (arrSel.length > 0) {
    menuclass = '#contextmenuUserMul'
  }

  const menu = document.querySelector(menuclass) as HTMLElement;
  if (!menu) return;

  event.preventDefault();
  menu.style.left = `${event.pageX - 200}px`;
  menu.style.top = `${event.pageY - 180}px`;
  menu.style.display = "block";
  menu.style.zIndex = "1000";

  document.addEventListener("click", hideRightMenu);
}

//隐藏右键菜单
const hideRightMenu = () => {
  const menu = document.querySelector("#contextmenu") as HTMLElement;
  menu!.style.display = "none";
  const menu2 = document.querySelector("#contextmenuUser") as HTMLElement;
  menu2!.style.display = "none";
  const menu3 = document.querySelector("#contextmenuUserMul") as HTMLElement;
  menu3!.style.display = "none";

  document.removeEventListener("click", hideRightMenu);
}

//获取所有选中的人员
const getAllSelectUser = () => {
  //遍历depData
  let arrSel = []
  for (let one of depData) {
    getSelectUser(one, arrSel)
  }
  return arrSel
}
const getSelectUser = (item, arraySel) => {
  if (item.check && item.type == 'user') {
    arraySel.push(item.username)
    // console.log(item)
  }
  //遍历children
  if (item.children) {
    for (let one of item.children) {
      getSelectUser(one, arraySel)
    }
  }
}

//处理菜单响应
const onMenuButton = async (menu) => {
  console.log(menu.name)
  hideRightMenu()
  if (!currentRow.value)
    return
  if (menu.name == 'addDep') {
    showModifyDep.value = true
    modifyDepTitle.value = t('userTable.addDep')
    //复位
    inputDeptName.value = ""
  }
  else if (menu.name === 'modifyDep') {
    showModifyDep.value = true
    modifyDepTitle.value = t('userTable.modifyDep')
    inputDeptName.value = currentRow.value.name
  }
  else if (menu.name === 'delDep') {

    ElMessageBox.confirm(
      t('msg.delDeptMsg'),
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {
        //删除组织
        const ret = await delDepApi({
          ids: [currentRow.value.id]
        })
        if (ret) {
          ElMessage({
            type: 'success',
            message: t('msg.success'),
          })
          getDepartmentTreeInfo()
        }

      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })

  }
  else if (menu.name === 'addUser') {

  }
  else if (menu.name === 'moveUser') //移动单个人员
  {
    const info = wsCache.get(appStore.getUserInfo)
    if(info.id == currentRow.value.id){
      ElMessage({
        type:'warning',
        message: '不能移动自己',
      })
      return
    }

    showDeptSel.value = true
    curSelectUserParam.splice(0, curSelectUserParam.length, currentRow.value.username)
  }
  else if (menu.name == 'moveUserMul') //批量移动人员
  {
    showDeptSel.value = true
    //拿到所有选中的人
    const arrSel = getAllSelectUser()
    if (arrSel.length <= 0) {
      ElMessage({
        type: 'warning',
        message: t('msg.selectUser'),
      })
      return
    }
    const info = wsCache.get(appStore.getUserInfo)
    //不能删除自己,如果有则去掉自己
    if(arrSel.includes(info.id+'')){
      arrSel.splice(arrSel.indexOf(info.id+''),1)
    }
    console.log(arrSel)
    return
    curSelectUserParam.splice(0, curSelectUserParam.length, ...arrSel)

  }
  else if (menu.name === 'delUser') {
    //拿到所有选中的人
    const arrSel = getAllSelectUser()
    if (arrSel.length <= 0) {
      if (currentRow.value != undefined) {
        arrSel.push(currentRow.value.id)
      }
      else {
        ElMessage({
          type: 'warning',
          message: t('msg.selectUser'),
        })
        return
      }
    }

    const info = wsCache.get(appStore.getUserInfo)
    //不能删除自己,如果有则去掉自己
    if(arrSel.includes(info.id+'')){
      arrSel.splice(arrSel.indexOf(info.id+''),1)
    }
    console.log(arrSel)
    
    //删除用户
    ElMessageBox.confirm(t('msg.delUser')+arrSel.length+t('msg.person'), t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'warning'
    }).then(async () => {
      const ret = await delUserApi({
        "ids": arrSel
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.delOK'),
        })
        getDepartmentTreeInfo()
      }
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: t('msg.delChannel'),
      })
    })
    return

  }
  else if(menu.name === 'modifyUser') //修改人员
  {
    push({
      path: '/usermanage/adduser',
      query: {
        id:currentRow.value.id,
        src:'/usermanage/deptmanage'
      }
    })  
  }
}


//设置表头class
const setHeadClassName = (row, rowindex) => {
  return "tableHeader"
}

//查询组织信息
const getDepartmentTreeInfo = async () => {
  const ret = await getDepartmentListApi({})
  console.log(ret)
  if (ret) {
    let modifyData = cloneDeep(ret.data.all_depts)

    //需要展开的列表先清空
    arrExpandRowKeys.splice(0, arrExpandRowKeys.length)

    for (let one of modifyData) {
      await modifyDepData(one)
      arrExpandRowKeys.push(one.guuid)
    }

    depData.splice(0, depData.length, ...modifyData);
    console.log(depData)
    //当前节点展开
    if (currentRow.value) {
      arrExpandRowKeys.push(currentRow.value.guuid)
    }
  }
}

//根据服务器数据构造界面数据
const modifyDepData = async (data) => {
  data.check = false
  if (data.hasOwnProperty('login_time')) {
    data.type = 'user';
    return;
  }
  data.type = 'dept';

  data.children = [...data.sub_dept, ...data.sub_user];

  delete data.sub_dept;
  delete data.sub_user;
  for (let item of data.children) {
    await modifyDepData(item);
  }
}



//保存部门修改
const onClickSaveDep = async () => {
  console.log(modifyDepTitle.value)
  if (!currentRow.value) {
    return
  }
  if (inputDeptName.value.trim() === "") {
    ElMessage({
      type: 'error',
      message: t('msg.noDepName'),
    })
    return
  }

  console.log(currentRow)
  if (modifyDepTitle.value === t('userTable.addDep')) {
    const ret = await addDepApi({
      parent_id: currentRow.value.id,
      name: inputDeptName.value,
      note: ''
    })
    if (ret) {
      ElMessage({
        type: 'success',
        message: t('msg.success'),
      })
      showModifyDep.value = false
      getDepartmentTreeInfo()

    }
  }
  else if (modifyDepTitle.value === t('userTable.modifyDep')) {
    console.log('进入修改')
    const ret = await modifyDepApi({
      ids: [currentRow.value.id],
      name: inputDeptName.value,
      note: ''
    })
    if (ret) {
      ElMessage({
        type: 'success',
        message: t('msg.success'),
      })
      showModifyDep.value = false
      getDepartmentTreeInfo()
    }
  }
}

//勾选发生变化
const onSelChange = (value, item) => {
  if (item.row.type === 'dept') //如果是部门节点则需要自动选中他下面所有人员
  {
    modifyCheckAuto(item.row, value)
  }

}
//批量勾选处理部门节点下所有用户
const modifyCheckAuto = (row, value) => {
  if (!value) {
    row.check = value
  }
  else {
    if (row.total_user_count > 0 || row.type == 'user') {
      row.check = value
    }
  }

  //遍历子节点children递归调用自己来修改value
  if (row.children) {
    row.children.forEach(item => {
      modifyCheckAuto(item, value)
    })
  }
}

//移动指定用户到指定部门
const onMoveUser = async (depts, users) => {
  for(let one of users) {
      const ret = await updateUserApi({
      "username": one,
      "depts": depts
    })
    if (ret) {
      ElMessage({
        type: 'success',
        message: t('msg.success'),
      })
      
    }
  }
  getDepartmentTreeInfo()
}

//--------------------------------启动逻辑----------------------------------
getDepartmentTreeInfo()

onMounted(() => {
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})

</script>

<template>
  <ContentWrap :title="t('userManage.deptManage')">
    <Infotip
      :show-index="false"
      :title="'团队设置'"
      :schema="[
        {
          label: '请在列表中单击鼠标右键，在弹出菜单中可新增、删除、修改团队信息!',
          keys: ['鼠标右键']
        },
      ]"
    />


    <el-table ref="tableRef" :data="depData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border stripe
      :expand-row-keys="arrExpandRowKeys" highlight-current-row @current-change="handleCurrentChange"
      @row-contextmenu="rightClick" :header-cell-class-name="setHeadClassName">
      <el-table-column :label="tableName" min-width="60%">
        <template #default="scope">
          <el-checkbox v-model="scope.row.check" size="small" class="!mr-2" @change="onSelChange($event, scope)" />
          <Icon :icon="scope.row.type == 'dept' ? 'octicon:organization-16' : 'teenyicons:user-outline'"
            :class="scope.row.name ? 'iconDept' : 'iconUser'" />
          <span style="margin-left: 10px">{{ scope.row.name ? (scope.row.name +
            " (" + scope.row.total_user_count + "人)") : "("+scope.row.username+") "+scope.row.resident_name }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" min-width="10%" prop="roles_name" :label="tableRole" />
      <el-table-column align="center" min-width="10%" prop="emp_status" :label="tableStatus" />
      <el-table-column align="center" width="200px" prop="login_time" :label="tableTime" />
    </el-table>

  </ContentWrap>

  <!-- 部门右键菜单 -->
  <div id="contextmenu" v-show="false" class="menu font-bold">
    <div v-for="(item, index) in menuDepartment" :key="index" class="contextmenu_item divide-x-1"
      @click="onMenuButton(item)">
      <Icon v-show="item.name == '分割线' ? false : true" :icon="item.icon" />
      <div v-show="item.name == '分割线' ? false : true" class="ml-1 pl-3">{{ item.title }}</div>
      <el-divider class="!m-1" v-show="item.name == '分割线' ? true : false" />
    </div>
  </div>
  <!-- 人员右键菜单 -->
  <div id="contextmenuUser" v-show="false" class="menu font-bold">
    <div v-for="(item, index) in menuUser" :key="index" class="contextmenu_item divide-x-1" @click="onMenuButton(item)">
      <Icon v-show="item.name == '分割线' ? false : true" :icon="item.icon" />
      <div v-show="item.name == '分割线' ? false : true" class="ml-1 pl-3">{{ item.title }}</div>
      <el-divider class="!m-1" v-show="item.name == '分割线' ? true : false" />
    </div>
  </div>
  <!-- 人员批量处理右键菜单 -->
  <div id="contextmenuUserMul" v-show="false" class="menu font-bold">
    <div v-for="(item, index) in menuUserMulti" :key="index" class="contextmenu_item divide-x-1"
      @click="onMenuButton(item)">
      <Icon v-show="item.name == '分割线' ? false : true" :icon="item.icon" />
      <div v-show="item.name == '分割线' ? false : true" class="ml-1 pl-3">{{ item.title }}</div>
      <el-divider class="!m-1" v-show="item.name == '分割线' ? true : false" />
    </div>
  </div>

  <!-- 编辑部门弹窗 -->
  <Dialog v-if="showModifyDep" v-model="showModifyDep" :title="modifyDepTitle" :max-height="100">
    <el-descriptions class="flex-1" :column="2" border>
      <el-descriptions-item :label="t('modifyDept.depname')" class="flex">
        <el-input v-model="inputDeptName" clearable />
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <ElButton type="primary" :loading="loading" @click="onClickSaveDep">
        {{ t('common.save') }}
      </ElButton>
      <ElButton @click="showModifyDep = false">{{ t('common.channel') }}</ElButton>
    </template>
  </Dialog>

  <!-- 选择部门 -->
  <DialogDept :show="showDeptSel" :title="t('userDemo.index')" :depData="depData" :param="curSelectUserParam"
    v-model:showHide="showDeptSel" @onSubmit="onMoveUser" />
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #ffe48d !important;
}

//修改选中时的颜色，取消hover时的颜色变化
:deep(.el-table) {
  --el-table-current-row-bg-color: #ffe48d;
  --el-table-row-hover-bg-color: unset;
}

:deep(.tableHeader) {
  background-color: #73b0e8 !important;
  color: #fff;
  // font-weight: 400;
}

// :deep(.el-table__cell){
//   background-color:#66b1ff;
// }

.menu {
  position: absolute;
  background-color: #fff;
  width: 130px;
  /*height: 106px;*/
  font-size: 12px;
  color: #444040;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  white-space: nowrap;
  z-index: 1000;
}

.contextmenu_item {
  display: flex;
  line-height: 34px;
  text-align: center;
  align-items: center;
  justify-content: left;
  padding-left: 10px;
}

// .contextmenu_item:not(:last-child) {
//   border-bottom: 1px solid rgba(0, 0, 0, 0.1);
// }

.contextmenu_item:hover {
  cursor: pointer;
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}

.iconDept {
  color: #66b1ff;
}

.iconUser {
  color: #66b1ff;
}

:deep(.el-table .el-table_cell) {
  height: 22px !important;
}
</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDatePicker,ElButton, ElTreeSelect, ElInput, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessageBox, ElMessage } from 'element-plus';
import { reactive, ref, onMounted, onBeforeUnmount,unref } from 'vue'
import { useEmitt } from '@/hooks/web/useEmitt'
import { useRouter } from 'vue-router'
import { setCanlogin,getUserListApi, delUserApi,getDepartmentListApi,getRoleListApi, updateUserApi } from '@/api/usermanage'
import { cloneDeep } from 'lodash-es';

import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
const { wsCache } = useCache()
const appStore = useAppStore()
const { push,currentRoute } = useRouter()
const { t } = useI18n()

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//定义搜索条件
const defaultCondition = {
  username:'',
  resident_name:'',
  dept:'',
  role:'',
  job:'',
  emp_status: '在职',
  登录日期: ['', ''],
  用户类型: '',
  page:1,
  count: 15,
  id:''
  // ids:[],
}
const searchCondition = reactive({
  ...defaultCondition,
  reset(){
    Object.assign(this,defaultCondition)
  }
})


//职位 
const jobLiST = reactive(
    [  
    "设计",  
    "财务",  
    "出纳",  
    "会计",  
    "工程文员",  
    "员工",  
    "PMC",  
    "业务助理",  
    "总经理",  
    "总经理助理",  
    "经理",  
    "总监",  
    "主管",  
    "包装组长",  
    "组长",  
    "司机",  
    "行政",  
    "人事",  
    "保安",  
    "保洁员",  
    "网管",  
    "电工",  
    "助理",  
    "行政经理",  
    "行政主管",  
    "业务经理",  
    "业务主管",  
    "业务员",  
    "业务助理",  
    "工人"  
    ]
)

//部门数据源
const deptData = reactive([])

//角色数据源
const roleData = reactive([])

//用户数据缓存
const userData = reactive([])

//新增用户
const onAddUser = () => {
  console.log(currentRoute.value.name)
  if(currentRoute.value.name === 'usermanage')
    push('/usermanage/adduser')
  else
    push('/humanmanage/adduser')
}

//处理菜单消息
const handleOper = async(info, scope) => {

  if (info == "edit" || info == "info") {
    let path = currentRoute.value.name === 'usermanage'?'/usermanage/adduser':'/humanmanage/adduser'
    push({
      path: path,
      query: {
        id:scope.row.id,
        type:info
      }
    })  
  }
  else if (info == 'del') {
    const info = wsCache.get(appStore.getUserInfo)
    if(info.id == scope.row.id){
      ElMessage({
        type:'warning',
        message: '不能删除自己',
      })
      return
    }
    ElMessageBox.confirm(
      t('msg.delUserMsg'),
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        const ret = await delUserApi({ 'username':scope.row.username })
        if (ret) {
          getUserList()

          ElMessage({
            type: 'success',
            message: t('msg.delOK'),
          })
        }
        //不直接删除用户，只改状态
        // const ret = await setCanlogin({ 'username':scope.row.username, 'is_ban':2 })
        // if (ret) {
        //   getUserList()

        //   ElMessage({
        //     type: 'success',
        //     message: t('msg.optOK'),
        //   })
        // }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })
  }
  else if(info === 'nologin' || info === 'canlogin')
  {
    let msg = info==='nologin'?t('msg.noLogin'):t('msg.canLogin')
    const is_ban = info==='nologin'?1:0
    ElMessageBox.confirm(
      msg,
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        const ret = await setCanlogin({ 'username':scope.row.username, 'is_ban':is_ban })
        if (ret) {
          getUserList()

          ElMessage({
            type: 'success',
            message: t('msg.optOK'),
          })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.optFail'),
        })
      })
  }
  else if (info === 'zhuanzheng')
  {
    ElMessageBox.confirm(
      '请确认是否将该试用员工转正？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        let tmp = cloneDeep(scope.row)
        tmp.ids = [tmp.id]
        tmp.type = '正式员工'
        console.log('-----------',tmp)
        const ret = await updateUserApi(tmp)
        if (ret) {
          getUserList()

          ElMessage({
            type: 'success',
            message: '员工转正成功！',
          })
        }


      })
      .catch(() => {
        ElMessage
        ({
          type: 'info',
          message: t('msg.optFail'),
        })
      })
  
  }
}

useEmitt({
  name: 'testmsg',
  callback: (type: string) => {
    console.log("有通知进来" + type)
  }
})

//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 200
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()

}


const usertype = ref('')

let timmer
// 在组件挂载时开始监听窗口大小变化
onMounted(() => {
  if (currentRoute.value.name === 'HumanUserManageZZ') {
    searchCondition.emp_status = '在职'
    usertype.value = '(在职)'
  }
  if (currentRoute.value.name === 'HumanUserManageLZ') {
    searchCondition.emp_status = '离职'
    usertype.value = '(离职)'
  }

  
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);
  //获取用户列表
  getUserList()
  //获取部门列表
  // getDepartmentTreeInfo()
  //获取角色信息
  getRoleList()

  //启动定时刷新
  // timmer = setInterval(timmerFun, 1000);

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
});
// 在组件销毁前移除窗口大小变化的监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize);
  clearInterval(timmer)
});

//条件查询
const onSearch = async ()=>{
  getUserList()
}
//清除条件
const onClear = ()=>{
  searchCondition.reset()
}

let curPage = 1
const timmerFun = ()=>{
  getUserList(curPage)
}
//查询用户列表
const getUserList = async (page = 1) => {
  console.log('进入查询')
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  if (searchCondition.emp_status == '全部') {
    tmp.emp_status = ''
  }
  tmp.登录日期 = searchCondition.登录日期[0] + ',' + searchCondition.登录日期[1]

  //默认只能查自己部门下的用户
  const info = wsCache.get(appStore.getUserInfo)

  tmp.dept = info.depts[0]
  
  const ret = await getUserListApi(tmp);

  if (ret) {
    userData.splice(0, userData.length, ...ret.data);
    totleCount.value =  parseInt(ret.count)
  }
}

//查询组织信息
const getDepartmentTreeInfo = async () => {
  const ret = await getDepartmentListApi({})
  console.log(ret)
  if (ret) {

    //deptData.splice(0, deptData.length, ...data)
    deptData.splice(0, deptData.length, ...ret.data.all_depts)
    console.log(deptData.length)
  }
}

//查询角色信息
const getRoleList = async () => {
  const res = await getRoleListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    roleData.splice(0,roleData.length,...res.data)
  }

}


//page控件发生切换
const handleSizeChange = (val: number) => {
  getUserList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  curPage = val
  getUserList(val)
}


</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
    <div class="absolute top-3 left-10">
      <ElButton type="success" @click="onAddUser">
        <Icon icon="fluent-mdl2:people-add" />
        <div class="pl-2">{{ t('button.add') }}</div>
      </ElButton>
      <!-- <ElButton type="primary" plain>
        <Icon icon="clarity:import-line" />
        <div class="pl-2">{{ t('button.import') }}</div>
      </ElButton>
      <ElButton type="primary" plain>
        <Icon icon="carbon:export" />
        <div class="pl-2">{{ t('button.export') }}</div>
      </ElButton> -->
    </div>
    <div :title="t('userManage.userManage')" class="h-[100%] bg-white p-1">
      <div class="text-center mb-5 font-bold">{{ t('userManage.userManage')+usertype}}</div>
      <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-2 pl-5 pr-5 pb-1 mb-2 bg-light-200">
        <div class="inline-flex items-center mr-5">
          <div class="searchTitle">用户称呼</div>
          <el-input size="small" class="searchItem" v-model="searchCondition.resident_name" placeholder="" />
        </div>
        <div class="inline-flex items-center  mr-5">
          <div class="searchTitle">用户编号</div>
          <el-input size="small" class="searchItem" v-model="searchCondition.username" placeholder="" />
        </div>
        <div class="inline-flex items-center  mr-5">
          <div class="searchTitle">用户角色</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.role" placeholder="请选择"  >
            <el-option v-for="item in roleData" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </div>

        <div  class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">最后登录</div>
          <el-date-picker size="small"  class="searchItem" v-model="searchCondition.登录日期" type="daterange" range-separator="To"
            start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
        <div class="flex justify-end items-center mr-6 mt-2">
          <ElButton type="primary" @click="onSearch">
            <Icon icon="tabler:search" />
            <div class="pl-2">查询</div>
          </ElButton>
          <ElButton type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="pl-2">清除</div>
          </ElButton>
        </div>
      </div>

      <el-table ref="userTableRef" header-cell-class-name1="tableHeader" :data="userData" style="width: 100%"
        :height11="tableHeight" border stripe>
        <el-table-column show-overflow-tooltip fixed prop="id" :label="t('userTable.id')" width="60" />
        <el-table-column show-overflow-tooltip fixed prop="username" :label="t('userTable.userid')" />
        <el-table-column show-overflow-tooltip fixed prop="resident_name" :label="t('userTable.username')" >
          <template #default="scope">
            <div class="nameStyle" @click="handleOper('info', scope)">{{ scope.row.resident_name }}</div>
          </template>
        </el-table-column>

        <el-table-column align="center" show-overflow-tooltip prop="roles_name" :label="t('userTable.role')" />
        <el-table-column align="center" show-overflow-tooltip prop="is_ban" :label="t('userTable.status')"  >
          <template #default="scope">
          {{ scope.row.is_ban === 1 ? '禁用': '正常'}}
          </template>
        </el-table-column>
        <el-table-column align="center" show-overflow-tooltip prop="is_ban" :label="'在线'+t('userTable.status')"  >
          <template #default="scope">
          {{ scope.row.is_online === 1 ? '在线': '离线'}}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="last_time" label="最后在线时间" />
       
        <el-table-column fixed="right" :label="t('userTable.operate')" width="90">
          <template #default="scope">
            <el-dropdown trigger="click" placement="left">
              <span class="el-dropdown-link">
                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleOper('info', scope)">{{ t('userOpt.detail') }}</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('edit', scope)">{{ t('userOpt.edit') }}</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('del', scope)">{{ t('userOpt.del') }}</el-dropdown-item>
                  <el-dropdown-item :disabled="scope.row.is_ban == 1" @click="handleOper('nologin', scope)">{{ t('userOpt.nologin') }}</el-dropdown-item>
                  <el-dropdown-item :disabled="scope.row.is_ban  == 0" @click="handleOper('canlogin', scope)">{{ t('userOpt.canlogin') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination class="flex justify-end mt-2"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[15, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
    </div>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
} 
.searchItem{
  width: 200px;
}
</style>

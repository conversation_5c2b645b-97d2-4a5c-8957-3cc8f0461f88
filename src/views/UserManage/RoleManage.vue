<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { ElDropdown,ElDropdownItem,ElDropdownMenu ,ElButton, ElTable, ElTableColumn,ElTag, ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getRoleListApi,delRoleApi, updateUserApi } from '@/api/usermanage'
import { DialogUser } from '@/components/DialogUser'

const { push,currentRoute } = useRouter()
const { t } = useI18n()
//权限数据源
const roleData = reactive([])


//新增角色
const onAddRole = () => {
  console.log(currentRoute.value.name)
  if(currentRoute.value.name === 'RoleManage')
    push({ path: '/usermanage/addrole' })
  else
    push({ path: '/humanmanage/addrole' })
}

//获取角色数据
const getRoleList = async () => {
  const res = await getRoleListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    roleData.splice(0,roleData.length,...res.data)
  }

}

//编辑角色
const onEditRole = (row) => {
  let path = currentRoute.value.name === 'RoleManage' ? '/usermanage/addrole' : '/humanmanage/addrole'
  console.log(row)
  //进入编辑页面
  push({
    path: path,
    query: {
      id: row.id
    }
  })
}

//编辑角色数据权限
const onEditRoleData = (row) => {
  let path = currentRoute.value.name === 'RoleManage' ? '/usermanage/addroledata' : '/humanmanage/addroledata'
  console.log(row)
  //进入数据权限编辑页面
  push({
    path:path,
    query: {
      id: row.id
    }
  })
}


//删除角色
const onDelRole = async(row)=>{
    //删除用户
    ElMessageBox.confirm(t('msg.delRole')+row.name, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'warning'
    }).then(async () => {
      const ret  = await delRoleApi({ "ids": [row.id] })
      if(ret)
      {
        //提示删除成功
        ElMessage.success(t('msg.delOK'))
        getRoleList()
      }
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: t('msg.delChannel'),
      })
    })






}

onMounted(() => {
  getRoleList()
})


const lastRole = ref({})
//显示隐藏选择员窗口变量
const showAddUser = ref(false)
//显示选择销售员弹窗
const onAddUser = (row)=>{
  showAddUser.value = true
  lastRole.value = row
}
//选择销售员回调
const onSelSaleCallback = async(id,name,data,user)=>{
    console.log(id,name,user,lastRole.value)
    if(user.roles.includes(lastRole.value.id))
    {
      ElMessage.error('该用户已在该角色中')
      return
    }
    
  //设置用户
  const ret = await updateUserApi({
    ids:[id],
    roles:[...user.roles,lastRole.value.id]
  })
  if (ret) {
      console.log(ret)
      getRoleList()
  }
}

</script>

<template>
  <ContentWrap :title="t('userManage.roleManage')">
    <div class="mb-2">
      <ElButton type="success" @click="onAddRole">
        <Icon icon="fluent-mdl2:people-add" />
        <div class="pl-2">新增</div>
      </ElButton>
      <ElButton color="#409EFF" plain>
        <Icon icon="clarity:import-line" />
        <div class="pl-2">导入</div>
      </ElButton>
      <ElButton color="#409EFF" plain>
        <Icon icon="carbon:export" />
        <div class="pl-2">导出</div>
      </ElButton>
    </div>
    <el-table ref="tableRef" :data="roleData" style="width: 100%;min-height: 60vh;" row-key="id" border stripe
       header-cell-class-name="tableHeader">
      <el-table-column :label="t('roleTable.id')" prop="id" width="100px" />
      <el-table-column align="center" width="150px" prop="name" :label="t('roleTable.name')" />
      <el-table-column align="center" min-width="10%" prop="users_name" :label="t('roleTable.owner')" >
        <template #default="scope">
          <!-- <el-tag>{{ scope.row.users_name }}</el-tag> -->
          <el-tag class="mr-1 mb-2" effect="dark" color="#409EFF" v-for="item in scope.row.users_name" :key="item">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" width="100px" prop="name" :label="t('roleTable.opt')" >
        <template #default="scope">
          <!-- <ElButton @click="onEditRole(scope.row)">{{ t('userOpt.edit') }}</ElButton> -->
          <el-dropdown trigger="click" placement="bottom">
            <span class="el-dropdown-link">
              <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
            </span>
            <template #dropdown>
              <el-dropdown-menu>     
                <el-dropdown-item @click="onAddUser(scope.row)">添加员工到角色</el-dropdown-item>                   
                <el-dropdown-item @click="onEditRole(scope.row)">功能权限设置</el-dropdown-item>
                <el-dropdown-item @click="onEditRoleData(scope.row)">数据权限设置</el-dropdown-item>
                <el-dropdown-item @click="onDelRole(scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <DialogUser :param="''" v-model:show="showAddUser" :title="t('msg.selectUser')" @on-submit="onSelSaleCallback"/>
  </ContentWrap>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }
</style>

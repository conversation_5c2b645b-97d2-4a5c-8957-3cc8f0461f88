<script setup lang="ts">
import { ElForm,ElFormItem, ElButton,ElTag, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getProcessListApi,delProcessApi,updateProcessApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useCache } from '@/hooks/web/useCache'
import { DialogProcess } from '@/components/DialogProcess'
import { Dialog } from '@/components/Dialog'
import { DialogUser } from '@/components/DialogUser';

const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

//工序数据源
const processData = reactive([])
//当前操作的工序对象
const curOptProcessData = ref({})
//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const tableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  nick:'',
  name:'',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)
//显示人员管理窗口
const showPeopleMgr = ref(false)

//开始查询
const onSearch = () => {
  getProcessList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (tableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 300
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getProcessList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getProcessList(val)
}
//处理表格对象操作
const handleOper = (type, item) => {
  //编辑产品
  curOptProcessData.value = item
  if(type === 'edit' || type === 'info')
  {
    console.log(item)
    showProcessDialog.value = true    
  }
 
  else if(type === 'del') 
  {
    ElMessageBox.confirm(t('msg.confirm_del_process')+'--> '+item.nick, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const ret = await delProcessApi({
        ids: [item.id],
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })

        getProcessList()
      }
    }
    ).catch(() => {})
  }
  else if(type === 'peple_mgr')
  {
    showPeopleMgr.value = true
  }
}

//显示隐藏工艺编辑窗口
const showProcessDialog = ref(false)
//进入新增产品界面
const onAddNew = ()=>{
  showProcessDialog.value = true
  curOptProcessData.value = {}
}

//查询工序列表
const getProcessList = async (page = 1) => {
  const ret = await getProcessListApi(searchCondition)
  if(ret)
  {
    processData.splice(0,processData.length, ...ret.data)
    console.log(ret)
    console.log(processData)
    totleCount.value = parseInt(ret.count)
  }
}

//显示隐藏人员选择窗口
const showPeopleSel = ref(false)
//发起类型
const selType = ref('')
//新增负责人
const onAddManager = (type)=>{
  showPeopleSel.value = true
  selType.value = type
}

//人员选择完成
const onSelPeopleOK = (id,name,param)=>{
  console.log(id,name,param)
  if(param === '负责人')
  {
    funAddPeople(curOptProcessData.value.header_man,curOptProcessData.value.header_name,id,name)
  }
  else if(param === '操作员')
  {
    funAddPeople(curOptProcessData.value.operator_man,curOptProcessData.value.operator_name,id,name)
  }
  else if(param === '质检员')
  {
    funAddPeople(curOptProcessData.value.checker_man,curOptProcessData.value.checker_name,id,name)
  }

  console.log('----',curOptProcessData.value)
}

//添加人员到数组
const funAddPeople = (arrID,arrName,id,name)=>{
  //检测是否已经存在
  for(let i=0;i<arrID.length;i++)
  {
    if(arrID[i] == id)
    {
      return
    }
  }
  //新增
  arrID.push(id)
  arrName.push(name)
}
//删除数组人员
const funDelPeople = (arrID,arrName,id)=>{
  for(let i=0;i<arrID.length;i++)
  {
    if(arrID[i] == id)
    {
      arrID.splice(i,1)
      arrName.splice(i,1)
      return
    }
  }
}

//修改选中行
const onUpdateSel = async()=>{
  const ret = await updateProcessApi(curOptProcessData.value)
  if(ret)
  {
    getProcessList()
    showPeopleMgr.value = false
    //提示操作成功
    ElMessage.success({
      message: '操作成功',
      center: true
    })
  }
}

onMounted(() => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //更新产品列表
  getProcessList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});

</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] flex">    
    <div class="relative w-[100%] !bg-white">
      <div class="absolute top-6 left-10">
        <ElButton type="success" @click="onAddNew">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
        <ElButton color="#409EFF" plain>
          <Icon icon="clarity:import-line" />
          <div class="pl-2">{{ t('button.import') }}</div>
        </ElButton>
        <ElButton color="#409EFF" plain>
          <Icon icon="carbon:export" />
          <div class="pl-2">{{ t('button.export') }}</div>
        </ElButton>
      </div>
      <div class="h-[100%] bg-white p-7">
        <div class="text-center mb-5 font-bold">{{ t('project_manage.process_manage') }}</div>
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
          <!-- 检索条件 -->
          <div class="inline-flex items-center mr-12">
            <div class="searchTitle">{{ t('process.name') }}</div>
            <el-input v-model="searchCondition.name" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-12">
            <div class="searchTitle">{{ t('process.nick') }}</div>
            <el-input v-model="searchCondition.nick" placeholder="" class="searchItem" />
          </div>


          <div  class="mt-5 mb-2 flex items-center justify-end">
            <ElButton type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- process列表 -->
        <el-table ref="tableRef" header-cell-class-name="tableHeader" :data="processData" style="width: 100%" 
          :height="tableHeight" border stripe>
          <el-table-column show-overflow-tooltip  prop="name" :label="t('process.name')" width="100" />
          <el-table-column show-overflow-tooltip  prop="nick" :label="t('process.nick')" width="150" >
            <template #default="scope">
              <div class="nameStyle" @click="handleOper('info', scope.row)">{{ scope.row.nick }}</div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip  prop="work_hours" :label="t('process.work_hours')" width="150" />
          <el-table-column show-overflow-tooltip  prop="desc_txt" :label="t('process.tech')" width="150" />
          <el-table-column show-overflow-tooltip  prop="people" :label="t('process.peple')" >
            <template #default="scope">
              <div>
                <div class="flex" v-if="scope.row.header_name.length>0">
                  <div class="mr-1">
                    {{ t('process.manager') +':'}}
                  </div>
                  <div>
                    <el-tag v-for="(item, index) in scope.row.header_name" :key="index" >{{ item }}</el-tag>
                  </div>
                </div>
                <div class="flex" v-if="scope.row.operator_name.length>0">
                  <div class="mr-1">
                    {{ t('process.opter') +':'}}
                  </div>
                  <div>
                    <el-tag v-for="(item, index) in scope.row.operator_name" :key="index" >{{ item }}</el-tag>
                  </div>
                </div>
                <div class="flex" v-if="scope.row.checker_name.length>0">
                  <div class="mr-1">
                    {{ t('process.checker') +':'}}
                  </div>
                  <div>
                    <el-tag v-for="(item, index) in scope.row.checker_name" :key="index" >{{ item }}</el-tag>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip  prop="remark" :label="t('process.remark')" />
          <el-table-column show-overflow-tooltip  prop="status" :label="t('process.status')" width="100" />
          <el-table-column show-overflow-tooltip  prop="sort_num" :label="t('process.sort')" width="60" />
          <el-table-column fixed="right" :label="t('userTable.operate')" width="90">
            <template #default="scope">
              <el-dropdown trigger="click" placement="bottom">
                <span class="el-dropdown-link">
                  <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleOper('peple_mgr', scope.row)">{{ t('process.peple_mgr') }}</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('edit', scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination class="flex justify-end mt-4 mb-4"
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          :page-sizes="[10, 50, 100, 300]"
          :background="true"
          layout="sizes, prev, pager, next"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />

      </div>

      <!-- 工序创建窗口 -->
      <DialogProcess :bReturnData="false" v-model:show="showProcessDialog" v-model:data="curOptProcessData" :title="t('project_manage.process_config')" @on-submit="getProcessList" />


      <!-- 人员管理窗口 -->
      <Dialog v-model="showPeopleMgr" :title="t('process.peple_mgr')" :max-height="600" >
        <el-form>
          <el-form-item :label="t('process.name')">
              {{ curOptProcessData.name }}
          </el-form-item>
          <el-form-item :label="t('process.nick')">
              {{ curOptProcessData.nick }}
          </el-form-item>
          <el-form-item :label="t('process.manager')">            
            <el-tag v-for="(item, index) in curOptProcessData.header_name" :key="index" :closable="true" @close="funDelPeople(curOptProcessData.header_man,curOptProcessData.header_name,curOptProcessData.header_man[index])">{{ item }}</el-tag>
            <el-tag class="tag" effect="dark" @click="onAddManager('负责人')">新增</el-tag>
          </el-form-item>
          <el-form-item :label="t('process.opter')">            
            <el-tag v-for="(item, index) in curOptProcessData.operator_name" :key="index" :closable="true" @close="funDelPeople(curOptProcessData.operator_name,curOptProcessData.operator_name,curOptProcessData.operator_name[index])">{{ item }}</el-tag>
            <el-tag class="tag" effect="dark" @click="onAddManager('操作员')">新增</el-tag>
          </el-form-item>
          <el-form-item :label="t('process.checker')">            
            <el-tag v-for="(item, index) in curOptProcessData.checker_name" :key="index" :closable="true" @close="funDelPeople(curOptProcessData.checker_name,curOptProcessData.checker_name,curOptProcessData.checker_name[index])">{{ item }}</el-tag>
            <el-tag class="tag" effect="dark" @click="onAddManager('质检员')">新增</el-tag>
          </el-form-item>
        </el-form>
        <template #footer>
          <ElButton type="primary" @click="onUpdateSel">
            {{ t('msg.ok') }}
          </ElButton>
          <ElButton @click="showPeopleMgr = false">{{ t('common.channel') }}</ElButton>
        </template>
      </Dialog>

      <DialogUser :param="selType" :title="t('title.seluser')" v-model:show="showPeopleSel" @on-submit="onSelPeopleOK"/>

    </div>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 200px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
}
.headerBk{
  background-color: #6d92b4 !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

//修改form标签样式
:deep(.el-form-item__label){
    font-weight: 800;
    font-size: 16px;
}
//标签后批量加冒号
:deep(.el-form-item__label::after){
    content: ':';
    margin-left: 4px;
}
//tag距离
.el-tag{
  margin-right: 10px;
  margin-bottom: 6px;
}
//tag按钮手
.tag:hover {  
      cursor: pointer;  
    }  
</style>

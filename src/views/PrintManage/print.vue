<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElProgress, ElButton, ElMessage, ElMessageBox, ElImage, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElPagination } from 'element-plus';
import { reactive, ref, onMounted, onActivated,watch,onBeforeUnmount } from 'vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { getOemOrderListApi, delOemOrderApi, updateOemOrderApi } from '@/api/product'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { DialogFileList } from "@/components/DialogFileList";
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { closeOneTagByPath, checkPermissionApi, checkRouterPermissionApi, downloadFile } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { exportOemListApi } from '@/api/extra';
import { clearDefer, useDefer } from '@/hooks/web/useDefer';
const defer = useDefer()

import { DynamicScroller,DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const { currentRoute, push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//数据源
const orderData = reactive([])
//支付方式
const payTypeData = reactive([
  '现金',
  '月结',
  '月结45',
  '月结60',
  '支票',
  'POS机',
  '银行转账',
  '支付宝',
  '微信',
  '银联',
])
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state: '',
  oem_order_num: '',
  任务编号: '',
  产品编号: '',
  产品名称: '',
  发料比例: '',
  产品规格: '',
  受托商: '',
  收货状态: '',
  关联类型: '',
  关联单号: '',
  委外数量0: '',
  委外数量1: '',
  委外单价0: '',
  委外单价1: '',
  下单人员: '',
  支付方式: '',
  税率: '',
  备注: '',
  下单日期: ['', ''],
  交货日期: ['', ''],
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getOemOrderList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getOemOrderList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getOemOrderList(val)
}

//处理表格对象操作
const handleOper = (type, item, order = {}) => {
  //编辑产品
  if (type === 'edit' || type === 'info') {
    if (type === 'info' && !checkPermissionApi('委外订单明细查看')) {
      ElMessage.error('无权限')
      return
    }
    if (type === 'edit' && !checkPermissionApi('委外订单修改')) {
      ElMessage.error('无权限')
      return
    }


    push({
      path: '/oemmanage/addoemorder',
      query: {
        id: item.id,
        type: type,
        cmd: bCheckMode.value ? '审核' : ''
      }
    })
  }
  else if (type === 'del') {
    if (!checkPermissionApi('委外订单删除')) {
      ElMessage.error('无权限')
      return
    }

    ElMessageBox.confirm(t('msg.confirm_del_oemorder') + '--> ' + item.oem_order_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)

      const ret = await delOemOrderApi({
        ids: [item.id],
        fsm_exe_man_name: info.resident_name,
        fsm_exe_trig: '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getOemOrderList()
      }
    }
    ).catch(() => { })
  }
  else if (type === 'wlneed') {
    closeOneTagByPath('/oemmanage/wlneedinfo')
    console.log('------', item)
    push({
      path: '/oemmanage/wlneedinfo',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识
      }
    })
  }
  else if (type === 'wlneed_mul') //整单物料需求
  {
    closeOneTagByPath('/oemmanage/wlneedinfo')
    console.log('------', item)
    push({
      path: '/oemmanage/wlneedinfo',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: ''
      }
    })
  }
  else if (type === 'wlprepare') {
    closeOneTagByPath('/oemmanage/wlprepare')
    push({
      path: '/oemmanage/wlprepare',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识
      }
    })
  }
  else if (type === 'wlprepare_mul') //整单物料需求
  {
    closeOneTagByPath('/oemmanage/wlprepare')
    push({
      path: '/oemmanage/wlprepare',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: ''
      }
    })
  }


  else if (type === 'takeout') //发料
  {
    console.log('------22', item, order)
    push({
      path: '/oemmanage/addoemtakeout',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        parter_nick: order.parter_nick
      }
    })
  }
  else if (type === 'takeout_list') {
    push({
      path: '/oemmanage/oemtakeoutlist',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识
      }
    })
  }
  else if (type === 'wllock') //物料锁定
  {
    push({
      path: '/oemmanage/wllock',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        pdt_nick: item.nick,
        pdt_name: item.name
      }
    })
  }
  else if (type === 'takein') //领料
  {
    console.log('------22', item, order)
    push({
      path: '/oemmanage/addoemtakein',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        parter_nick: order.parter_nick
      }
    })
  }
  else if (type === 'takein_list') {
    push({
      path: '/oemmanage/oemtakeinlist',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识
      }
    })
  }
  else if (type === 'takechannel') {
    push({
      path: '/oemmanage/addoemtakechannel',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        parter_nick: order.parter_nick
      }
    })
  }
  else if (type === 'takechannel_list') {
    push({
      path: '/oemmanage/oemtakechannellist',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识
      }
    })
  }
  else if (type === 'remain') {
    push({
      path: '/oemmanage/addoemremain',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        parter_nick: order.parter_nick
      }
    })
  }
  else if (type === 'remain_list') {
    push({
      path: '/oemmanage/oemremainlist',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识
      }
    })
  }
  else if (type === 'drawin') //单独收货
  {
    wsCache.set('drawin_list', [item])
    push({
      path: '/oemmanage/addoemdrawin',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        parter_nick: order.parter_nick
      }
    })
  }
  else if (type === 'drawin_all')  //整单收货
  {
    wsCache.set('drawin_list', order.pdt_list)
    push({
      path: '/oemmanage/addoemdrawin',
      query: {
        oem_order_num: order.oem_order_num,
        parter_nick: order.parter_nick
      }
    })
  }
  else if (type === 'drawin_list') {
    push({
      path: '/oemmanage/oemdrawinlist',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识
      }
    })
  }
  else if (type === 'return') //退货
  {
    push({
      path: '/oemmanage/addoemreturn',
      query: {
        id: '',
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        type: '良品库'
      }
    })
  }
  else if (type === 'return_list') //良品退货单列表
  {
    push({
      path: '/oemmanage/oemreturnlist',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        type: '良品库'
      }
    })
  }
  else if (type === 'bad_return') //退货
  {
    push({
      path: '/oemmanage/addoemreturn',
      query: {
        id: '',
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        type: '不良品库'
      }
    })
  }
  else if (type === 'bad_return_list') //退货单列表
  {
    push({
      path: '/oemmanage/oemreturnlist',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        type: '不良品库'
      }
    })
  }
  else if (type === 'putin_list') //退货
  {
    push({
      path: '/oemmanage/oemputinlist',
      query: {
        oem_order_num: order.oem_order_num,
        pdt_biaoshi: item.标识,
        type: '良品库'
      }
    })
  }
  else if (type === 'syncbom') {
    ElMessageBox.confirm('是否确认同步该bom数据到订单？', t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)
      console.log(order.id)
      const ret = await updateOemOrderApi({
        id: order.id,
        pdt_biaoshi_list: [item.标识],
        fsm_exe_man_name: info.resident_name,
        fsm_exe_trig: '保存',
        is_init_bom: '1'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: '同步成功！',
        })
        getOemOrderList()
      }
    }
    ).catch(() => { })
  }
}

//进入新增界面
const onAddOemOrder = () => {
  push({
    path: '/oemmanage/addoemorder',
    query: {
      id: ''
    },
  })
}

//查询委外单列表
const getOemOrderList = async (page = 1) => {
  clearDefer()
  searchCondition.page = page

  let tmp = cloneDeep(searchCondition)
  delete tmp.委外数量0
  delete tmp.委外数量1
  delete tmp.委外单价0
  delete tmp.委外单价1

  tmp.委外数量 = searchCondition.委外数量0 + ',' + searchCondition.委外数量1
  tmp.委外单价 = searchCondition.委外单价0 + ',' + searchCondition.委外单价1
  tmp.下单日期 = searchCondition.下单日期[0] + ',' + searchCondition.下单日期[1]
  tmp.交货日期 = searchCondition.交货日期[0] + ',' + searchCondition.交货日期[1]

  isLoading.value = true
  orderData.splice(0, orderData.length)
  const ret = await getOemOrderListApi(tmp)
  if (ret) {
    orderData.splice(0, orderData.length, ...ret.data)
    console.log(orderData)
    totleCount.value = parseInt(ret.count)
  }
  isLoading.value = false
}

//审核模式
const bCheckMode = ref(false)

onMounted(() => {
  if (currentRoute.value.name === "OemOrderCheck") {
    bCheckMode.value = true
    searchCondition.fsm_cur_state = '等待审核'
  }
  if (currentRoute.value.query.oem_order_num != undefined)
    searchCondition.oem_order_num = currentRoute.value.query.oem_order_num
  getOemOrderList()

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
  adjustScrollerHeight()
  window.addEventListener('resize', adjustScrollerHeight)
})
const tTest = ref('')

//定义表格列CSS
const tableCssDef = reactive([
  'w-[30%] borderset',
  'w-[15%] borderset',
  'w-[15%] borderset',
  'w-[10%] borderset',
  'w-[10%] borderset',
  'w-[10%] borderset',
  'w-[10%] borderset',
  'w-[10%] borderset',
])



//显示文件列表
const curSelItem = ref({ id: '0', file_list: [], hetong_list: [] })
const curSelFileType = ref('图片')
const curTitle = ref('')
const showFileList = ref(false)
const onShowFileList = (item, type) => {
  showFileList.value = true
  console.log(item)
  curSelItem.value = item
  curSelFileType.value = type
  if (curSelFileType.value === '图片') {
    curTitle.value = '委外图片查看'
  }
  else {
    curTitle.value = '委外文件查看'
  }
}
const onUpdateFileList = async (list) => {
  if (curSelFileType.value === '图片') {
    curSelItem.value.file_list = [...list]
  }
  else {
    curSelItem.value.hetong_list = [...list]
  }
  // console.log('11111',curSelItem)
  curSelItem.value.fsm_exe_trig = '保存'
  //更新文件列表
  const ret = await updateOemOrderApi(curSelItem.value)
  if (ret) {
    ElMessage.success('更新文件成功！')
    getOemOrderList()
  }
}

//去打印
const dialogVisible = ref(false)
const toPrintPage = (item) => {
  // if(type=='pdt')
  // {
  //   let printInfo = { ...item, printType: 'OutItem' }
  //   sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  //   dialogVisible.value = true
  //   setTimeout(() => { dialogVisible.value = false })
  // } else {
  //   let printInfo = { ...item, printType: 'OutList' }
  //   sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  //   dialogVisible2.value = true
  //   setTimeout(() => { dialogVisible2.value = false })
  // }
  let printInfo = { ...item, printType: '委外单' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(() => { dialogVisible.value = false })
}

//页面切换后恢复滚动位置
let scrollY = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scrollY.value = document.getElementById('mainscroll')?.scrollTop
  console.log('离开了', scrollY)
  next()
})
onActivated(() => {
  document.getElementById('mainscroll')?.scrollTo(0, scrollY.value)
})

const isLoading = ref(false)


const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    delete tmp.委外数量0
    delete tmp.委外数量1
    delete tmp.委外单价0
    delete tmp.委外单价1

    tmp.委外数量 = searchCondition.委外数量0 + ',' + searchCondition.委外数量1
    tmp.委外单价 = searchCondition.委外单价0 + ',' + searchCondition.委外单价1
    tmp.下单日期 = searchCondition.下单日期[0] + ',' + searchCondition.下单日期[1]
    tmp.交货日期 = searchCondition.交货日期[0] + ',' + searchCondition.交货日期[1]

    const ret = await exportOemListApi(tmp)
    if (ret) {
      
      ElMessage.success('导出成功，等待下载！')
      setTimeout(() => {
        loadingExport.value = false
        downloadFile(ret.data.download, ret.data.filename)
      }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
  }, 10000)

}


// 调整 DynamicScroller 高度
const adjustScrollerHeight = () => {
  const height = document.getElementById('mainscroll')?.clientHeight - 300
  const scroller = document.getElementById('dynamicScroller');
  if (scroller) {
    scroller.style.height = `${height}px`;
  }
}
onBeforeUnmount(() => {
  window.removeEventListener('resize', adjustScrollerHeight)
})

watch(orderData, () => {
  adjustScrollerHeight()
})

// 计算百分比
const getPercentage = (pdt) => {
  const total = parseInt(pdt.委外数量) + parseInt(pdt.委外备品数量)
  const percentage = ((pdt.已收货 - pdt.良品退货 - pdt.不良退货) / total) * 100
  return Math.min(Math.max(percentage, 0), 100)
}

// 获取进度条状态
const getStatus = (pdt) => {
  const total = parseInt(pdt.委外数量) + parseInt(pdt.委外备品数量)
  const received = pdt.已收货 - pdt.良品退货 - pdt.不良退货
  if (received === total) {
    return 'success'
  } else if (received < total) {
    return 'warning'
  } else {
    return 'exception'
  }
}
</script>

<template>
  <!-- 销售单列表 -->
  <div ref="rootRef" class="flex-col w-[100%] relative h-[100%]">

    <div class="p-4 pt-0">
      <div class="pt-5 pr-5 pl-5 pb-5 mb-5 bg-white">

        <div class="absolute top-5 left-15">
          <ElButton v-if="checkPermissionApi('委外订单新增')" type="success" @click="onAddOemOrder">
            <Icon icon="carbon:document-add" />
            <div class="pl-2">{{ t('button.add') }}</div>
          </ElButton>
          <!-- <ElButton type="primary" plain>
              <Icon icon="clarity:import-line" />
              <div class="pl-2">{{ t('button.import') }}</div>
            </ElButton> -->
          <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
            <Icon icon="carbon:export" />
            <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton>
        </div>
        <div class="text-center mb-5 font-bold">{{ t('oem.order_list') }}</div>
        <!-- 检索条件 -->
        <div class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">{{ t('purchase.check_status') }}</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择">
            <el-option v-for="item in ['订单创建', '等待审核', '等待修改', '等待提交', '已审核', '已拒绝', '已关闭']" :key="item" :label="item"
              :value="item" />
          </el-select>
        </div>
        <div class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">委外单号</div>
          <el-input size="small" v-model.lazy="searchCondition.oem_order_num" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">任务编号</div>
          <el-input size="small" v-model.lazy="searchCondition.任务编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('product_manage.id') }}</div>
          <el-input size="small" v-model="searchCondition.产品编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('product_manage.name') }}</div>
          <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">发料比例</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.发料比例" placeholder="请选择">
            <el-option v-for="item in ['未发料+部分发料', '未发料', '部分发料', '完全发料', '超量发料']" :key="item" :label="item"
              :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品规格</div>
          <el-input size="small" v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">受托商</div>
          <el-input size="small" v-model="searchCondition.受托商" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">收货状态</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.收货状态" placeholder="请选择">
            <el-option v-for="item in ['未收货+部分收货', '未收货', '部分收货', '完全收货', '超量收货']" :key="item" :label="item"
              :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">关联类型</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.关联类型" placeholder="请选择">
            <el-option v-for="item in ['销售', '委外']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">关联单号</div>
          <el-input size="small" v-model="searchCondition.关联单号" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">委外数量</div>
          <el-input size="small" v-model="searchCondition.委外数量0" placeholder="" class="!w-[60px]" type="number" />
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small" v-model="searchCondition.委外数量1" placeholder="" class="!w-[60px]" type="number" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">委外单价</div>
          <el-input size="small" v-model="searchCondition.委外单价0" placeholder="" class="!w-[60px]" type="number" />
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small" v-model="searchCondition.委外单价1" placeholder="" class="!w-[60px]" type="number" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">下单人员</div>
          <el-input size="small" v-model="searchCondition.下单人员" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">支付方式</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.支付方式" placeholder="">
            <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">税率</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.税率" placeholder="">
            <el-option v-for="item in ['不含税', ...Array.from({ length: 17 }, (_, i) => `${i + 1}%`)]" :key="item"
              :label="item" :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">备注</div>
          <el-input size="small" v-model="searchCondition.备注" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">下单日期</div>
          <el-date-picker size="small" class="searchItem" v-model="searchCondition.下单日期" type="daterange"
            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
        <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">交货日期</div>
          <el-date-picker size="small" class="searchItem" v-model="searchCondition.交货日期" type="daterange"
            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>

        <div class="flex justify-end items-center mr-6 mt-3 mb-1">
          <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" />
          <ElButton class="ml-5" type="primary" @click="onSearch">
            <Icon icon="ri:phone-find-line" />
            <div class="pl-2">查询</div>
          </ElButton>
          <ElButton type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="pl-2">清除</div>
          </ElButton>
        </div>
      </div>
      <!-- 图例 -->
      <div class="flex mb-2">
        <div class="flex text-sm items-center mr-3">
          <div class="title_create w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          已创建
        </div>
        <div class="flex text-sm items-center mr-3">
          <div class="title_ok w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          审核通过
        </div>
        <div class="flex text-sm items-center mr-3">
          <div class="title_wait w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          驳回
        </div>
      </div>
      <!-- 产品列表 -->
      <div v-loading.lock="isLoading">
        <!-- 表头 -->
        <div class="flex header headerBk">
          <div :class="tableCssDef[0]" class="">产品名称</div>
          <div :class="tableCssDef[1]">数量及价格</div>
          <div :class="tableCssDef[2]">订单进度</div>
          <div :class="tableCssDef[3]">入库状态</div>
          <div :class="tableCssDef[4]">订单状态</div>
          <div :class="tableCssDef[5]">成本</div>
          <div :class="tableCssDef[6]">备注</div>
          <div :class="tableCssDef[7]">操作</div>
        </div>

        <!-- 表内容 -->
        <DynamicScroller :items="orderData" :min-item-size="145.17" key-field="id" class="scroller" id="dynamicScroller">
          <template #default="{ item, index,active }">
            <DynamicScrollerItem 
              :item="item" :size-dependencies="[
                  item.pdt_list.length
                ]" 
              :data-index="index"
              :active="active"
            >
              <div :style="{ height: (item.pdt_list.length * 115.17+30) + 'px' }">
                <div>
                  <div class="p-2 flex flex-nowrap text-[13px] h-[30px] border-b-1px font-bold"
                    style="background-color: #BFEFFF;">
                    <div class="flex items-center mr-1 w-80px flex-grow-0 flex-shrink-0">
                      <div class="rounded p-1 pl-2 pr-2" style="color: #fff;"
                        :class="{ 'title_create': ['订单创建', '等待审核', '等待修改', '等待提交'].includes(item.fsm_cur_state), 'title_ok': ['已入库', '已关闭', '已拒绝', '已审核'].includes(item.fsm_cur_state), 'title_wait': item.fsm_cur_state === '等待修改' }">
                        {{ item.fsm_cur_state }}
                      </div>
                    </div>
                    <div class="w-[35%] min-w-[100px] flex items-center">
                      <div class="mr-3 font-bold">{{ item.create_date.split(' ')[0] }}</div> 委外单号: {{ item.oem_order_num
                      }}
                    </div>
                    <div class="min-w-[100px] flex items-center">
                      <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                      {{ checkPermissionApi('受托商名称显示') ? item.parter_nick : '***' }}
                      <span>{{ '(' + item.pay_type + ')' }}</span>
                    </div>
                    <div class="flex justify-center items-center min-w-[150px]">
                      <div class="mr-5">下单员:{{ item.oem_man_name }}</div>
                      <el-progress :text-inside="true" :stroke-width="26" :percentage="70" />
                    </div>
                    <div class="flex justify-end items-center">
                      <Icon v-if="checkPermissionApi('委外图片查看')"
                        style="scale: 1.1;color: rgb(101, 101, 236); margin-right: 6px;" icon="solar:file-bold" />
                      <div v-if="checkPermissionApi('委外图片查看')" class="cursor-pointer"
                        @click="onShowFileList(item, '图片')">图片({{ item.file_list.length }})</div>
                      <Icon v-if="checkPermissionApi('委外文件查看')"
                        style="scale: 1.1;color: rgb(101, 101, 236); margin:0 5px 0 10px;"
                        icon="material-symbols:contract" />
                      <div v-if="checkPermissionApi('委外文件查看')" class="cursor-pointer"
                        @click="onShowFileList(item, '文件')">文件({{ item.hetong_list.length }})</div>
                    </div>
                    <!-- 靠右的其他信息 -->
                    <div class="ml-auto flex justify-center items-center min-w-[150px]">
                      <Icon class='mr-3'
                        style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;"
                        icon="material-symbols:print-outline" @click="toPrintPage(item)" />
                      <el-dropdown trigger="click" placement="bottom">
                        <span class="el-dropdown-link">
                          <ElButton type="warning" size="small">批量</ElButton>
                        </span>
                        <template #dropdown>
                          <div class="flex flex-wrap w-[110px]">
                            <el-dropdown-item @click="handleOper('wlneed_mul', '', item)">组单需求表</el-dropdown-item>
                            <el-dropdown-item @click="handleOper('wlprepare_mul', '', item)">组单备料表</el-dropdown-item>
                            <el-dropdown-item :disabled="item.fsm_cur_state != '已审核'"
                              @click="handleOper('drawin_all', item, item)">组单收货</el-dropdown-item>
                            <el-dropdown-item @click="handleOper('edit', item)">组单编辑</el-dropdown-item>
                            <el-dropdown-item @click="handleOper('del', item)">组单删除</el-dropdown-item>
                          </div>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>
                <div class="flex">
                <!-- 左边产品列表 -->
                <div class="w-[100%]  table_self">
                  <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                   
                      <div :class="tableCssDef[0]" class="flex justify-start items-center w-[100%] p-1">
                        <el-image v-if="pdt.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px] mr-2" :src="pdt.pics[0].url" />
                        <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px] mr-2" src="/nopic.jpg" />
                        <div class="inline-block text-left max-w-[100%]">
                          <div class="flex">
                            <div class="extxt mr-5 mb-1 flex flex-col">
                              <span>单号:</span>
                              <span>{{ pdt.子任务单号 }}</span>
                            </div>
                            <div v-if="pdt.sell_order_num!=''" style="color: rgb(81, 141, 146);" class="extxt  cursor-pointer flex flex-col">
                              <span>销售单:</span>
                              <span>{{ pdt.sell_order_num }}</span>
                            </div>
                            <div v-if="pdt.oem_order_num!=''" style="color: rgb(81, 141, 146);" class="extxt  cursor-pointer flex flex-col">
                              <span>委外单:</span>
                              <span>{{ pdt.oem_order_num }}</span>
                            </div>
                          </div>

                          <div style="white-space: normal;" class="mb-1 nameStyle" @click="handleOper('info', item)">{{ '['+pdt.name+']'+pdt.nick }}</div>
                        </div>
                      </div>
                     
                    <div :class="tableCssDef[1]">
                      <div style="font-size: larger; font-weight: 400; color: rgb(255, 17, 17);">{{ pdt.委外数量+pdt.base_unit }}</div>
                      <div class="ex_text mb-2">备{{ pdt.委外备品数量+pdt.base_unit }}</div>
                      <div>{{ (parseInt(pdt.委外数量)+parseInt(pdt.委外备品数量))+pdt.base_unit+'  总价:'+  (checkPermissionApi('委外订单价格显示')?pdt.总价:'*')+'元' }}</div>
                      <div class="flex">
                        <div class="mr-2">{{ item.pay_type }}</div>
                        <div>税率:{{ pdt.发票税率 }}%</div>
                      </div>
                      <div>交期:{{ pdt.交货日期}}</div>
                    </div>   
                    <div :class="tableCssDef[2]" class="flex justify-center flex-col">
                      <div class="flex">
                        <div class="mr-2">已收货:{{ pdt.已收货 }}</div>
                        <div>未收货:{{ pdt.未收货 }}</div>
                      </div>
                      <el-progress
                          class="w-[90%]"
                          style="font-size: smaller;"
                          :text-inside="true"
                          :stroke-width="12"
                          :percentage="getPercentage(pdt)"
                          :status="getStatus(pdt)"
                        />
                      <div class="flex">
                        <div class="mr-2">发料数:{{ pdt.发料数 }}</div>
                        <div>领料数:{{ pdt.领料数 }}</div>
                      </div>
                      <div class="flex">
                        <div class="mr-2">良品数:{{ pdt.良品数 }}</div>
                        <div class="mr-2">不良数:{{ pdt.不良数 }}</div>                        
                      </div>
                      <div class="flex">
                        <div class="mr-2">良品退货:{{ pdt.良品退货 }}</div>
                        <div>不良退货:{{ pdt.不良退货 }}</div>
                      </div>
                      


   

                    </div>     
                    <div :class="tableCssDef[3]" class="flex justify-center flex-col">
        
                      <div>
                        <div class="text-center mr-2">已入库:{{ pdt.已入库 }}</div>
                        <el-progress
                         class="w-[90%]"
                         style="font-size: smaller;"
                          :text-inside="true"
                          :stroke-width="14"
                          :percentage="Math.min(parseFloat((((pdt.已入库-pdt.良品退货-pdt.不良退货) / (parseInt(pdt.委外数量)+parseInt(pdt.委外备品数量))) * 100).toFixed(2)), 100)"
                          :status="(pdt.已入库-pdt.良品退货-pdt.不良退货) == (parseInt(pdt.委外数量)+parseInt(pdt.委外备品数量)) ? 'success' : ((pdt.已入库-pdt.良品退货-pdt.不良退货) < (parseInt(pdt.委外数量)+parseInt(pdt.委外备品数量)) ? 'warning' : 'exception')" />
                      </div>

                    </div>
                    <div :class="tableCssDef[4]" class="flex justify-center items-center flex-col">
                      <text class="p-2 pt-0.5 pb-0.5" style="font-size: 14px;font-weight: 900; border-radius: 5px;color: #fff;" :class="{'title_create': ['订单创建','等待审核','等待修改','等待提交'].includes(item.fsm_cur_state), 'title_checked': item.fsm_cur_state === '已审核', 'title_ok': ['已入库','已关闭','已拒绝'].includes(item.fsm_cur_state), 'title_wait': item.fsm_cur_state === '等待修改'}">{{ item.fsm_cur_state }}</text>
                      <div class="text-red-400 mt-1">{{ item.fsm_log_list.length>0?item.fsm_log_list[0][5]:'' }}</div>  
                    </div>
                    <div :class="tableCssDef[5]" class="flex justify-center items-center">{{ checkPermissionApi('委外订单价格显示')?( parseFloat((pdt.oem_price_aft_tax+pdt.附加费-pdt.物料赔付金).toFixed(2))):'*' }}</div>
                    <div :class="tableCssDef[6]" class="flex justify-center items-center">{{ pdt.委外备注 }}</div>
                    <div :class="tableCssDef[7]" class="flex justify-center items-center">       
                      <ElButton v-if="bCheckMode && item.fsm_can_trig_data.审核触发.length>0" type="success" size="small" @click="handleOper('info',item)">{{ t('cmd.check') }}</ElButton>
                      <el-dropdown v-if="!bCheckMode" trigger="click" placement="bottom">
                        <span class="el-dropdown-link">
                          <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                        </span>
                        <template #dropdown>
                          <div class="flex flex-wrap w-[240px]">
                            <el-dropdown-item @click="handleOper('edit', item)">{{ t('oem.t_modify') }}</el-dropdown-item>
                            <el-dropdown-item @click="handleOper('del', item)">{{ t('oem.t_del') }}</el-dropdown-item>
                            <el-dropdown-item v-if='checkRouterPermissionApi("/oemmanage/wlneedinfo")'  @click="handleOper('wlneed', pdt,item)">{{ t('oem.t_wlxq') }}</el-dropdown-item>
                            <el-dropdown-item v-if='checkRouterPermissionApi("/oemmanage/wlneedinfo")'  @click="handleOper('wlprepare', pdt,item)">委外备料</el-dropdown-item>
                            <el-dropdown-item @click="handleOper('syncbom', pdt,item)">同步BOM</el-dropdown-item>
                            <span class="flex flex-wrap w-[240px]" v-if="item.fsm_cur_state == '已审核'">
                              <el-dropdown-item  @click="handleOper('wllock', pdt,item)">{{ t('oem.t_wl')+'('+pdt.物料数+')' }}</el-dropdown-item>
                              
                              <div>-----------------------------------------</div>
                              <el-dropdown-item  @click="handleOper('takeout', pdt,item)">{{ t('oem.t_wwfl') }}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('takeout_list', pdt,item)">{{ t('oem.t_flmx')+'('+pdt.发料明细+')' }}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('takein', pdt,item)">{{ t('oem.t_wwll') }}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('takein_list', pdt,item)">{{ t('oem.t_llmx')+ '('+pdt.领料明细+')'}}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('takechannel', pdt,item)">{{ t('oem.t_wlth') }}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('takechannel_list', pdt,item)">{{ t('oem.t_thmx') +'('+pdt.退料明细+')'}}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('remain', pdt,item)">{{ t('oem.t_ylfh') }}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('remain_list', pdt,item)">{{ t('oem.t_fhmx') + '('+pdt.返回明细+')'}}</el-dropdown-item>
                              <div>-----------------------------------------</div>
                              <el-dropdown-item  @click="handleOper('drawin', pdt,item)">{{ t('oem.t_cpsh') }}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('drawin_list', pdt,item)">{{ t('oem.t_shmx') +'('+pdt.收货明细+')'}}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('return', pdt,item)">{{ t('oem.t_cpth') }}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('return_list', pdt,item)">{{ t('oem.t_ttmx')+ '('+pdt.良品退明细+')'}}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('bad_return', pdt,item)">{{ t('oem.t_blth') }}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('bad_return_list', pdt,item)">{{ t('oem.t_blthmx') +'('+pdt.不良退明细+')'}}</el-dropdown-item>
                              <el-dropdown-item  @click="handleOper('putin_list', pdt,item)">{{ t('oem.t_rkmx') +'('+pdt.入库明细+')'}}</el-dropdown-item>
                              
                            </span>
                            
                          </div>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>

              </div>

              </div>

            </DynamicScrollerItem>
          </template>
        </DynamicScroller>


        <div class="mt-3 bg-white" v-for="(item, index) in orderData" :key="item.id"
          style="box-shadow:var(--el-box-shadow-lighter);">


        </div>
      </div>
    </div>

    <el-pagination class="flex justify-end mb-4" v-model:current-page="searchCondition.page"
      v-model:page-size="searchCondition.count" :page-sizes="[10, 50, 100, 300]" :background="true"
      layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
      @current-change="handleCurrentChange" />

    <DialogFileList :path="'file/ww/' + curSelItem.oem_order_num + '/'" v-model:show="showFileList"
      :files="curSelFileType == '图片' ? curSelItem.file_list : curSelItem.hetong_list" @on-update="onUpdateFileList"
      :type="curSelFileType" :title="curTitle" />
    <PrintModal v-model:show="dialogVisible" />
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  cursor: pointer;
}

.nameStyle:hover {
  color: rgb(130, 130, 255);
}


:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem {
  width: 150px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  color: #333;
  font-weight: bold;
}

.headerBk {
  background-color: #fff !important;
}

.content {
  &:extend(.header);
  font-size: 14px;
}

.header>div,
.content>div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}

.header>div:last-child,
.content>div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self {
  font-size: 14px;
}

.table_self>div,
.right>div {
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.test {
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text {
  font-size: 11px;
  color: #646464;
}

.ex_text_danger {
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
  flex: 1;
  min-width: 0;
  /* 设置最小宽度，防止内容撑大 */
  text-align: center;
  /* 文字居中对齐 */
  word-wrap: break-word;
  /* 文字超长时换行处理 */
  font-size: 11px;
}

.rightcss_title {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}

.extxt {
  font-size: 12px;
  color: #646464;

}

.borderset {
  border-right: 1px solid #e2e2e2;
  font-size: 12px;
  padding-left: 5px;
}

//---------------列表对象标题栏条件颜色-------------------
.title_create {
  //已创建
  background-color: #79bbff;
}

.title_checked {
  //已审核
  background-color: #95d475;
}

.title_ok {
  //已入库
  background-color: #b1b3b8;
}

.title_wait {
  //等待修改
  background-color: #f89898;
}
</style>

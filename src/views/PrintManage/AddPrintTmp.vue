<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch, reactive } from 'vue';
import { ElButton, ElSelect, ElOption, ElUpload, ElPopconfirm, ElInput, ElMessage } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { getProductListApi } from '@/api/product'
import { DialogProduct } from '@/components/DialogProduct';
import { DialogProductSel } from '@/components/DialogProductSel'
import { useRouter } from 'vue-router'
import { useCache } from '@/hooks/web/useCache'
import Vue3KindEditor from '@teihin/vue3-kind-editor'
import { addPrintTmpApi,getPrintTmpInfoApi,updatePrintTmpApi } from '@/api/print';
import { zip,unzip } from '@/api/tool';
import { nextTick } from 'process';

const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

let LODOP = window.getLodop();

//默认参数
const defData = {

    modifyText: '修改内容',
    changeCss: 'hidden-box',

    nPageWidth: 210,
    nPageHeight: 297,
    nPaddingTop: 5,
    nPaddingBottom: 5,
    nPaddingLeft: 5,
    nPaddingRight: 5,
    nTopHeight: 0,
    nFootHeight: 0,
    nLeftWidth: 0,
    nRightWidth: 0,
    nMiddleHeight: 287,
    nMiddleWidth: 200,
    nFollowHeight: 0,

    sLodopMode: "Cloud",//打印模式
    printList: [],//打印机列表
    sPrintName: { label: "", value: '' },//打印机名称和val
    strPageSizeList: [],//纸张列表
    sPageType: " ",//纸类型
    nDirection: 1,
    pageDirection: 1,//纸方向
    offset_x: 0,
    offset_y: 0,
    nWidth: 2100,
    nHeight: 2970,
    sPrintTitle: '标题',
    sIdList: "00008532:销售单唐山启奥科技股份有限公司_00008532:1",//打印单据的第一个编号

    sTitleRandom: "",//随机数标题
    sTaskCmd: "",
    sBatchPrintFlag: "",
    sBodyType: "html",

    printTitle:'',  //模板标题
    printContent:'' //打印模板内容   zip base64

}

const tmpData = reactive(cloneDeep(defData))



const show = ref(false);
const props = defineProps<{
  show: boolean,
  id: string, //模板ID
  type:string, //模板类型
  copydata: any //拷贝内容
}>()


//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    show.value = true
    initShow()
  }
})

//定义通知
const emit = defineEmits(['update:show', 'onSubmit'])
//提交选择的数据
const onSubmit = async() => {
  console.log('1111')
  let tmp = cloneDeep(tmpData)
  if(props.id == '')
  {
    const ret = await addPrintTmpApi({
      template:tmpData.printTitle,
      type:props.type,
      position:'',
      present:'0',
      enable:'1',
      content: zip(JSON.stringify(tmp)),
      remark:''
    })
    if(ret)
    {
      ElMessage.success('提交成功')
      emit('onSubmit')
      closeDlg()
    }
  }
  else
  {
    const ret = await updatePrintTmpApi({
      ids:[props.id],
      template:tmp.printTitle,
      content:zip(JSON.stringify(tmp)),
    })
    if(ret)
    {
      ElMessage.success('成功')
      emit('onSubmit')
      closeDlg()
    }
  }
  
  
}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

const initShow = async() => {

  //新增模板
  if (props.id == '') {
    
    setTimeout(()=>{
      if(props.copydata != '')
      {
        const out = unzip(props.copydata.content)
        Object.assign(tmpData,JSON.parse(out))
      }
      else 
      {
        Object.assign(tmpData,defData)
      }
    },500)
  }
  else{
    const ret = await getPrintTmpInfoApi({
      id:props.id
    })
    if(ret)
    {
      console.log(ret)
      const out = unzip(ret.data.content)
      Object.assign(tmpData,JSON.parse(out))
    }
  }
  createPrintList()
  //加载纸张类型
  createPagSizeList();
}

//打印机列表
const createPrintList = ()=>{
  console.log(LODOP)
    //控件和云打印模式加载打印机
    if (tmpData.sLodopMode == "Internet") {
      
    } else {      
      printList.splice(0, printList.length)
      let nPrintCount = LODOP.GET_PRINTER_COUNT();//获取打印机的个数
      for (let i = 0; i <= nPrintCount - 1; i++) {
          let strDName = LODOP.GET_PRINTER_NAME(i);
          printList.push({ value: i, label: strDName })
      }
    }
}
//加载纸张列表
const createPagSizeList =()=> {
    strPageSizeList.splice(0, strPageSizeList.length)
    if (tmpData.sLodopMode == "Internet") {
        return
    } else {
        var sPrintId = tmpData.sPrintName.value;
        strPageSizeList.push(...LODOP.GET_PAGESIZES_LIST(sPrintId, "\n").split("\n"))
    }
}

const printList:any[] = reactive([]) //打印机列表
const strPageSizeList:any[] = reactive([]) //纸张列表



const onChangePrinter = ()=>{
  createPagSizeList();
}

</script>

<template>
  <Dialog destroy-on-close v-model="show" title="模板编辑" max-height="80vh" width="90%" @close="closeDlg">
    <table class="htable" style="margin: 0 auto;width:95%;height:100%;">
      <tbody>
        <tr>
          <th style="width:120px;height:30px;">模板名称</th>
          <td style="text-align:left">
            <el-input style="width:98%;height:26px;" v-model="tmpData.printTitle"  />
            </td>
        </tr>
        <tr>
          <th style="height:30px;">页面设计(毫米)</th>
          <td style="text-align:left;">
            <span id="birdgelist"></span><span id="printlist">
              <ElSelect id='printlist selectprint' v-model="tmpData.sPrintName" placeholder="--请选择打印机--"
                  size="small" @change="onChangePrinter">
                  <ElOption v-for="item in printList" :key="item.value" :label="item.label"
                      :value="item" />
              </ElSelect>
            </span>
            纸张大小：
            <ElSelect id='spanPaperList PaperList' v-model="tmpData.sPageType" placeholder="选择纸张"
                  size="small">
                  <ElOption v-for="item in strPageSizeList" :key="item.value" :label="item.label"
                      :value="item" />
              </ElSelect>
            <br/>
            纸张宽：<input type="text" id="width" name="width" v-model="tmpData.nPageWidth" style="width:30px;height:20px;"/>mm
            纸张高：<input type="text" id="height" name="height" v-model="tmpData.nPageHeight" style="width:30px;height:20px;"/>mm
            进纸方向：
            <ElSelect id='pagedirection' v-model="tmpData.pageDirection" style='width:100px;height:25px;'
                size="small" >
                <ElOption label="纵向" :value="1" />
                <ElOption label="横向" :value="2" />
            </ElSelect>
            <br/>
            左边距：<input type="text" id="paddingleft" name="left" v-model="tmpData.nPaddingLeft"
              style="width:30px;height:20px;"/>mm
            右边距：<input type="text" id="paddingright" name="right" v-model="tmpData.nPaddingRight"
              style="width:30px;height:20px;"/>mm
            上边距：<input type="text" id="paddingtop" name="top" v-model="tmpData.nPaddingTop" style="width:30px;height:20px;"/>mm
            下边距：<input type="text" id="paddingbottom" name="bottom" v-model="tmpData.nPaddingBottom"
              style="width:30px;height:20px;"/>mm
            <br/>
            页眉高度：<input type="text" id="topheight" name="topheight" v-model="tmpData.nTopHeight"
              style="width:30px;height:20px;"/>mm
            页脚高度：<input type="text" id="footheight" name="footheight" v-model="tmpData.nFootHeight"
              style="width:30px;height:20px;"/>mm
            左固定项宽度：<input type="text" id="leftwidth" name="leftwidth" v-model="tmpData.nLeftWidth"
              style="width:30px;height:20px;"/>mm
            右固定项宽度：<input type="text" id="rightwidth" name="rightwidth" v-model="tmpData.nRightWidth"
              style="width:30px;height:20px;"/>mm
            正文跟随项高度：<input type="text" id="followheight" name="followheight" v-model="tmpData.nFollowHeight"
              style="width:30px;height:20px;"/>mm
          </td>
        </tr>
        <!-- <tr>
          <th>内容类型</th>
          <td style="text-align:left;">
            <input type="radio" name="bodytype" value="html" onclick="typechange()"
              checked=""/>标准HTML<input type="radio" name="bodytype" value="table"
              onclick="typechange()"/>表格(不是表格的内容将被过滤)
          </td>
        </tr> -->
        <tr>
          <td colspan="3" style="text-align:center;" >
            <div class="w-[100%] flex justify-center">
              <vue3-kind-editor id="printtmp" height="800px" width="800px" v-model="tmpData.printContent"
              :loadStyleMode="false">1</vue3-kind-editor>
            </div>
          </td>
        </tr>

        <!-- <tr>
          <td colspan="3" style="text-align:center;">
            <input type="button" value="提&nbsp;交" onclick="Save()" style="width:200px;"/>
          </td>
        </tr> -->
        <!-- <tr>
          <th>打印变量</th>
          <td style="text-align:left;">
            ==========================================================================================<br/>
            说明：最低优先级替换<br/>
            ==========================================================================================<br/>
            页眉页脚中的页码的示例代码如下：
            &lt;span tdata="pageNO"&gt;第##页&lt;/span&gt;/&lt;span tdata="pageCount"&gt;共##页&lt;/span&gt;<br/>
            可以使用的变量有：<br/>
            ==========================================================================================<br/>
            {销售订单:制单人}<br/>{#:客户关联订单号}<br/>{#:客户}<br/>{销售订单:客户}<br/>{#:客户编号}<br/>{销售订单:销售日期}<br/>{销售订单:销售时间}<br/>{#:币种}<br/>{#:交货日期}<br/>{#:送货方式}<br/>{#:汇率}<br/>{#:快递公司地址}<br/>{#:快递公司联系人}<br/>{#:快递公司联系电话}<br/>{#:运费}<br/>{#:快递名称}<br/>{#:快递单号}<br/>{#:减免金额}<br/>{#:扩展信息1}<br/>.......<br/>{#:扩展信息60}<br/>{#:安装方式}<br/>{销售订单:联系手机}<br/>{销售订单:联系人}<br/>{#:制单时间}<br/>{员工:传真}<br/>{员工:手机}<br/>{员工:姓名}<br/>{员工:电话}<br/>{员工:QQ}<br/>{#:其他费用}<br/>{#:已付金额}<br/>{#:付款方式}<br/>{#:产品金额}<br/>{#:收货地址}<br/>{#:收货联系电话}<br/>{#:收货人}<br/>{#:收货人手机}<br/>{#:收货人电话}<br/>{#:收款人员}<br/>{#:销售日期}<br/>{#:业务部门}<br/>{销售订单:销售人员}<br/>{#:编号}<br/>{销售订单:编号}<br/>{#:销售人员手机}<br/>{#:销售人员}<br/>{#:销售人员编号}<br/>{#:销售人员电话}<br/>{销售订单:收款人员}<br/>{#:备注}<br/>{#:订单状态}<br/>{#:销售时间}<br/>{#:金额}<br/>{#:总数量}<br/>{#:税额金额}<br/>{#:销售总数量}<br/>{#:辅助总数量}<br/>{#:合计产品体积}<br/>{#:税前金额}<br/>{#:合计产品重量}<br/>{#:跟单人员}<br/>{#:跟单人员编号}<br/>{#:装卸方式}<br/>{#:未付金额}<br/>{#:分}<br/>{#:角}<br/>{#:元}<br/>{#:十}<br/>{#:百}<br/>{#:千}<br/>{#:万}<br/>{#:十万}<br/>{#:百万}<br/>{#:千万}<br/>{#:亿}<br/>注:金额需要大写的变量:{金额大写:{#:xxxxx}},例如:{金额大写:{#:金额}}<br/>
            ===========================================================================================<br/>
            下面是明细的替换变量：注意下面的变量中$后面标识一致的变量放在表格中的同一行中，否则系统将自动替换<br/>
            ===========================================================================================<br/>
            {$:备注}<br/>{$:仓位名称}<br/>{$:产品编码}<br/>{$:产品简称}<br/>{$:产品类型}<br/>{$:产品描述}<br/>{$:产品名称}<br/>{$:产品品牌}<br/>{$:产品所属分类}<br/>{$:产品体积}<br/>{$:产品条形码}<br/>{$:产品英文名称}<br/>{$:产品重量}<br/>{$:产品最小包装数}<br/>{$:单价}<br/>{$:发票类型}<br/>{$:辅助单位}<br/>{$:辅助数量}<br/>{$:规格}<br/>{$:规格显示过滤}<br/>{$:交货日期}<br/>{$:金额}<br/>{$:可用库存}<br/>{$:客户产品编码}<br/>{$:客户产品规格}<br/>{$:客户产品名称}<br/>{$:扩展列1}<br/>{$:扩展列10}<br/>{$:扩展列2}<br/>{$:扩展列3}<br/>{$:扩展列4}<br/>{$:扩展列5}<br/>{$:扩展列6}<br/>{$:扩展列7}<br/>{$:扩展列8}<br/>{$:扩展列9}<br/>{$:明细编号}<br/>{$:生产单号}<br/>{$:数量}<br/>{$:税额}<br/>{$:税额总价}<br/>{$:税率}<br/>{$:税前单价}<br/>{$:税前总价}<br/>{$:未发货辅助计量数量}<br/>{$:未发货销售计量数量}<br/>{$:未发货主计量数量}<br/>{$:物料编码}<br/>{$:销售计量单位}<br/>{$:销售计量数量}<br/>{$:序号}<br/>{$:已发货辅助计量数量}<br/>{$:已发货销售计量数量}<br/>{$:已发货主计量数量}<br/>{$:已生产数量}<br/>{$:折扣}<br/>{$:主计量单位}<br/>{$:主计量数量}<br/>{$:助记码}<br/>{$:分}<br/>{$:角}<br/>{$:元}<br/>{$:十}<br/>{$:百}<br/>{$:千}<br/>{$:万}<br/>{$:十万}<br/>{$:百万}<br/>{$:千万}<br/>{$:亿}<br/>{$:C001}<br/>.......<br/>{$:C060}<br/>{$:P001}<br/>.......<br/>{$:P060}<br/>{图片:{产品:{$:产品编码}:size}:height:width}或{图片:{产品:{$:产品编号}:size}:height:width}size:图片大小;height:图片最大高度;width:图片最大宽度;其中{产品:{$:产品编号}:size}可以使用图片的第三方在线链接
            例如:{图片:{产品:{$:产品编码}:60}:50:50}或者{图片:https://www.xxxxxx.jpg:50:50} <br/><br/>




          </td>
        </tr> -->
        <!-- <tr>
          <th>公共变量</th>
          <td style="text-align:center;height:60px">
            <input class="layui-btn layui-btn-primary header-btn" onclick="ShowVariable()" placeholder="查看公共变量"
              readonly="readonly"/><br/>
          </td>
        </tr> -->
      </tbody>
    </table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
@import './print.css';
:deep(.cell) {
  color: black;
}
</style>
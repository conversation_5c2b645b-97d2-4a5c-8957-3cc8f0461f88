// 参数说明：num 要格式化的数字 n 保留小数位
function formatMoney(num) {
    if (!/^(-?\d+)(\.\d+)?$/.test(num)) {
        return num;
    }

    try {
        num = formatNumber(num, 2);
    } catch (e) {}

    return num;
}

// 参数说明：num 要格式化的数字 n 保留小数位
function formatNumber(num, n) {
    try {
        num = parseFloat(num).toFixed(2);
    } catch (e) {}

    return num;
}



/**
 * 金额保留小数
 */
Number.prototype.formatMoney = function () {
    var value = this + "";
    return formatMoney(parseFloat(value.replace(/,/g, "")));
}


/**
 * 数量保留小数
 */
Number.prototype.formatNum = function () {
    var value = this + "";
    return formatNumber(parseFloat(value.replace(/,/g, "")), 2);
}





export function HzxJson(sInput) {

    var nP1 = sInput.indexOf("≮");
    var nP2 = sInput.indexOf("≯");
    this.data = "§" + sInput.substring(nP1 + 1, nP2) + "§";

}

HzxJson.prototype.getValue = function (lable) {
    var sValue = "";
    var nP1 = this.data.indexOf("§" + lable + "┇");
    if (nP1 >= 0) {
        var nP2 = this.data.indexOf("§", nP1 + 1);
        if (nP2 > nP1) {
            sValue = this.data.substring(nP1 + lable.length + 2, nP2);
        }
    }
    return sValue
}
<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch,reactive } from 'vue';
import { ElCheckbox,ElButton,ElTable,ElTableColumn,ElSwitch, ElPopconfirm,ElRadio, ElMessage, ElMessageBox } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { useCache } from '@/hooks/web/useCache'

import { getPrintTmpListApi,getPrintTmpInfoApi,delPrintTmpApi, updatePrintTmpApi } from '@/api/print'
import AddPrintTmp from './AddPrintTmp.vue'

const { push} = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

const show = ref(false);
const props = defineProps<{
  show: boolean,
  printType: string,
}>()

const printTmpData = reactive([])

//监听外部传入的显示设置
watch(() => props.show, async(val) => {
  if (val) {
    show.value = true
    getPrintTmpList()
  }
})

//定义通知
const emit = defineEmits(['update:show','onSubmit'])
//提交选择的数据
const onSubmit = ()=>{
  emit('onSubmit')
  closeDlg()
}
//关闭
const closeDlg = ()=>{
  emit('update:show',false)
  show.value = false
}

//查询模板列表
const getPrintTmpList = async(page = 1)=>{
  console.log(props)
  const ret = await getPrintTmpListApi({
    page,
    count:100,
    type:props.printType
  })
  if(ret)
  {
    printTmpData.splice(0,printTmpData.length, ...ret.data)
  }
}

//新增,编辑模板
const curSelID = ref('')
const curCopyData = ref('') //拷贝内容
const bShowModifyPrintTmp = ref(false)
const onAddNewPrintTmp = ()=>{
  bShowModifyPrintTmp.value = true
  curSelID.value = ''
  curCopyData.value = ''
}
//编辑模板
const onEditTmp = (row)=>{
  curSelID.value = row.id
  bShowModifyPrintTmp.value = true
}
//删除模板
const onDeleteTmp = async(row)=>{
  const ret = await delPrintTmpApi({ "ids": [row.id] })
    if (ret) {
      getPrintTmpList()

      ElMessage({
        type: 'success',
        message: t('msg.delOK'),
      })
    }

}
//复制模板
const onCopyTmp = async(row)=>{
  curSelID.value = ''
  bShowModifyPrintTmp.value = true
  const ret = await getPrintTmpInfoApi({
    id:row.id
  })
  if(ret)
  {
    curCopyData.value = ret.data
    bShowModifyPrintTmp.value = true
  }


}

//禁用模板
const onDisableTmp = async(event,row)=>{
  // console.log(event)
  // return
  const ret = await updatePrintTmpApi({
    ids:[row.id],
    enable:event
  })
  if(ret)
  {
    getPrintTmpList()
    ElMessage.success('修改成功！')
  }
}

//切换默认模板
const onChangeDef = async(event,row)=>{
  const ret = await updatePrintTmpApi({
    ids:[row.id],
    preset:event
  })
  if(ret)
    {
      getPrintTmpList()
      ElMessage.success('修改成功！')
    }
}
</script>

<template>
  <Dialog v-model="show" title="打印模板管理" max-height="60vh" width="80%" @close="closeDlg">
    <el-button class="m-5" type="success" @click="onAddNewPrintTmp">{{ t('button.add') + '模板' }}</el-button>

    <el-table  ref="tableRef" :data="printTmpData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border
      header-cell-class-name="tableHeader" >
      <el-table-column show-overflow-tooltip align="center" fixed prop="index" label="序号">
        <template #default="scope">
        {{ scope.$index }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip align="center" fixed prop="template" label="名称"/>
      <el-table-column show-overflow-tooltip align="center" fixed prop="type" label="类型"/>
      <el-table-column show-overflow-tooltip align="center" fixed prop="preset" label="是否默认">
        <template #default="scope">
          <el-checkbox v-model="scope.row.preset" label="" size="large" trueLabel="1" falseLabel="0" @change="onChangeDef($event,scope.row)"/>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip align="center" fixed prop="enable" label="是否启用">
        <template #default="scope">
          <el-switch
            v-model="scope.row.enable"
            class="ml-2"
            inline-prompt
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
            active-text="是"
            inactive-text="否"
            active-value="1"
            inactive-value="0"
            @change="onDisableTmp($event,scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" fixed label="操作"  width="200px">        
        <template #default="scope">
          <div class="flex">
            <ElButton size="small" type="success" @click="onEditTmp(scope.row)">编辑</ElButton>
            <ElButton size="small" type="warning" @click="onCopyTmp(scope.row)">复制</ElButton>
            <el-popconfirm  title="是否确认删除该模板?" @confirm="onDeleteTmp(scope.row)">
                <template #reference>
                  <ElButton size="small" type="danger" >删除</ElButton>
                </template>
            </el-popconfirm>            
          </div>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmit">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="onSubmit">{{ t('common.channel') }}</ElButton>
      </div>
    </template>

    <AddPrintTmp v-model:show="bShowModifyPrintTmp" :id="curSelID" :type="props.printType" :copydata="curCopyData" @on-submit="getPrintTmpList"/>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.cell){
  color: black;
}
</style>
<template>
    <div style="" ref="printRef" class="print-con11">
123
    </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, defineExpose, ref } from 'vue'
import * as printUtils from "@/utils/printUtils.js";

const printRef = ref(null)
const _this = reactive({
    printInfo: {},
    num: 0
})

onMounted(async () => {
    _this.printInfo = JSON.parse(sessionStorage.getItem('printInfo') || '{}')
    _this.printInfo.pdt_list.map((item) => {
        _this.num += item.发货数量 * 1
        console.log(_this.num)
    })
    // console.log('_this.orderInfo', JSON.parse(sessionStorage.getItem('printInfo') || '{}'))
})

defineExpose({ printRef })
</script>

<style soped lang="less">
.border-table {
    td {
        border-width: 1px;
        border-color: #000;
    }
}

/* table 样式 */
::v-deep table {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
}

::v-deep table td,
::v-deep table th {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    padding: 3px 5px;
    height: 32px;
}

::v-deep table th {
    border-bottom: 2px solid #ccc;
    text-align: center;
    background-color: #f1f1f1;
}
</style>

<template>
    <div ref="printRef" style="display: none;">
        <table align="center" class="ke-zeroborder" bordercolor="#000000" style="width:100%;font-size:14px;" border="0"
            cellspacing="0" cellpadding="2">
            <tbody>
                <tr>
                    <td colspan="3" style="text-align:center;">
                        <span style="font-size:28px;">东莞市文博工艺品有限公司</span>
                    </td>
                </tr>
                <tr>
                    <td colspan="3" style="text-align:center;">
                        <span style="font-size:18px;">委外发料单</span>
                    </td>
                </tr>
                <tr>
                    <td>委外商：{{ _this.printInfo.parter_nick }}</td>
                    <td>发料日期：{{ _this.printInfo.oem_date }}</td>
                    <td>发料单号：{{ _this.printInfo.oem_order_num }}</td>
                </tr>
                <tr>
                    <td>产品编码：{{ _this.printInfo.name }} <br /></td>
                    <td>产品名称：{{ _this.printInfo.nick }}<br /></td>
                    <td>产品规格：<br /></td>
                </tr>
            </tbody>
        </table>
        <table align="center" bordercolor="#000000" style="width:100%;font-size:14px;text-align:center;" border="1"
            cellspacing="0" cellpadding="2">
            <tbody>
                <tr>
                    <td>序号</td>
                    <td>产品编号</td>
                    <td>产品名称</td>
                    <td>规格</td>
                    <td>单位</td>
                    <td>需求数量</td>
                    <td>现发数量</td>
                    <td>备注</td>
                </tr>
                <tr v-for="(item, index) in _this.printInfo.pdt_list" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.nick }}</td>
                    <td>{{ item.specs_name }}</td>
                    <td>{{ item.base_unit }}</td>
                    <td>{{ item.计划数量 }}</td>
                    <td>{{ item.实际发料数量 }}</td>
                    <td>{{ item.发料备注 }}</td>
                </tr>
                <tr v-for="(it, index) in 9" :key="index">
                    <td><br /></td>
                    <td><br /></td>
                    <td><br /></td>
                    <td><br /></td>
                    <td><br /></td>
                    <td><br /></td>
                    <td><br /></td>
                    <td><br /></td>
                </tr>
                <tr>
                    <td>合计<br /></td>
                    <td><br /></td>
                    <td><br /></td>
                    <td><br /></td>
                    <td><br /></td>
                    <td><br /></td>
                    <td>{{ _this.sum }}<br /></td>
                    <td><br /></td>
                </tr>
            </tbody>
        </table>
        <table align="center" class="ke-zeroborder" bordercolor="#000000" style="width:100%;" border="0" cellspacing="0"
            cellpadding="2">
            <tbody>
                <tr>
                    <td>
                        <span style="font-size:14px;">外发单位及经手人（盖章）：</span><br />
                    </td>
                    <td>
                        <span style="font-size:14px;">收货单位及经手人（盖章）：</span><br />
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, defineExpose, ref } from 'vue'
import * as printUtils from "@/utils/printUtils.js";

const printRef = ref(null)
const _this = reactive({
    printInfo: {},
    sum: 0,//合计
})

onMounted(async () => {
    _this.printInfo = JSON.parse(sessionStorage.getItem('printInfo') || '{}')
    console.log('_this.orderInfo', JSON.parse(sessionStorage.getItem('printInfo') || '{}'))
    try {
        _this.sum = _this.printInfo.map((x, y) => {
            return x + y
        })
    }
    catch (err) {

    }
})

defineExpose({ printRef })
</script>
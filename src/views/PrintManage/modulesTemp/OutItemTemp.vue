<template>
    <div ref="printRef" style="display: none;">
        <div style="left:40px;position:absolute;">
            <img width="100" height="75" alt=""
                src="https://publicimage.haikebao.com/allimage/2023/20230109/ljz/605500000033.png" />
        </div>
        <table align="center" class="ke-zeroborder" bordercolor="#000000" style="width:100%;" border="0" cellspacing="0"
            cellpadding="2">
            <tbody>
                <tr height="45">
                    <td colspan="3">
                        <h1 align="center">
                            东莞市文博工艺品有限公司
                        </h1>
                    </td>
                </tr>
                <tr height="30">
                    <td style="text-align:center;" colspan="3">
                        <strong><span style="font-size:18px;">委外加工单</span></strong><br />
                        <br />
                    </td>
                </tr>
                <tr>
                    <td>
                        <span style="font-size:13px;">供货单位:{{ _this.printInfo?.parter_nick }}</span>
                    </td>
                    <td>
                    </td>
                    <td>
                        <span style="font-size:13px;">委外加工单NO：{{ _this.printInfo?.oem_order_num }}</span><br />
                    </td>
                </tr>
                <tr>
                    <td>
                        <span style="font-size:13px;">供货电话:{{ }}</span>
                    </td>
                    <td>
                    </td>
                    <td>
                        <span style="font-size:13px;">购方电话:0769-81397177&nbsp; 0769-8129883</span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span style="font-size:13px;">供货联系人:{{ }}</span>
                    </td>
                    <td>
                    </td>
                    <td>
                        <span style="font-size:13px;">购方联系人:{{ _this.printInfo?.oem_man_name }}</span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span style="font-size:13px;">供货地址:{{ }}</span>
                    </td>
                    <td>
                    </td>
                    <td>
                        <span style="font-size:13px;">购方地址:东莞市石排镇田寮村东桥一路5号</span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span style="font-size:13px;">交货日期：{{ _this.pdt.交货日期 }}</span>
                    </td>
                    <td>
                    </td>
                    <td>
                        <span style="font-size:14px;"><span style="font-size:13px;">接单日期:{{ _this.printInfo?.oem_date
                                }}</span></span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <br />
                    </td>
                    <td>
                        <br />
                    </td>
                    <td>
                        <br />
                    </td>
                </tr>
            </tbody>
        </table>
        <table bordercolor="#000000" style="width:100%;height:10%;border-collapse:collapse;" border="1" cellspacing="0"
            cellpadding="0">
            <tbody>
                <tr>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">订单编号</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">产品名称</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">产品图片</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">外发数量</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">单位</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">加工工序</span>
                    </td>
                    <td style="text-align:center;width:16%;">
                        <span style="font-size:12px;">含税单价 </span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;width:10%;">金额（RMB）</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">税率</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">备注</span>
                    </td>
                </tr>
                <tr v-for="(item, index) in _this.printInfo.pdt_list" :key="index">
                    <td style="text-align:center;">
                        <span style="font-size:12px;">{{ item.标识 }}</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">{{ item.nick }}</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="text-align:center;text-wrap:wrap;">
                            <div
                                style='text-align: center;display: table-cell;width:85px;vertical-align:middle;border:0px solid #f00;;'>
                                <img v-for="oo in item.pics" :src='oo.url' :key="oo.url"
                                    style='max-width:80px;max-height:80px;' />
                            </div>
                        </span><span style="font-size:14px;"></span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">{{ item.委外数量 }}</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">{{ item.specs_name }}</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">{{ item.base_unit }}</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">{{ item.oem_price_aft_tax }}</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">{{ item.总价 }}</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">{{ item.发票税率 }}%</span>
                    </td>
                    <td style="text-align:center;">
                        <span style="font-size:12px;">{{ item.委外备注 }}</span>
                    </td>
                </tr>
                <tr>
                    <td><br /></td>
                    <td><br /></td>
                    <td> <br /></td>
                    <td> <br /></td>
                    <td> <br /></td>
                    <td><br /></td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                </tr>
                <tr>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                    <td><br />
                    </td>
                </tr>
                <tr>
                    <td style="text-align:left;" colspan="10">
                        <span style="font-size:12px;">合计金额：（RMB）：<span style="text-align:center;text-wrap:wrap;">{{
                            _this.pdt.总价 }}</span></span><br />
                    </td>
                </tr>
                <tr>
                    <td colspan="10">
                        备注：
                    </td>
                </tr>
                <tr style="height:40px;">
                    <td colspan="10">
                        <p>
                            产品图片：<span style="font-family:Arial, 微软雅黑, 宋体;white-space:normal;background-color:#FFFFFF;">
                                <div
                                    style='text-align: center;display: table-cell;width:125px;vertical-align:middle;border:0px solid #f00;'>
                                    <img v-for="oo in _this.pdt.pics" :src='oo.url' :key="oo.url"
                                        style='max-width:120px;max-height:120px;' />
                                </div>
                            </span>
                        </p>
                        <p>
                            <span
                                style="font-family:Arial, 微软雅黑, 宋体;white-space:normal;background-color:#FFFFFF;"><br />
                            </span>
                        </p>
                    </td>
                </tr>
                <tr>
                    <td colspan="10">
                        <p class="MsoNormal ">
                            <span style="font-size:13px;"><strong>1、品质要求：</strong></span> <br />
                            <span style="font-size:13px;"> 1、品质须与样品一致，所有产品不可有瑕疵、缺陷和污损现象，注意数量不可短数。<br />
                                2、交货期不可随意推后，每拖期一天，扣除1%货款作为违约金。<br />
                                <b>2、备注事项：</b><br />
                                1：供方收到此订单后请务必签名盖章，并立即回传购方，以便确认！
                                2：供方必须按照购方指定时间内一次性交完大货，在购方同意下可分批交货，无购方同意下供方不能延期交期：如供方无法在交期内交货，因延期交货而造成损失，及我方客人要求之赔偿：如（空运，快递，扣款等）一切损失由供方负责！<br />
                                3：供方如果使用快递寄货，费用是寄方付。<br />
                                4：货物经购方验收如发现品质问题，数量短缺或规格不符等购方有权退货，如因此给购方造成损失，一切损失由供方负责赔偿。<br />
                                5：领料数量尽可能按接单数量领取，如在加工过程中损坏物料的，允许0.5%-1%的损耗范围，超出损耗范围的，按实际成本扣款；另外损坏物料要保留好，作回收处理，回料数量要与领料数量保持一致。财务管理中心对账只接受订单数量+订单数量的1%总和为有效数量。<br />
                                6：因出现异常（品质不良、损耗超标等），则一律按照业务接单的“整个价格”扣款。<br />
                                7：属于PMC外发包装、印刷工序的订单，结算方式按实际订单数量结算。
                                8：在我司未同意下不得将我司产品转发给第三家生产，如有发现我司将收回订单，如因货期问题造成一切经济损失的由贵司承担。<br />
                                9：包装资料上的客户信息必须保密，包装、生产的产品，不得以任何形式进行媒体发售和宣传，不良品必须退回。<br />
                                10：货物验收标准有购方事先向供方说明，且货物验收标准标准有购方定制。<br />
                                11：模具（ ）套，由我司提供，供方在使用过程中人为损坏（压模、撞模等等）由供方负责维修及费用自理，订单完成后及时退回，如有不退将作为货款抵压；<br />
                                12：结款方式：以收到发票日期算起月结60天内付款<br />
                                13：以上条例均赋有法律追述权利。</span>
                        </p>
                        <span style="font-size:13px;"></span> <br />
                    </td>
                </tr>
            </tbody>
        </table>
        <div>
            <table align="center" class="ke-zeroborder" bordercolor="#000000" style="width:100%;font-size:14px;"
                border="0" cellspacing="0" cellpadding="5">
                <tbody>
                    <tr>
                        <td>
                            供应商签名（盖章）
                        </td>
                        <td>
                            审核批准：
                        </td>
                        <td>
                            购方签名：（盖章）
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, defineExpose, ref } from 'vue'
import * as printUtils from "@/utils/printUtils.js";

const printRef = ref(null)
const _this = reactive({
    printInfo: {},
    pdt: {}
})

onMounted(async () => {
    _this.printInfo = JSON.parse(sessionStorage.getItem('printInfo') || '{}')
    console.log('_this.orderInfo', JSON.parse(sessionStorage.getItem('printInfo') || '{}'))
    _this.pdt = _this.printInfo.pdt_list[0]
})

defineExpose({ printRef })
</script>
<script setup lang="ts">
import { ElButton, ElOption, ElSelect, ElMessageBox, ElMessage, ElDialog, ElInput, ElCheckbox, ElRadio, ElRadioGroup } from 'element-plus'
import * as printUtils from "@/utils/printUtils.js";
import { HzxJson } from "./common.js";
import { reactive, onBeforeUnmount, onMounted, shallowRef, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import PurchaseTemp from "./modulesTemp/PurchaseTemp.vue";
import SaleTemp from "./modulesTemp/SaleTemp.vue";
import SaleOutTemp from './modulesTemp/SaleOutTemp.vue'
import OutItemTemp from './modulesTemp/OutItemTemp.vue'
import OutListTemp from './modulesTemp/OutListTemp.vue'
import TakeOutTemp from './modulesTemp/TakeOutTemp.vue'

import Vue3KindEditor from '@teihin/vue3-kind-editor'

const router = useRouter()
const printSaleRef: any = ref(null)
const printPurRef: any = ref(null)
const printSaleOutRef: any = ref(null)
const printOutItemRef: any = ref(null)
const printOutListRef: any = ref(null)
const printTakeOutRef: any = ref(null)

const editorRef = shallowRef();
const valueHtml = ref('')
const dialogVisible = ref(false)

let LODOP
const _this: any = reactive({
    printInfo: {},
    modifyText: '修改内容',
    changeCss: 'hidden-box',

    nPageWidth: 210,
    nPageHeight: 297,
    nPaddingTop: 5,
    nPaddingBottom: 5,
    nPaddingLeft: 5,
    nPaddingRight: 5,
    nTopHeight: 0,
    nFootHeight: 0,
    nLeftWidth: 0,
    nRightWidth: 0,
    nMiddleHeight: 287,
    nMiddleWidth: 200,
    nFollowHeight: 0,

    sLodopMode: "Cloud",//打印模式
    printList: [],//打印机列表
    sPrintName: { label: "", value: '' },//打印机名称和val
    strPageSizeList: [],//纸张列表
    sPageType: " ",//纸类型
    nDirection: 1,
    pageDirection: 1,//纸方向
    offset_x: 0,
    offset_y: 0,
    nWidth: 2100,
    nHeight: 2970,
    sPrintTitle: '标题',
    sIdList: "00008532:销售单唐山启奥科技股份有限公司_00008532:1",//打印单据的第一个编号

    sTitleRandom: "",//随机数标题
    sTaskCmd: "",
    sBatchPrintFlag: "",
    sBodyType: "html",

})

const updateTitleRandom = () => {
    _this.sTitleRandom = printUtils.randomNumber(6);
}
//获取标题
const getTitle = () => {
    var sTitleTemp = _this.sPrintTitle;
    if (_this.sPrintTitle != "") {
        sTitleTemp += "_";
    }
    sTitleTemp += _this.sTitleRandom;
    return sTitleTemp;
}
//打印机列表
function createPrintList() {
    _this.printList = []
    //控件和云打印模式加载打印机
    if (_this.sLodopMode == "Internet") {

    } else {
        let nPrintCount = LODOP.GET_PRINTER_COUNT();//获取打印机的个数
        for (let i = 0; i <= nPrintCount - 1; i++) {
            let strDName = LODOP.GET_PRINTER_NAME(i);
            _this.printList.push({ value: i, label: strDName })
        }
    }
}
//打印机更换
function printChange() {
    createPagSizeList();
    HzxPrintView("");
}
//加载纸张列表
function createPagSizeList() {
    _this.strPageSizeList = [];
    if (_this.sLodopMode == "Internet") {
        return
    } else {
        var sPrintId = _this.sPrintName.value;
        _this.strPageSizeList = LODOP.GET_PAGESIZES_LIST(sPrintId, "\n");
    }
    _this.strPageSizeList = _this.strPageSizeList.split("\n");
}
//初始化
const printInit = () => {
    if (_this.sLodopMode == "ActiveX") {
        // LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
    } else if (_this.sLodopMode == "Cloud") {
        LODOP = window.getLodop();
    }
    LODOP.PRINT_INIT(getTitle());
    //获取打印机列表
    createPrintList();
    //加载纸张类型
    createPagSizeList();
    //纸张方向
    //获取div内容
    getContext();
    HzxPrintView('');
    if (_this.sPdfFlag == "pdf" && _this.sPrintType == "more") {
        setTimeout("BatchPrint()", 1000);
    }
}
const isGZ = ref(false)
//获取替换内容
const getContext = (e) => {
    switch (_this.printInfo.printType) {
        case 'purchase':
            valueHtml.value = printPurRef.value.printPurRef.innerHTML;
            break;
        case 'sale':
            valueHtml.value = printSaleRef.value.printSaleRef.innerHTML;
            break;
        case 'saleOut':
            valueHtml.value = printSaleOutRef.value.printRef.innerHTML;
            break;
        case 'OutItem':
            valueHtml.value = printOutItemRef.value.printRef.innerHTML;
            break;
        case "OutList":
            valueHtml.value = printOutListRef.value.printRef.innerHTML;
        case "TakeOut":
            valueHtml.value = printTakeOutRef.value.printRef.innerHTML;
        default:
            valueHtml.value = printTakeOutRef.value.printRef.innerHTML;
    }
}
const GZEvt = (e) => {
    printChange()
}
const AddPrintHTM = () => {
    console.log('222222222222')
    LODOP.PRINT_INIT(getTitle());//预览初始化  将内容置空
    LODOP.SET_PRINT_MODE("POS_BASEON_PAPER", true);//设置输出位置以纸张边缘为基点。
    // LODOP.SET_SHOW_MODE("HIDE_PBUTTIN_PREVIEW", true);//隐藏打印按钮
    LODOP.SET_SHOW_MODE("HIDE_SBUTTIN_PREVIEW", true);//隐藏打印设置按钮
    //纸张方向
    if (!printUtils.isDecimal(_this.offset_x + '') || !printUtils.isDecimal(_this.offset_y + '')) {
        ElMessageBox.alert("偏移值办理入不正确，请办理入一个有效的整数或小数");
        return;
    }
    getContext()
    var nOffSetX = parseInt(Number(_this.offset_x));
    var nOffSetY = parseInt(Number(_this.offset_y));
    if (_this.sPageType == " ") {
        if (_this.pageDirection == 1) {
            LODOP.SET_PRINT_PAGESIZE(1, _this.nWidth, _this.nHeight, "");
        } else {
            LODOP.SET_PRINT_PAGESIZE(2, _this.nHeight, _this.nWidth, "");
        }
    } else {
        LODOP.SET_PRINT_PAGESIZE(_this.pageDirection - 0, 0, 0, _this.sPageType);
    }
    //页眉
    var Top = "";
    var Left = "";
    var Width = "";
    var Height = "";
    if (_this.nTopHeight > 0) {
        Top = (_this.nPaddingTop + nOffSetY) + "mm";
        Left = (_this.nPaddingLeft + nOffSetX) + "mm";
        Width = (_this.nPageWidth - _this.nPaddingLeft - _this.nPaddingRight) + "mm";
        Height = _this.nTopHeight + "mm";
        LODOP.ADD_PRINT_HTM(Top, Left, Width, Height, _this.sTopContext);
        LODOP.SET_PRINT_STYLEA(0, "ItemType", 1);
    }
    //下述代码主要是取消分页符在真正打印中出现
    var sContext = valueHtml.value;
    // console.log('valueHtml.value', valueHtml.value)
    var nPosition1 = sContext.indexOf("<hr");
    var nPosition2 = sContext.indexOf("/>", nPosition1);
    while (nPosition1 > 0 && nPosition2 > 0) {
        sContext = sContext.substring(0, nPosition1) + "<div style=\"page-break-after: always;\">&nbsp;</div>" + sContext.substring(nPosition2 + 2);
        nPosition1 = sContext.indexOf("<hr");
        nPosition2 = sContext.indexOf("/>", nPosition1);
    }
    if (_this.sBodyType == "table") {
        Top = (_this.nPaddingTop + _this.nTopHeight + nOffSetY) + "mm";
        Left = (_this.nPaddingLeft + _this.nLeftWidth + nOffSetX) + "mm";
        Width = _this.nMiddleWidth + "mm";
        Height = (_this.nMiddleHeight - _this.nFollowHeight) + "mm";
        LODOP.ADD_PRINT_TABLE(Top, Left, Width, Height, sContext);
        LODOP.SET_PRINT_STYLEA(0, "ItemType", 0);
    } else {
        Top = (_this.nPaddingTop + _this.nTopHeight + nOffSetY) + "mm";
        Left = (_this.nPaddingLeft + _this.nLeftWidth + nOffSetX) + "mm";
        Width = _this.nMiddleWidth + "mm";
        Height = (_this.nMiddleHeight - _this.nFollowHeight) + "mm";
        LODOP.ADD_PRINT_HTM(Top, Left, Width, Height, sContext);
        LODOP.SET_PRINT_STYLEA(0, "ItemType", 0);
    }

    LODOP.SET_SHOW_MODE("LANDSCAPE_DEFROTATED", 1);//横向时的正向显示
    //判断是否选择打印机
    LODOP.SET_PRINTER_INDEX(_this.sPrintName.value);//指定打印设备
    LODOP.SET_PRINT_MODE("RESELECT_ORIENT", true);//设置是否可以重新选择打印方向
    LODOP.SET_PRINT_MODE("RESELECT_PAGESIZE", true);//设置是否可以重新选择纸张
    LODOP.SET_PRINT_MODE("RESELECT_COPIES", true);//设置是否可以重新选择打印份数。

    //=====授权信息，请不要更改或删除！！===================================
    LODOP.SET_LICENSES("", "949BF0C7516C3851E0F5AA728041700D", "C94CEE276DB2187AE6B65D56B3FC2848", "");
    //============================================================
}

//调用打印预览函数
const HzxPrintView = (type = "") => {
    let sPrintId = _this.sPrintName.value;
    if (sPrintId === "") {
        return ElMessage({
            message: '请选择打印机!',
            type: 'warning',
        })
    }
    AddPrintHTM();
    if (type == 'full') {
        LODOP.SET_SHOW_MODE("PREVIEW_IN_BROWSE", false);
        LODOP.PREVIEW();
    } else {
        _this.sTaskCmd = "PREVIEW";
        if (_this.sLodopMode == "ActiveX") {
            LODOP.SET_SHOW_MODE("PREVIEW_IN_BROWSE", true);
            LODOP.PREVIEW();
            if (_this.sBatchPrintFlag == "begin") {
                ActiveXNextPrint(nPrintCount);
            }
        } else {
            LODOP.PREVIEW('iframelodop');
        }
    }
}
//全屏预览
function FullScreen() {
    HzxPrintView('full');
}

const props = defineProps<{
    show: boolean,
}>()
const show = ref(false);
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
    if (val) {
        show.value = true
        console.log(11111)
        openPrint()
    }
})
const openPrint = async () => {
    _this.printInfo = JSON.parse(sessionStorage.getItem('printInfo') || '{}')
    console.log('_this.orderInfo', JSON.parse(sessionStorage.getItem('printInfo') || '{}'))

    if (_this.sLodopMode == "ActiveX") {//控件模式
        printInit();
    }
    updateTitleRandom();
    _this.sLodopMode = "Cloud";
    LODOP = await window.getLodop();
    printInit();
}

/***********批量打印结束****************/
//另存为图片
function SaveImage() {
    if (_this.sLodopMode != "ActiveX") {
        AddPrintHTM();
    }
    var sTitleTemp = _this.sPrintTitle;
    if (_this.sPrintTitle == "") {
        sTitleTemp = _this.sDate;
    }
    LODOP.SAVE_TO_FILE(sTitleTemp + ".jpg");
}
function changeLodopType() {
    // printInit()
}
function Modify() {
    var s = _this.modifyText;
    if (s == "修改内容") {
        _this.changeCss = ''
        _this.modifyText = "保存修改";
    } else {
        _this.changeCss = 'hidden-box'
        _this.modifyText = "修改内容";
        HzxPrintView();
    }
}


// 组件销毁时，也及时销毁编辑器，重要！
onBeforeUnmount(() => {
    const editor = editorRef.value;
    if (editor == null) return;
    editor.destroy();
});

// 编辑器回调函数
const handleCreated = (editor) => {
    editorRef.value = editor; // 记录 editor 实例，重要！
};
</script>

<template>
    <div>
        <el-dialog v-model="show" fullscreen title="打印" >
            <!-- <div class="bg-white"> -->
            <form id='mainform' action="#" method="post" class="h-[100%]">
                <table class='htable h-[94%]' style='width:100%;margin:0px auto;' >
                    <tr style='height:35px;'>
                        <td colspan=4 style='height:30px;'>
                            <div class="flex justify-evenly pl-2 pr-2">
                                <ElButton id='buttonModify' style='width:150px;height:30px;line-height:30px'
                                    @click="() => { show = false }">
                                    关闭
                                </ElButton>
                                <ElButton id='buttonModify' style='width:150px;height:30px;line-height:30px'
                                    @click="Modify">
                                    {{ _this.modifyText }}
                                </ElButton>
                                &nbsp;&nbsp;&nbsp;
                                <ElButton id='buttonModify' style='width:150px;height:30px;line-height:30px'
                                    @click="FullScreen">全屏预览
                                </ElButton>
                                &nbsp;&nbsp;&nbsp;
                                <ElButton style='width:150px;height:30px;line-height:30px' @click="() => LODOP.PRINT()">
                                    另保存(pdf)
                                </ElButton>
                                &nbsp;&nbsp;&nbsp;
                                <ElButton style='width:150px;height:30px;line-height:30px' @click="SaveImage">
                                    另保存(图片)
                                </ElButton>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th style='width:120px;height:30px;'>打印机信息</th>
                        <td class="p-1" style='text-align:left;'>
                            <span id='birdgelist'></span>
                            <ElSelect id='printlist selectprint' v-model="_this.sPrintName" placeholder="--请选择打印机--"
                                size="small" @change="printChange">
                                <ElOption v-for="item in _this.printList" :key="item.value" :label="item.label"
                                    :value="item" />
                            </ElSelect>
                            <ElSelect id='spanPaperList PaperList' v-model="_this.sPageType" placeholder="选择纸张"
                                size="small" @change="HzxPrintView()">
                                <ElOption label="自定义纸张" value=" " />
                                <ElOption v-for="item in _this.strPageSizeList" :key="item" :label="item"
                                    :value="item" />
                            </ElSelect>
                            &nbsp;(210mm*297mm)&nbsp;&nbsp;&nbsp;
                            进纸方向:
                            <ElSelect id='pagedirection' v-model="_this.pageDirection" style='width:100px;height:25px;'
                                size="small" @change="HzxPrintView()">
                                <ElOption label="纵向" :value="1" />
                                <ElOption label="横向" :value="2" />
                            </ElSelect>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            水平偏移:
                            <ElInput id='offset_x' v-model='_this.offset_x' @change="HzxPrintView()" size="small"
                                style='width:30px;margin:3px;height:20px;text-align:center' />
                            毫米&nbsp;&nbsp;&nbsp;&nbsp;
                            垂直偏移:
                            <ElInput id='offset_y' v-model='_this.offset_y' @change="HzxPrintView()" size="small"
                                style='width:30px;margin:3px;height:20px;text-align:center' />
                            毫米&nbsp;&nbsp;&nbsp;&nbsp;
                        </td>
                        <td class="w-150px text-center">
                            <ElSelect id='lodoptype' style='width:100px;height:25px;' size="small"
                                v-model="_this.sLodopMode" @change='changeLodopType()'>
                                <!-- <ElOption value='ActiveX' label="控件模式" /> -->
                                <ElOption value='Cloud' label="云打印模式" />
                                <!-- <ElOption value='Internet' label="互联网打印" /> -->
                            </ElSelect>
                        </td>
                    </tr>
                    <tr>
                        <th style='width:120px;height:30px;'>模板</th>
                        <td class="p-1">
                            <el-radio-group v-model="isGZ" @change="GZEvt">
                                <el-radio :label="0" size="small">无章</el-radio>
                                <el-radio size="small" :label="1">盖章</el-radio>
                            </el-radio-group>
                        </td>
                        <td class="w-150px text-center">
                            <ElButton>设置</ElButton>
                        </td>
                    </tr>
                    <tr id='trText' :class="_this.changeCss" style='height:100%;'>
                        <th>内容</th>
                        <td style='text-align:center;' colspan='2'>
                            <vue3-kind-editor id="editor_1" height="1000" width="800" v-model="valueHtml"  :loadStyleMode="false">1</vue3-kind-editor>
                        </td>
                    </tr>
                    <tr id='trLodop' :class="_this.changeCss ? '' : 'hidden-box'" style="height:1000px;">
                        <td colspan=3 style='text-align:center; height: 1000px'>
                            <iframe id='iframelodop' name='iframelodop' style='width:100%;height:100%'></iframe>
                        </td>
                    </tr>
                </table>
            </form>
            <SaleTemp v-show="false" ref="printSaleRef" />
            <PurchaseTemp v-show="false" ref="printPurRef" :isGZ="isGZ" />
            <SaleOutTemp v-show="false" ref="printSaleOutRef" />
            <OutItemTemp v-show="false" ref="printOutItemRef" />
            <OutListTemp v-show="false" ref="printOutListRef" />
            <TakeOutTemp v-show="false" ref="printTakeOutRef" />
            <!-- </div> -->
        </el-dialog>
    </div>
    <div id='topcontext' style="display:none;"></div>
    <div id='footcontext' style='display:none;'></div>
    <div id='contextfollow' style='display:none;'></div>
    <div id='leftcontext' style='display:none;'></div>
    <div id='rightcontext' style='display:none;'></div>
</template>

<style lang="less" scoped>
@import './print.css';

.hidden-box {
    display: none !important;
}

:deep(.el-dialog__body) {
    padding: 0;
    color: var(--el-text-color-regular);
    font-size: var(--el-dialog-content-font-size);
    height: 100%;
}
</style>

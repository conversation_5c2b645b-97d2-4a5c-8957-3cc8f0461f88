<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getSellPrepareListApi,getCategListApi, getSaleDemandInfoApi, updateCategApi, delCategApi,getInventoryListApi, updateProductApi,delProductApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { RightMenu } from '@/components/RightMenu'
import { Dialog } from '@/components/Dialog'
import { computed,nextTick } from 'vue';
import { useCache } from '@/hooks/web/useCache'
import { DialogSaleDemandDetail } from '@/components/DialogSaleDemandDetail'
import type { RouteLocationNormalizedLoaded, RouteRecordRaw } from 'vue-router'
import { usePermissionStore } from '@/store/modules/permission'
import { Layout, getParentLayout } from '@/utils/routerHelper'
import { closeOneTagByName, closeOneTagByPath, getGUID } from '@/api/tool';

const { currentRoute,push,addRoute,back } = useRouter()
const router = useRouter();
const permissionStore = usePermissionStore()
const { t } = useI18n()
const { wsCache } = useCache()

//分类树
const categTree = ref()
//当前选中分类节点
const currentCatg = ref('')
//当前选中分类节点详细信息
const currentCatgData = ref({})
//分类树数据源
const categData = reactive([])
//分类树默认属性
const defaultProps = {
  children: 'sub_categ',
  label: 'name',
}
//分类树默认展开节点
const expandCateg = reactive([])
//分类树右键菜单classname
const categMenuClassName = 'categ_menu'
//分类右键菜单
const menuCateg = reactive([
  {
    icon: "icon-park-outline:add",
    name: 'add_categ',
    title: t('product_manage.add_categ')
  },
  {
    icon: "icon-park-outline:add",
    name: 'update_categ',
    title: t('product_manage.update_categ')
  },
  {
    icon: "icon-park-outline:add",
    name: 'delete_categ',
    title: t('product_manage.delete_categ')
  },
])

//需求数据
const demandData = reactive([])

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  categ_id:'',
  物料编号:'',
  物料名称:'',
  规格:'',
  page: 1,
  count: 300
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//查询产品分类树
const getCategList = async () => {
  const ret = await getCategListApi({
    page: 1,
    count: 10000
  })
  if (ret) {
    console.log(ret)
    console.log('当前选中')

    //设置默认选中
    const lastSelCateg = wsCache.get('last_sel_categ') || {}
    console.log(categTree.value)
    console.log(lastSelCateg.id)
    currentCatgData.value = lastSelCateg
    currentCatg.value = lastSelCateg.id
    searchCondition.categ_id = lastSelCateg.id
    if(lastSelCateg.id != undefined)
    {
      nextTick(()=>{
        categTree.value.setCurrentNode(lastSelCateg)
        console.log(categTree.value?.getCurrentNode())
      })
    }


    categData.splice(0, categData.length, ret.data.all_categs)
    //设置默认展开
    expandCateg.splice(0, expandCateg.length, ret.data.all_categs.id)
    if (currentCatg.value) {
      expandCateg.push(currentCatg.value)
    }
  }
}
//分类树点击左键
const leftClick = (data) => {
  currentCatgData.value = data
  searchCondition.categ_id = data.id
  //更新最后一次选中
  wsCache.set('last_sel_categ',data)
  getSellPrepareList()
}

//分类树点击右键菜单
const rightClick = (event, data) => {
    return //不支持
  
}
//隐藏右键菜单
const hideRightMenu = () => {
  const menu = document.querySelector("#" + categMenuClassName) as HTMLElement;
  menu!.style.display = "none";
  document.removeEventListener("click", hideRightMenu);
}
//处理分类树菜单消息
const handleMenuEvent = (item) => {
  console.log(item)
  hideRightMenu()
}


//开始查询
const onSearch = () => {
  getSellPrepareList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getSellPrepareList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getSellPrepareList(val)
}

//显示隐藏需求明细查询
const showDemandDetail = ref(false)
//当前选择的需要显示明细的pdt
const curSelPdt = reactive({})

//处理表格对象操作
const handleOper = async(type, item) => {
  //显示需求详情
  if(type === 'detail')
  {
    showDemandDetail.value = true
    Object.assign(curSelPdt, item)
  }
  else if(type === 'tobuy') //转采购
  {
    if(item.目前缺口>0)
    {
      const arPdtBuyNum =  creatJumpParam(item)
      //缓存数据
      wsCache.set('tobuy', arPdtBuyNum)
      // addRouter()
      closeOneTagByPath('/salemanage/addpurchase')
      push({
        path: '/salemanage/addpurchase',
        query:{
            id:'',
            tobuy:'1',
            source:'销售转采购物料'
        },    
        })
    }
    else
    {
      ElMessage.warning('当前没有采购需求')
    }      

  }
  else if(type === 'tooem')
  {
    if(item.目前缺口>0)
    {
      const arPdtBuyNum =  creatJumpParam(item)
      //缓存数据
      wsCache.set('tooem', arPdtBuyNum)
      // addRouter()
      push({
        path: '/salemanage/addoemorder',
        query:{
            id:'',
            tooem:'1',
            source:'销售备料转委外'
        },    
      })
    }
    else
    {
      ElMessage.warning('当前没有采购需求')
    } 
   
    
  }
  else if(type === 'tobuy_all') //批量转采购
  {
    console.log('11111111111')
    const tmp = arrayWLSel.filter(item=>item.目前缺口>0)
    if(tmp.length == 0)
    {
      ElMessage.warning('请选择要转采购的产品')
      return
    }
    //构造pdt列表
    let arPdtBuyNum:any[] = []
    for(let item of tmp)
    {
      const arGet = creatJumpParam(item)
      //校验是否有重复表欧式
      arPdtBuyNum.push(...creatJumpParam(item))
    }

    if(arPdtBuyNum.length > 0)
    {
      //缓存数据
      wsCache.set('tobuy', arPdtBuyNum)
      // addRouter()
      closeOneTagByPath('/salemanage/addpurchase')
      push({
        path: '/salemanage/addpurchase',
        query:{
            id:'',
            tobuy:'1',
            source:'销售转采购物料'
        },    
        })
    }
    else
      {
        ElMessage.warning('当前没有采购需求')
      }         

  }
  else if(type === 'tooem_all')//批量转委外
  {
    const tmp = arrayWLSel.filter(item=>item.目前缺口>0)
    if(tmp.length == 0)
    {
      ElMessage.warning('请选择要转委外的产品')
      return
    }
    //构造pdt列表
    let arPdtBuyNum:any[] = []
    for(let item of tmp)
    {
      arPdtBuyNum.push(...creatJumpParam(item))
    }

    //缓存数据
    wsCache.set('tooem', arPdtBuyNum)
    // addRouter()
    push({
      path: '/salemanage/addoemorder',
      query:{
          id:'',
          tooem:'1',
          source:'销售备料转委外',
      },    
    })
  }

}

const creatJumpParam = (item)=>{
  let arPdtBuyNum: any[] = []
  let param = {
    pdt_name : item.物料编号,
    sell_order_num :'',
    当前需求:item.目前缺口,
    标识: '',//item.pdt_list[0].标识.split('_')[0],       
    sell_order_sub:'',
    子任务单号:'',
    销售备品数量: 0,   //默认转单不带备品     
    需求数量:'',
    物料总用量:item.物料总用量
  }
  let arOrderNum:string[] = []
  let arSub: string[] = []
  let arZi: string[] = []
  let arCount: string[] = []
  let arBS: string[] = []
  for(let one of item.pdt_list)
  {
    arOrderNum.push(one.sell_order_num)
    arSub.push(one.sell_order_sub)
    arZi.push(item.子任务单号)
    arCount.push(one.当前需求)
    // arBS.push(one.标识.split('_')[0])
    arBS.push(one.标识)
  }

  

  let arTmp:string[] = []
  let nTotle = item.当前总供应  //仓库可以提供的总数
  for(let one of item.pdt_list)
  {
    //库存优先留给最早创建的单子
    //扣除库存还需要采购的数量
    nTotle = nTotle-one.当前需求
    if(nTotle>=0) //库存够他用
    {
      arTmp.push('0')
      continue
    }
    //库存不够
    let nNeed = Math.abs(nTotle)
    arTmp.push(nNeed.toString())
  
    //总库存复位0
    nTotle = 0
  }
  //去掉arTmp前面有'0'的部分
  let nIndex = 0
  for(let one of arTmp)
  {
    if(one == '0')
    {
      nIndex++
    }
    else
    {
      break
    }
  }
  arTmp.splice(0,nIndex)
  arOrderNum.splice(0,nIndex)
  arSub.splice(0, nIndex)
  arZi.splice(0, nIndex)
  arBS.splice(0, nIndex)
  
  //填写需求量
  param.需求数量 = arTmp.join(',')
  param.sell_order_num = arOrderNum.join(',')
  param.sell_order_sub = arSub.join(',')
  param.子任务单号 = arZi.join(',')
  param.标识 = arBS.join(',')


  arPdtBuyNum.push(param)
  return arPdtBuyNum
  // let arPdtBuyNum:any[] = []
  //   let nTotle = item.当前总供应  //仓库可以提供的总数

  //   for(let one of item.pdt_list)
  //   {
  //     //库存优先留给最早创建的单子
  //     //扣除库存还需要采购的数量
  //     nTotle = nTotle-one.当前需求
  //     if(nTotle>=0) //库存够他用
  //     {
  //       continue
  //     }
  //     //库存不够
  //     let nNeed = Math.abs(nTotle)
  //     arPdtBuyNum.push({
  //           pdt_name : item.物料编号,
  //           sell_order_num : one.sell_order_num,
  //           当前需求:nNeed,
  //           标识:one.标识,       
  //           sell_order_sub:one.sell_order_sub,
  //           子任务单号:one.子任务单号,
  //           销售备品数量:0   //默认转单不带备品     
  //         })
  //     //总库存复位0
  //     nTotle = 0
  //   }
  //   return arPdtBuyNum
}
const isLoading = ref(false)
//查询需求列表
const getSellPrepareList = async (page = 1) => {
  const lastSelCateg = wsCache.get('last_sel_categ') || {}
  searchCondition.categ_id = lastSelCateg.id
  searchCondition.page = page

  isLoading.value = true
  const ret = await getSellPrepareListApi(searchCondition)
  if(ret)
  {
    demandData.splice(0,demandData.length, ...ret.data)
    console.log(demandData)
    totleCount.value = parseInt(ret.count)
    tableRef.value!.clearSelection()

    if(currentRoute.value.query.ignored == '1') //忽略库存模式
    {
      for(let one of demandData)
      {
        one.可用库存 = 0
        one.当前总供应 = one.正在采购+one.正在委外
        one.目前缺口 = one.需求总量-one.正在采购-one.正在委外-one.已出库
      }
    }
  }
  isLoading.value = false
}


onMounted(() => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  if(currentRoute.value.query.sell_order_num != undefined)
  {
    searchCondition.sell_order_num = currentRoute.value.query.sell_order_num
    searchCondition.is_combine = 1
  }
  else
  {
    searchCondition.is_combine = 1
    //更新分类树
    getCategList()
  }
  if(currentRoute.value.query.pdt_biaoshi != undefined)
  {
    searchCondition.pdt_biaoshi = currentRoute.value.query.pdt_biaoshi
  }



  //更新需求列表
  getSellPrepareList()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
  
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});


const tableRowClassName = (row,index)=>{
  if(row.row.目前缺口>0)
  {
    if(row.row.正在采购>0)
    {
      return 'error-row2'
    }
    else
      return 'error-row'
  }
}

//当前选择的物料列表
const arrayWLSel = reactive([])
const handleSelectionChange = (vals)=>{  
  arrayWLSel.splice(0,arrayWLSel.length, ...vals)
  console.log('----',arrayWLSel,vals)
}

//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}


const tableRef = ref<InstanceType<typeof ElTable>>()
</script>

<template>
  <div class="flex">   
    <!-- 左侧分类栏 -->
    <div class="w-[25%] max-w-[250px] bg-white p-2" v-show="currentRoute.query.sell_order_num == undefined">
      <el-tree ref="categTree" :data="categData" :props="defaultProps" :default-expanded-keys="expandCateg" node-key="id"
        @node-contextmenu="rightClick" @node-click="leftClick" highlight-current :current-node-key="currentCatg"
        :expand-on-click-node="false" :render-after-expand="true">
        <template #default="{ node }">
          <Icon icon="bx:category" />
          <div class="pl-2">{{ node.data.name  }}
          </div>
        </template>
      </el-tree>
      <RightMenu :id="categMenuClassName" :menuDate="menuCateg" @on-menu-event="handleMenuEvent" />
    </div>
    <div class="w-2"></div>
    <!-- 右侧产品列表 -->
    <div ref="rootRef" class="relative w-[100%] !bg-white flex-grow overflow-hidden">
      <div class="h-[100%] bg-white p-7">
        <el-button v-show="currentRoute.query.sell_order_num != undefined" @click="baskFront()">
            <Icon icon="ep:arrow-left" class="mr-5px" />
            {{ t('common.back') }}
          </el-button>
        <div class="text-center mb-5 font-bold">销售物料备料表</div>
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200" >
          <!-- 检索条件 -->          
          <div class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input v-model="searchCondition.物料名称" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input v-model="searchCondition.物料编号" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
            <el-input v-model="searchCondition.规格" placeholder="" size="small" class="searchItem" />
          </div>


          
          <div  class="text-center mt-5 mb-2 flex justify-end items-center">
            <el-checkbox :label="t('customer.senior')" v-model="senior" size="small"/>
            <ElButton class="ml-5" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 产品列表 -->
        <el-table v-loading.lock="isLoading" ref="tableRef" :data="demandData" style="width: 100%; margin-bottom: 20px" row-key="id" border 
         header-cell-class-name="tableHeader" :row-class-name="tableRowClassName" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column show-overflow-tooltip width='120'  align="center"  prop="物料编号" :label="t('oem.wl_name')" />
            <el-table-column show-overflow-tooltip width='150' align="center"  prop="物料名称" :label="t('oem.wl_nick')" >
              <template #default="scope">
                <div class="whitespace-normal">{{ scope.row.物料名称}}</div>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip align="center" :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        class="box-item"
                        effect="dark"
                        :content="scope.row.规格文本"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.规格名称 }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip align="center"   prop="单位" :label="t('product_manage.b_unit')" />
            <el-table-column show-overflow-tooltip align="center"   prop="需求总量" :label="t('saledemand.totle')" />
            <el-table-column show-overflow-tooltip align="center"   prop="受托商可用库存" :label="t('oem.parter_stone')" />
            <el-table-column show-overflow-tooltip align="center"   prop="受托商锁定库存" :label="t('oem.parter_lock')" />
            <el-table-column show-overflow-tooltip align="center"   prop="已出库" :label="t('oem.out_count')" />
            <el-table-column show-overflow-tooltip align="center"   prop="当前需求" :label="t('saledemand.cur')" />
            <el-table-column show-overflow-tooltip align="center"   prop="锁定数量" :label="t('saledemand.locked')" />
            <el-table-column show-overflow-tooltip align="center"   prop="可用库存" :label="t('saledemand.canuse')" />
            <el-table-column show-overflow-tooltip align="center"   prop="正在收货" :label="t('saledemand.curreceive')" />
            <el-table-column show-overflow-tooltip align="center"   prop="正在采购" :label="t('saledemand.curbuy')" />
            
            <el-table-column show-overflow-tooltip align="center"   prop="正在生产" :label="t('saledemand.cursc')" />
            <el-table-column show-overflow-tooltip align="center"   prop="正在委外" :label="t('saledemand.curoutbuy')" />
            <el-table-column show-overflow-tooltip align="center"   prop="当前总供应" :label="t('saledemand.totlesup')" />
            <el-table-column show-overflow-tooltip fixed="right" align="center"   prop="目前缺口" :label="t('saledemand.curneed')" />

          <el-table-column show-overflow-tooltip fixed="right" align="center" width="80px" prop="name" :label="t('roleTable.opt')" >
            <template #default="scope">
                <el-dropdown trigger="click" placement="bottom" >
                    <span class="el-dropdown-link">
                      <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleOper('tobuy', scope.row)">{{ t('saledemand.tobuy') }}</el-dropdown-item>
                        <!-- <el-dropdown-item @click="handleOper('toProduct', scope.row)">{{ t('saledemand.toProduct') }}</el-dropdown-item> -->
                        <!-- <el-dropdown-item @click="handleOper('tooem', scope.row)">{{ t('saledemand.toOut') }}</el-dropdown-item> -->
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <div class="mb-3">
          <ElButton color="#409eff" style="color: #fff;" @click="handleOper('tobuy_all',null)">批量转采购</ElButton>
          <!-- <ElButton type="success" @click="handleOper('tooem_all',null)">批量转委外</ElButton> -->
        </div>
        <div class='flex items-center'>
          <div class="ml-auto mr-2 text-red-600">
            已勾选<span><strong>{{ arrayWLSel.length }}</strong></span>条
          </div>
          <el-pagination class="flex justify-end"
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          :page-sizes="[300,500,1000]"
          :background="true"
          layout="sizes, prev, pager, next"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
        </div>
      <!-- <div class="mb-[200px]"></div> -->
      </div>
      <!-- <DialogSaleDemandDetail v-model:show="showDemandDetail" :pdt="curSelPdt" :order_nums="searchCondition.order_nums"/> -->
    </div>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   font-size: 11px;
//   font-weight: 500;
// }

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color:#e1f3d8 !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
// .header {
//   display: flex;
//   border: 1px solid #e2e2e2;
//   border-collapse: collapse;
//   width: 100%;
//   color: var(--el-text-color-regular);
//   font-size: 15px;
// }
// .headerBk{
//   background-color: #6d92b4 !important;
// }
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

:deep(.error-row .el-table__cell){
  // background-color: rgb(252, 196, 196);
  color:#F56C6C;
}

:deep(.error-row2 .el-table__cell){
  // background-color: rgb(252, 196, 196);
  color:#F56C6C;
  background-color: #ffe4e4
}

</style>

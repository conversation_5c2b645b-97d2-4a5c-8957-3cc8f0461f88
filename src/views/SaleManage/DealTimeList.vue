<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElProgress, ElCol, ElTag, ElButton, ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio, ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getSaleListApi, delSaleApi, updateSaleApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { DialogProduct } from '@/components/DialogProduct';
import { useAppStore } from '@/store/modules/app'
import { DialogSellBakInfo } from '@/components/DialogSellBakInfo';
import { DialogFileList } from "@/components/DialogFileList";
import { checkPermissionApi, checkRouterPermissionApi, getMoneyFlag } from '@/api/tool';
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { DialogUser } from '@/components/DialogUser'

const { currentRoute, push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//交货日期列表数据源
const dealTimeData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    page: 1,
    count: 10
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
    getSaleList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

const getDealTimeList = () => {

}


</script>

<template>
    <div ref="rootRef" class="absolute top-[0px] right-[0px] left-[0px] bottom-[0px] overflow-auto">
        <div class="p-7 pt-0 relative " style="background-color: var(--app-content-bg-color);">

            <!-- <div class="absolute top-5 left-15">
                <ElButton v-if="checkPermissionApi('销售订单新增')" type="success" @click="onAddPurchase">
                    <Icon icon="carbon:document-add" />
                    <div class="pl-1">{{ t('button.add') }}</div>
                </ElButton>
            </div> -->

            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-2 mb-2 bg-white">
                <div class="text-center mb-5 font-bold">{{ t('dealtime.list') }}</div>
                <div class="inline-flex items-center ml-2 mb-2">
                    <div class="searchTitle">{{ t('customer.name') }}</div>
                    <el-input size="small" v-model="searchCondition.buyer_nick" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-2 mb-2">
                    <div class="searchTitle">{{ t('product_manage.id') }}</div>
                    <el-input size="small" v-model="searchCondition.pdt_name" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-2 mb-2">
                    <div class="searchTitle">{{ t('product_manage.name') }}</div>
                    <el-input size="small" v-model="searchCondition.pdt_nick" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-2 mb-2">
                    <div class="searchTitle">销售人员</div>
                    <el-input size="small" v-model="searchCondition.sell_man_name" placeholder="" class="!w-[150px]" />
                    <ElButton size="small" @click="onSelSale" class="w-[50px]">
                        <Icon icon="iconamoon:search-bold" />
                    </ElButton>
                </div>
                <div class="inline-flex items-center ml-2 mb-2">
                    <div class="searchTitle">交货状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.status" placeholder="请选择">
                        <el-option v-for="item in ['未出库', '部分出库', '完全出库', '超量出库', '出库且退货']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>
                <div class="inline-flex items-center ml-2 mb-2">
                    <div class="searchTitle">备货状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.status" placeholder="请选择">
                        <el-option v-for="item in ['未出库', '部分出库', '完全出库', '超量出库', '出库且退货']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>
                <div class="inline-flex items-center ml-2 mb-2">
                    <div class="searchTitle">交货日期</div>
                    <el-date-picker size="small" class="searchItem !w-[250px]" v-model="searchCondition.create_date" type="daterange"
                        range-separator="To" start-placeholder="Start date" end-placeholder="End date"
                        value-format="YYYY-MM-DD" />
                </div>

                <div class="flex justify-end items-center mr-6 mt-1 mb-2">
                    <ElButton type="primary" class="ml-4" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="pl-2">查询</div>
                    </ElButton>
                    <ElButton type="warning" class="ml-4" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="pl-2">清除</div>
                    </ElButton>
                </div>
            </div>
            <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
                <!-- <el-table header-cell-class-name="tableHeader" :data="dealTimeData" style="width: 100%;color: #666666;"
                border stripe>
                <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='客户名称' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='销售单号' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='客户订单号' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='产品编号' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='产品名称' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='规格' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='订单数量' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='出库数量' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='交货周期' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='交货日期' width="90" />
                <el-table-column show-overflow-tooltip prop="mainer_name" label='备货状态' width="90" />
            </el-table> -->
                <table class="mtable" style="width: 99%; margin-top: 10px;" id="tabdetail">
                    <tbody>
                        <tr>
                            <th><input type="checkbox" id="selectorders" name="selectorders" onchange="ChangeOrder()"
                                    title="全选"></th>
                            <th id="lno" style="display: table-cell;">序号</th>
                            <th id="clientno" style="display: none;">客户编号</th>
                            <th style="min-width:100px;max-width:200px;">客户名称</th>
                            <th id="saleopername" style="display: none;">销售人员</th>
                            <th style="min-width:100px;max-width:150px;">销售单号</th>
                            <th id="salelno" style="display: table-cell;">明细编号</th>
                            <th id="bnolink" style="display: table-cell;">客户订单号</th>
                            <th id="productno" style="display: table-cell;">产品编码</th>
                            <th style="width:180px;">产品名称</th>
                            <th id="productbrand" style="display: none;">产品品牌</th>
                            <th style="width: 200px; display: table-cell;" id="productnorms">规格</th>
                            <th id="pnum">订单数量</th>
                            <th id="outnum">出库数量</th>
                            <th id="backnum" style="display: none;">退货数量</th>
                            <th>交货周期</th>
                            <th>交货日期</th>
                            <th id="supplyflag" style="display: table-cell;">备货状态</th>
                            <th id="bremark" style="display: none;">备注</th>
                            <th id="productno1" style="display: none;">客户产品编码</th>
                            <th id="productname1" style="display: none;">客户产品名称</th>
                            <th id="productnorms1" style="display: none;">客户产品规格</th>
                        </tr>
                        <tr style="text-align:center;">
                            <td><input type="checkbox" id="" value="00008749:1" name="selectorder"
                                    onchange="ChangeAll()"></td>
                            <td>1</td>
                            <td id="clientno" style="display: none;">00000675</td>
                            <td>卓天商务有限公司-希音</td>
                            <td id="saleopername" style="display: none;">甘苗</td>
                            <td>00008749</td>
                            <td id="salelno" style="display: table-cell;">1</td>
                            <td id="bnolink" style="display: table-cell;"></td>
                            <td id="productno" style="display: table-cell;">00044132</td>
                            <td>爱心小熊系列钥匙扣-蓝色-SHEIN</td>
                            <td id="productbrand" style="display: none;"></td>
                            <td id="productnorms" style="display: table-cell;"></td>
                            <td id="pnum">2000</td>
                            <td id="outnum">0</td>
                            <td id="backnum" style="display: none;">0</td>
                            <td>距离交期 <span style="color:#0000ff;font-size:18px;">1</span> 天</td>
                            <td>2024-04-03</td>
                            <td id="supplyflag" style="display: table-cell;"><span style="color:red;"
                                    onclick="ShowStockUp(&quot;00008749&quot;,&quot;1&quot;,&quot;prepare&quot;)">备货中：2000</span>
                            </td>
                            <td id="bremark" style="width: 200px; display: none;"></td>
                            <td id="productno1" style="display: none;"></td>
                            <td id="productname1" style="display: none;"></td>
                            <td id="productnorms1" style="display: none;"></td>
                        </tr>
                        <tr style="text-align:center;">
                            <td><input type="checkbox" id="" value="00008749:2" name="selectorder"
                                    onchange="ChangeAll()"></td>
                            <td>2</td>
                            <td id="clientno" style="display: none;">00000675</td>
                            <td>卓天商务有限公司-希音</td>
                            <td id="saleopername" style="display: none;">甘苗</td>
                            <td>00008749</td>
                            <td id="salelno" style="display: table-cell;">2</td>
                            <td id="bnolink" style="display: table-cell;"></td>
                            <td id="productno" style="display: table-cell;">00044132</td>
                            <td>爱心小熊系列钥匙扣-蓝色-SHEIN</td>
                            <td id="productbrand" style="display: none;"></td>
                            <td id="productnorms" style="display: table-cell;"></td>
                            <td id="pnum">20</td>
                            <td id="outnum">0</td>
                            <td id="backnum" style="display: none;">0</td>
                            <td>距离交期 <span style="color:#0000ff;font-size:18px;">1</span> 天</td>
                            <td>2024-04-03</td>
                            <td id="supplyflag" style="display: table-cell;"><span style="color:red;"
                                    onclick="ShowStockUp(&quot;00008749&quot;,&quot;2&quot;,&quot;prepare&quot;)">备货中：20</span>
                            </td>
                            <td id="bremark" style="width: 200px; display: none;"></td>
                            <td id="productno1" style="display: none;"></td>
                            <td id="productname1" style="display: none;"></td>
                            <td id="productnorms1" style="display: none;"></td>
                        </tr>
                    </tbody>
                </table>
            </div>


            <el-pagination class="flex justify-end mt-8" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[10, 50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </div>
</template>

<style lang="less" scoped>
@import './hkb.css';
//修改选中时的颜色
:deep(.current-row) {
    background-color: #f0f9eb !important;
}

:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.searchItem {
    width: 100px;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

</style>

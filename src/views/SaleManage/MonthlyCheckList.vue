<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessageBox,ElMessage,ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption,ElDatePicker,ElCheckbox,ElDropdown,ElDropdownItem,ElDropdownMenu,ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi,delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { checkPermissionApi } from '@/api/tool';

const { push } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(240)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  page: 1,
  count: 20
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//对账单数据源
const checkData = reactive([])
//当前选中行
const currentRow = ref(null)

//查询客户数据
const getSellMonthlyCheckListData = async()=>{
//   const ret = await geBuyerListApi(searchCondition)
//   if(ret)
//   {
//     console.log(ret)
//     checkData.splice(0,checkData.length,...ret.data)
//     totleCount.value =  parseInt(ret.count)
//   }
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getSellMonthlyCheckListData()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getSellMonthlyCheckListData()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getSellMonthlyCheckListData()
}
//创建新客户
const onAddCheck = ()=>{
//   push({
//     path: '/customermanage/addcustomer',
//     query:{
//       id:''
//     }
//   })
}

//处理表格对象操作
const handleOper = (type,row) => {
  if(type==='edit' || type == 'info' ){
    push({
      path: '/customermanage/addcustomer',
      query:{
        id:row.id,
        type:'个人',
        mode:type
      }
    })
  }
  else if(type==='del'){
    ElMessageBox.confirm(
      '确定是否删除该对账单？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        // const ret = await delBuyerApi({ "ids": [row.id] })
        // if (ret) {
        //     getSellMonthlyCheckListData()

        //   ElMessage({
        //     type: 'success',
        //     message: t('msg.delOK'),
        //   })
        // }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })
  }
}
//设置当前选中行
const setCurrentRow = (value)=>{
  currentRow.value =value
}


onMounted(()=>{

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
  
  //刷新表格
  getSellMonthlyCheckListData()
})

</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[100px]">
    <div  class="flex h-[100%]">
      <div class="h-[100%] bg-white p-7 relative w-[100%] !bg-white flex-grow " style="color:#666666">
        <div class="absolute top-8 left-8">
          <ElButton type="success" @click="onAddCheck">
            <Icon icon="fluent-mdl2:people-add" />
            <div class="pl-2">{{ t('button.add') }}</div>
          </ElButton>
          <!-- <ElButton color="#409EFF" type="primary" plain>
            <Icon icon="clarity:import-line" />
            <div class="pl-2">{{ t('button.import') }}</div>
          </ElButton>
          <ElButton color="#409EFF" type="primary" plain>
            <Icon icon="carbon:export" />
            <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton> -->
        </div>
        <div class="text-center mb-5 font-bold" style="color:#333">{{ t('customer.perCustomerMgr') }}</div>
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-7 pl-12 bg-light-200">
          <div class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.name') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.buyer_nick" placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.id') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.buyer_name" placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.comname') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.corp_name" placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.phone1') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.phone" placeholder="" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.telephone') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.telephone" placeholder="" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.mainer') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.mainer_name" placeholder="" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.follower') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.followe_name" placeholder="" />
          </div>
          <div v-if="senior" class="inline-flex items-center  mr-5  mb-2">
            <div class="text-sm w-20 flex-none flex justify-end ">{{ t('customer.phone_status') }}:</div>
            <el-select size="small"  class="ml-4 w-53" v-model="searchCondition.phone_status" placeholder="">
              <el-option v-for="item in phoneStatus" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.address') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.address" placeholder="" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.remark') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.remark" placeholder="" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.createdate') }}:</div>
            <el-date-picker size="small"   class="ml-4" v-model="searchCondition.create_date" type="daterange" range-separator="To"
              start-placeholder="Start date" end-placeholder="End date" value-format="YYYY-MM-DD" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5  mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.modifydate') }}:</div>
            <el-date-picker size="small"  class="ml-4" v-model="searchCondition.modify_date" type="daterange" range-separator="To"
              start-placeholder="Start date" end-placeholder="End date" value-format="YYYY-MM-DD" />
          </div>
          <div class="flex justify-end items-center mr-6 mt-8 mb-2">
            <el-checkbox  :label="t('customer.senior')" v-model="senior" size="small" />
            <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
              <Icon icon="tabler:search" />
              <div class="ml-1">查询</div>
            </ElButton>
            <ElButton class="ml-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="ml-1">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- <div class="mb-2 flex">
          <ElButton color="#409EFF" type="primary" plain size="small">{{ t('button.new_order') }}</ElButton>
          <ElButton color="#409EFF" type="primary" plain size="small">{{ t('button.change_to_corp_cus') }}</ElButton>
          <ElButton color="#409EFF" type="primary" plain size="small">{{ t('button.changge_to_traded') }}</ElButton>
        </div> -->
        <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="customerData" style="width: 100%;color: #666666;"  @current-change="setCurrentRow"
          :height11="tableHeight" border stripe>
          <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" />
          <el-table-column show-overflow-tooltip  prop="buyer_name" :label="t('customer.id')" width="100" >
            <template #default="scope">
              {{ checkPermissionApi('客户编码显示')?scope.row.buyer_name:'***'}}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip  prop="buyer_nick" :label="t('customer.name')" width="100">
            <template #default="scope">
              <div class="nameStyle" @click="handleOper('info', scope.row)">{{ checkPermissionApi('客户名称显示')? scope.row.buyer_nick:'***' }}</div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip  prop="phone1" :label="t('customer.phone1')" width="120" />
          <el-table-column show-overflow-tooltip  prop="corp_name" :label="t('customer.comname')" width="200" />
          <el-table-column show-overflow-tooltip prop="mainer_name" :label="t('customer.mainer')" width="90" />
          <el-table-column show-overflow-tooltip prop="follower_name" :label="t('customer.follower')" width="80" />
          <el-table-column show-overflow-tooltip prop="phone_status" :label="t('customer.phone_status')" width="100" />
          <el-table-column show-overflow-tooltip prop="address" :label="t('customer.address')" min-width="200" />
          <el-table-column show-overflow-tooltip prop="remark" :label="t('customer.remark')"  min-width="200"/>
          <el-table-column show-overflow-tooltip prop="last_time" :label="t('customer.last_time')" width="130" />
          <el-table-column fixed="right" :label="t('userTable.operate')" width="90">
            <template #default="scope">
              <el-dropdown trigger="click" placement="left">
                <span class="el-dropdown-link">
                  <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- <el-dropdown-item @click="handleOper('order', scope.row)">{{ t('button.new_order') }}</el-dropdown-item> -->
                    <el-dropdown-item @click="handleOper('info', scope.row)">{{ t('userOpt.detail') }}</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('edit', scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  color: #00BA80;
  cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}
</style>

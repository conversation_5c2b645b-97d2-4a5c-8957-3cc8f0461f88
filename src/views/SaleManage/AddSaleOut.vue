<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard,ElOption,ElSelect,ElTooltip,ElTable,ElPopconfirm,ElTag,ElDatePicker,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,set,nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogProductSel } from '@/components/DialogProductSel'
import {checkFormRule, checkPermissionApi, closeOneTagByName, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getInventoryListApi,getStoreListApi,updateSaleOutApi,addSaleOutApi,getSaleOutInfoApi,getSaleOutNewnumApi,getSaleInfoApi } from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { DialogSelSaleOutPdt } from '@/components/DialogSelSaleOutPdt'


const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//销售单数据
const saleData = reactive({})

//出库单数据
const outData = reactive(
    {
    "buyer_id": "",
    "sell_order_num": "",
    "sell_takeout_num": "",
    "store_id": "",
    "takeout_man_id": "",
    "takeout_date": "",
    "pdt_list": [],
    "note": "",
    "type": "", //出库方式
    "express_fee": 0.00,
    "other_fee": 0.00,
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:['提交审核']
    }, //审核决策
    fsm_cur_state:'',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[],
    express_data:{
        快递单号:'',
        快递公司:'',
        货物重量:'',
        货物重量单位:'kg(千克)',
        货物体积:'',
        快递运费:'',
        收货地址:'',
        快递备注:'',
    },    
}
)

//快递列表
const kdList = ['顺丰速运','圆通速递','韵达快运','中通速递','宅急送','天天快递','邮政快递','德邦快递','EMS','百世快递','全峰快递','申通快递']
//快递重量单位
const weightDW = ['g(克)','kg(千克)','t(吨)']

//获取最新ID
const onChangeID = async()=>{
    const ret = await getSaleOutNewnumApi()
    if(ret)
    {
        console.log(ret)
        outData.sell_takeout_num = ret.data.new_id
    }
}


//缓存产品列表
const pdtList = ref([])

onMounted(async () => {    

    //查询获取关联销售单信息
    const ret = await getSaleInfoApi({
        sell_order_num:currentRoute.value.query.sell_order_num
    })
    if(!ret)
    {
        return
    }


    Object.assign(saleData, ret.data)
    Object.assign(pdtList.value, saleData.pdt_list)

    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('saleout.add')
        onChangeID()
        //克隆必要信息
        outData.sell_order_num  = saleData.sell_order_num
        // receiptData.supplier_nick = purchaseData.supplier_nick
        // receiptData.supplier_id = purchaseData.supplier_id

         const info = wsCache.get(appStore.getUserInfo)
         outData.takeout_man_id = info.id
         outData.takeout_man_name = info.resident_name
        outData.takeout_date = getTodayDate()
        //追加所有产品到出库列表
        outData.pdt_list = saleData.pdt_list
        await getStoreList()
        //新增属性
        for(let item of outData.pdt_list)
        {            
            item.销售备品数量 = parseFloat(item.销售备品数量)
            item.销售数量 = parseFloat(item.销售数量)
            console.log((parseFloat(item.销售数量)+parseFloat(item.销售备品数量))-item.已发货+item.已退货,'---',item.可用数量)
            // item.发货数量 = Math.min((parseFloat(item.销售数量)+parseFloat(item.销售备品数量))-item.已发货+item.已退货,item.可用数量)
            item.备品发货数量 = 0
            item.正品发货数量 = 0
            item.发货数量 = 0 
        }
    }
    else
    {        
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('saleout.look')
        }
        else
        {
            title.value = t('saleout.modify')
        }
        await getStoreList()
        //查询信息 
        const ret = await getSaleOutInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(outData, ret.data)
            outData.pdt_list = ret.data.pdt_list;
        }
        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
        
    }
    
})

//校验输入
const recomputeCount = (pdt) => {
    pdt.正品发货数量 = parseFloat(pdt.正品发货数量) || 0
    pdt.备品发货数量 = parseFloat(pdt.备品发货数量) || 0
    
    pdt.正品发货数量 = parseFloat(pdt.正品发货数量)
    pdt.备品发货数量 = parseFloat(pdt.备品发货数量)
    

    const nMax = (parseFloat(pdt.销售数量) + parseFloat(pdt.销售备品数量)) - pdt.已发货 + pdt.已退货
    if (pdt.正品发货数量 + pdt.备品发货数量 > nMax) {
        pdt.正品发货数量 = nMax-pdt.备品发货数量
    }

    if(pdt.正品发货数量<=0)
        pdt.正品发货数量 = 0
    if((pdt.正品发货数量 + pdt.备品发货数量)>Math.max(pdt.可用数量,pdt.锁定数量))
    {
        pdt.正品发货数量 = Math.max(pdt.可用数量,pdt.锁定数量)-pdt.备品发货数量
    }
    
    if (pdt.正品发货数量 > parseFloat(pdt.销售数量))
    {
        pdt.正品发货数量 = parseFloat(pdt.销售数量)
    
    }

    pdt.发货数量 = pdt.正品发货数量+pdt.备品发货数量
    
    // if(pdt.发货数量>nMax)
    //     pdt.发货数量 = nMax

    // if(pdt.发货数量>Math.max(pdt.可用数量,pdt.锁定数量))
    // {
    //     pdt.发货数量 = Math.max(pdt.可用数量,pdt.锁定数量)
    // }
}

//校验备品
const recomputeBakCount = (pdt)=>{
    // if(pdt.备品发货数量>pdt.发货数量)
    // {
    //     pdt.备品发货数量 = pdt.发货数量
    // }
    pdt.正品发货数量 = parseFloat(pdt.正品发货数量) || 0
    pdt.备品发货数量 = parseFloat(pdt.备品发货数量) || 0
    console.log('进入',pdt.备品发货数量,pdt.销售备品数量)
    if(pdt.备品发货数量>pdt.销售备品数量)
    {
        pdt.备品发货数量 = pdt.销售备品数量
        ElMessage.warning('备品发货数量不能大于备品数量')
        console.log('备品发货数量不能大于备品数量')
    }


    const nMax = (parseFloat(pdt.销售数量) + parseFloat(pdt.销售备品数量)) - pdt.已发货 + pdt.已退货
    if (pdt.正品发货数量 + pdt.备品发货数量 > nMax) {
        pdt.备品发货数量 = nMax-pdt.正品发货数量
    }


    if(pdt.备品发货数量<=0)
        pdt.备品发货数量 = 0
    if((pdt.正品发货数量 + pdt.备品发货数量)>Math.max(pdt.可用数量,pdt.锁定数量))
    {
        pdt.备品发货数量 = Math.max(pdt.可用数量,pdt.锁定数量)-pdt.正品发货数量
    }


    pdt.发货数量 =  parseFloat(pdt.正品发货数量+pdt.备品发货数量)


}


//添加出库产品
const showSelOutPdt = ref(false)
const onAddPdt = ()=>{
    showSelOutPdt.value = true
}
//选择产品回调
const onSelOutPdtCallback = (pdt)=>{
    outData.pdt_list.push(pdt)
}

//删除某一个产品
const onDelPdt = (index)=>{
    outData.pdt_list.splice(index,1)
}



//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)

    outData.store_id = storeData[0].id

    await updatePdtStock(outData.store_id)
  }

}

//产品仓库分布数据
const detailData  = reactive([])
//更新pdt库存
const updatePdtStock = async (store_id:string) => {
    if(outData.pdt_list.length<=0)
        return
    let arrPdtIDs = []
    for(let one of outData.pdt_list)
    {
        arrPdtIDs.push(one.id)
    }

    //查询这个产品在那些仓库有库存
    const ret = await getInventoryListApi({
                store_id:store_id,
                pdt_ids:[...arrPdtIDs],
                page: 1,
                count: 10000
            })
    if(ret)
    {
        detailData.splice(0,detailData.length, ...ret.data)        
        //更新统计数据到
        for(let item of outData.pdt_list)
        {
            item.可用数量 = 0
            item.锁定数量 = 0
            for(let one of detailData)
            {
                let bFind = false
                if(item.id == one.pdt_id)
                {
                    item.可用数量 = one.良品数量+one.不良品数量-one.锁定数量
                    item.锁定数量 = one.锁定数量
                    bFind = true
                }
                if(bFind)
                    break
            }
        }
    }
    console.log(outData.pdt_list)
}

//切换仓库更新库存
const onChangeStore = async (store_id) => {
    updatePdtStock(store_id)
}

//显示隐藏选择出库员窗口变量
const showSelOutUserDlg = ref(false)
//显示选择出库员弹窗
const onSelOutUser = ()=>{
    showSelOutUserDlg.value = true
}
//选择出库员回调
const onSelReturnCallback = (id,name)=>{
    console.log(id,name)
    outData.takeout_man_id = id
    outData.takeout_man_name = name
}

//计算合计金额
const 合计金额 = computed(
    ()=>{
        let sum = 0
        for(let item of outData.pdt_list)
        {
            sum += (item.发货数量) * item.sell_price_aft_tax
        }
        return parseFloat((sum +parseFloat(outData.express_fee)+parseFloat(outData.other_fee)).toFixed(2))
    }
)

//保存
const handleCheck = async(btn)=>{
    console.log(outData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //提交时自动过滤数量为0或者为空的产品
    //outData.pdt_list = outData.pdt_list.filter(item=>parseFloat(item.发货数量)>0) 

    const tmp = cloneDeep(outData)
    tmp.pdt_list = tmp.pdt_list.filter(item=>parseFloat(item.发货数量)>0) 
    
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }



    //增加审核流程参数
    const info = wsCache.get(appStore.getUserInfo)
    tmp.fsm_exe_man_name = info.resident_name
    tmp.fsm_exe_trig = btn
    
    if(tmp.id == undefined)
    {
        const ret = await addSaleOutApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newReceiptSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        const ret =await updateSaleOutApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateReceiptSuccess'))
            baskFront()
        }
    }


}

const baskFrontNoFreshFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
    //刷新上一页
    closeOneTagByPath('/salemanage/salemanage')
}
</script>

<template>
    <ContentDetailWrap :title="title" @back="baskFrontNoFreshFront()">
        <template #right>
            <el-popconfirm  title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && (outData.fsm_can_trig_data.操作触发.includes('提交审核')|| outData.fsm_can_trig_data.操作触发.includes('保存'))"   type="success" >提交审核</ElButton>
                </template>
            </el-popconfirm>
            <!-- <el-popconfirm  title="是否确认发起操作?操作将影响库存！" @confirm="onSave">
                <template #reference>
                    <ElButton  type="primary">
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        提交
                    </ElButton>
                </template>
            </el-popconfirm> -->
        </template>
        <el-card id="check" v-if="outData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%]">
            <template #header>
            <div class="flex items-center">
                <span>当前节点:</span>
                <span class="text-red-500 mr-3">{{ outData.fsm_cur_state }}</span>
                
                <el-popconfirm  title="是否确认同意发货，确认后将影响库存！" @confirm="handleCheck('同意')">
                    <template #reference>
                        <ElButton v-show="outData.fsm_can_trig_data.审核触发.includes('同意')" type="success" >同意</ElButton>
                    </template>
                </el-popconfirm>
                
                <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="outData.fsm_can_trig_data.审核触发.includes('驳回')" type="danger" >驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="outData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                    </template>
                </el-popconfirm>

                <!-- <div class="flex ml-4 items-center">
                    <div class="mr-2">出库日期:</div>
                    <el-date-picker v-model="outData.putin_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                </div> -->
                
                
            </div>
            </template>
            <el-input v-model="outData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        </el-card>
        <el-form :rules="rules" :model="outData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('saleout.id')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  v-model="outData.sell_takeout_num" :disabled="outData.id!=undefined" />
                            <ElButton class="ml-2" v-if="outData.id==undefined" type="primary"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="t('sale.name')"
                    class="flex">
                        <div>{{ outData.sell_order_num }}</div> 
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('customer.name')"
                    class="flex">
                        {{  checkPermissionApi('客户名称显示')?saleData.buyer_nick:'***' }}
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('saleout.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="outData.takeout_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('store.store')"
                    class="flex">
                    <el-select v-model="outData.store_id" placeholder="请选择" @change="onChangeStore(outData.store_id)">
                        <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('return.user')"
                    class="flex">
                    <el-form-item>
                        <div class="rounded mr-2 border pl-2 pr-2 w-150px">{{ outData.takeout_man_name?outData.takeout_man_name:'请选择' }}</div> 
                        <ElButton @click="onSelOutUser" v-if="currentRoute.query.type != 'info'">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        
        </el-form>
        <div class="p-2">
            <ElButton type="success" @click="onAddPdt" v-if="currentRoute.query.type != 'info'">
                <Icon class="mr-0.5" icon="icons8:plus" />
                新增
            </ElButton>
        </div>

        <el-table header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="outData.pdt_list" style="width: 100%" border stripe>
            <el-table-column  :label="t('process.opt')" width="60" >
                <template #default="scope">
                    <div  type="primary">
                        <el-popconfirm title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon v-show="currentRoute.query.type != 'info'" icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>
                        
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index+1 }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div  class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130"/>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip                        
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="类型" :label="t('sale.type')" />
            <el-table-column show-overflow-tooltip :label="t('sale.count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.销售数量 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="备品数量"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.销售备品数量 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="sell_price_aft_tax" :label="t('sale.price')" >
                <template #default="scope">
                    {{ checkPermissionApi('销售发货单显示价格')?scope.row.sell_price_aft_tax:'*' }}
                </template>
            </el-table-column>

            <el-table-column show-overflow-tooltip :label="t('saleout.count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.已发货-scope.row.已退货 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('inventory.avalid_count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.可用数量 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('inventory.lock_count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.锁定数量 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed="right" :label="'正品数量'">
                <template #default="scope">
                    <el-input  v-model="scope.row.正品发货数量" @blur="recomputeCount(scope.row)" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed="right"  :label="'备品数量'">
                <template #default="scope">
                    <el-input  v-model="scope.row.备品发货数量" @blur="recomputeBakCount(scope.row)" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed="right"  :label="'发货总数'">
                <template #default="scope">
                    {{ scope.row.发货数量 }}
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  :label="t('sale.out_date')">
                <template #default="scope">
                    {{ scope.row.交货日期 }}
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="销售备注" :label="t('sale.remark')"  />
            <el-table-column show-overflow-tooltip fixed="right"  :label="t('saleout.remark')">
                <template #default="scope">
                    <el-input  v-model="scope.row.发货备注" />
                </template>
            </el-table-column>
        </el-table>

        <!-- 显示合计 -->
        <div class="flex mt-4">
        <!-- 左边部分 -->
        <div class="w-[60%] text-center weightdw">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">{{ t('sale.remark') }}</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ saleData.note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">发货备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="outData.note"  :autosize="{ minRows: 3, maxRows: 3 }" type="textarea" />
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">快递:</td>
                        <td class="table_self !w-[100%] p-3 flex flex-wrap">
                            <div class="flex justify-start items-center mr-[45px]">
                                <div class="table_self_title mr-1 min-w-[80px]">快递单号:</div>
                                <el-input class="max-w-[200px]" v-model="outData.express_data.快递单号" />
                            </div>

                            <div class="flex justify-start items-center mr-5">
                                <div class="table_self_title mr-1 min-w-[80px]">快递公司:</div>
                                <div>
                                    <el-select
                                        class="weightdw"
                                        v-model="outData.express_data.快递公司"                                        
                                        filterable
                                        allow-create
                                        default-first-option
                                        :reserve-keyword="false"
                                        placeholder="请选择快递公司"
                                        style="width: 200px"
                                    >
                                        <el-option
                                        v-for="item in kdList"
                                        :key="item"
                                        :label="item"
                                        :value="item"
                                        />
                                    </el-select>
                                </div>
                            </div>

                            <div class="flex justify-start items-center mr-3">
                                <div class="table_self_title mr-1 min-w-[80px]">货物重量:</div>
                                <el-input class="max-w-[100px]" v-model="outData.express_data.货物重量" />
                                <el-select
                                        class="weightdw"
                                        v-model="outData.express_data.货物重量单位"                                        
                                        filterable
                                        allow-create
                                        default-first-option
                                        :reserve-keyword="false"
                                        placeholder=""
                                        style="width: 100px;font-size: smaller;"
                                    >
                                        <el-option
                                        v-for="item in weightDW"
                                        :key="item"
                                        :label="item"
                                        :value="item"
                                        />
                                    </el-select>
                            </div>

                            <div class="flex justify-start items-center mr-3">
                                <div class="table_self_title mr-1 min-w-[80px]">货物体积:</div>
                                <el-input class="max-w-[200px]" v-model="outData.express_data.货物体积" />
                                <span class="ml-1">m³</span>
                            </div>
                            <div class="flex justify-start items-center mr-3">
                                <div class="table_self_title mr-1 min-w-[80px]">快递运费:</div>
                                <el-input class="max-w-[200px]" v-model="outData.express_data.快递运费" />
                            </div>
                            <div class="flex justify-start items-center mr-3 w-[100%]">
                                <div class="table_self_title mr-1 min-w-[80px]">收货地址:</div>
                                <el-input class="!w-[80%] text-left" v-model="outData.express_data.收货地址" />
                            </div>
                            <div class="flex justify-start items-center mr-3 w-[100%]">
                                <div class="table_self_title mr-1 min-w-[80px]">快递备注:</div>
                                <el-input class="!w-[80%]" v-model="outData.express_data.快递备注" />
                            </div>
                        </td>                        
                    </tr>
                </table>
            </div>
        </div>
        <div class="flex-grow text-center">
            <div class="flex">
                <div class="w-[100%]">
                    <div>
                        <td class="table_self_title min-w-[100px] p-2">运费:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="outData.express_fee" type="number"/>
                        </td>
                    </div>
                    <div>
                        <td class="table_self_title min-w-[100px] p-2">其他费用:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="outData.other_fee" type="number"/>
                        </td>
                    </div>
                    <div>
                        <td class="table_self_title min-w-[100px] p-2 ">合计金额:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ checkPermissionApi('销售发货单显示价格')?合计金额:'*' }}
                        </td>
                    </div>
                </div>
            </div>
        </div>
    </div>

        
        





    <DialogSelSaleOutPdt v-model:show="showSelOutPdt" :param="pdtList" :title="t('saleout.add_pdt')" @on-submit="onSelOutPdtCallback"/>
    <!-- 选择发货领取员 -->
    <DialogUser :param="''" v-model:show="showSelOutUserDlg" :title="t('msg.selectUser')" @on-submit="onSelReturnCallback"/>


    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
  white-space: nowrap;
  text-align: center;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}

:deep(.weightdw .el-input__inner){
    font-size:13px;
    }
</style>
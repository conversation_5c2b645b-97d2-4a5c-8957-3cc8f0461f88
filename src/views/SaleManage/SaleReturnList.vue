<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getSaleReturnListApi,delQualityCheckApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { checkPermissionApi } from '@/api/tool';


const { currentRoute,push } = useRouter()
const { t } = useI18n()


//退货单数据源
const returnData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getReturnList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getReturnList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getReturnList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
    console.log(item)
  //编辑
  if(type === 'info' || type == 'edit')
  {
    const retPath = currentRoute.value.name == "StoneSaleReturnList"?'/inventorymanage/stoneaddsalereturn':'/salemanage/addsalereturn'
    
    push({
      path: retPath,
      query:{
          id:item.id,
          sell_order_num:item.sell_order_num,
          type:type
      }
    })
  }
  // else if(type === 'del')
  // {
  //   ElMessageBox.confirm(t('msg.confirm_del_qualitycheck')+'--> '+item.buy_drawin_num, t('msg.notify'), {
  //     confirmButtonText: t('msg.ok'),
  //     cancelButtonText: t('msg.channel'),
  //     type: 'error',
  //   }
  //   ).then(async () => {
  //     console.log(item)
  //     const ret = await delQualityCheckApi({
  //       ids: [item.id],
  //     })
  //     if (ret) {
  //       ElMessage({
  //         type: 'success',
  //         message: t('msg.success'),
  //       })
  //       getReturnList()
  //     }
  //   }
  //   ).catch(() => {})
  // }
}


//查询退货单列表
const getReturnList = async (page = 1) => {
  searchCondition.page = page
  const ret = await getSaleReturnListApi(searchCondition)
  if(ret)
  {
    returnData.splice(0,returnData.length, ...ret.data)
    console.log(returnData)
    totleCount.value = parseInt(ret.count)
  }
}

//跳转到销售订单
const onNaviSale = (item)=>{
  push({
    path: '/salemanage/salemanage',
    query:{
        id:'',
        sell_order_num:item.sell_order_num
    },    
  })
}

onMounted(async() => {
  if(currentRoute.value.query.sell_order_num != undefined)
    searchCondition.sell_order_num = currentRoute.value.query.sell_order_num as string
  getReturnList()  

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})

onBeforeMount(() => {
  
});

</script>

<template>
    <!-- 列表 -->
    <div ref="rootRef">

      <div class="p-7">
        <div class="text-center mb-5 font-bold">{{ t('salereturn.list') }}</div>
        <div  class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-white">
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('sale.name') }}</div>
            <el-input v-model="searchCondition.sell_order_num" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input v-model="searchCondition.pdt_name" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input v-model="searchCondition.pdt_nick" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.supplier') }}</div>
            <el-input v-model="searchCondition.buyer_name" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.status') }}</div>
            <el-select class="searchItem" v-model="searchCondition.status" placeholder="请选择" >
              <el-option v-for="item in ['进行中','已完成','已暂停','已停止']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.receipt_status') }}</div>
            <el-select class="searchItem" v-model="searchCondition.instatus" placeholder="请选择" >
              <el-option v-for="item in ['未收货','部分收货','完全收货','超量收货','收货且退货']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.supplier_sup_id') }}</div>
            <el-input v-model="searchCondition.supplier_order_num" placeholder="" class="searchItem" />
          </div>
          <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
            <el-input v-model="searchCondition.specs" placeholder="" class="searchItem" />
          </div>

          <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.buy_price') }}</div>
            <el-input v-model="searchCondition.price_begin" placeholder="" class="!w-[60px]" />
            <div class="searchTitle !w-32px">到</div>
            <el-input v-model="searchCondition.price_end" placeholder="" class="!w-[60px]" />
          </div>
          <div  class="text-center mt-5 mb-2 flex justify-end items-center">
            <el-checkbox  size="small" :label="t('customer.senior')" v-model="senior"/>
            <ElButton  class="ml-5" type="primary" @click="onSearch">
              <Icon icon="tabler:search" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 产品列表 -->
        <div class="bg-white">
            <!-- 表头 -->
            <div class="flex header headerBk">              
              <div class="w-[70%] flex">
                <div class="min-w-[60%] text-center">产品名称</div>
                <div class="min-w-[20%] text-center">退货数量</div>
                <div class="min-w-[20%] text-center">价格</div>
              </div>
              <div class="flex flex-grow !p-0">
                <div class="rightcss rightcss_title">状态</div>
                <div class="rightcss rightcss_title">备注</div>
                <div class="rightcss_title !min-w-[70px]">操作</div>
              </div>
            </div>
            <!-- 表内容 -->
            <div class="mt-2" v-for="item in returnData" :key="item.id">
              <!-- 内容头 -->
              <div>
                <div class="bg-light-500 p-2 flex flex-nowrap text-[13px]">
                    <div class="w-[45%]  flex">
                        <div class="mr-3 font-bold">
                            {{ item.create_date.split(' ')[0] }}
                        </div> 
                        <div class="mr-2">退货单号: {{ item.sell_cancel_num }}</div>
                        <div class="cursor-pointer" @click="onNaviSale(item)">销售单号: {{ item.sell_order_num }}</div>
                    </div>
                  <div class="min-w-[200px]">
                    <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                    {{ checkPermissionApi('客户名称显示')?item.buyer_nick:'***' }}
                  </div>
                  <div class="flex justify-center items-center min-w-[250px]">
                    
                    <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin-right: 5px;" icon="solar:file-bold" />
                    <div>文件(0)</div>
                    <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin:0 5px 0 10px;" icon="material-symbols:contract" />
                    <div>合同(0)</div>
                  </div>
                  <!-- 靠右的其他信息 -->
                  <div class="ml-auto flex justify-center items-center min-w-[200px]">
                    <div class="mr-5">退回人:{{ item.cancel_man_name }}</div>
                    <div class="mr-5">仓库:{{ item.store_nick }}</div>
                  </div>
                </div>
              </div>
              <!-- 内容体 -->
              <div class="flex">
                <!-- 左边产品列表 -->
                <div class="w-[70%]  table_self">
                  <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                    <div class="min-w-[60%] text-center flex-grow ">
                      <div class="flex justify-start items-start w-[100%] p-1">
                        <el-image  class="object-fill w-[50px] h-[50px] mr-1" src="/nopic.jpg" />
                        <div class="inline-block text-left max-w-[100%]">
                          <div style="white-space: normal;">{{ '['+pdt.name+']'+pdt.nick }}</div>
                        </div>
                      </div>
                    </div>                  
                    <div class="min-w-[20%] text-center">{{ pdt.退货数量+' '+pdt.base_unit }}</div>
                    <div class="min-w-[20%] text-center" v-if="pdt.退货类型 != '备品'">{{ checkPermissionApi('销售退货单显示价格')?(pdt.退货数量+'*'+pdt.sell_price_aft_tax+'='+ (parseFloat(pdt.退货数量)*pdt.sell_price_aft_tax).toFixed(2)):'*' }}</div>
                    <div class="min-w-[20%] text-center" v-if="pdt.退货类型 == '备品'">{{ '备品退货' }}</div>
                  </div>
                </div>
                <!-- 右边其他数据 -->
                <div class="flex flex-grow text-center  right">
                  <div class="rightcss" style="text-align:center;"> 已入库 </div>
                  <div class="rightcss">{{ item.note }}</div>
                  <div class="!min-w-[70px]" style="text-align: center;">
                    <el-dropdown trigger="click" placement="bottom">
                      <span class="el-dropdown-link">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                      </span>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.info') }}</el-dropdown-item>
                          <el-dropdown-item @click="handleOper('edit', item)">修改类型</el-dropdown-item>
                          <!-- <el-dropdown-item @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item> -->
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>

      <el-pagination class="flex justify-end mt-8"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
    <div class="h-[300px]"></div>

    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #ffe48d !important;
}

:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 180px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  color:#333;
  font-size: 15px;
  font-weight: bold;
}
.headerBk{
  // background-color: #6d92b4 !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
 // white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: left; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
  }

.rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}
</style>

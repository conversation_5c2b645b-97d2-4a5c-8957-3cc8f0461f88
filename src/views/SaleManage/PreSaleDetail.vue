<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElForm,ElFormItem,ElRadio,ElRadioGroup,ElTabs, ElTabPane, ElButton, ElPopconfirm, ElCard, ElDescriptions, ElDescriptionsItem, ElInput, FormInstance, FormRules, ElMessageBox, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted, set } from 'vue'
import { updatePreSaleApi, getPreSaleInfoApi, addPreSaleApi,getPreSaleNewnumApi } from '@/api/product'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { DialogCheckShowEx } from '@/components/DialogCheckShowEx'

import TabRequestPreSale from './components/TabRequestPreSale.vue'
import TabBasePricePreSale from './components/TabBasePricePreSale.vue'
import TabFinalPricePreSale from './components/TabFinalPricePreSale.vue'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { getGUID,checkFormRule, closeOneTagByName, checkPermissionApi } from '@/api/tool'
import { DialogSelCustomer } from '@/components/DialogSelCustomer'
import { DialogUser } from '@/components/DialogUser'

const { wsCache } = useCache()
const appStore = useAppStore()
const { currentRoute, back,push } = useRouter()
const { t } = useI18n()
//标题
const title = ref('')

//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    sell_offer_num: [{ required: true, message: t('msg.noNum'), trigger: 'blur' }],
    buyer_nick: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
    pdt_nick: [{ required: true, message: t('msg.noPdtNick'), trigger: 'blur' }],
    pdt_amount: [{ required: true, message: t('msg.noCount'), trigger: 'blur' }, { pattern: /^[0-9]+$/, message: t('msg.noCount'), trigger: 'blur' }]
})

//当前切换页
const activeTab = ref('0')

//单个产品定义
const pdtData = {
    标识: '',
    pdt_nick: '',
    "pdt_stuff": "", //产品材质
    "pdt_specs": "", //产品规格
    "pdt_skill": "", //产品工艺
    "pdt_print": "", //产品印刷
    "pdt_amount": "", //产品数量
    "pdt_color": "", //产品颜色
    "pdt_pack": "", //产品包装
    "note": "", //其它
    pics: [],


    // pdt_pic_list:[],
    other_data: {
        大货模具费备注: '单款数量超过20万以上',
        加急大货模具费备注: '业务告知客户',
        报价有效期: '30',
        装箱数: 0,
        净重: 0,
        毛重: 0,
        出厂合计成本: 0,
        运费单价: 0,
        运费数量: 0,
        运费备注: '',
        其他费用单价: 0,
        其他费用数量: 0,
        其他费用备注: '',
        销管费数量: 9,
        销管费备注: '',
        毛利率: 23,
        毛利率备注: '',
        增值税率: 13,

        长: 0,
        宽: 0,
        高: 0,
        打样时间: 0,
        打样时间备注: '',
        开模时间: 0,
        开模时间备注: '',
        打样费: 0,
        打样费备注: '',
        大货模具费: 0,
        加急大货模具费: 0,
        报价有效期备注: '',
        是否含税: '不含税',
        税率: 0,
        生产日期: '',
        大货时间:0
    },
    process_list: [],
}

//报价单数据
const presaleData = reactive({
    "sell_offer_num": "",
    "buyer_id": "",
    buyer_nick: "",
    "sell_man_id": "", //负责工程师
    sell_man_name: "",
    "pdt_list": [], //报价产品列表
    "sell_man_note": "正常", //负责工程师备注：加急，不加急

    fsm_can_trig_data: {
        审核触发: [],
        操作触发: []
    }, //审核决策
    fsm_cur_state: '',    //当前节点状态
    fsm_exe_man_name: '',
    fsm_exe_log: '',
    fsm_exe_trig: '',//决策内容
    fsm_log_list: []
})



onMounted(async () => {
    console.log(currentRoute.value)
    title.value = t('presale.list')
    await initShow()
})

const initShow = async()=>{

    if(currentRoute.value.query.id  == undefined)
    {
        onChangeID()
        const tmp = [...presaleData.fsm_can_trig_data.操作触发, '保存销售报价','完成销售报价']
        presaleData.fsm_can_trig_data.操作触发 = tmp
        //添加一个默认产品
        let pdt = cloneDeep(pdtData)
        pdt.标识 = getGUID(5)
        presaleData.pdt_list.push(pdt)
        presaleData.fsm_cur_state = '等待销售报价'
    }
    else
    {
        const ret = await getPreSaleInfoApi({
            id: currentRoute.value.query.id == undefined?'':currentRoute.value.query.id,
            num: currentRoute.value.query.sell_offer_num == undefined?'':currentRoute.value.query.sell_offer_num,
            page: 1,
            count: 100
        })
        if (ret) {
            console.log(ret)
            Object.assign(presaleData, ret.data)

            for (let pdt of presaleData.pdt_list) {
                pdt.activeTab = '0'
            }
        }
    }


}




//提交审核意见
const handleCheck = async (btn: string) => {
    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        ElMessage.warning(t('msg.checkRule'))
        return
    }


    //提交的数据校验
    if(presaleData.pdt_list.length<=0)
    {
        ElMessage.error('请添加报价产品！')
        return
    }
    console.log(presaleData)
    //校验必填项
    for(let i=0;i< presaleData.pdt_list.length;i++)
    {
        const pdt = presaleData.pdt_list[i]
        if(pdt.pdt_nick.length<=0)
        {
            ElMessage.error('产品'+(i+1)+'的名称不能为空！',pdt.pdt_nick)
            activeTab.value = i.toString()
            return
        }
        if(parseFloat(pdt.pdt_amount)<=0)
        {
            ElMessage.error('产品'+(i+1)+'的数量必须大于0！')
            activeTab.value = i.toString()
            return
        }
    }


    const info = wsCache.get(appStore.getUserInfo)
    presaleData.fsm_exe_man_name = info.resident_name
    presaleData.fsm_exe_trig = btn

    if (presaleData.id == undefined) {

        const ret = await addPreSaleApi(presaleData)
        if (ret) {
            ElMessage.success('报价单创建成功!')
            baskFront()            
        }
    }
    else //修改
    {
        const ret = await updatePreSaleApi(presaleData)
        if (ret) {
            ElMessage.success('报价单修改成功!')

            if (btn.indexOf('修改') >= 0) {
                //修改完毕之后刷新当前界面
                Object.assign(presaleData, ret.data)
            }
            else {
                baskFront()
            }


        }
    }
}
//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = () => {
    showCheckHisDlg.value = true
}



//子控件修改pdt 后同步
const updatePdt = (index, updatedValue) => {
    updatedValue.activeTab = presaleData.pdt_list[index].activeTab  //这里保护下tab索引，避免被更新之后的数据切换
    presaleData.pdt_list[index] = updatedValue;
};

//计算是否有毛利率低于20的
const checkLowMLL20 = computed(() => {
    for (let pdt of presaleData.pdt_list) {
        if (parseFloat(pdt.other_data.毛利率) < 20) {
            return true
        }
    }
    return false
})
//计算是否有销管费低于15%的
const checkLowXGF15 = computed(() => {
    for (let pdt of presaleData.pdt_list) {
        if (parseFloat(pdt.other_data.销管费数量) < 15) {
            return true
        }
    }
    return false
})

const getPopTitle = ()=>{
    if(checkLowMLL20.value)
    {
        return '毛利率低于20%需要审核?'
    }

    if(checkLowXGF15.value)
    {
        return '销管费低于15%需要审核?'
    }

    return '是否确认完成销售报价?'
}

//获取最新ID
const onChangeID = async () => {
    const ret = await getPreSaleNewnumApi()
    if (ret) {
        console.log(ret)
        presaleData.sell_offer_num = ret.data.new_id
    }
}

//新增产品
const onAddPdt = () => {
    let pdt = cloneDeep(pdtData)
    pdt.标识 = getGUID(5)
    presaleData.pdt_list.push(pdt)
    activeTab.value = (presaleData.pdt_list.length - 1).toString()
    console.log(activeTab.value)
}
//删除产品
const onRemovePdt = (index) => {
    console.log(index)
    if (presaleData.pdt_list[index].pdt_nick == '') {
        presaleData.pdt_list.splice(index, 1)
        activeTab.value = (index - 1).toString()
    }
    else {
        ElMessageBox.confirm('是否确认删除该产品的申报?', t('msg.notify'), {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'error',
        }
        ).then(async () => {
            presaleData.pdt_list.splice(index, 1)
        }
        ).catch(() => { })
    }
}

//显示隐藏选择客户弹窗
const showSelCustomerDlg = ref(false)
//选择BOM关联客户
const onSelCustomer = () => {
    showSelCustomerDlg.value = true
}
//选择客户回调
const onSelCustomerCallback = (id, name, nick) => {
    console.log(id, name, nick)
    presaleData.buyer_id = id
    presaleData.buyer_name = name
    presaleData.buyer_nick = nick
}


//显示隐藏选择工程师窗口变量
const showSelEngineerDlg = ref(false)
//显示选择工程师弹窗
const onSelEngineer = () => {
    showSelEngineerDlg.value = true
}
//选择工程师回调
const onSelEngineerCallback = (id, name) => {
    presaleData.sell_man_id = id
    presaleData.sell_man_name = name
}


//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}
</script>

<template>
    <ContentDetailWrap :title="title + '-' + presaleData.fsm_cur_state" @back="baskFront()"
        style="background-color:var(--app-content-bg-color);">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史
            </ElButton>
        </template>
        <template #right>            
            <!-- 销售报价保存修改和提交 -->
            <el-popconfirm
                v-if="currentRoute.query.mode == '报价单' && presaleData.fsm_can_trig_data.操作触发.includes('反审')"
                title="是否确认反审该报价单?" @confirm="handleCheck('反审')">
                <template #reference>
                    <ElButton type="primary">反审</ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm
                v-if="currentRoute.query.mode == '报价单' && presaleData.fsm_can_trig_data.操作触发.includes('保存销售报价')"
                title="是否确认保存?" @confirm="handleCheck('保存销售报价')">
                <template #reference>
                    <ElButton color="#409EFF" style="color: #fff;" >
                        <Icon class="mr-0.5" icon="carbon:save" />
                        保存报价单
                    </ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm
                v-if="currentRoute.query.mode == '报价单' && presaleData.fsm_can_trig_data.操作触发.includes('修改销售报价')"
                title="是否确认修改?保存后流程将回到销售报价步骤！" @confirm="handleCheck('修改销售报价')">
                <template #reference>
                    <ElButton type="primary">修改销售报价</ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm
                v-if="presaleData.fsm_can_trig_data.操作触发.includes('完成销售报价') && currentRoute.query.mode == '报价单'"
                :title="getPopTitle()" @confirm="handleCheck('完成销售报价')">
                <template #reference>
                    <ElButton type="success">
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        完成销售报价
                    </ElButton>
                </template>
            </el-popconfirm>

            <el-popconfirm v-if="presaleData.fsm_can_trig_data.操作触发.includes('确认报价') && currentRoute.query.mode == '报价单'"
                :title="'客户是否已经确认了销售报价?'" @confirm="handleCheck('确认报价')">
                <template #reference>
                    <ElButton :type="(checkLowMLL20||checkLowXGF15) ? 'danger' : 'success'">确认报价</ElButton>
                </template>
            </el-popconfirm>

            <el-popconfirm v-if="presaleData.fsm_can_trig_data.操作触发.includes('关闭')" title="是否确认关闭订单?"
                @confirm="handleCheck('关闭')">
                <template #reference>
                    <ElButton type="danger">
                        <Icon class="mr-0.5" icon="carbon:close-outline" />
                        关闭订单
                    </ElButton>
                </template>
            </el-popconfirm>
        </template>

        <el-card id="check" v-if="presaleData.fsm_can_trig_data.审核触发.length > 0 && currentRoute.query.mode == '报价审核'"
            class="border   min-w-[900px] w-[80%] m-auto mb-2" style="box-shadow: 0px 6px 14px 4px rgba(208, 208, 208, 0.34);background-color: #fff;">
            <template #header>
                <div class="flex items-center">
                    <span>当前节点:</span>
                    <span class="text-red-500 mr-3">{{ presaleData.fsm_cur_state }}</span>
                    <ElButton v-show="presaleData.fsm_can_trig_data.审核触发.includes('同意')" type="success"
                        @click="handleCheck('同意')">同意</ElButton>
                    <el-popconfirm title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                        <template #reference>
                            <ElButton v-show="presaleData.fsm_can_trig_data.审核触发.includes('驳回')" type="danger">驳回</ElButton>
                        </template>
                    </el-popconfirm>
                    <el-popconfirm title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                        <template #reference>
                            <ElButton v-show="presaleData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger">拒绝</ElButton>
                        </template>
                    </el-popconfirm>
                </div>
            </template>
            <el-input v-model="presaleData.fsm_exe_log" class="mt-3" :autosize="{ minRows: 5, maxRows: 2 }"
                type="textarea" />
        </el-card>
        <el-form :rules="rules" :model="presaleData" ref="ruleFormRef" class="border p-16  min-w-[900px] w-[80%] m-auto mb-2" style="box-shadow: 0px 6px 14px 4px rgba(208, 208, 208, 0.34);background-color: #fff;">
            <el-descriptions  :column="2"   border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                    :label="t('presale.name')+':'" class="flex" :span="3">
                    <el-form-item prop="sell_offer_num">
                        <div class="flex">
                            <el-input style="width: 400px;" :disabled="presaleData.id != undefined" v-model="presaleData.sell_offer_num" />
                            <ElButton class="ml-2" v-if="presaleData.id == undefined" type="primary" @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                    :label="t('bom.custom')+':'" class="flex" :span="1">
                    <el-form-item prop="buyer_nick">
                        <div class="mr-2 border-1.4px min-w-30 pl-2 rounded-md">
                            {{  checkPermissionApi('客户名称显示')?(presaleData.buyer_nick?presaleData.buyer_nick:'请选择'):'***' }}
                        </div>
                        <ElButton @click="onSelCustomer" v-if="!['等待确认','已确认','等待审核'].includes(presaleData.fsm_cur_state)">
                            <Icon icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <!-- <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle"
                    :label="t('presale.engineer_buy')+':'" class="flex" :span="1">
                    <el-form-item>
                        <div class="mr-2 border-1.4px min-w-30 pl-2 rounded-md">
                            {{ presaleData.sell_man_name?presaleData.sell_man_name:'请选择' }}
                        </div>
                        <ElButton @click="onSelEngineer" v-if="!['等待确认','已确认','等待审核'].includes(presaleData.fsm_cur_state)">
                            <Icon icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item> -->
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('presale.hurry')+':'"
                    class="flex"  :span="3">
                    <el-form-item>
                        <el-radio-group v-model="presaleData.sell_man_note">
                            <el-radio :label="'正常'">正常</el-radio>
                            <el-radio :label="'加急'">加急</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>
        


        <div class="border pl-16 pr-16 pb-16 pt-4  min-w-[900px] w-[80%] m-auto" style="box-shadow: 0px 6px 14px 4px rgba(208, 208, 208, 0.34);background-color: #fff;">   
            <ElButton class="mt-2 mb-2" type="success" @click="onAddPdt" v-if="!['等待确认','已确认','等待审核'].includes(presaleData.fsm_cur_state)">
                <Icon class="mr-0.5" icon="icons8:plus" />
                新增产品
            </ElButton>
            <el-tabs type="border-card" v-model="activeTab" class="rounded-4px border-0.5px" :closable="currentRoute.query.type != 'info'"  @tab-remove="onRemovePdt">
                <el-tab-pane v-for="pdt, index in presaleData.pdt_list" :key="pdt.标识" :label="'产品' + (index + 1)"
                    :name="index.toString()">
                    <el-tabs v-model="pdt.activeTab" class="mb-20 tab-no-b">
                        <el-tab-pane label="产品信息" name="0">
                            <TabRequestPreSale :capmode="false" :fsm_cur_state="presaleData.fsm_cur_state" :pdt="pdt" :presale-data="presaleData" @onUpdate="updatePdt(index, $event)"/>
                        </el-tab-pane>
                        <el-tab-pane label="产品成本分析" name="1">
                            <TabBasePricePreSale :capmode="false" :fsm_cur_state="presaleData.fsm_cur_state" :pdt="pdt"
                                @onUpdate="updatePdt(index, $event)" />
                        </el-tab-pane>
                        <!-- 成交报价单在没有出厂报价前不显示 -->
                        <el-tab-pane label="产品成交报价" name="2">
                            <TabFinalPricePreSale :capmode="false" :fsm_cur_state="presaleData.fsm_cur_state" :pdt="pdt"
                                @onUpdate="updatePdt(index, $event)" />
                        </el-tab-pane>
                    </el-tabs>
                </el-tab-pane>
            </el-tabs>
        </div>

        <!-- v-if="presaleData.status ==t('status.wait_base_price') || presaleData.status == t('status.wait_review') || presaleData.status == t('status.wait_cus_comfirm')|| presaleData.status == t('status.cus_comfirm')"  -->
        <div class="mb-20"></div>

        <!-- 显示任务历史记录 -->
        <DialogCheckShowEx v-model:show="showCheckHisDlg" type="FSM" module="SellOffer"
            :title="presaleData.sell_offer_num" />

        <!-- 选择客户弹窗 -->
        <DialogSelCustomer v-model:show="showSelCustomerDlg" :title="t('bom.sel_cus')" @on-submit="onSelCustomerCallback" />

        <!-- 选择工程师 -->
        <DialogUser :param="''" v-model:show="showSelEngineerDlg" :title="t('msg.selectUser')"
            @on-submit="onSelEngineerCallback" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    background-color: #fff;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    white-space: nowrap;
    text-align: center;
    font-weight: bold;
}

//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
    margin-bottom: 10px;
}

//设置表单元格属性
:deep(.table_cell .cell) {
    padding-left: 3px;
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
    /* 添加你的样式 */
    text-align: center;
}

:deep(.bakinput .el-input__wrapper) {
    background-color: #f4f4f4;
}

:deep(.el-card) {
    border-width: 0px;
    background-color: var(--app-content-bg-color);
}

:deep(.el-card) .tab-no-b .el-tabs__content {
    padding: 15px;
}
</style>
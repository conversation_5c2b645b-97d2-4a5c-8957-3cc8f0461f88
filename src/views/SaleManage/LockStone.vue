<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import type { FormInstance } from 'element-plus'
import {checkFormRule} from '@/api/tool'
import { updateSaleApi,getInventoryDetailListApi,getSaleInfoApi  } from '@/api/product'
import { getTodayDate } from '@/api/tool'
import { DialogSelReceiptPdt } from '@/components/DialogSelReceiptPdt'
import { Dialog } from '@/components/Dialog'


const { currentRoute,back,push } = useRouter()
const { t } = useI18n()

//标题
const title = ref('')

//定义搜索条件
const defaultCondition = {
    pdt_id:'',
    page: 1,
    count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

//库存明细列表
const detailData = reactive([])


//查询库存明细列表
const getInventoryDetailList = async (page = 1) => {
  searchCondition.page = page
  const ret = await getInventoryDetailListApi(searchCondition)
  if(ret)
  {
    detailData.splice(0,detailData.length, ...ret.data)
    console.log(detailData)
  }
}

//切换流水页面
const onShowStatement = (item)=>{
  push({
      path: '/inventorymanage/inventorystatement',
      query:{
        pdt_id:item.pdt_id,
        pdt_name:item.pdt_name,
        pdt_nick:item.pdt_nick,
        store_id:item.store_id
      }
    })
}

//销售单数据源
const saleData = reactive({})

//pdt销售数量
const pdtCur = reactive({})

// //锁定

const onLock = (item)=>{
  ElMessageBox.confirm(t('msg.confirm_lock_pdt')+'-->'+item.锁定数量+item.pdt_base_unit, t('common.reminder'), {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    type: 'warning'
  })
  .then(async () => {
    //更新指定pdt锁定内容并提交
    for(let pdt of saleData.pdt_list)
    {
      if(pdt.标识 == pdtCur.标识)
      {
        //找到对应仓库锁定内容
        let bFind = false
        for(let stone of pdt.locked_list)
        {
          if(stone.store_id ==item.store_id)
          {
            stone.amount = parseInt(item.锁定数量)
            bFind = true
            break
          }
        }
        if(!bFind)
        {
          console.log('1111',item)
          pdt.locked_list.push({
            store_id:item.store_id,
            amount: parseInt(item.锁定数量)
          })
        }
        break;
      }
    }
    console.log(saleData)
    //提交更新
    const ret =await updateSaleApi(saleData)
    if(ret)
    {
        ElMessage.success(t('msg.updateSaleSuccess'))
        await updateUIData()
    }
  })
  .catch(() => {})
}


//锁定数字校验
const onValueCheck = (item)=>{
    
}

onMounted(async () => {    
    searchCondition.pdt_id = currentRoute.value.query.pdt_id as string
    title.value = '['+currentRoute.value.query.pdt_name+']'+currentRoute.value.query.pdt_nick+' 库存锁定'

    await updateUIData()
    
})

const updateUIData = async()=>{
  await getInventoryDetailList()
  //查询销售单信息 
  const ret = await getSaleInfoApi({
      sell_order_num:currentRoute.value.query.sell_order_num,
      page:1,
      count:100
  })
  if(ret)
  {
      Object.assign(saleData,ret.data)
      for(let one of saleData.pdt_list)
      {
          if(one.标识 == currentRoute.value.query.bs)
          {
              Object.assign(pdtCur,one)
              break
          }
      }
  }

  //更新产品在所有仓库中的的锁定情况
  for(let lock of pdtCur.locked_list)
  {
      for(let stone of detailData)
      {
          if(lock.store_id == stone.store_id)
          {
              stone.锁定数量 = lock.amount
              break
          }
      }
  }
}

</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">

        <el-table ref="tableRef" :data="detailData" style="width: 100%; margin-bottom: 20px" row-key="id" border stripe
          highlight-current-row header-cell-class-name="tableHeader">
          <el-table-column show-overflow-tooltip align="center"  prop="store_name" :label="t('store.name')" />
          <el-table-column show-overflow-tooltip align="center"  prop="store_nick" :label="t('store.nick')" />
          <el-table-column show-overflow-tooltip align="center"  prop="库存数量" :label="t('inventory.cur_count')" />
          <el-table-column show-overflow-tooltip align="center"  prop="pdt_base_unit" :label="t('product_manage.b_unit')" />
          <el-table-column show-overflow-tooltip align="center"  prop="avg_price_bef_tax" :label="t('inventory.price_avr_bef_tax')" />
          <el-table-column show-overflow-tooltip align="center"  prop="avg_price_aft_tax" :label="t('inventory.price_avr_aft_tax')" />
          <el-table-column show-overflow-tooltip align="center"   :label="t('sale.count')" >
            <template #default="scope">
                {{ pdtCur.销售数量 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"   :label="t('saleout.count')" >
            <template #default="scope">
                {{ pdtCur.已发货 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"  prop="锁定数量" :label="t('inventory.lock_count')" >
            <template #default="scope">
                <el-input v-model="scope.row.锁定数量" @input="onValueCheck(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip fixed="right" align="center" width="100px" prop="name" :label="t('roleTable.opt')" >
            <template #default="scope">
              <ElButton type="success" size="small" @click="onLock(scope.row)">{{ t('inventory.lock') }}</ElButton>
            </template>
          </el-table-column>
        </el-table>
        <!-- <Dialog  v-model="showInput" :title="inputTitle" :max-height="100">
            <el-descriptions class="flex-1" :column="2" border>
            <el-descriptions-item :label="t('inventory.input_count')" class="flex">
                <el-input v-model="inputCount" clearable />
            </el-descriptions-item>
            </el-descriptions>
            <template #footer>
            <ElButton type="primary" @click="onModify">
                {{ t('common.save') }}
            </ElButton>
            <ElButton @click="showInput = false">{{ t('common.channel') }}</ElButton>
            </template>
        </Dialog> -->

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}

:deep(.el-input__inner){
  text-align: center;
}


</style>
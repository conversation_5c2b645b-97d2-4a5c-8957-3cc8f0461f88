<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi, updatePayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, closeOneTagByPath } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { DialogGetMoney } from '@/components/DialogGetMoney'
import { getSellOutReportCusApi, getSellValueReportCusApi, getSellValueReportOrder<PERSON>pi, getSellValueReportOutApi } from '@/api/tj';

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    年份: '',
    月份: '',
    类型: '按年统计',
    page: 1,
    count: 50
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//数据源
const reportData = reactive([])
//当前选中行
const currentRow = ref(null)


const getSellValueReportOut = async () => {
    let tmp = cloneDeep(searchCondition)
    if (tmp.类型 == '按年统计') {
        tmp.月份 = ''
    }
    else if (tmp.类型 == '按月统计') {
        tmp.年份 = ''
    }
    loading.value = true
    const ret = await getSellValueReportOutApi(tmp)
    if (ret) {
        reportData.splice(0, reportData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
    loading.value = false
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getSellValueReportOut()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getSellValueReportOut()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getSellValueReportOut()
}

//处理表格对象操作
const handleOper = (type, row) => {
    if (type === 'edit' || type == 'info') {

    }

}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}

const setDate = () => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    if (searchCondition.类型 == '按年统计') {
        searchCondition.年份 = currentYear.toString()
    }
    else if (searchCondition.类型 == '按月统计') {
        searchCondition.月份 = currentYear.toString()+'-'+(currentDate.getMonth() + 1).toString().padStart(2, '0')
    }
    console.log(searchCondition)
    //刷新表格
    getSellValueReportOut()
}

onMounted(() => {

    setDate()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

 
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if (['出货总金额','出货毛利'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                const sum = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
                sums[index] = '￥' + sum.toLocaleString(); // 添加千位分隔符
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}

const loading = ref(false)
</script>

<template>
    <div ref="rootRef">
        <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">

            <div class="text-center pt-2 mb-2 font-bold" style="color:#333">{{searchCondition.类型=='按年统计'?searchCondition.年份:searchCondition.月份}}发货毛利统计表</div>
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">类型</div>
                    <el-select size="small" @change="setDate" class="searchItem" v-model="searchCondition.类型" placeholder="">
                        <el-option v-for="item in ['按年统计', '按月统计']" :key="item" :label="item" :value="item" />
                    </el-select>
                </div>
                <div v-if="searchCondition.类型 == '按年统计'" class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">统计年份</div>
                    <el-date-picker size="small" @change="getSellValueReportOut" :clearable="false" v-model="searchCondition.年份"
                        type="year" placeholder="Pick a year" format="YYYY" value-format="YYYY" />
                </div>
                <div v-if="searchCondition.类型 == '按月统计'" class="inline-flex items-center mr-1 mb-1">
                    <div class="searchTitle">统计年月</div>
                    <el-date-picker @change="getSellValueReportOut"  size="small" v-model="searchCondition.月份" type="month" placeholder=""
                        format="YYYY-MM" value-format="YYYY-MM" />
                </div>

            </div>

            <el-table v-loading="loading" header-cell-class-name="tableHeader" :data="reportData" style="width: 100%;color: #666666;"
                show-summary :summary-method="getSummaries" @current-change="setCurrentRow" border stripe>
                <el-table-column align="center" show-overflow-tooltip prop="日期" :label="'日期'" />
                <el-table-column align="center" show-overflow-tooltip prop="订单个数" :label="'订单个数'" width="100"/>
                <el-table-column align="center" show-overflow-tooltip prop="出货总金额" :label="'出货总金额'">
                    <template #default="scope">
                        <div>{{ '￥' + scope.row.出货总金额.toLocaleString('en-US') }}</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="出货毛利" :label="'出货毛利'" >
                    <template #default="scope">
                        <div>{{ '￥' + scope.row.出货毛利.toLocaleString('en-US') }}</div>
                    </template>
                </el-table-column>

                <!-- 
                <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
                    <template #default="scope">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                    </template>
                </el-table-column> -->
            </el-table>
            <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>

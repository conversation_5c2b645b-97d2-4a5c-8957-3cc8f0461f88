<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi, updatePayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, closeOneTagByPath } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { DialogGetMoney } from '@/components/DialogGetMoney'
import { getSellOutReportCusApi } from '@/api/tj';

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    出库业务日期: ['', ''],
    客户: '',
    销售人员: '',
    跟单人员: '',
    排序方式: '出库金额升序',
    page: 1,
    count: 50
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//数据源
const reportData = reactive([])
//当前选中行
const currentRow = ref(null)


const getSellOutReportCus = async () => {
    let tmp = cloneDeep(searchCondition)
    tmp.出库业务日期 = searchCondition.出库业务日期[0] + ',' + searchCondition.出库业务日期[1]
    const ret = await getSellOutReportCusApi(tmp)
    if (ret) {
        reportData.splice(0, reportData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getSellOutReportCus()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getSellOutReportCus()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getSellOutReportCus()
}

//处理表格对象操作
const handleOper = (type, row) => {
    if (type === 'edit' || type == 'info') {

    }

}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}

function formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}
const initTime = () => {
    const currentDate = new Date();
    const firstDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    const tmp = [formatDate(firstDate), formatDate(lastDate)];

    defaultCondition.出库业务日期 = []
    defaultCondition.出库业务日期.push(...tmp);
    searchCondition.reset()
}

onMounted(() => {
    initTime()
    if (currentRoute.value.query.user_name != undefined) {
        searchCondition.销售人员 = currentRoute.value.query.user_name as string
    }

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    //刷新表格
    getSellOutReportCus()
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if (['出库数量', '出库金额', '退货数量', '退货金额', '合计数量', '合计金额'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                const sum = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
                sums[index] = '￥' + sum.toLocaleString(); // 添加千位分隔符
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}

</script>

<template>
    <div ref="rootRef">
        <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">

            <div class="text-center pt-2 mb-2 font-bold" style="color:#333">销售出库统计表(客户)</div>
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">

                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">客户</div>
                    <el-input size="small" v-model="searchCondition.客户" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">销售人员</div>
                    <el-input size="small" v-model="searchCondition.销售人员" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">跟单人员</div>
                    <el-input size="small" v-model="searchCondition.跟单人员" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">排序方式</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.排序方式" placeholder="">
                        <el-option v-for="item in ['出库金额升序', '出库金额倒序', '出库数量升序', '出库数量倒序']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>
                <div class="inline-flex items-center mr-1 mb-1">
                    <div class="searchTitle">出库日期</div>
                    <el-date-picker size="small" class="searchItem" v-model="searchCondition.出库业务日期" type="daterange"
                        range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间"
                        value-format="YYYY-MM-DD" />
                </div>

                <div class="flex justify-end items-center mr-6 mt-2">
                    <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="ml-1">查询</div>
                    </ElButton>
                    <ElButton class="ml-4" type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="ml-1">清除</div>
                    </ElButton>
                </div>
            </div>

            <el-table header-cell-class-name="tableHeader" :data="reportData" style="width: 100%;color: #666666;"
                show-summary :summary-method="getSummaries" @current-change="setCurrentRow" border stripe>
                <el-table-column align="center" show-overflow-tooltip prop="客户编号" :label="'客户编号'" />
                <el-table-column align="center" show-overflow-tooltip prop="客户名称" :label="'客户名称'" />
                <el-table-column align="center" show-overflow-tooltip prop="销售人员" :label="'销售人员'" />
                <el-table-column align="center" show-overflow-tooltip prop="跟单人员" :label="'跟单人员'" />
                <el-table-column align="center" show-overflow-tooltip prop="出库" :label="'出库'">
                    <el-table-column align="center" show-overflow-tooltip prop="出库数量" :label="'数量'" />
                    <el-table-column align="center" show-overflow-tooltip prop="出库金额" :label="'金额'">
                        <template #default="scope">
                            <div>{{ '￥' + scope.row.出库金额.toLocaleString('en-US') }}</div>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="退货" :label="'退货'">
                    <el-table-column align="center" show-overflow-tooltip prop="退货数量" :label="'数量'" />
                    <el-table-column align="center" show-overflow-tooltip prop="退货金额" :label="'金额'">
                        <template #default="scope">
                            <div>{{ '￥' + scope.row.退货金额.toLocaleString('en-US') }}</div>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="合计" :label="'合计'">
                    <el-table-column align="center" show-overflow-tooltip prop="合计数量" :label="'数量'" />
                    <el-table-column align="center" show-overflow-tooltip prop="合计金额" :label="'金额'">
                        <template #default="scope">
                            <div>{{ '￥' + scope.row.合计金额.toLocaleString('en-US') }}</div>
                        </template>
                    </el-table-column>
                </el-table-column>

                <!-- 
                <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
                    <template #default="scope">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                    </template>
                </el-table-column> -->
            </el-table>
            <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>

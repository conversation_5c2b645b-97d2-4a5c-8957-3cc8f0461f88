<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton, ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio, ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { onBeforeMount } from 'vue'
import { RightMenu } from '@/components/RightMenu'
import { Dialog } from '@/components/Dialog'
import { decodeString } from '@/api/tool'
import { computed, nextTick } from 'vue';
import { useCache } from '@/hooks/web/useCache'
import { delPreSaleApi, getPreSaleListApi, getPreSalePriceExcelApi } from '@/api/product'
import { useAppStore } from '@/store/modules/app'
import { DialogPdtBind } from '@/components/DialogPdtBind'
import { cloneDeep } from 'lodash-es';
import { geBuyerListApi } from '@/api/customer'
import { checkPermissionApi } from '@/api/tool'

const { currentRoute, push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//售前报价单数据源
const presaleData = reactive([])

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
const defaultCondition = {
  fsm_cur_state: '',
  产品名称:'',
  产品编号:'',
  sell_offer_num:'',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getPreSaleList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getPreSaleList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getPreSaleList(val)
}


//处理表格对象操作
const handleOper = async (type, item) => {
  //编辑产品
  if (type === 'info' || type === 'edit') {
    if(type === 'info' && !checkPermissionApi('报价单明细查看'))
    {
      ElMessage.error('无权限')
      return
    }
    if(type === 'edit' && !checkPermissionApi('报价单修改'))
    {
      ElMessage.error('无权限')
      return
    }

    push({
      path: '/presalemanage/presaledetail',
      query: {
        id: item.id,
        type: type,
        mode: t(currentRoute.value.meta.title) as string
      }
    })
  }
  // else if (type === 'edit') {
  //   push({
  //     path: '/presalemanage/addpresale',
  //     query: {
  //       id: item.id
  //     }
  //   })
  // }
  else if (type === 'del') //删除
  {
    if(!checkPermissionApi('报价单删除'))
    {
      ElMessage.error('无权限')
      return
    }

    ElMessageBox.confirm(t('msg.confirm_del_presale'), t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)
      const ret = await delPreSaleApi({
        ids: [item.id],
        fsm_exe_man_name: info.resident_name,
        fsm_exe_trig: '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getPreSaleList()
      }
    }
    ).catch(() => { })
  }
  else if (type === 'down') //下载报价
  {
    const ret = await getPreSalePriceExcelApi({
      id: item.id
    })
    if (ret) {
      //downloadFile(import.meta.env.VITE_UPLOAD_URL+'/server/files/excel/'+ret.data.filename,ret.data.filename)
      downloadFile(ret.data.download, ret.data.filename)
    }
  }
  else if (type === 'tobuy') //售前转销售
  {
    console.log('00000',item)
    //检测客户是否填了税号
    const ret = await geBuyerListApi({
            ids:[item.buyer_id],
            page:1,
            count:100
        })
    if(ret)
    {
        if(ret.data.length<=0)
        {
            ElMessage({
                type: 'error',
                message: t('msg.searchFalse'),
            })
            return
        }
        //Object.assign(customerData, ret.data[0])
        console.log(ret)
        if(ret.data[0].corp_taxnum == '')
        {
            ElMessage({
                type: 'error',
                message: '客户未配置开票信息，请先配置！',
            })
            return
        }

            //弹出pdt绑定界面
        showPdtBindDialog.value = true
        curSelPresell.value = item
    }



  }
}

const showPdtBindDialog = ref(false)

//实现downloadFile直接调用浏览器下载文件
function downloadFile(url, filename) {
  fetch(url)
    .then(res => res.blob())
    .then(blob => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      a.remove();
    });
}

//进入新增界面
const onAddPreSale = () => {
  // push({
  //   path: '/presalemanage/addpresale'
  // })
  push({
    path: '/presalemanage/presaledetail',
    query: {
      type: 'edit',
      mode: t(currentRoute.value.meta.title) as string
    }
  })
}

//进入设置出厂报价页面
const onSetBasePrice = (item) => {

}

//查询列表
const getPreSaleList = async (page = 1) => {

  // searchCondition.module_name = currentRoute.value.meta.title

  // if (t(currentRoute.value.meta.title as string) == t('presale.review_list')) {
  //   searchCondition.status = t('status.wait_review')
  // }

  searchCondition.page = page

  if (searchCondition.fsm_cur_state == '全部') {
    searchCondition.fsm_cur_state = ''
  }

  //支持组合查询
  const tmp = cloneDeep(searchCondition)
  if (searchCondition.fsm_cur_state == '等待审核') {
    tmp.fsm_cur_state_list = ['等待审核', '等待反审']
  }
  else {
    tmp.fsm_cur_state_list = [searchCondition.fsm_cur_state]
  }


  const ret = await getPreSaleListApi(tmp)
  if (ret) {
    presaleData.splice(0, presaleData.length, ...ret.data)
    console.log(presaleData)
    totleCount.value = parseInt(ret.count)
  }
}

//页面标题
const title = ref('')
onMounted(async () => {
  title.value = currentRoute.value.meta.title as string

  if(currentRoute.value.query.sell_offer_num != undefined)
  {
    searchCondition.sell_offer_num = currentRoute.value.query.sell_offer_num as string
  }

  // if (t(currentRoute.value.meta.title) == '工程报价单') {
  //   searchCondition.fsm_cur_state = '等待工程报价'
  // }
  if (t(currentRoute.value.meta.title) == '报价审核') {
    searchCondition.fsm_cur_state = '等待审核'
  }

  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);



  //更新产品列表
  getPreSaleList()

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

})


//当前选择的收钱但
const curSelPresell = ref({})


</script>

<template>
  <div class="flex">
    <!-- 右侧产品列表 -->
    <div ref="rootRef" class="relative w-[100%] flex-grow overflow-hidden">
      <div class="h-[100%] bg-white p-7 pt-0 relative" style="background-color: #f5f7f9;">
        <div class="absolute top-3 left-15">
          <ElButton v-if="checkPermissionApi('报价单新增') && currentRoute.meta.title == 'presale.list'" type="success" @click="onAddPreSale">
            <Icon icon="carbon:document-add" />
            <div class="pl-1" >{{ t('button.add') }}</div>
          </ElButton>
        </div>
        <!-- 检索条件 -->
        <div style="background-color:#fff" class="pt-2 pb-2 mb-2">
          <!-- 标题 -->
          <div class="text-center mb-10 font-bold">{{ t(title) }}</div>
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.check_status') }}</div>
            <el-select size="small" class="ml-1 searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择审核状态">
              <el-option v-for="item in ['全部', '等待销售报价', '等待审核', '等待修改', '等待确认', '已拒绝', '已确认', '已删除']" :key="item"
                :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">售前单号</div>
            <el-input size="small"   v-model="searchCondition.sell_offer_num" placeholder=""  class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品名称</div>
            <el-input size="small"   v-model="searchCondition.产品名称" placeholder=""  class="searchItem" />
          </div>

          
          <div class="flex justify-end items-center mr-6 mt-2 mb-2">
            <!-- <el-checkbox size="small" :label="t('customer.senior')" v-model="senior" /> -->
            <ElButton type="primary" class="ml-4" @click="onSearch">
              <!-- <Icon icon="ri:phone-find-line" /> -->
              <Icon icon="tabler:search" />
              <div class="pl-1">查询</div>
            </ElButton>
            <ElButton type="warning" class="ml-4" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-1">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 图例 -->
        <div class="flex mb-4">
          <div class="flex text-sm items-center mr-4">
            <div class="title_wait_presale  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
            等待售前申报
          </div>
          <div class="flex text-sm items-center mr-4">
            <div class="title_wait_project rounded-[50%] w-[14px] h-[14px] mr-1"></div>
            等待工程报价
          </div>
          <div class="flex text-sm items-center mr-4">
            <div class="title_wait_sale rounded-[50%] w-[14px] h-[14px] mr-1"></div>
            等待销售报价
          </div>
          <div class="flex text-sm items-center mr-4">
            <div class="title_wait_check rounded-[50%] w-[14px] h-[14px] mr-1"></div>
            等待审核
          </div>
          <div class="flex text-sm items-center mr-4">
            <div class="title_wait_modify rounded-[50%] w-[14px] h-[14px] mr-1"></div>
            等待修改
          </div>
          <div class="flex text-sm items-center mr-4">
            <div class="title_wait_confirm rounded-[50%] w-[14px] h-[14px] mr-1"></div>
            等待确认
          </div>
          <div class="flex text-sm items-center mr-4">
            <div class="title_ok rounded-[50%] w-[14px] h-[14px]  mr-1"></div>
            已确认
          </div>
          <div class="flex text-sm items-center">
            <div class="title_no rounded-[50%] w-[14px] h-[14px]  mr-1"></div>
            已拒绝
          </div>
        </div>

        <!-- 列表 -->
        <div>
          <!-- 表头 -->
          <div class="flex header headerBk items-center">
            <div class="w-[85%] flex">
              <div class="w-[30%] ">产品名称</div>
              <div class="w-[5%] ">数量</div>
              <div class="w-[5%] ">材质</div>
              <div class="w-[10%] ">规格/克重</div>
              <div class="w-[10%] ">工艺要求</div>
              <div class="w-[10%] ">Logo要求/印刷</div>
              <div class="w-[10%] ">颜色</div>
              <div class="w-[10%] ">包装要求</div>
              <div class="w-[10%] ">其他</div>
            </div>
            <div class="flex flex-grow !p-0">
              <!-- <div class="rightcss rightcss_title ">加急状态</div> -->
              <div class="rightcss rightcss_title ">订单状态</div>
              <div class="rightcss_title !min-w-[100px] ">操作</div>
            </div>
          </div>
          <!-- 表内容 -->
          <div class="mt-4 bg-white rounded" v-for="item in presaleData" :key="item.id"
            style="box-shadow:var(--el-box-shadow-lighter);">
            <!-- 内容头 -->
            <div class="font-bold border-b-1px border-gray-300">
              <div class="p-2 pt-5 pb-5 flex  justify-between flex-nowrap text-[13px] h-[30px]">
                <div class="w-[35%] min-w-[300px] flex items-center">
                  <!-- <div
                    :class="{ 'mr-1': true, 'rounded-[12rem]': true, 'p-1.5': true, 'title_wait_presale': ['等待售前申报'].includes(item.fsm_cur_state), 'title_wait_project': item.fsm_cur_state === '等待工程报价', 'title_wait_sale': item.fsm_cur_state == '等待销售报价', 'title_wait_check': item.fsm_cur_state === '等待审核', 'title_wait_modify': item.fsm_cur_state === '等待修改', 'title_wait_confirm': item.fsm_cur_state === '等待确认', 'title_ok': ['已确认', '已转销售'].includes(item.fsm_cur_state) }">
                  </div> -->
                  <div class="flex items-center flex-shrink-0 mr-4 w-150px">
                    <div class="p-1 pl-2 pr-2 rounded" style="color: #fff;"
                      :class="{ 'title_no': item.fsm_cur_state == '已拒绝', 'title_wait_presale': ['等待售前申报'].includes(item.fsm_cur_state), 'title_wait_project': item.fsm_cur_state === '等待工程报价', 'title_wait_sale': item.fsm_cur_state == '等待销售报价', 'title_wait_check': ['等待审核', '等待反审'].includes(item.fsm_cur_state), 'title_wait_modify': item.fsm_cur_state === '等待修改', 'title_wait_confirm': item.fsm_cur_state === '等待确认', 'title_ok': ['已确认', '已转销售'].includes(item.fsm_cur_state) }">
                      {{ item.fsm_cur_state }}
                    </div>
                  </div>
                  <div class="mr-28 font-bold flex-shrink-0">{{ item.create_date.split(' ')[0] }}</div>
                  <div class="flex-shrink-0 flex items-center text-12px rounded-[4px] p-1 pr-3 pl-3 }">
                    订单号: {{ item.sell_offer_num }}
                  </div>

                </div>
                <div class="min-w-[200px] ml-40 flex-shrink-0 flex items-center ">
                  <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                  {{ checkPermissionApi('客户名称显示')?item.buyer_nick:'***' }}
                </div>
                <!-- 靠右的其他信息 -->
                <div class=" flex justify-between items-center w-[280px] ">
                  <div class="flex items-center">
                    <div>创建人: {{ item.create_man_name }}</div>
                  </div>
                  <div class="flex items-center min-w-[86px]">
                    <div>工程师: {{ item.sell_man_name }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 内容体 -->
            <div class="flex" style="border-bottom-left-radius: 4px;border-bottom-right-radius: 4px;">
              <!-- 左边产品列表 -->
              <div class="w-[85%]  table_self">
                <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%] border-b-0.5px">
                  <div class="w-[30%] flex-grow ">
                    <div class="flex justify-start items-center w-[100%] p-1">
                      <el-image v-if="pdt.pics.length > 0" class="object-fill w-[80px] h-[80px] min-w-[80px]"
                        :src="pdt.pics[0].url" />
                      <el-image v-if="pdt.pics.length <= 0" class="object-fill w-[80px] h-[80px] min-w-[80px]"
                        src="/nopic.jpg" />
                      <div class="ml-4 inline-block text-left max-w-[100%]">
                        <div style="white-space: normal;" class="nameStyle" @click="handleOper('info', item)">{{
                          pdt.pdt_nick }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="w-[5%] flex justify-center items-center border-left-1px">{{ pdt.pdt_amount }}</div>
                  <div class="w-[5%] flex justify-center items-center border-left-1px">{{ pdt.pdt_stuff }}</div>
                  <div class="w-[10%] flex justify-center items-center border-left-1px">{{ pdt.pdt_specs }}</div>
                  <div class="w-[10%] flex justify-center items-center border-left-1px">{{ pdt.pdt_skill }}</div>
                  <div class="w-[10%] flex justify-center items-center border-left-1px">{{ pdt.pdt_print }}</div>
                  <div class="w-[10%] flex justify-center items-center border-left-1px">{{ pdt.pdt_color }}</div>
                  <div class="w-[10%] flex justify-center items-center border-left-1px">{{ pdt.pdt_pack }}</div>
                  <div class="w-[10%] flex justify-center items-center border-left-1px">{{ pdt.note }}</div>

                </div>
              </div>
              <!-- 右边其他数据 -->
              <div class="flex flex-grow text-center  right border-b-0.5px">

                <div v-if="'正常' == item.sell_man_note"
                  class="rightcss flex justify-center items-center border-l-0.5px  border-r-0.5px" c>
                  <ElButton style="font-size: 14px;" plain color="#00BA80" size="small">
                    {{ item.sell_man_note }}</ElButton>
                </div>
                <div v-else class="rightcss flex justify-center items-center border-l-0.5px  border-r-0.5px">
                  <ElButton plain color="#F56C6C" style="font-size: 14px;" size="small">
                    {{ item.sell_man_note }}
                  </ElButton>
                </div>

                <div class="!min-w-[100px] flex flex-col justify-center items-center m-auto">
                  <el-dropdown class="mb-2" v-if="t(currentRoute.meta.title) == '报价单'" trigger="click" placement="bottom">
                    <span class="el-dropdown-link">
                      <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                    </span>
                    <template #dropdown>
                      <!-- <div  class="flex flex-wrap w-[240px]"> -->
                      <el-dropdown-menu>
                        <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('保存销售报价')"
                          @click="handleOper('edit', item)">编辑报价单</el-dropdown-item>
                        <el-dropdown-item
                          @click="handleOper('info', item)">查看报价单</el-dropdown-item>
                        <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('反审')"
                          @click="handleOper('edit', item)">反审</el-dropdown-item>
                        <el-dropdown-item v-if="item.fsm_cur_state === '等待提交'"
                          @click="handleOper('info', item)">提交报价</el-dropdown-item>
                        <el-dropdown-item v-if="item.fsm_cur_state === '等待确认'"
                          @click="handleOper('info', item)">确认报价</el-dropdown-item>
                        <!-- <el-dropdown-item v-if="item.fsm_cur_state === '等待销售报价'"
                          @click="handleOper('info', item)">销售报价</el-dropdown-item> -->
                        <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('删除')"
                          @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                        <el-dropdown-item v-if="['已确认','已转销售'].includes(item.fsm_cur_state)"
                          @click="handleOper('tobuy', item)">转销售</el-dropdown-item>
                      </el-dropdown-menu>
                      <!-- </div> -->
                    </template>
                  </el-dropdown>

                  <ElButton type="primary" class="mb-2" v-if="t(currentRoute.meta.title) == '报价审核'" size="small"
                    @click="handleOper('info', item)">审核</ElButton>
                  <ElButton type="primary"
                    v-if="t(currentRoute.meta.title) == '报价单' && ['等待确认', '已确认','已转销售'].includes(item.fsm_cur_state)"
                    size="small" @click="handleOper('down', item)">下载报价</ElButton>

                  <div class="rightcss !max-w-[100px] mt-3 text-red-500">
                    {{ item.fsm_log_list[0][5] }}
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>

        <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[10, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>

    </div>

    <DialogPdtBind v-model:show="showPdtBindDialog" :data="curSelPresell" @on-submit="() => { }" />
  </div>
  <!-- <div class="mb-20"></div> -->
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  cursor: pointer;
}

.nameStyle:hover {
  color: rgb(130, 130, 255);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem {
  width: 140px;
}



.searchItem {
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  color: #333;
  font-size: 14px;
  height: 35px;
  font-weight: bold;
}

.headerBk {
  background-color: #fff !important;
}

.content {
  &:extend(.header);
  font-size: 14px;
}

.header>div,
.content>div {
  display: flex;
  // border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 14px;
}

.header>div:last-child,
.content>div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self {
  font-size: 13px;
  color: #666
}

.table_self>div>div,
.right>div {
  // border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.test {
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text {
  font-size: 11px;
  color: #646464;
}

.ex_text_danger {
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
  flex: 1;
  min-width: 0;
  /* 设置最小宽度，防止内容撑大 */
  text-align: center;
  /* 文字居中对齐 */
  word-wrap: break-word;
  /* 文字超长时换行处理 */
  font-size: 11px;
}

.rightcss_title {
  display: flex;
  // border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}

.title_wait_presale {
  //等待售前申报
  background-color: rgba(90, 139, 243, 1);
  // border: #d9d9d9 1px solid;
}

.title_wait_project {
  //等待工程报价
  background-color: rgba(252, 219, 105, 1);
}

.title_wait_sale {
  //等待销售报价
  background-color: rgba(249, 183, 123, 1);
}

.title_wait_check {
  //等待审核
  background-color: rgba(188, 178, 244, 1);
}

.title_wait_modify {
  //等待修改
  background-color: rgba(255, 192, 192, 1);
}

.title_wait_confirm {
  //等待确认
  background-color: rgba(117, 224, 243, 1);
}

.title_ok {
  //已确认
  background-color: rgba(0, 186, 128, 1);
}

.title_wait_presale2 {
  //等待售前申报
  color: rgba(90, 139, 243, 1);
  border: 1px solid rgba(90, 139, 243, 1);
  background-color: color-mix(in oklab, rgba(90, 139, 243, 1), transparent 80%);
}

.title_wait_project2 {
  //等待工程报价
  color: rgba(252, 219, 105, 1);
  border: 1px solid rgba(252, 219, 105, 1);
  background-color: color-mix(in oklab, rgba(252, 219, 105, 1), transparent 80%);
}

.title_wait_sale2 {
  //等待销售报价
  color: rgba(249, 183, 123, 1);
  border: 1px solid rgba(249, 183, 123, 1);
  background-color: color-mix(in oklab, rgba(249, 183, 123, 1), transparent 80%);
}

.title_wait_check2 {
  //等待审核
  color: rgba(188, 178, 244, 1);
  border: 1px solid rgba(188, 178, 244, 1);
  background-color: color-mix(in oklab, rgba(188, 178, 244, 1), transparent 80%);
}

.title_wait_modify2 {
  //等待修改
  color: rgba(255, 192, 192, 1);
  border: 1px solid rgba(255, 192, 192, 1);
  background-color: color-mix(in oklab, rgba(255, 192, 192, 1), transparent 80%);
}

.title_wait_confirm2 {
  //等待确认
  color: rgba(117, 224, 243, 1);
  border: 1px solid rgba(117, 224, 243, 1);
  background-color: color-mix(in oklab, rgba(117, 224, 243, 1), transparent 80%);
}

.title_ok2 {
  //已确认
  color: rgba(0, 186, 128, 1);
  border: 1px solid rgba(0, 186, 128, 1);
  background-color: color-mix(in oklab, rgb(0, 186, 128, 1), transparent 80%);
}

.title_no {
  //已拒绝
  background-color: #F56C6C;
}
</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getBomInfoApi } from '@/api/product'
import { usePermissionStore } from '@/store/modules/permission'
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { closeOneTagByName} from '@/api/tool'

const { currentRoute,push,back } = useRouter()
const router = useRouter();
const permissionStore = usePermissionStore()
const { t } = useI18n()

const tableData = ref([])

//查询BOM结构图
const getBomStruct = async(bomid)=>{
  const ret = await getBomInfoApi({
    id:bomid,
    is_all_level :1
  })
  if(ret)
  {
    console.log(ret)
    tableData.value = [ret.data]
  }
}

onMounted(async() => {
    if(currentRoute.value.query.bomid != undefined)
    {
      getBomStruct(currentRoute.value.query.bomid)
    }
})

//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}


</script>

<template>
  <ContentDetailWrap :title="currentRoute.query.nick+' [BOM结构]'" @back="baskFront()">
    <div ref="rootRef" class="relative w-[100%] !bg-white flex-grow overflow-hidden">
      <div class="h-[100%] bg-white p-7">
        <el-table
          :data="tableData"
          style="width: 100%; margin-bottom: 20px"
          row-key="pdt_data.id"
          border
          default-expand-all
          :tree-props="{ children: 'sub_bom'}"
        >
        <el-table-column prop="pdt_data.nick" label="产品名称" show-overflow-tooltip />
        <el-table-column prop="pdt_level" label="层级" show-overflow-tooltip width="70"/>
        <el-table-column prop="pdt_data.name" label="物料编码" show-overflow-tooltip  width="130" />
        <el-table-column label="规格" show-overflow-tooltip  max-width="230" >
          <template #default="scope">        
            <el-tooltip
              v-if="scope.row.pdt_data.specs_name != '' && scope.row.pdt_data.specs_name != undefined"
              class="box-item"
              effect="dark"
              :content="scope.row.pdt_data.specs_text"
              placement="bottom"
            >
            <el-tag style="white-space: normal;max-width: 300px;overflow: hidden;text-overflow: ellipsis;" type="success" effect="dark">{{ scope.row.pdt_data.specs_name=='自定义规格'?scope.row.pdt_data.specs_text:scope.row.pdt_data.specs_name }}</el-tag>
            
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="pdt_data.用量" label="用量" show-overflow-tooltip width="100"/>
        <el-table-column prop="pdt_data.base_unit" label="单位" show-overflow-tooltip width="100"/>
        <el-table-column prop="pdt_data.损耗率" label="损耗率" show-overflow-tooltip width="100"/>
        <el-table-column prop="pdt_data.remark" label="备注" show-overflow-tooltip />
        </el-table>
      </div>
    </div>
  </ContentDetailWrap>
</template>

<style lang="less" scoped>

</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDatePicker,ElTag,  ElButton,  ElMessage, ElMessageBox, ElImage,  ElInput, ElSelect, ElOption, ElCheckbox, ElDropdown, ElDropdownItem,  ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getSaleOutListApi,delSaleOutApi } from '@/api/product'
import { useCache } from '@/hooks/web/useCache'
import { DialogSaleSel } from '@/components/DialogSaleSel'
import { checkPermissionApi, downloadFile, getMoneyFlag } from '@/api/tool';
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { cloneDeep } from 'lodash-es';
import { exportSellOutListApi } from '@/api/extra';
import { useAppStore } from '@/store/modules/app'

const { currentRoute,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()

//出库数据源
const outData = reactive([])

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state:'',
  销售单号:'',
  出库单号:'',
  产品名称:'',
  产品编码:'',
  产品品牌:'',
  产品规格:'',
  客户名称:'',
  出库人员:'',
  销售人员:'',
  跟单人员:'',
  默认排序:'出库日期倒序',
  支付方式:'',
  仓库:'',
  出库备注:'',
  快递单号:'',
  快递公司:'',
  收件人:'',
  客户订单号:'',
  出库业务日期:['',''],
  最后出库时间:['',''],
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])
const rootData = {
  added: {
    
  }
}
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getSaleOutList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getSaleOutList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getSaleOutList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
  //编辑产品
  if(type === 'info' || type === 'edit' || type === 'check')
  {
    const retPath = currentRoute.value.name.indexOf('Stone')>=0?'/inventorymanage/stoneaddsaleout':'/salemanage/addsaleout'
    push({
      path: retPath,
      query:{
          id:item.id,
          sell_order_num:item.sell_order_num,
          type:type === 'check'?'info':type,
          cmd:type === 'check'?'审核':''
      }
    })
  }
  else if(type === 'del') 
  {
    ElMessageBox.confirm(t('msg.confirm_del_saleout')+'--> '+item.sell_takeout_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)
      const ret = await delSaleOutApi({
        ids: [item.id],
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getSaleOutList()
      }
    }
    ).catch(() => {})
  }


}

//选择销售订单弹窗
const showSaleSel = ref(false)
const onAddSaleOut = ()=>{
    showSaleSel.value = true
}
//选择采购订单回调
const onSaleOutSelCallback = (item)=>{
    console.log(item)
    const retPath = currentRoute.value.name == "StoneSaleOutList"?'/inventorymanage/stoneaddsaleout':'/salemanage/addsaleout'
    
    push({
    path: retPath,
    query:{
        id:'',
        sell_order_num:item.sell_order_num
    },    
  })
}

//跳转到销售订单
const onNaviSale = (item)=>{
  if(currentRoute.value.name == "StoneSaleOutList")
  {
    return
  }
  push({
    path: '/salemanage/salemanage',
    query:{
        id:'',
        sell_order_num:item.sell_order_num
    },    
  })
}

//查询销售出库单列表
const getSaleOutList = async (page = 1) => {
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  tmp.出库业务日期 = searchCondition.出库业务日期[0]+','+searchCondition.出库业务日期[1]
  tmp.最后出库时间 = searchCondition.最后出库时间[0]+','+searchCondition.最后出库时间[1]

  const ret = await getSaleOutListApi(tmp)
  if(ret)
  {
    outData.splice(0,outData.length, ...ret.data)
    console.log(outData)
    totleCount.value = parseInt( ret.count)

    //修复下没有的字段
    for(let item of outData)
    {
      for(let pdt of item.pdt_list)
      {
        if(pdt.备品发货数量 == undefined)
        {
          pdt.备品发货数量 = 0
        }
      }
    }

  }
  Object.assign(rootData, ret)
}
const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: '销售出库单' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}

onMounted(async() => {
  if(currentRoute.value.name === "StoneSaleOutCheck")
  {
    searchCondition.fsm_cur_state = '等待审核'
  }


  if(currentRoute.value.query.sell_order_num != undefined)
    searchCondition.sell_order_num = currentRoute.value.query.sell_order_num
    getSaleOutList()  
      //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})



const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    tmp.出库业务日期 = searchCondition.出库业务日期[0]+','+searchCondition.出库业务日期[1]
    tmp.最后出库时间 = searchCondition.最后出库时间[0]+','+searchCondition.最后出库时间[1]
    const ret = await exportSellOutListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}

</script>

<template>
    <!-- 销售出库单列表 -->
    <div ref="rootRef">
      <div class="absolute top-2 left-10">
        <ElButton type="success" @click="onAddSaleOut">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
        <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
            <Icon icon="carbon:export" />
            <div class="pl-2">{{ t('button.export') }}</div>
        </ElButton>
      </div>
      <div class="p-2">
        <div class="text-center mb-5 font-bold">{{ t('sale.out_list') }}</div>
        <div  class="p-5 bg-white">
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1" >
            <div class="searchTitle">订单状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择"  >
              <el-option v-for="item in ['等待提交', '等待审核', '等待修改', '等待确认', '已出库', '已拒绝', '已删除']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('sale.name') }}</div>
            <el-input size="small"   v-model="searchCondition.销售单号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">出库单号</div>
            <el-input size="small"   v-model="searchCondition.出库单号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品名称</div>
            <el-input size="small"   v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品编码</div>
            <el-input size="small"   v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品品牌</div>
            <el-input size="small"   v-model="searchCondition.产品品牌" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品规格</div>
            <el-input size="small"   v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">客户名称</div>
            <el-input size="small"   v-model="searchCondition.客户名称" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">出库人员</div>
            <el-input size="small"   v-model="searchCondition.出库人员" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">销售人员</div>
            <el-input size="small"   v-model="searchCondition.销售人员" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">跟单人员</div>
            <el-input size="small"   v-model="searchCondition.跟单人员" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">默认排序</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.默认排序" placeholder="请选择" >
              <el-option v-for="item in ['出库日期正序','出库日期倒序','出库编号正序','出库编号倒序']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">支付方式</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.支付方式" placeholder="" >
              <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">仓库</div>
            <el-input size="small"   v-model="searchCondition.仓库" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">出库备注</div>
            <el-input size="small"   v-model="searchCondition.出库备注" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">快递单号</div>
            <el-input size="small"   v-model="searchCondition.快递单号" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">快递公司</div>
            <el-input size="small"   v-model="searchCondition.快递公司" placeholder="" class="searchItem" />
          </div>
          <!-- <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">收件人</div>
            <el-input size="small"   v-model="searchCondition.收件人" placeholder="" class="searchItem" />
          </div> -->
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">客户订单号</div>
            <el-input size="small"   v-model="searchCondition.客户订单号" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle !w-[100px]">出库业务日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.出库业务日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>
          <!-- <div v-show="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle !w-[100px]">最后出库时间</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.最后出库时间" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div> -->




          <div  class="text-center mt-1 mb-1 flex justify-end items-center">
            <el-checkbox size="small" :label="t('customer.senior')" v-model="senior"/>
            <ElButton class="ml-4" type="primary" @click="onSearch">
              <Icon icon="tabler:search" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 产品列表 -->
        <div class="mt-2">
            <!-- 表头 -->
            <div class="flex header headerBk">
              <div class="w-[60%] flex">
                <div class="w-[50%] ">产品名称</div>
                <div class="w-[10%] ">发货数量</div>
                <div class="w-[20%] ">价格</div>
                <div class="w-[20%] ">利润</div>
              </div>
              <div class="flex flex-grow !p-0">
                <div class="rightcss rightcss_title">销售人员</div>
                <div class="rightcss rightcss_title">收款方式</div>
                <div class="rightcss rightcss_title">订单状态</div>
                <div class="rightcss rightcss_title">备注</div>
                <div class="rightcss_title !min-w-[100px]">操作</div>
              </div>

            </div>
            <!-- <div class="flex">
              <div class="flex text-sm items-center mr-3">
                <div class="title_unreceipt w-[24px] h-[14px]"></div>
                未出库
              </div>
              <div class="flex text-sm items-center mr-3">
                <div class="title_partial w-[24px] h-[14px]"></div>
                部分出库
              </div>
              <div class="flex text-sm items-center mr-3">
                <div class="title_ok w-[24px] h-[14px]"></div>
                全部出库
              </div>
              <div class="flex text-sm items-center">
                <div class="title_over w-[24px] h-[14px]"></div>
                超量出库
              </div>
            </div> -->
            <!-- 表内容 -->
            <div class="mt-4 bg-white" style="box-shadow: 0 1px 8px 1px rgba(165,165,165,0.2)" v-for="item in outData" :key="item.id">
              <!-- 内容头 -->
              <div>
                <div class="p-2 pl-4 pr-4 flex flex-nowrap items-center text-[13px] title_ok font-bold" style="background: #ECF4FF;border: 1px solid #AACAF5;"
                 :class-="{'title_unreceipt': item.发货状态 === '未发货', 'title_partial': item.发货状态 === '部分发货', 'title_ok': item.发货状态 === '全部发货', 'title_over': item.发货状态 === '超量发货'}">
                  <div class="w-[45%]  flex">
                        <div class="mr-4 font-bold">
                            {{ item.create_date.split(' ')[0] }}
                        </div> 
                        <div class="mr-4">发货单号: {{ item.sell_takeout_num }}</div>
                        <div class="cursor-pointer" @click="onNaviSale(item)">销售单号: {{ item.sell_order_num }}</div>
                    </div>
                  <div class="min-w-[200px] flex items-center">
                    <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                    {{ checkPermissionApi('客户名称显示')?item.buyer_nick:'***' }}
                  </div>
                  <div class="flex justify-center items-center min-w-[250px]">
                    <div class="mr-5">销售:{{ item.sell_man_name }}</div>
                    <!-- <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin-right: 5px;" icon="solar:file-bold" />
                    <div>文件(0)</div>
                    <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin:0 5px 0 10px;" icon="material-symbols:contract" />
                    <div>合同(0)</div> -->
                  </div>
                  <!-- 靠右的其他信息 -->
                  <div class="ml-auto flex justify-center items-center min-w-[200px]">
                    <div>{{ item.store_nick }}</div>
                    <Icon style="scale: 1.1; margin:0 5px 0 50px;" icon="ic:baseline-qrcode" />
                    <Icon style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;" icon="material-symbols:print-outline"  @click="toPrintPage(item)"/>
                    <Icon style="scale: 1.1; margin:0 5px 0 10px;" icon="uil:copy" />
                  </div>
                </div>
              </div>
              <!-- 内容体 -->
              <div class="flex">
                <!-- 左边产品列表 -->
                <div class="w-[60%]  table_self">
                  <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                    <div class="w-[50%] flex-grow p-1">
                      <div class="flex justify-start items-center w-[100%] p-2">
                        <el-image v-if="pdt.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="pdt.pics[0].url" />
                        <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                        <div class="inline-block text-left max-w-[100%]">
                          <div style="white-space: normal;">{{ '['+pdt.name+']'+pdt.nick }}</div>
                        </div>
                      </div>
                    </div>                  
                    <div class="w-[10%] p-2 border-left-1 flex items-center justify-center flex-col">
                      {{ pdt.发货数量+' '+pdt.base_unit }}
                      <div class="ex_text">备品:{{ pdt.备品发货数量 }}</div> 
                    </div>
                    <div class="w-[20%] p-2 border-left-1 flex items-center justify-center flex-col">                      
                        {{ getMoneyFlag(item.money_type)+ (checkPermissionApi('销售发货单显示价格')?(pdt.sell_price_aft_tax+'x'+(pdt.发货数量-pdt.备品发货数量)+'='+parseFloat((pdt.sell_price_aft_tax*(pdt.发货数量-pdt.备品发货数量)).toFixed(2))):'*') }}
                        <div class="ex_text">含税{{ pdt.发票税率 }}%</div>                      
                    </div>
                    <div class="w-[20%] p-2 text-red-400 border-left-1 flex items-center justify-center flex-col" style="font-size: smaller;">
                      <div>税前:{{ getMoneyFlag(item.money_type)+ (checkPermissionApi('销售发货单显示价格')?parseFloat((pdt.sell_price_bef_tax*(pdt.发货数量-pdt.备品发货数量)-pdt.avg_price_bef_tax*(pdt.发货数量-pdt.备品发货数量)).toFixed(2)):'*') }}</div>
                      <div>税后:{{ getMoneyFlag(item.money_type)+ (checkPermissionApi('销售发货单显示价格')?parseFloat((pdt.sell_price_aft_tax*(pdt.发货数量-pdt.备品发货数量)-pdt.avg_price_aft_tax*(pdt.发货数量-pdt.备品发货数量)).toFixed(2)):'*') }}</div>
                    </div>
                  </div>
                </div>
                <!-- 右边其他数据 -->
                <div class="flex flex-grow text-center  right">
                  <div class="rightcss" style="text-align: left;">
                    <div>销售:{{ item.sell_man_name }}</div>
                    <div>跟单:{{ item.follow_man_name }}</div>
                  </div>
                  <div class="rightcss">
                    {{ item.pay_type }}
                  </div>
                  <div class="rightcss">
                    <el-tag type="success">{{ item.fsm_cur_state }}</el-tag>
                  </div>
                  <div class="rightcss" style="text-align: left;">{{ item.note }}</div>
                  <div class="!min-w-[100px] flex items-center justify-center">
                    
                    <ElButton v-if='currentRoute.name === "StoneSaleOutCheck"' type="success" size="small" @click="handleOper('check',item)">审核</ElButton>
                    <el-dropdown v-if='currentRoute.name != "StoneSaleOutCheck"' trigger="click" placement="bottom">
                      <span class="el-dropdown-link">
                        <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                      </span>
                      <template #dropdown>
                        <div class="flex flex-wrap w-[140px]">
                          <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.info') }}</el-dropdown-item>
                          <el-dropdown-item v-if='!["已出库"].includes(item.fsm_cur_state)' @click="handleOper('edit', item)">{{ t('userOpt.edit') }}</el-dropdown-item>
                          <el-dropdown-item v-if='!["已出库"].includes(item.fsm_cur_state)' @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                        </div>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>

      <div class="flex mt-5 items-center">
        <div class="ml-auto ">
          <span class="font-bold">税前总金额:</span>
          <span class="mr-6">{{ rootData.added.税前合计 }}</span>
          <span class="font-bold">总金额:</span>
          <span class="mr-6">{{ rootData.added.税后合计 }}</span>
          <span class="font-bold">总数量:</span>
          <span class="mr-6">{{ rootData.added.发货合计 }}</span>
        </div>
        <el-pagination 
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          :page-sizes="[10, 50, 100, 300]"
          :background="true"
          layout="sizes, prev, pager, next"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

    <DialogSaleSel :title="t('sale.list')" v-model:show="showSaleSel" @on-submit="onSaleOutSelCallback" />
    <PrintModal v-model:show="dialogVisible" />
    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #ffe48d !important;
}

:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  color: #333;
  font-size: 15px;
  font-weight: bold;
}
.headerBk{
  background-color: #fff !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    // padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: center; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}


//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt{ //未收货
  background-color: #BFEFFF;
  border: #d9d9d9 1px solid;
}
.title_partial{ //部分收货
  background-color: #FFF8D7;
  border: #d9d9d9 1px solid;
}
.title_ok{ //完全收货
  background-color: #F2F2F2;
  border: #d9d9d9 1px solid;
}
.title_over{//超量收货
  background-color: #FFC0CB;
  border: #d9d9d9 1px solid;
}
</style>


.hzx_fieldset{
    font-size:12px;
    border: 1px solid #e6e6e6;
    padding: 0px 0px 1px 0px;
    margin: 0px 0px 2px 0px;
    text-align: left;
}
.hzx_legend {
    display: block;
    min-width:56px;
    max-width:58px;
    margin-left: 15px;
    margin-bottom: 0px;
    font-size:12px;
    line-height: inherit;
    color: #333333;
    border: 0px solid #e6e6e6;
    padding:2px 4px 0px 4px;
}

.title{font-size:22px;color:#333333;padding:0px;margin:4px 5px 1px 5px;font-family:微软雅黑;}

.mborder {border-width: 1px;border-color:#e6e6e6;border-style:solid;border-collapse:collapse;}

.mtable {border-width:1px;border-color:#6699cc;border-style:solid;border-collapse:collapse;word-break: break-all;word-wrap: break-word;margin-left:auto;margin-right:auto;}
.mtable th{background-color: #f2f2f2;height:25px;color: #000000;font-size: 12px;border:1px solid #e6e6e6;}
.mtable td{font-size: 12px;border:1px solid #e6e6e6;height:25px;}

.mtable tr:hover {background-color: #f2f2f2;}
.mtable tr .group{background-color: #8DD9F9;text-align:left;font-weight:bold;font-size:13px;}

.total td{background-color: #8DD9F9;}




.htable {border-width: 1px solid #b7c0c9;border-collapse:collapse;word-break: break-all;word-wrap: break-word;margin:3px auto 3px auto;width:99%;margin-left:auto;margin-right:auto;}
.htable th{background-color: #f8f8f8;color: #000000;font-size: 14px;border:1px solid #b7c0c9;}
.htable td{font-size: 12px;border:1px solid #b7c0c9;vertical-align:middle;}
.htable tr {height:25px;}
.htable tr:hover {background-color: #f1f1f1;}




.inputhidden {text-align:center;border:1px solid #ffffff;width:90%;height:30px;}
.inputhidden:hover{border:1px solid #000000;}


.bill{border: 1px solid #b7c0c9;border-collapse:collapse;width:98%;margin-left:auto;margin-right:auto;}
.bill th{background-color: #f8f8f8;color: #000000;font-size: 14px;border:1px solid #b7c0c9;}
.bill td{font-size: 12px;border:1px solid #b7c0c9;vertical-align:middle;}
.bill tr {height:35px;}
.bill tr:hover {background-color: #f1f1f1;;}
.bill input[type="text"]{text-align:center;border:1px solid #ffffff;width:90%;height:30px;}
.bill input[type="text"]:hover {border:1px solid #000000;}
.bill textarea{width:90%;height:90%;}


.bill tr:hover .delLite{ background:url(../images/icon2.png) no-repeat -20px -56px; width:13px; height:13px; display:inline-block; margin:3px 5px 0 5px;}
.bill tr:hover .delLite:hover{ background:url(../images/icon2.png) no-repeat -20px -73px;}
.bill tr:hover .num{ display:none;}
.bill tr:hover .addLite{ background:url(../images/icon2.png) no-repeat 0px -56px; width:13px; height:13px; display:inline-block; margin:3px 5px 0 5px;}
.bill tr:hover .addLite:hover{ background:url(../images/icon2.png) no-repeat 0px -73px;}



.billhead {width:98%;font-szie:14px;margin:0px auto 2px auto;background: url("../images/line.png") repeat-x scroll center bottom rgba(0, 0, 0, 0);}
.billhead td{vertical-align:bottom;padding-bottom:6px;}


.inputMore{ background-image:url(../images/package.png); background-position:-455px -377px; float:left; width:28px; height:28px;  cursor:pointer;  position:absolute; margin-left:-28px; top:0px; right:0px;}
.inputMore:hover{ background-position:-490px -377px;}

.billhead2 {width:98%;margin:0px auto 2px auto;}

.hzx_title{letter-spacing: 20px;font-size: 28px;margin:0 auto;text-align:center;}
.hzx_hr1{width:200px;border: 0.2px solid #999;background-color:#999;height:0.5px;margin:2px auto;}
.hzx_hr2{width:200px;border: 0.2px solid #999;background-color:#999;height:0.5px;margin:2px auto;}
.hzx_table{border: 2px solid #000000;border-collapse:collapse;width:98%;margin-left:auto;margin-right:auto;}
.hzx_table th{font-size: 12px;font-weight:normal;vertical-align:middle;border:1px solid #b7c0c9;color: #000000;text-align:center;}
.hzx_table td{font-size: 12px;border:1px solid #b7c0c9;vertical-align:middle;color: #000000;}
.hzx_table tr {height:30px;}
.hzx_table input[type="text"]{text-align:center;border:1px solid #ffffff;width:98%;height:30px;}
.hzx_table input[type="text"]:hover {border:1px solid #b7c0c9;}
.hzx_table textarea{width:90%;height:90%;border:0px solid #b7c0c9;}


.button {font-size:12px;padding: 0px 8px 0px 8px;margin:2px 2px 2px 2px;color:#000000;text-decoration:none;border-radius:4px;border: 1px solid #999999;background: -ms-linear-gradient(top, #fff,  #dbdade);background:-moz-linear-gradient(top,#fff,#f1f1f1);
display:inline-block;}
.button:hover {
	background: -ms-linear-gradient(top, #fff,  #a19fa5);
	background:-moz-linear-gradient(top,#fff,#a19fa5);}
	
.button1 {font-size:12px;padding: 0px 1px 0px 1px;margin:0px 2px 0px 2px;color:#000000;text-decoration:none;border-radius:2px;border: 1px solid #999999;background: -ms-linear-gradient(top, #fff,  #dbdade);
	background:-moz-linear-gradient(top,#fff,#f1f1f1);font-size:12px;}
.button1:hover {background: -ms-linear-gradient(top, #fff,  #a19fa5);background:-moz-linear-gradient(top,#fff,#a19fa5);}
.button2 {display:inline-block;margin:1px 3px 1px 3px;padding: 1px 8px 1px 8px;color:#333333;border-radius:10px;border: 1px solid #999999;background: -ms-linear-gradient(top, #fff,  #dbdade);
	background:-moz-linear-gradient(top,#fff,#f1f1f1);font-size:12px;}
.button2:hover {
	background: -ms-linear-gradient(top, #fff,  #a19fa5);	
background:-moz-linear-gradient(top,#fff,#a19fa5);
}

.savebutton{
	border-radius:4px;
	width:120px;
	margin-top: 18px;
	height: 30px;
	color:#49484b;
	background: -ms-linear-gradient(top, #fff,  #dbdade);
	background:-moz-linear-gradient(top,#fff,#dbdade);
	border:1px solid #dbdade;
	font-size: 15px;
	text-align: center;
	box-shadow:1px  1px  0px  #6c6b6d;
	-moz-box-shadow:1px  1px  0px  #6c6b6d;
}
.savebutton:HOVER{
	border-radius:4px;
	width:120px;
	margin-top: 18px;
	height: 30px;
	color:#49484b;
	background: -ms-linear-gradient(top, #fff,  #a19fa5);
	background:-moz-linear-gradient(top,#fff,#a19fa5);
	border:1px solid #dbdade;
	font-size: 15px;
	text-align: center;
	box-shadow:1px  1px  0px  #6c6b6d;
	-moz-box-shadow:1px  1px  0px  #6c6b6d;
	animation:mymove 5s infinite;
	-webkit-animation:mymove 5s infinite;
}
.product_image {
    display: table-cell;
    vertical-align: middle;
    position: relative;
    border: 0px solid #cfd6d9;
    width: 70px;
    height: 70px;
    text-align: center;
}
.product_image img {
    max-width: 60px;
    max-height: 60px;
}
/* 智能提示框样式 20141017 */
.autoCompleteBox {
    border: 1px solid #D7D7D7;
    position: absolute;
    overflow-y: auto;
    overflow-x: hidden;
    display: none;
    z-index: 999999;
    background-color:#ffffff;
}

.autoCompleteBox table {
    background: none repeat scroll 0 0 #FFFFFF;
    cursor: default;
    width: 100%;
    border-collapse: collapse;
}

.autoCompleteBox table th {
    background: #e3f4fe;
    border: 1px solid #d7d7d7;
    height: 23px;
    font-size: 12px;
    padding: 0 4px;
    line-height: 23px;
    color: #9a6a00;
    text-align: left;
}

.autoCompleteBox table tr {
    background: #fcf3f3;
    height: 23px;
}

.autoCompleteBox table td {
    border: 1px solid #eee;
    height: 23px;
    line-height: 23px;
    color: #4c4c4b;
    padding: 0 4px;
    font-size: 12px;
    background-color: #FFFFFF;
}

.autoCompleteBox table td:hover {
    background: #f7fff3;
}

.autoCompleteBox .nowRow {
    background: #fbdab0;
    border-bottom: 1px solid #e2d2ac;
    height: 23px;
    line-height: 23px;
    color: #4c4c4b;
    cursor: pointer;
}

.autoCompleteBox .key {
    color: #F00;
    font-weight: bold;
}
.imageicon{
	height:11px;
	width:11px;
	background-color: #7ca0d3;
	display: inline-block;
	text-align: center;
	line-height: 11px;
	color:#ffffff;
	border-radius:5px;
	position: relative;
	left:21px;
	top:-5px;
}
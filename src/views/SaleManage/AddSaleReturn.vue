<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElPopconfirm,ElTable,ElTag,ElDatePicker,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,nextTick } from 'vue'
import { addSaleReturnApi,updateSaleReturnApi,getSaleInfoApi,getSaleReturnInfoApi,getSaleReturnNewnumApi,getInventoryDetailListApi,getStoreListApi,getReturnNewnumApi,getPurchaseInfoApi,delReturnApi,updateReturnApi,getReturnInfoApi,getReturnListApi,addReturn<PERSON><PERSON> } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount } from 'vue'
import {checkFormRule, checkPermissionApi} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'



const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})

//销售单数据
const saleData = reactive({})

//退货单数据
const returnData = reactive(
    {
    "buyer_id": "",
    "sell_order_num": "",
    "sell_cancel_num": "",
    "cancel_date": "", //入库日期
    "cancel_man_id": "", //入库人员
    "store_id": "",
    "pdt_list": [],
    "note": "",
    "express_fee": 0.00,
    "other_fee": 0.00,
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:[]
    }, //审核决策
    fsm_cur_state:'',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)

//产品仓库分布数据
const detailData  = reactive([])

//获取最新ID
const onChangeID = async()=>{
    const ret = await getSaleReturnNewnumApi()
    if(ret)
    {
        console.log(ret)
        returnData.sell_cancel_num = ret.data.new_id
    }
}


//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)

    returnData.store_id = storeData[0].id

  }

}


//计算输入
const recomputeNum = (row)=>{
    if(row.退货数量<0)
        row.退货数量 = 0
    else if(row.退货数量>row.已发货-row.已退货)
    {
        row.退货数量 = row.已发货-row.已退货
        //提示退货数量不能超过已发货数量减去已退货数量
        ElMessage.warning(t('msg.returnNumTooBig'))
    }
}


onMounted(async() => {    
    //查询获取关联采购单信息
    const ret = await getSaleInfoApi({
        sell_order_num:currentRoute.value.query.sell_order_num as string
    })
    if(!ret)
    {
        return
    }
    console.log(ret)    
    Object.assign(saleData,ret.data)



    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = '新增销售退货出库'
        onChangeID()
        returnData.sell_order_num  = currentRoute.value.query.sell_order_num as string
        returnData.cancel_date = getTodayDate()
        //所有产品到列表
        returnData.pdt_list = saleData.pdt_list

        //设置pdt默认属性
        for(let pdt of returnData.pdt_list)
        {
            pdt.退货数量 = 0
            pdt.退货类型 = '正品'
        }

        //设置出库人员
        const info = wsCache.get(appStore.getUserInfo)
        returnData.cancel_man_id = info.id
        returnData.cancel_man_name = info.resident_name
    }
    else
    {
        if(currentRoute.value.query.type == 'info')
        {
            title.value = '查询销售退货出库'
        }
        else
        {
            title.value = '修改销售退货出库'
        }

        console.log('111111')
        //查询产品信息 
        const ret = await getSaleReturnInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(returnData, ret.data)
            returnData.pdt_list = ret.data.pdt_list;
            for(let pdt of returnData.pdt_list)
            {
                if (pdt.退货类型 == undefined || pdt.退货类型 == '') {
                    pdt.退货类型 = '正品'
                }
                
            }
        }

        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }
    await getStoreList()
 
})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})



//显示隐藏选择出库员窗口变量
const showSelReturnUserDlg = ref(false)
//显示选择出库员弹窗
const onSelReturnUser = ()=>{
    showSelReturnUserDlg.value = true
}
//选择出库员回调
const onSelReturnCallback = (id,name)=>{
    console.log(id,name)
    returnData.cancel_man_id = id
    returnData.cancel_man_name = name
}



//保存
const onSave = async()=>{
    console.log(returnData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //删除returnData.pdt_list中入库数量为0的行
    // returnData.pdt_list = returnData.pdt_list.filter(pdt=>pdt.退货数量!=0)

    const tmp = cloneDeep(returnData)
    tmp.pdt_list = tmp.pdt_list.filter(pdt=>pdt.退货数量!=0)
    
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }

    //增加审核流程参数
    const info = wsCache.get(appStore.getUserInfo)
    tmp.fsm_exe_man_name = info.resident_name
    tmp.fsm_exe_trig =  currentRoute.value.query.type == 'edit'?'保存':'直接入库'

    
    if(tmp.id == undefined)
    {
        const ret = await addSaleReturnApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newReturnSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret =await updateSaleReturnApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateReturnSuccess'))
            back()
        }
    }

}


</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <el-popconfirm  title="是否确认发起退货?操作将影响库存！" @confirm="onSave">
                <template #reference>
                    <ElButton v-if="returnData.id==undefined" type="primary">
                        提交
                    </ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm  title="是否确认修改订单?" @confirm="onSave">
                <template #reference>
                    <ElButton v-if="currentRoute.query.type == 'edit'" type="primary">
                        保存
                    </ElButton>
                </template>
            </el-popconfirm>
        </template>
        <el-form :rules="rules" :model="returnData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('salereturn.num')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  v-model="returnData.sell_cancel_num" :disabled="returnData.id!=undefined" />
                            <ElButton v-if="returnData.id==undefined" type="warning"  @click="onChangeID">{{ t('button.update') }}</ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('sale.name')"
                    class="flex">
                    <div>{{ saleData.sell_order_num }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('store.store')"
                    class="flex">
                    <el-select v-model="returnData.store_id" placeholder="选择仓库">
                        <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.name')"
                    class="flex">
                    <div>{{ checkPermissionApi('客户名称显示')?saleData.buyer_nick:'***' }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('sale.saler')"
                    class="flex">
                    <div>{{ saleData.sell_man_name }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('sale.date')"
                    class="flex">
                    <div>{{ saleData.sell_date }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('return.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ returnData.cancel_man_name }}</div> 
                        <ElButton @click="onSelReturnUser">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('inventory.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="returnData.cancel_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>

            </el-descriptions>
        
        </el-form>

        <el-table header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="returnData.pdt_list" style="width: 100%" border stripe>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image class="xiangpian" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="120" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130" />
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.id != undefined && scope.row.specs != ''"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name=='自定义规格'?scope.row.specs_text:scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="类型" :label="t('sale.type')"/>
            <el-table-column  show-overflow-tooltip  :label="t('sale.count')" >
                <template #default="scope">
                    <div>{{ scope.row.销售数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('salereturn.can_count')" >
                <template #default="scope">
                    <div>{{ scope.row.已发货-scope.row.已退货 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('return.count')" >
                <template #default="scope">
                    <div>{{ scope.row.已退货 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('return.count2')" >
                <template #default="scope">
                    <el-input :disabled="currentRoute.query.type == 'edit'" v-model="scope.row.退货数量" @input="recomputeNum(scope.row)" type="number"/>
                </template>
            </el-table-column>      
            <el-table-column show-overflow-tooltip   label="类型" >
                <template #default="scope">
                    <el-select v-model="scope.row.退货类型" placeholder="退货类型">
                        <el-option v-for="item in ['正品','备品']" :key="item" :label="item" :value="item" />
                    </el-select>
                </template>
            </el-table-column>       

            <el-table-column show-overflow-tooltip  prop="销售备注" :label="t('sale.remark')" />
            
            <el-table-column show-overflow-tooltip  :label="t('return.remark')" >
                <template #default="scope">
                    <el-input v-model="scope.row.退货备注" />
                </template>
            </el-table-column>
        </el-table>
    <!-- 显示合计 -->
    <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[60%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">销售单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ saleData.note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">退货单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="returnData.note" clearable :autosize="{ minRows: 2, maxRows: 4 }"     type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="flex-grow text-center">
            <div class="flex">
                <div class="w-[100%]">
                    <div>
                        <td class="table_self_title min-w-[100px] p-2">运费:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="returnData.express_fee" type="number"/>
                        </td>
                    </div>
                    <div>
                        <td class="table_self_title min-w-[100px] p-2">其他费用:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="returnData.other_fee" type="number"/>
                        </td>
                    </div>
                </div>
            </div>
        </div>
    </div>

        
        




        <div class="mb-60"></div>

        <!-- 选择退货员 -->
        <DialogUser :param="''" v-model:show="showSelReturnUserDlg" :title="t('msg.selectUser')" @on-submit="onSelReturnCallback"/>

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}
//扩展文字
.ex_text{
  font-size: 11px;
  color: #646464;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
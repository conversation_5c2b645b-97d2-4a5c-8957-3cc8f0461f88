<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { onBeforeMount } from 'vue'
import { RightMenu } from '@/components/RightMenu'
import { Dialog } from '@/components/Dialog'
import { decodeString } from '@/api/tool'
import { computed,nextTick } from 'vue';
import { useCache } from '@/hooks/web/useCache'
import { delPreSaleApi,getPreSaleListApi } from '@/api/product'

const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

//售前报价单数据源
const presaleData = reactive([])

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
const defaultCondition = {
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getPreSaleList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getPreSaleList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getPreSaleList(val)
}


//处理表格对象操作
const handleOper = (type, item) => {
  //编辑产品
  if(type === 'edit')
  {
    push({
      path: '/presalemanage/addpresale',
      query:{
          id:item.id
      }
    })
  }
  else if(type === 'del') //删除
  {
    ElMessageBox.confirm(t('msg.confirm_del_presale')+'--> '+item.nick, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const ret = await delPreSaleApi({
        ids: [item.id],
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getPreSaleList()
      }
    }
    ).catch(() => {})
  }
}

//进入新增界面
const onAddPreSale = ()=>{
  push({
    path: '/presalemanage/addpresale'    
  })
}

//进入设置出厂报价页面
const onSetBasePrice = (item)=>{

}

//查询列表
const getPreSaleList = async (page = 1) => {
  searchCondition.page = page
  const ret = await getPreSaleListApi(searchCondition)
  if(ret)
  {
    presaleData.splice(0,presaleData.length, ...ret.data)
    console.log(presaleData)
    totleCount.value = parseInt(ret.count)
  }
}

onMounted(() => {
    updateTableHeight(); // 首次设置表格高度
    window.addEventListener('resize', handleWindowResize);

    //更新产品列表
    getPreSaleList()  
      //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('keyup', event => {
        if (event.key === 'Enter') {
          onSearch();
        }
      });
    });
})

</script>

<template>
  <div class="flex">
    <!-- 右侧产品列表 -->
    <div ref="rootRef" class="relative w-[100%] !bg-white flex-grow overflow-hidden">
      <div class="absolute top-3 left-10">
        <ElButton type="success" @click="onAddPreSale">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>

      </div>
      <div class="h-[100%] bg-white p-7">
        <div class="text-center mb-5 font-bold">{{ t('presale.list') }}</div>
        <!-- 检索条件 -->
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
          <!-- 检索条件 -->
          <div class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input v-model="searchCondition.nick" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-5">
            <div class="searchTitle">{{ t('presale.name') }}</div>
            <el-input v-model="searchCondition.name" placeholder="" size="small" class="searchItem" />
          </div>
          <div  class="text-center mt-5 mb-2">
            <ElButton type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
            <el-checkbox class="ml-5" :label="t('customer.senior')" v-model="senior"/>
          </div>
        </div>
        <!-- 列表 -->
        <el-table ref="tableRef" :data="presaleData" style="width: 100%; margin-bottom: 20px" row-key="id" border stripe
          highlight-current-row header-cell-class-name="tableHeader">
          <el-table-column show-overflow-tooltip align="center"  prop="sell_offer_num" :label="t('product_manage.id')" />
          <el-table-column show-overflow-tooltip align="center"  prop="pdt_nick" :label="t('product_manage.name')" />
          <el-table-column show-overflow-tooltip align="center"  prop="buy_man_name" :label="t('customer.name')" />
          <el-table-column show-overflow-tooltip align="center"  prop="sell_man_note" :label="t('presale.hurry')" />
          <el-table-column show-overflow-tooltip align="center"  prop="status" :label="t('presale.status')" />
          <el-table-column show-overflow-tooltip align="center"  prop="modify_date" :label="t('inventory.last_date')" min-width="100"/>
          <el-table-column show-overflow-tooltip align="center"  prop="modify_date" :label="t('presale.create_date')" min-width="100"/>
          
    
          <el-table-column show-overflow-tooltip fixed="right" align="center" width="80px" prop="name" :label="t('roleTable.opt')" >
            <template #default="scope">
                <el-dropdown trigger="click" placement="bottom">
                    <span class="el-dropdown-link">
                    <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                    </span>
                    <template #dropdown>
                    <div class="flex flex-wrap">
                        <el-dropdown-item @click="handleOper('info',scope.row)">{{ t('userOpt.info') }}</el-dropdown-item>
                        <el-dropdown-item @click="handleOper('edit', scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                        <el-dropdown-item @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                    </div>
                    </template>
                </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
      </div>




    </div>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #ffe48d !important;
}

:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
}
.headerBk{
  background-color: #6d92b4 !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

</style>

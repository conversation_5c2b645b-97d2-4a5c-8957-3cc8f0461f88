<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElProgress,ElUpload, ElButton, ElMessage, ElMessageBox,ElImage, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElPagination } from 'element-plus';
import { reactive, ref, onMounted, onActivated,onBeforeUnmount,watch } from 'vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { getSaleListApi,delSaleApi,updateSaleApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { DialogProduct } from '@/components/DialogProduct';
import { useAppStore } from '@/store/modules/app'
import { DialogSellBakInfo } from '@/components/DialogSellBakInfo';
import { DialogFileList } from "@/components/DialogFileList";
import { checkPermissionApi, checkRouterPermission<PERSON><PERSON>, closeOneTagByPath, downloadFile, getDateArea, getMoneyFlag } from '@/api/tool';
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { DialogUser } from '@/components/DialogUser'
import { getOssSignApi, ossUpload } from '@/api/oss';
import { DialogImportTask } from '@/components/DialogImportTask'
import { importSellOrderApi } from  '@/api/task'
import { cloneDeep } from 'lodash-es';
import { exportSellListApi } from '@/api/extra';
import { clearDefer, useDefer } from '@/hooks/web/useDefer';

import { DynamicScroller,DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { DialogProductLite } from '@/components/DialogProductLite';
const defer = useDefer()

const { currentRoute,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//销售数据源
const saleData = reactive([])
//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])
//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])
const rootData = {
  added: {
    
  }
}

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state:'',
  sell_order_num:'',
  sell_offer_num:'',
  pdt_name:'',
  pdt_nick:'',
  sell_man_name:'',
  follow_man_name:'',
  buyer_nick:'',
  status:'',
  产品编码:'',
  产品名称:'',
  客户名称:'',
  出库状态:'',
  销售人员:'',
  跟单人员:'',
  产品品牌:'',
  产品规格:'',
  是否收款:'',
  客户订单号:'',
  销售价格0:'',
  销售价格1:'',
  是否含税:'',
  支付方式:'',
  开票标识:'',
  结算币种:'',
  产品数量0:'',
  产品数量1:'',
  产品单价0:'',
  产品单价1:'',
  订单金额0:'',
  订单金额1:'',
  发票金额0:'',
  发票金额1:'',
  销售备注:'',
  收货人员:'',
  收货号码:'',
  备货状态:'',
  下单日期:['',''],
  交货日期:['',''],
  产品分类:'',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getSaleList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getSaleList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getSaleList(val)
}

//处理表格对象操作
const handleOper = (type, item,param = null) => {
  
  //编辑产品
  if(type === 'edit' || type === 'info' || type === 'check')
  {
    if(type === 'info' && !checkPermissionApi('销售订单明细查看'))
    {
      ElMessage.error('无权限1')
      return
    }
    if(type === 'edit' && !checkPermissionApi('销售订单修改'))
    {
      // console.log('111',type,)
      ElMessage.error('无权限2')
      return
    }

    if(type == 'check')
    {
      type = 'info'
    }
    const retPath = (currentRoute.value.name?.indexOf("Stone")>=0)?'/inventorymanage/stoneaddsale':'/salemanage/addsale'
    

    push({
      path: retPath,
      query:{
          id:item.id,
          type:type,
          cmd:bCheckMode.value?'审核':''
      }
    })
  }
  else if(type === 'del') 
  {
    if(!checkPermissionApi('销售订单删除'))
    {
      ElMessage.error('无权限')
      return
    }
    ElMessageBox.confirm(t('msg.confirm_del_sale')+'--> '+item.sell_order_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)

      const ret = await delSaleApi({
        ids: [item.id],
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getSaleList()
      }
    }
    ).catch((err) => {
      console.log(err)
    })
  }
  else if(type === 'out'){
    push({
      path: '/salemanage/addsaleout',
      query:{
          id:'',
          sell_order_num:item.sell_order_num
      },    
    })
  }
  else if(type === 'out_list')
  {
    push({
      path: '/salemanage/saleoutlist',
      query:{
          id:'',
          sell_order_num:item.sell_order_num
      }
    })
  }
  else if(type === 'return')
  {
    push({
      path: '/salemanage/addsalereturn',
      query:{
          id:'',
          sell_order_num:item.sell_order_num
      }
    })
  }
  else if(type === 'return_list') //退货单列表
  {
    push({
      path: '/salemanage/salereturnlist',
      query:{
        sell_order_num:item.sell_order_num
      }
    })
  }
  else if(type === 'demand' || type === 'demand2') //销售需求
  {
    closeOneTagByPath('/salemanage/saledemand')
    push({
      path: '/salemanage/saledemand',
      query:{
        sell_order_num:item.sell_order_num,
        //mode:item.fsm_cur_state == '已审核'?'edit':'info'
        ignored:type === 'demand2'?'1':'0'   //是否忽略库存
      }
    })
  }
  else if(type === 'demand_pdt') //销售需求-单一产品
  {
    closeOneTagByPath('/salemanage/saledemand')
    push({
      path: '/salemanage/saledemand',
      query:{
        sell_order_num:param?.sell_order_num,
        // pdt_biaoshi:item.标识,
        mode:param?.fsm_cur_state == '已审核'?'edit':'info'
      }
    })
  }
  else if(type === 'prepare' || type === 'prepare2') //销售需求
  {
    closeOneTagByPath('/salemanage/sellwlprepare')
    push({
      path: '/salemanage/sellwlprepare',
      query:{
        sell_order_num: item.sell_order_num,
        ignored:type === 'prepare2'?'1':'0'   //是否忽略库存
      }
    })
  }
}

//进入新增界面
const onAddPurchase = ()=>{
  push({
    path: '/salemanage/addsale',
    query:{
        id:''
    },    
  })
}

const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: '销售订单' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}

const 总销售数量 = ref(0)

//查询销售单列表
const getSaleList = async (page = 1) => {
  clearDefer()
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  delete tmp.销售价格0
  delete tmp.销售价格1
  delete tmp.产品数量0
  delete tmp.产品数量1
  delete tmp.产品单价0
  delete tmp.产品单价1
  delete tmp.订单金额0
  delete tmp.订单金额1
  delete tmp.发票金额0
  delete tmp.发票金额1
  tmp.销售价格 = searchCondition.销售价格0+','+searchCondition.销售价格1
  tmp.产品数量 = searchCondition.产品数量0+','+searchCondition.产品数量1
  tmp.产品单价 = searchCondition.产品单价0+','+searchCondition.产品单价1
  tmp.订单金额 = searchCondition.订单金额0+','+searchCondition.订单金额1
  tmp.发票金额 = searchCondition.发票金额1+','+searchCondition.发票金额1
  tmp.下单日期 = searchCondition.下单日期[0]+','+searchCondition.下单日期[1]
  tmp.交货日期 = searchCondition.交货日期[0]+','+searchCondition.交货日期[1]
  isLoading.value = true
  saleData.splice(0,saleData.length)
  const ret = await getSaleListApi(tmp)
  if(ret)
  {
    saleData.splice(0,saleData.length, ...ret.data)
    console.log(saleData)
    totleCount.value = parseInt(ret.count)
    总销售数量.value = 0
    saleData.forEach(item=>{
      总销售数量.value += (parseFloat(item.销售数量)+parseFloat(item.销售备品数量))
    })
  
  }
  isLoading.value = false
  Object.assign(rootData, ret)
}

//进入锁定产品界面
const onClickLock = (item,pdt)=>{
  push({
    path: '/salemanage/lockstone',
    query:{
        sell_order_num:item.sell_order_num,
        pdt_id:pdt.id,
        pdt_name:pdt.name,
        pdt_nick:pdt.nick,
        bs:pdt.标识
    }})
}

//审核模式
const bCheckMode = ref(false)
onMounted(() => {
  //初始化默认时间
  let days = getDateArea(180)
  defaultCondition.下单日期 = [days[0], days[1]]
  searchCondition.reset()
  

  if(currentRoute.value.name === "PMCSaleCheck")
  {
    bCheckMode.value = true
    searchCondition.fsm_cur_state = '等待PMC交期审核'
  }
  if(currentRoute.value.name === "ProductMgrSaleCheck")
  {
    bCheckMode.value = true
    searchCondition.fsm_cur_state = '等待生产经理交期审核'
  }
  if(currentRoute.value.name === "ProjectMgrSaleCheck")
  {
    bCheckMode.value = true
    searchCondition.fsm_cur_state = '等待业务部门经理审核'
  }

  if(currentRoute.value.query.sell_order_num != undefined)
    searchCondition.sell_order_num = currentRoute.value.query.sell_order_num
    getSaleList()  

      //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  adjustScrollerHeight()
  window.addEventListener('resize', adjustScrollerHeight)
})

onBeforeMount(() => {
  
});

//显示产品信息
const curSelItem = ref({id:'0',file_list:[],hetong_list:[]})
const showPdtInfo = ref(false)
const onShowPdtInfo = (item)=>{
  showPdtInfo.value = true
  curSelItem.value = item
}

//打开备数据
const curBakOrderNum = ref('')
const curBakPdtBiaoshi = ref('')
const showBakData = ref(false)
const onClickBak = (pdt,item)=>{
  curBakOrderNum.value = item.sell_order_num
  curBakPdtBiaoshi.value = pdt.标识
  showBakData.value = true
}


//显示文件列表
const curSelFileType = ref('图片')
const curTitle = ref('')
const showFileList = ref(false)
const onShowFileList = (item,type)=>{
  showFileList.value = true
  console.log(item)
  curSelItem.value = item
  curSelFileType.value = type 

  if(curSelFileType.value === '图片')
  {
    curTitle.value = '销售图片查看'
  }
  else
  {
    curTitle.value = '销售文件查看'
  }
}
const onUpdateFileList = async(list)=>{
  if(curSelFileType.value === '图片')
  {
    curSelItem.value.file_list = [...list]
  }
  else
  {
    curSelItem.value.hetong_list = [...list]
  }
  // console.log('11111',curSelItem)
  curSelItem.value.fsm_exe_trig= '保存'
  //更新文件列表
  const ret =await updateSaleApi(curSelItem.value)
  if(ret)
  {
      ElMessage.success('更新文件成功！')
      getSaleList()
  }
}

//显示隐藏选择销售员窗口变量
const showSelSaleUserDlg = ref(false)
//显示选择销售员弹窗
const onSelSale = ()=>{
    showSelSaleUserDlg.value = true
}
//选择销售员回调
const onSelSaleCallback = (id,name)=>{
    console.log(id,name)
    searchCondition.sell_man_id = id
    searchCondition.sell_man_name = name
}

//显示隐藏选择跟单员窗口变量
const showSelFollowerUserDlg = ref(false)
//显示选择跟单员弹窗
const onSelFollower = ()=>{
    showSelFollowerUserDlg.value = true
}
//选择跟单员回调
const onSelFollowerCallback = (id,name)=>{
    console.log(id,name)
    searchCondition.follow_man_id = id
    searchCondition.follow_man_name = name
}


//正在上传
const bShowTaskDlg = ref(false)
const onShowTaskDlg = async()=>{
  bShowTaskDlg.value = true
}
const loading = ref(false)
const uploadImg = async(file) => {
  const info = wsCache.get(appStore.getUserInfo)
  console.log(info)

    let path = 'import/'+info.username+'/sell/'
    
    const ret =await getOssSignApi({upload_dir:path})
    if(ret)
    {    
        loading.value = true
        const end = await ossUpload(ret.data.token,file.file,path,(pro)=>{
            console.log('pppp',pro)
            
        })  
        loading.value = false   
        // fileData.push({
        //     name:file.file.name,
        //     url:end.url
        // })
        //上传完成调用导入
        const imp = await importSellOrderApi({
          url:end.url
        })
        if(imp)
        {
            ElMessage.success("上传文件成功，等待系统导入！")
            console.log('上传完成',end.url)
            onShowTaskDlg()
        }
        else
        {
          ElMessage.error("创建导入任务失败，请重试！")
        }

    }
}
const downImportTmp = ()=>{
  downloadFile('/批量下单模版.xlsx','批量导入销售订单模版.xlsx')
}


//页面切换后恢复滚动位置
let scrollY = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scrollY.value = document.getElementById('mainscroll')?.scrollTop
  console.log('离开了',scrollY)
  next()
})
onActivated(()=>{
  document.getElementById('mainscroll')?.scrollTo(0,scrollY.value)
})


const isLoading = ref(false)


const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    delete tmp.销售价格0
    delete tmp.销售价格1
    delete tmp.产品数量0
    delete tmp.产品数量1
    delete tmp.产品单价0
    delete tmp.产品单价1
    delete tmp.订单金额0
    delete tmp.订单金额1
    delete tmp.发票金额0
    delete tmp.发票金额1
    tmp.销售价格 = searchCondition.销售价格0+','+searchCondition.销售价格1
    tmp.产品数量 = searchCondition.产品数量0+','+searchCondition.产品数量1
    tmp.产品单价 = searchCondition.产品单价0+','+searchCondition.产品单价1
    tmp.订单金额 = searchCondition.订单金额0+','+searchCondition.订单金额1
    tmp.发票金额 = searchCondition.发票金额1+','+searchCondition.发票金额1
    tmp.下单日期 = searchCondition.下单日期[0]+','+searchCondition.下单日期[1]
    tmp.交货日期 = searchCondition.交货日期[0]+','+searchCondition.交货日期[1]
    const ret = await exportSellListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}

//跳转到售前
const onShowPreSell = (pdt)=>{
  push({
    path:'/presalemanage/presalemanage',
    query:{
      sell_offer_num:pdt.sell_offer_num
    }
  })
}



// 调整 DynamicScroller 高度
const adjustScrollerHeight = () => {
  const height = document.getElementById('mainscroll')?.clientHeight
  const scroller = document.getElementById('dynamicScroller');
  if (scroller) {
    scroller.style.height = `${height}px`;
  }
}
onBeforeUnmount(() => {
  window.removeEventListener('resize', adjustScrollerHeight)
})

watch(saleData, () => {
  adjustScrollerHeight()
})

</script>

<template>
    <!-- 销售单列表 -->
    <div  ref="rootRef"  class11="absolute top-[0px] right-[0px] left-[0px] bottom-[0px] overflow-auto">
      <div class11="p-7 pt-0 relative " style="background-color: var(--app-content-bg-color);">
        
        <div class="absolute top-8 left-15 flex w-[90%]" v-if="currentRoute.name.indexOf('Stone')<0">
          <ElButton class="mr-2" v-if="checkPermissionApi('销售订单新增')" type="success" @click="onAddPurchase">
            <Icon icon="carbon:document-add" />
            <div class="pl-1">{{ t('button.add') }}</div>
          </ElButton>
          <el-upload
              class='mr-3'
              :http-request="(file) => uploadImg(file)"
              :auto-upload="true"
              :show-file-list = 'false'
              >
            <template #trigger>
              <el-button color="#409EFF" plain :loading="loading" :disabled="loading">
                <Icon icon="clarity:import-line" />
                {{ loading?'上传中..':'导入' }} 
              </el-button>
            </template>
          </el-upload>
          <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
              <Icon icon="carbon:export" />
              <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton>
          <div class="flex flex-1 justify-end"> <!-- 使用 flex-1 类确保占据剩余空间 -->
            <ElButton  @click="downImportTmp" color="#409EFF" plain>
              <Icon icon="carbon:document-add" />
              <div class="pl-1">导入模版下载</div>
            </ElButton>
          </div>
        </div>

        <div  style1="border: 1px solid rgb(143, 143, 143);" class="pt-3 pb-2 mb-1 bg-white">
          <div class="text-center mb-5 font-bold">{{ t('sale.list') }}</div>
          <!-- 检索条件 -->
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1" >
            <div class="searchTitle">订单状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择"  >
              <el-option v-for="item in ['等待提交', '等待PMC交期审核', '等待生产经理交期审核', '等待业务部门经理审核', '等待修改', '已审核', '已拒绝', '已关闭', '已删除']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('sale.name') }}</div>
            <el-input size="small"   v-model="searchCondition.sell_order_num" placeholder=""  class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">售前单号</div>
            <el-input size="small"   v-model="searchCondition.sell_offer_num" placeholder=""  class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input size="small"  v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input size="small"  v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('customer.name') }}</div>
            <el-input size="small"  v-model="searchCondition.客户名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">出库状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.出库状态" placeholder="请选择" >
              <el-option v-for="item in ['未出库','部分出库','完全出库','超量出库','出库且退货']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">销售人员</div>
              <el-input size="small"   v-model="searchCondition.sell_man_name" placeholder=""  class="!w-[100px]" />
              <ElButton size="small" @click="onSelSale" class="w-[50px]">
                  <Icon  icon="iconamoon:search-bold" />
              </ElButton>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">跟单人员</div>
              <el-input size="small"   v-model="searchCondition.follow_man_name" placeholder=""  class="!w-[100px]" />
              <ElButton size="small" @click="onSelFollower" class="w-[50px]">
                  <Icon  icon="iconamoon:search-bold" />
              </ElButton>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品品牌</div>
            <el-input size="small"  v-model="searchCondition.产品品牌" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品规格</div>
            <el-input size="small"  v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">是否收款</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.是否收款" placeholder="" >
              <el-option v-for="item in ['未收款','完全收款','部分收款']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>

          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('sale.cus_sef_id') }}</div>
            <el-input size="small"  v-model="searchCondition.客户订单号" placeholder="" class="searchItem" />
          </div>

          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('sale.price') }}</div>
            <el-input size="small"  v-model="searchCondition.销售价格0" placeholder="" class="!w-[60px]" type="number">
              <!-- <template #append>
                元
              </template> -->
            </el-input>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.销售价格1" placeholder="" class="!w-[60px]" type="number">
              <!-- <template #append>
                元
              </template> -->
            </el-input>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">是否含税</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.是否含税" placeholder="" >
              <el-option v-for="item in ['不含税','含税']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">支付方式</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.支付方式" placeholder="" >
              <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <!-- <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">开票标识</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.开票标识" placeholder="" >
              <el-option v-for="item in ['不开发票','未开发票','部分开票','完全开票']" :key="item" :label="item" :value="item" />
            </el-select>
          </div> -->
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品数量</div>
            <el-input size="small"  v-model="searchCondition.产品数量0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.产品数量1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">结算币种</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.结算币种" placeholder="" >
              <el-option v-for="item in moneyTypeData" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品单价</div>
            <el-input size="small"  v-model="searchCondition.产品单价0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.产品单价1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">订单金额</div>
            <el-input size="small"  v-model="searchCondition.订单金额0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.订单金额1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">发票金额</div>
            <el-input size="small"  v-model="searchCondition.发票金额0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.发票金额1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">销售备注</div>
            <el-input size="small"  v-model="searchCondition.销售备注" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">收货人员</div>
            <el-input size="small"  v-model="searchCondition.收货人员" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">收货号码</div>
            <el-input size="small"  v-model="searchCondition.收货号码" placeholder="" class="searchItem" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">备货状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.备货状态" placeholder="" >
              <el-option v-for="item in ['未备货','备货中(无缺口)','备货中(有缺口)','备货完成(可发货)']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <!-- <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">收货号码</div>
            <el-input size="small"  v-model="searchCondition.收货号码" placeholder="" class="searchItem" />
          </div> -->
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">下单日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.下单日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">交货日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.交货日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品分类</div>
            <el-input size="small"  v-model="searchCondition.产品分类" placeholder="" class="searchItem" />
          </div>

          <div  class="flex justify-end items-center mr-6 mt-2 mb-1">
            <el-checkbox size="small" :label="t('customer.senior')" v-model="senior"/>
            <ElButton type="primary" class="ml-4" @click="onSearch">
              <Icon icon="tabler:search" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" class="ml-4" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 图例 -->
        <div class="flex mb-1">
              <div class="flex text-sm items-center mr-3">
                <div class="title_unreceipt rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                未发货
              </div>
              <div class="flex text-sm items-center mr-3">
                <div class="title_partial rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                部分发货
              </div>
              <div class="flex text-sm items-center mr-3">
                <div class="title_ok rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                完全发货
              </div>
              <div class="flex text-sm items-center">
                <div class="title_over rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                超量发货
              </div>
            </div>
        <!-- 产品列表 -->
        <div v-loading.lock="isLoading">
            <!-- 表头 -->
            <div class="flex header headerBk">
              <div class="w-[60%] flex">
                <div class="w-[50%] flex-grow">产品名称</div>
                <div class="w-[10%] ">数量</div>
                <div class="w-[20%] " v-show="checkPermissionApi('销售订单价格显示')">单价</div>
                <div class="w-[20%] ">交货日期</div>
              </div>
              <div class="w-[40%] flex !p-0">
                <div class="!max-w-[100px] rightcss rightcss_title" v-show="checkPermissionApi('销售订单价格显示')">金额</div>
                <div class="!max-w-[200px] rightcss rightcss_title">出库状态</div>
                <!-- <div class="!max-w-[100px] rightcss rightcss_title">订单状态</div> -->
                <div class="rightcss rightcss_title">备注</div>
                <div class="rightcss_title !min-w-[70px]">操作</div>
              </div>

            </div>
            
            <!-- 表内容 -->
            <DynamicScroller :items="saleData" :min-item-size="82.53" key-field="id" class="scroller" id="dynamicScroller">
              <template #default="{ item, index,active }">
                <DynamicScrollerItem 
                  :item="item" :size-dependencies="[
                      item.pdt_list.length
                    ]" 
                  :data-index="index"
                  :active="active"
                >
                  <div >
              
                  <!-- 内容头 -->
                  <div  class="font-bold  border-b-1px border-gray-300">
                    <div class="p-1 pl-4 pr-4 flex flex-nowrap text-[12px] justify-between" style="background-color: #BFEFFF;">
                      <div class="w-[25%] min-w-[200px] flex items-center">
                        <div class="mr-4  flex items-center">
                          <!-- <div class="mr-3 rounded-[12rem] p-1.5" :class="{'title_unreceipt': item.发货状态 === '未发货', 'title_partial': item.发货状态 === '部分发货', 'title_ok': item.发货状态 === '完全发货', 'title_over': item.发货状态 === '超量发货'}"></div> -->
                          <div class="flex items-center mr-4 w-80px">
                            <div class="mr-3 rounded p-1 pl-2 pr-2" style="color: #fff;"
                            :class="{'title_unreceipt': item.发货状态 === '未发货', 'title_partial': item.发货状态 === '部分发货', 'title_ok': item.发货状态 === '完全发货', 'title_over': item.发货状态 === '超量发货'}">
                              {{ item.发货状态}}
                            </div>
                          </div>
                          <div>{{ item.create_date.split(' ')[0] }}</div>
                        </div>
                        订单号: {{ item.sell_order_num }}
                      </div>
                      <div class="min-w-[200px] flex items-center">
                        <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                        {{ checkPermissionApi('客户名称显示')?item.buyer_nick:'***' }}
                      </div>
                      <div class="flex items-center min-w-[100px]">
                        <div class="mr-5">销售:{{ item.sell_man_name }}</div>
                      </div>
                      <div class="flex justify-end items-center">
                        <Icon v-if="checkPermissionApi('销售图片查看')" style="scale: 1.1;color: rgb(101, 101, 236); margin-right: 5px;" icon="solar:file-bold" />
                        <div v-if="checkPermissionApi('销售图片查看')" class="cursor-pointer" @click="onShowFileList(item,'图片')">图片({{ item.file_list.length }})</div>
                        <Icon v-if="checkPermissionApi('销售文件查看')" style="scale: 1.1;color: rgb(101, 101, 236); margin:0 5px 0 10px;" icon="material-symbols:contract" />
                        <div v-if="checkPermissionApi('销售文件查看')" class="cursor-pointer" @click="onShowFileList(item,'文件')">文件({{ item.hetong_list.length }})</div>
                      </div>
                      <!-- 靠右的其他信息 -->
                      <div class="flex justify-end items-center min-w-[200px]">
                        <!-- <Icon style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);" icon="ic:baseline-qrcode" /> -->
                        <Icon title="打印" style="scale: 1.5; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;" icon="material-symbols:print-outline" @click="toPrintPage(item)"/>
                        <!-- <Icon style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);" icon="uil:copy" /> -->
                      </div>
                    </div>
                  </div>
                  <!-- 内容体 -->
                  <div  class="flex ">
                    <!-- 左边产品列表 -->
                    <div class="w-[60%]  table_self">
                      <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%] border-b-1px h-[70px]">
                        <div class="w-[50%] flex-grow !min-w-[300px] h-[100%]">
                          <div class="flex justify-start items-center w-[100%] p-1">
                            <el-image v-if="pdt.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" :src="pdt.pics[0].url" @click="onShowPdtInfo(pdt)"/>
                            <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" src="/nopic.jpg" @click="onShowPdtInfo(pdt)"/>
                            <div class="ml-2 inline-block text-left max-w-[100%]">
                              <div style="white-space: normal;font-size:'12px'" class="nameStyle" @click="onShowPdtInfo(pdt)">{{ '['+pdt.name+']'+pdt.nick }}</div>
                              <div v-if="pdt.sell_offer_num!=''" style="color: rgb(81, 141, 146);" class="cursor-pointer" @click="onShowPreSell(pdt)">售前单:{{pdt.sell_offer_num}}</div>
                              <div class="flex text-sm">
                                <div class="mr-2 border text-green-700 border-green-700 pl-1 pr-1 cursor-pointer rounded" style="font-size: smaller;" @click="onClickLock(item,pdt)">锁定:{{ pdt.锁定数量 }}</div>
                                <div class="mr-2 border text-red-700 border-red-800 pl-1 pr-1 cursor-pointer rounded" style="font-size: smaller;" @click="onClickBak(pdt,item)">备货:{{ pdt.备货数量 }}</div>
                                <div class="mr-2 border text-red-500 border-red-500 pl-1 pr-1 cursor-pointer rounded" style="font-size: smaller;" @click="handleOper('demand_pdt', pdt,item)">缺货:{{ pdt.缺口数量 }}</div>
                                <!-- pdt.未发货-pdt.锁定数量-pdt.备货数量 -->
                              </div>
                            </div>
                          </div>
                        </div>                  
                        <div class="w-[15%] flex-col flex justify-center items-center  border-left-1px">
                          <div>{{ pdt.销售数量+' '+pdt.base_unit }}</div>
                          <div class="ex_text">备:{{ pdt.销售备品数量+' '+pdt.base_unit }}</div>
                        </div>
                        <div class="w-[10%] flex-col flex justify-center items-center  border-left-1px" v-show="checkPermissionApi('销售订单价格显示')">                      
                            {{ getMoneyFlag(item.money_type)+pdt.sell_price_aft_tax }}
                            <div class="ex_text">含税{{ pdt.发票税率 }}%</div>                      
                        </div>
                        <div class="w-[25%] flex-col flex justify-center border-left-1px">
                          <div class="ml-4">
                            {{ pdt.交货日期.split(' ')[0] }}
                          </div>
                          <div class="ex_text ml-4 flex mt-1"><div class="mr-2">已发货{{ pdt.已发货 }}</div><div class="mr-2">未发货{{ pdt.未发货 }}</div><div v-if="pdt.已退货>0" class=" text-red-500">已退货{{ pdt.已退货 }}</div><div v-if="(pdt.已发货-pdt.已退货)-(parseInt(pdt.销售数量)+parseInt(pdt.销售备品数量))>0" class=" text-red-500">超量{{ (pdt.已发货-pdt.已退货)-(parseInt(pdt.销售数量)+parseInt(pdt.销售备品数量)) }}</div></div>
                          <el-progress class="w-[90%] mt-1 ml-2"
                            :percentage="Math.min(parseFloat((((pdt.已发货-pdt.已退货) / (parseInt(pdt.销售数量)+parseInt(pdt.销售备品数量))) * 100).toFixed(2)), 100)"
                            :status="(pdt.已发货-pdt.已退货) == ( parseInt(pdt.销售数量)+parseInt(pdt.销售备品数量)) ? 'success' : ((pdt.已发货-pdt.已退货) < (parseInt(pdt.销售数量)+parseInt(pdt.销售备品数量)) ? 'warning' : 'exception')"
                          />
                        </div>
                      </div>
                    </div>  
                    <!-- 右边其他数据 -->
                    <div class="w-[40%] flex  text-center  right">
                      <div class="!max-w-[100px] rightcss flex justify-center items-center border-l-0.5px" v-show="checkPermissionApi('销售订单价格显示')">
                        <div style="word-wrap: break-word;">{{getMoneyFlag(item.money_type)+ item.合计+' ' }}</div>
                        <div class="ex_text">{{ item.pay_type }}</div>
                      </div>
                      <div class="!max-w-[200px] rightcss flex flex-col justify-center items-center border-l-0.5px">
                        <div class="mb-1">{{ item.发货状态 }}</div>
                        <el-progress class="w-[100%] mb-1" :stroke-width="9"
                          :percentage="parseFloat((((item.发货数量-item.退货数量)/(parseInt(item.销售数量)+parseInt(item.销售备品数量)))*100).toFixed(2))"
                        />
                        <!-- :status="(item.发货数量-item.退货数量)==item.销售数量?'success':((item.发货数量-item.退货数量) < item.销售数量?'warning':'exception')"                       -->
                        <div style="font-size: 12px;border-radius: 5px;padding: 4px 8px;" :class="{'title_create': ['订单创建','等待审核','等待PMC交期审核','等待生产经理交期审核','等待业务部门经理审核','等待修改','等待提交'].includes(item.fsm_cur_state), 'title_checked': item.fsm_cur_state === '已审核', 'title_ok2': ['已入库','已关闭','已拒绝'].includes(item.fsm_cur_state), 'title_wait': item.fsm_cur_state === '等待修改'}">{{ item.fsm_cur_state }}</div>
                        <div class="text-red-400 mt-1">{{ item.fsm_log_list.length>0?item.fsm_log_list[0][5]:'' }}</div>
                      </div>
          
                      <div class="rightcss border-l-0.5px !max-w-[400px] " style="text-align: left;">{{ item.note }}</div>
                      <div class="!min-w-[70px] flex justify-center items-center border-l-0.5px">
                        <ElButton v-if="currentRoute.name.indexOf('Stone')>=0" type="primary" size="small" @click="handleOper('info', item)">查看</ElButton>
                        <el-dropdown v-if="currentRoute.name.indexOf('Stone')<0" trigger="click" placement="bottom">
                          <span class="el-dropdown-link">
                            <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                          </span>
                          <template #dropdown>
                            <div class="flex flex-wrap w-[240px]">
                              <span class="flex flex-wrap w-[240px]" v-if="item.fsm_cur_state == '已审核'">
                                <el-dropdown-item :disabled="item.发货状态 == '完全发货'" @click="handleOper('out', item)">{{ t('sale.out') }}</el-dropdown-item>
                                <el-dropdown-item @click="handleOper('out_list', item)">{{ t('saleout.get_list')+'('+item.takeout_count+')' }}</el-dropdown-item>
                                <el-dropdown-item @click="handleOper('return', item)">{{ t('sale.return') }}</el-dropdown-item>
                                <el-dropdown-item @click="handleOper('return_list', item)">{{ t('receipt.out_list') +'('+item.cancel_count+')'}}</el-dropdown-item>                            
                              </span>    
                              <el-dropdown-item v-if="item.fsm_cur_state.indexOf('审核')>=0 && currentRoute.name != 'SaleManage' " @click="handleOper('check', item)">{{ '审核' }}</el-dropdown-item>
                              <el-dropdown-item v-if="checkRouterPermissionApi('/salemanage/saledemand')" @click="handleOper('demand', item)">{{ t('sale.demand') }}</el-dropdown-item>
                              <el-dropdown-item v-if="checkRouterPermissionApi('/salemanage/saledemand')" @click="handleOper('demand2', item)"> 销售需求(忽略库存)</el-dropdown-item>
                              <el-dropdown-item v-if='checkPermissionApi("销售备料")' @click="handleOper('prepare', item)">销售备料</el-dropdown-item>
                              <el-dropdown-item class="!w-[210px]" v-if='checkPermissionApi("销售备料")' @click="handleOper('prepare2', item)">销售备料(忽略库存)</el-dropdown-item>
                              <el-dropdown-item v-if="item.fsm_cur_state == '等待提交'" @click="handleOper('info', item)">提交订单</el-dropdown-item>
                              <el-dropdown-item @click="handleOper('info', item)">查看</el-dropdown-item>
                              <el-dropdown-item v-if="currentRoute.name == 'SaleManage'" @click="handleOper('edit', item)">{{ t('userOpt.edit') }}</el-dropdown-item>
                              <el-dropdown-item v-if="item.takeout_count<=0 && currentRoute.name == 'SaleManage'" @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                              
                            </div> 
                          </template>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                            </div>

                </DynamicScrollerItem>
              </template>
            </DynamicScroller>



        </div>
        <div class="flex mt-5 items-center">
          <div class="ml-auto ">
            <span class="font-bold">税前总金额:</span>
            <span class="mr-6">{{ rootData.added.税前合计 }}</span>
            <span class="font-bold">总金额:</span>
            <span class="mr-6">{{ rootData.added.税后合计 }}</span>
            <span class="font-bold">总数量:</span>
            <span class="mr-6">{{ rootData.added.销售合计 }}</span>
          </div>
          <el-pagination 
            v-model:current-page="searchCondition.page"
            v-model:page-size="searchCondition.count"
            :page-sizes="[10, 50, 100, 300]"
            :background="true"
            layout="sizes, prev, pager, next"
            :total="totleCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>


      </div>

      

    <DialogProductLite :id="curSelItem.id" type="info" categ="" :exinfo="{}"  :title="t('product_manage.add_product')" v-model:show="showPdtInfo" @on-submit="()=>{}"/>

    <DialogSellBakInfo v-model:show="showBakData" :sell_order_num="curBakOrderNum" :pdt_biaoshi="curBakPdtBiaoshi" />

    <DialogFileList :path="'file/xs/'+curSelItem.sell_order_num+'/'" v-model:show="showFileList" :files="curSelFileType=='图片'?curSelItem.file_list:curSelItem.hetong_list" @on-update="onUpdateFileList" :type="curSelFileType" :title="curTitle"/>
    
    <DialogUser :param="''" v-model:show="showSelSaleUserDlg" :title="t('msg.selectUser')" @on-submit="onSelSaleCallback"/>
    <DialogUser :param="''" v-model:show="showSelFollowerUserDlg" :title="t('msg.selectUser')" @on-submit="onSelFollowerCallback"/>

    
    <PrintModal v-model:show="dialogVisible" />


    <DialogImportTask v-model:show="bShowTaskDlg" :mod="'sell_order'" :cmd="'import_list'" @on-submit="getSaleList"/>

    
    <!-- <div class="h-20"></div> -->
    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #f0f9eb !important;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #fff;
  font-weight: 400;
}

.nameStyle {
  cursor: pointer;
}
.nameStyle:hover{
  color: rgb(130, 130, 255);
}
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 150px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  color: #333;
  font-size: 15px;
}
.headerBk{
  background-color: #fff !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 14px;
  font-weight: bold;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self >div >div,
.right >div
{
    // border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: center; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
  }
  .rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}


//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt{ //未收货
  background-color: #79bbff;
  // border: #d9d9d9 1px solid;
}
.title_partial{ //部分收货
  background-color: #eebe77;
}
.title_ok{ //完全收货
  background-color: #95d475;
}
.title_over{//超量收货
  background-color: #FFC0CB;
}
.title_create{ //已创建
  color: #409EFF;
  background-color: color-mix(in oklch, #409EFF, transparent 80%);
}
.title_checked{ //已审核
  color: #67C23A;
  background-color: color-mix(in oklch, #67C23A, transparent 80%);
}
.title_wait{//等待修改
  background-color: #FFC0CB;
  background-color: color-mix(in oklch, #FFC0CB, transparent 80%);
}
.title_ok2{ //完全收货
  background-color: #95d475;
  background-color: color-mix(in oklch, #95d475, transparent 80%);
}
:deep(.el-progress__text) {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-left: 5px;
    min-width: auto; 
    line-height: 1;
}
:deep(.el-progress__text) {
    font-size: 12px !important;
}

// div{
//   font-size: 12px;
// }

</style>

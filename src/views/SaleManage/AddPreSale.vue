<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard, ElPopconfirm, ElDialog, ElUpload, ElTabs, ElTabPane, ElRadio, ElRadioGroup, ElButton, ElForm, ElFormItem, FormRules, ElDescriptions, ElDescriptionsItem, ElInput, ElImage, ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted, nextTick } from 'vue'
import type { FormInstance, UploadProps } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount, watch } from 'vue'
import { DialogSelCustomer } from '@/components/DialogSelCustomer'
import { DialogProductSel } from '@/components/DialogProductSel'
import { checkFormRule, checkPermissionApi, getGUID } from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { updatePreSaleApi, addPreSaleApi, getPreSaleNewnumApi, getPreSaleInfoApi } from '@/api/product'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { cloneDeep } from 'lodash-es'
import { getOssSignApi, ossUpload } from '@/api/oss'
import { DialogCheckShow } from '@/components/DialogCheckShow'

const { currentRoute, back,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    sell_offer_num: [{ required: true, message: t('msg.noNum'), trigger: 'blur' }],
    buyer_nick: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
    pdt_nick: [{ required: true, message: t('msg.noPdtNick'), trigger: 'blur' }],
    pdt_amount: [{ required: true, message: t('msg.noCount'), trigger: 'blur' }, { pattern: /^[0-9]+$/, message: t('msg.noCount'), trigger: 'blur' }]
})
//当前选中Tab
const activeTab = ref('0')

//单个产品定义
const pdtData = {
    标识: '',
    pdt_nick: '',
    "pdt_stuff": "", //产品材质
    "pdt_specs": "", //产品规格
    "pdt_skill": "", //产品工艺
    "pdt_print": "", //产品印刷
    "pdt_amount": "", //产品数量
    "pdt_color": "", //产品颜色
    "pdt_pack": "", //产品包装
    "note": "", //其它
    pics: [],


    // pdt_pic_list:[],
    other_data: {
        大货模具费备注: '单款数量超过20万以上',
        加急大货模具费备注: '业务告知客户',
        报价有效期: '30',
        装箱数: 0,
        净重: 0,
        毛重: 0,
        出厂合计成本: 0,
        运费单价: 0,
        运费数量: 0,
        运费备注: '',
        其他费用单价: 0,
        其他费用数量: 0,
        其他费用备注: '',
        销管费数量: 0,
        销管费备注: '',
        毛利率: 23,
        毛利率备注: '',
        增值税率: 13,

        长: 0,
        宽: 0,
        高: 0,
        打样时间: 0,
        打样时间备注: '',
        开模时间: 0,
        开模时间备注: '',
        打样费: 0,
        打样费备注: '',
        大货模具费: 0,
        加急大货模具费: 0,
        报价有效期备注: '',
        是否含税: '不含税',
        税率: 0,
        生产日期: '',
        大货时间:0
    },
    process_list: [],
}

//报价单数据
const presaleData = reactive({
    "sell_offer_num": "",
    "buyer_id": "",
    buyer_nick: "",
    "sell_man_id": "", //负责工程师
    sell_man_name: "",
    "pdt_list": [], //报价产品列表
    "sell_man_note": "正常", //负责工程师备注：加急，不加急

    fsm_can_trig_data: {
        审核触发: [],
        操作触发: ['保存销售报价']
    }, //审核决策
    fsm_cur_state: '',    //当前节点状态
    fsm_exe_man_name: '',
    fsm_exe_log: '',
    fsm_exe_trig: '',//决策内容
    fsm_log_list: []
})

//获取最新ID
const onChangeID = async () => {
    const ret = await getPreSaleNewnumApi()
    if (ret) {
        console.log(ret)
        presaleData.sell_offer_num = ret.data.new_id
    }
}


onMounted(async () => {

    if (currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined) {
        title.value = t('presale.add')
        onChangeID()

        //新建需要默认可以提交工程师报价
        const tmp = [...presaleData.fsm_can_trig_data.操作触发, '提交到工程报价']
        presaleData.fsm_can_trig_data.操作触发 = tmp

        //添加一个默认产品
        let pdt = cloneDeep(pdtData)
        pdt.标识 = getGUID(5)
        presaleData.pdt_list.push(pdt)
    }
    else {
        if (currentRoute.value.query.type == 'info') {
            title.value = t('presale.look')
        }
        else {
            title.value = t('presale.modify')
        }



        //查询产品信息 
        const ret = await getPreSaleInfoApi({
            id: currentRoute.value.query.id,
            page: 1,
            count: 100
        })
        if (ret) {
            console.log(ret)
            Object.assign(presaleData, ret.data)
        }


        nextTick(() => {
            if (currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if (excludeDiv != null) {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }
})


//显示隐藏选择客户弹窗
const showSelCustomerDlg = ref(false)
//选择BOM关联客户
const onSelCustomer = () => {
    showSelCustomerDlg.value = true
}
//选择客户回调
const onSelCustomerCallback = (id, name, nick) => {
    console.log(id, name, nick)
    presaleData.buyer_id = id
    presaleData.buyer_name = name
    presaleData.buyer_nick = nick
}


//显示隐藏选择工程师窗口变量
const showSelEngineerDlg = ref(false)
//显示选择工程师弹窗
const onSelEngineer = () => {
    showSelEngineerDlg.value = true
}
//选择工程师回调
const onSelEngineerCallback = (id, name) => {
    presaleData.sell_man_id = id
    presaleData.sell_man_name = name
}

const checkPdt = () => {
    if (presaleData.pdt_list.length <= 0) {
        ElMessage.warning('请创建报价产品！')
        return false
    }

    //校验核心数据是否填完
    for (let i = 0; i < presaleData.pdt_list.length; i++) {
        const pdt = presaleData.pdt_list[i]
        if (pdt.pdt_nick.length <= 0 || pdt.pdt_amount.length <= 0) {
            ElMessage.warning('请填写核心数据！')
            activeTab.value = i.toString()
            return false
        }
    }

    const arrayPdt = presaleData.pdt_list.filter(pdt => pdt.pdt_nick.length > 0)
    if (arrayPdt.length <= 0) {
        ElMessage.warning('请选择报价产品！')
        return false
    }

    return true
}


//保存
const onSave = async () => {
    console.log(presaleData)

    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    if (!checkPdt()) {
        return
    }

    if (presaleData.id == undefined) {

        const ret = await addPreSaleApi(presaleData)
        if (ret) {
            ElMessage.success(t('msg.newSaleSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret = await updatePreSaleApi(presaleData)
        if (ret) {
            ElMessage.success(t('msg.updatePreSaleSuccess'))
            back()
        }
    }


}

//提交审核意见
const handleCheck = async (btn: string,self = false) => {
    console.log(11)
    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //如果是提交到工程报价 但是没有选择工程师 则报错
    if(btn == '提交到工程报价' && presaleData.sell_man_id == '' && !self)
    {
        ElMessage.error('请选择工程师！')
        return
    }


    if (!checkPdt()) {
        return
    }

    const info = wsCache.get(appStore.getUserInfo)
    presaleData.fsm_exe_man_name = info.resident_name
    presaleData.fsm_exe_trig = btn


    if (presaleData.id == undefined) {

        const ret = await addPreSaleApi(presaleData)
        if (ret) {
            ElMessage.success(t('msg.newSaleSuccess'))
            console.log('---',ret)
            if(self) //自主报价 直接跳转报价页面
            {
                push({
                    path: '/presalemanage/presaledetail',
                    query: {
                        sell_offer_num: ret.data.sell_offer_num,
                        type: 'edit',
                        mode: '工程报价单',
                        self:'自主报价'
                    }
                })
            }
            else
            {
                // back()
                push({
                        path:'/presalemanage/presalemanage'
                    })
            }

            
        }
    }
    else //修改
    {
        const ret = await updatePreSaleApi(presaleData)
        if (ret) {
            ElMessage.success(t('msg.updatePreSaleSuccess'))

            if (btn.indexOf('修改') >= 0) {
                Object.assign(presaleData, ret.data)
            }
            else {
                if(self) //自主报价 直接跳转报价页面
                {
                    push({
                        path: '/presalemanage/presaledetail',
                        query: {
                            sell_offer_num: ret.data.sell_offer_num,
                            type: 'edit',
                            mode: '工程报价单',
                            self:'自主报价'
                        }
                    })
                }
                else
                {
                    // back()
                    push({
                        path:'/presalemanage/presalemanage'
                    })
                }
            }

        }
    }
}
//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = () => {
    showCheckHisDlg.value = true
}


//新增产品
const onAddPdt = () => {
    let pdt = cloneDeep(pdtData)
    pdt.标识 = getGUID(5)
    presaleData.pdt_list.push(pdt)
    activeTab.value = (presaleData.pdt_list.length - 1).toString()
    console.log(activeTab.value)
}
//删除产品
const onRemovePdt = (index) => {
    if(currentRoute.value.query.type == 'info')
        return
    console.log(index)
    if (presaleData.pdt_list[index].pdt_nick == '') {
        presaleData.pdt_list.splice(index, 1)
        activeTab.value = (index - 1).toString()
    }
    else {
        ElMessageBox.confirm('是否确认删除该产品的申报?', t('msg.notify'), {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'error',
        }
        ).then(async () => {
            presaleData.pdt_list.splice(index, 1)
        }
        ).catch(() => { })
    }
}

const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
    console.log(uploadFile, uploadFiles)
    const pdt = presaleData.pdt_list[activeTab.value]
    for (let one of pdt.pics) {
        if (one.url == uploadFile.raw.weburl) {
            pdt.pics.splice(pdt.pics.indexOf(one), 1)
            break
        }
    }
    console.log('删除图片', pdt.pics)
}

const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const handlePreviewPic: UploadProps['onPreview'] = (uploadFile) => {
    dialogImageUrl.value = uploadFile.url!
    dialogVisible.value = true
    console.log('预览图片')
}

const uploadImg = async (file) => {
    let path = 'presale/' + presaleData.sell_offer_num + '/'
    console.log('====', file)

    const ret = await getOssSignApi({ upload_dir: path })
    if (ret) {
        const end = await ossUpload(ret.data.token, file.file, path, (pro) => {
            console.log('pppp', pro)
        })

        const pdt = presaleData.pdt_list[activeTab.value]
        pdt.pics.push({
            name: file.file.name,
            url: end.url
        })
        file.file.weburl = end.url
        console.log('完成', pdt.pics, file)
    }
}

</script>

<template>
    <ContentDetailWrap :title="title" @back="back()" style="background-color:var(--app-content-bg-color);">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史
            </ElButton>
            <!-- <el-popconfirm v-if="presaleData.fsm_can_trig_data.操作触发.includes('锁定')"  title="是否确认临时锁定该销售单?" @confirm="handleCheck('锁定')">
                <template #reference>
                    <ElButton    type="danger" >锁定</ElButton>
                </template>
            </el-popconfirm> -->

        </template>
        <template #right>
            <ElButton v-if="currentRoute.query.type != 'info' && presaleData.fsm_can_trig_data.操作触发.includes('保存销售报价')"
                color="#409EFF" style="color: #fff;" @click="handleCheck('保存销售报价')">
                <Icon class="mr-0.5" icon="carbon:save" />
                保存
            </ElButton>
            <ElButton v-if="currentRoute.query.type != 'info' && presaleData.fsm_can_trig_data.操作触发.includes('修改销售报价')"
                type="primary" @click="handleCheck('修改销售报价')">修改</ElButton>


            <el-popconfirm v-if="presaleData.fsm_can_trig_data.操作触发.includes('提交到工程报价')" title="是否确认自主报价?"
                @confirm="handleCheck('提交到工程报价',true)">
                <template #reference>
                    <ElButton type="success">
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        自主报价
                    </ElButton>
                </template>
            </el-popconfirm>

            <el-popconfirm v-if="presaleData.fsm_can_trig_data.操作触发.includes('提交到工程报价')" title="是否确认提交到工程报价?"
                @confirm="handleCheck('提交到工程报价')">
                <template #reference>
                    <ElButton type="success">
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        提交到工程报价
                    </ElButton>
                </template>
            </el-popconfirm>

            <el-popconfirm v-if="presaleData.fsm_can_trig_data.操作触发.includes('关闭')" title="是否确认关闭订单?"
                @confirm="handleCheck('关闭')">
                <template #reference>
                    <ElButton type="danger">关闭订单</ElButton>
                </template>
            </el-popconfirm>
        </template>

        <div class="min-w-[900px] flex justify-center paper-div">
            <el-form :rules="rules" :model="presaleData" ref="ruleFormRef" class="w-[84%]">
                <div class="text-16px font-700 text-center m-20px">售前申价单</div>
                <el-descriptions class="flex-1 mt-2 rounded-4px border-0.5px p-4" style="overflow: hidden;" :column="2"
                    border>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                        :label="t('presale.name')+':'" class="flex" :span="3">
                        <el-form-item prop="sell_offer_num">
                            <div class="flex">
                                <el-input style="width: 400px;" :disabled="presaleData.id != undefined" v-model="presaleData.sell_offer_num" />
                                <ElButton class="ml-2" v-if="presaleData.id == undefined" type="primary" @click="onChangeID">
                                    <Icon class="mr-0.5" icon="radix-icons:update" />
                                    {{ t('button.update') }}
                                </ElButton>
                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                        :label="t('bom.custom')+':'" class="flex" :span="1">
                        <el-form-item prop="buyer_nick">
                            <div class="mr-2 border-1.4px min-w-30 pl-2 rounded-md">
                                {{ checkPermissionApi('客户名称显示')?(presaleData.buyer_nick?presaleData.buyer_nick:'请选择'):'***' }}
                            </div>
                            <ElButton @click="onSelCustomer">
                                <Icon icon="iconamoon:search-bold" />
                            </ElButton>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle"
                        :label="t('presale.engineer_buy')+':'" class="flex" :span="1">
                        <el-form-item>
                            <div class="mr-2 border-1.4px min-w-30 pl-2 rounded-md">
                                {{ presaleData.sell_man_name?presaleData.sell_man_name:'请选择' }}
                            </div>
                            <ElButton @click="onSelEngineer">
                                <Icon icon="iconamoon:search-bold" />
                            </ElButton>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('presale.hurry')+':'"
                        class="flex"  :span="3">
                        <el-form-item>
                            <el-radio-group v-model="presaleData.sell_man_note">
                                <el-radio :label="'正常'">正常</el-radio>
                                <el-radio :label="'加急'">加急</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-descriptions-item>
                </el-descriptions>

                <div class="flex">
                    <ElButton class="mt-2 mb-2" type="success" @click="onAddPdt">
                        <Icon class="mr-0.5" icon="icons8:plus" />
                        新增产品
                    </ElButton>
                </div>

                <el-tabs class="rounded-4px border-0.5px" type="border-card" v-model="activeTab" closable
                    @tab-remove="onRemovePdt">
                    <!-- pdt.pdt_nick==''?('产品'+(index+1)):pdt.pdt_nick -->
                    <el-tab-pane v-for="pdt, index in presaleData.pdt_list" :key="pdt.标识" :label="'产品' + (index + 1)"
                        :name="index.toString()">
                        <el-form :rules="rules" :model="pdt">
                            <el-descriptions class="flex-1 mt-2" :column="2" border direction="vertical">
                                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                                    :label="t('product_manage.name')+':'" class="flex" :span="2">
                                    <el-form-item prop="pdt_nick">
                                        <el-input v-model="pdt.pdt_nick" class="!w-[100%]" placeholder="客户产品简称+材质+品类 如：李白滴胶钥匙扣"/>
                                    </el-form-item>
                                </el-descriptions-item>
                                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                                    :label="t('presale.count')+':'" class="flex" :span="2">
                                    <el-form-item prop="pdt_amount">
                                        <el-input v-model="pdt.pdt_amount" type="number" placeholder="请输入数量"/>
                                    </el-form-item>
                                </el-descriptions-item>
                                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                                    :label="t('sale.material')+':'" class="flex" :span="2">
                                    <el-form-item prop="name">
                                        <el-input v-model="pdt.pdt_stuff" placeholder="请输入材质"/>
                                    </el-form-item>
                                </el-descriptions-item>
                                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                                    :label="t('presale.specs_weight')+':'" class="flex" :span="2">
                                    <el-form-item prop="name">
                                        <el-input v-model="pdt.pdt_specs" clearable :autosize="{ minRows: 3, maxRows: 3 }"
                                            type="textarea" :placeholder="'例如：公仔主体：50*27*55\nMM带子：16*237MM\n小吊牌10.5*32ＭＭ'"/>
                                    </el-form-item>
                                </el-descriptions-item>
                                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle" 
                                    :label="t('presale.rule')+':'" class="flex" :span="2">
                                    <el-form-item prop="name">
                                        <el-input v-model="pdt.pdt_skill" clearable :autosize="{ minRows: 3, maxRows: 3 }"
                                            type="textarea" :placeholder="'例如：1.手感漆\n2.眼睛亮油\n3.模具抛光'"/>
                                    </el-form-item>
                                </el-descriptions-item>
                                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                                    :label="t('presale.print')+':'" class="flex" :span="2">
                                    <el-form-item prop="name">
                                        <el-input v-model="pdt.pdt_print" clearable :autosize="{ minRows: 3, maxRows: 3 }"
                                            type="textarea" placeholder="请输入Logo要求/印刷"/>
                                    </el-form-item>
                                </el-descriptions-item>
                                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                                    :label="t('presale.color')+':'" class="flex" :span="2">
                                    <el-form-item prop="name">
                                        <el-input v-model="pdt.pdt_color" placeholder="请输入颜色"/>
                                    </el-form-item>
                                </el-descriptions-item>
                                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                                    :label="t('presale.pack_requir')+':'" class="flex" :span="2">
                                    <el-form-item prop="name">
                                        <el-input v-model="pdt.pdt_pack" placeholder="例如：彩盒+铝箔袋"/>
                                    </el-form-item>
                                </el-descriptions-item>
                                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                                    :label="t('presale.other')+':'" class="flex" :span="2">
                                    <el-form-item prop="name">
                                        <el-input v-model="pdt.note" clearable :autosize="{ minRows: 3, maxRows: 3 }"
                                            type="textarea" placeholder=""/>
                                    </el-form-item>
                                </el-descriptions-item>
                                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                                    :label="t('presale.img')+':'" class="flex" :span="2">
                                    <el-upload :disabled="currentRoute.query.type == 'info'" :file-list="pdt.pics"
                                        list-type="picture-card" :on-preview="handlePreviewPic" :on-remove="handleRemove"
                                        :accept="'.jpg,.png,.bmp'" :http-request="(file) => uploadImg(file)">
                                        <Icon icon="fluent:add-24-filled" />
                                    </el-upload>
                                </el-descriptions-item>
                            </el-descriptions>
                        </el-form>
                    </el-tab-pane>
                </el-tabs>
            </el-form>
        </div>


        <div class="mb-20"></div>

        <!-- 选择客户弹窗 -->
        <DialogSelCustomer v-model:show="showSelCustomerDlg" :title="t('bom.sel_cus')" @on-submit="onSelCustomerCallback" />

        <!-- 选择工程师 -->
        <DialogUser :param="''" v-model:show="showSelEngineerDlg" :title="t('msg.selectUser')"
            @on-submit="onSelEngineerCallback" />

        <el-dialog v-model="dialogVisible">
            <img w-full :src="dialogImageUrl" alt="预览" />
        </el-dialog>

        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="presaleData.fsm_log_list" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    background-color: #fff !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% ;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

:deep(.tableHeader) {
    // background-color: #6d92b4 !important;
    background-color: RGBA(246, 246, 246, 1) !important;
    color: #333;
    white-space: nowrap;
    text-align: center;
}

//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
    margin-bottom: 10px;
}

//设置表单元格属性
:deep(.table_cell .cell) {
    padding-left: 3px;
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
    /* 添加你的样式 */
    text-align: center;
}

:deep(.bakinput .el-input__wrapper) {
    background-color: #f4f4f4;
}

:deep(.infomode) {
    border: none;
    /* 设置边框为 none */
    border-radius: 0;
    /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none;
    /* 如果有阴影，也可以设置为 none */
}

:deep(.el-card) {
    background-color: var(--app-content-bg-color);
    border-color: var(--app-content-bg-color);
}

:deep(.el-card__body) {
    // padding: 0 240px var(--el-card-padding);
    display: flex;
    justify-content: center;
}

.paper-div {
    background-color: #fff;
    box-shadow: 0px 6px 14px 4px rgba(208, 208, 208, 0.34);
    border-radius: 4px;
    padding: var(--el-card-padding) var(--el-card-padding) calc(6 * var(--el-card-padding));
    width: 100%;
}

:deep(.el-tabs__item) {
    border-radius: 4px;
}

:deep(.el-descriptions__body) .el-descriptions__table.is-bordered .el-descriptions__cell {
    border: none;
    padding: 8px 11px;
}

:deep(.el-tabs--border-card)>.el-tabs__content {
    padding: 15px 15px 64px ;
}
</style>
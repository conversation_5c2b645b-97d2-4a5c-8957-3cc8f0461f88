<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElPopconfirm,ElDialog, ElForm, ElFormItem, ElTag, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { addPayUrlApi, delPayUrlApi, getPayUrlListApi, getWithdrawListApi, getWithdrawTJ<PERSON>pi, resetAccountOrder, updatePayUrlApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';
import { getHashApi, getHashSysListApi, getJiangchiListApi, setHashApi, setHashSyspi, updateJiangchiApi } from '@/api/extra';

const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    page: 1,
    count: 2000
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})

//配置列表
const payUrlConfig = reactive([])

const getPayUrlList = async () => {
    let ret = await getPayUrlListApi(searchCondition)
    if (ret) {
        console.log(ret)
        if (ret.data) {
            // Object.assign(payUrlConfig, ret.data)
            payUrlConfig.splice(0, payUrlConfig.length, ...ret.data)
            totleCount.value = parseInt(ret.count)
        }
    }
}


//更新表高度
const updateTableHeight = () => {
    if (userTableRef.value && rootRef.value) {
        tableHeight.value = rootRef.value.clientHeight - 400
    }
}
//浏览器大小变化
const handleWindowResize = () => {
    updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {

}
//page控件发生切换
const handleCurrentChange = (val: number) => {

}
//创建新客户
const onAddCustmer = () => {

}

//处理表格对象操作
const handleOper = async (type, row) => {
    console.log(row)
    if (type == '修改') {
        showEdit.value = true
        Object.assign(selItem, row)
    }
    else if (type == '确定修改') {
        showEdit.value = false
        if (selItem.id == '') {
            const ret = await addPayUrlApi(row)
            if (ret) {
                getPayUrlList();

                ElMessage({
                    type: 'success',
                    message: '操作成功',
                });
            }
        }
        else {
            const ret = await updatePayUrlApi(row)
            if (ret) {
                getPayUrlList();

                ElMessage({
                    type: 'success',
                    message: '操作成功',
                });
            }
        }


    }
    else if (type == '删除') {
        ElMessageBox.confirm(
            '是否确认删除该设置？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {

                const ret = await delPayUrlApi(row)
                if (ret) {
                    getPayUrlList();

                    ElMessage({
                        type: 'success',
                        message: '操作成功',
                    });
                    showEdit.value = false;

                }


            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: '操作取消',
                })

            })
    }
}


onMounted(() => {
    updateTableHeight(); // 首次设置表格高度
    window.addEventListener('resize', handleWindowResize);
    getPayUrlList()

})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
    window.removeEventListener('resize', handleWindowResize);
});


const showEdit = ref(false)
const selItemdef = {
    id: '',
    name: '',
    url: '',
    lx: '',
    status: 1,
    mode:'普通账户'
}
const selItem = reactive({
    ...selItemdef,
    reset() {
        for (let key in this) {
            if (this.hasOwnProperty(key) && !(key in selItemdef) && key != 'reset') {
                delete this[key];
            }
        }
        Object.assign(this, selItemdef)
    }
})

const onAddConfig = () => {
    showEdit.value = true
    selItem.reset()
}

const onResetAll = async() => {
    const ret = await resetAccountOrder({})
    if (ret) {
        getPayUrlList();
        ElMessage({
            type:'success',
            message: '操作成功',
        });
    }
}
</script>

<template>
    <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
        <div ref="rootRef" class="flex relative">
            <div class="absolute top-8 left-8">
                <ElButton type="success" @click="onAddConfig">
                    <Icon icon="fluent-mdl2:people-add" />
                    <div class="pl-2">{{ t('button.add') }}</div>
                </ElButton>
                <ElButton type="warning" @click="getPayUrlList">
                    刷新
                </ElButton>

                <el-popconfirm title="确定是否全部复位优先级?" @confirm="onResetAll">
                    <template #reference>
                        <ElButton class="ml-auto" type="danger" >
                           复位
                        </ElButton>
                    </template>
                </el-popconfirm>
            </div>
            <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
                <div class="text-center mb-5 font-bold" style="color:#333">收款账号配置</div>
                <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="payUrlConfig"
                    style="width: 100%;color: #666666;" :height1="tableHeight" border stripe>
                    <el-table-column show-overflow-tooltip prop="name" label="名称" />
                    <el-table-column show-overflow-tooltip prop="url" label="url" />
                    <el-table-column show-overflow-tooltip prop="status" label="状态">
                        <template #default="scope">
                            <el-tag type="success" v-if="scope.row.status == 1">开启</el-tag>
                            <el-tag type="danger" v-else>禁用</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="mode" label="账户类型" />
                    <el-table-column show-overflow-tooltip prop="created_at" label="添加时间" />
                    <el-table-column show-overflow-tooltip prop="updated_at" label="修改时间" />
                    <el-table-column show-overflow-tooltip prop="lx" label="优先" />
                    <el-table-column fixed="right" :label="t('userTable.operate')" width="220">
                        <template #default="scope">
                            <div class="flex items-center justify-center">
                                <ElButton type="success" size="small" @click="handleOper('修改', scope.row)">修改</ElButton>
                                <ElButton type="danger" size="small" @click="handleOper('删除', scope.row)">删除</ElButton>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

            </div>
        </div>


        <el-dialog title="新增/修改" v-model="showEdit" width="700" align-center destroy-on-close>
            <el-form>
                <el-form-item class="titleShow" label="名称:">
                    <el-input v-model="selItem.name" placeholder="请输入名字"></el-input>
                </el-form-item>
                <el-form-item class="titleShow" label="地址:">
                    <el-input v-model="selItem.url" placeholder="请输入支付地址"></el-input>
                </el-form-item>
                <el-form-item class="titleShow" label="状态:">
                    <el-select class="w-53" v-model="selItem.status" placeholder="">
                        <el-option v-for="item in [{ key: 2, name: '禁用' }, { key: 1, name: '开启' }]" :key="item.key"
                            :label="item.name" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item class="titleShow" label="账户类型:">
                    <el-select class="w-53" v-model="selItem.mode" placeholder="">
                        <el-option v-for="item in ['普通账户','大额账户']" :key="item"
                            :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="selItem.id != ''" class="titleShow" label="优先:">
                    <el-input v-model="selItem.lx" placeholder="请输入"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="showEdit = false">取消</el-button>
                    <el-button type="danger" @click="handleOper('确定修改', selItem)">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    cursor: pointer;
    color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
    /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
}

:deep(.el-form-item__label) {
    width: 120px;
    /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
    text-align: right;
    /* 让文本右对齐，使标题看起来更整齐 */
    padding-right: 10px;
    /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTooltip, ElSwitch, ElDialog, ElForm, ElFormItem, ElRow, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi, getTodayDate } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { getWithdrawListApi, getWithdrawTJ<PERSON><PERSON>, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';
import { Howl } from 'howler';

const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  user_id: '',
  status: '',
  start_date: '',
  end_date: '',
  date: ['', ''],
  page: 1,
  count: 20
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

const sound = ref(null);
const playAudio = () => {
  console.log('播放')
  sound.value = new Howl({
    src: ['notify.mp3'],
    volume: 1.0,
  });
  sound.value.play();
};


//客户数据源
const withdrawData = reactive([])

const nLastCount = ref(-1) //上次提现条数
//查询客户数据
const getWithdrawList = async () => {
  let tmp = cloneDeep(searchCondition)
    tmp.start_date = searchCondition.date[0]
    tmp.end_date = searchCondition.date[1]
  delete tmp.date
    
  const ret = await getWithdrawListApi(tmp)
  if (ret) {
    console.log(ret)
    withdrawData.splice(0, withdrawData.length, ...ret.data)
    totleCount.value = parseInt(ret.count)
    if (totleCount.value != nLastCount.value && nLastCount.value != -1 && autoFresh.value) {
      //播放public中的声音文件
      playAudio()
    }
    nLastCount.value = totleCount.value
  }
  await getWithdrawTJ()
}

const txTJ = reactive({
  seven_days_ago: "0",
  thirty_days_ago: '0',
  ninety_days_ago: '',
  this_month: '',
  today: '0',
})
//查询统计数据
const getWithdrawTJ = async () => {
  let tmp = {}
  tmp.start_date = searchCondition.date[0]
  tmp.end_date = searchCondition.date[1]
  const ret = await getWithdrawTJApi(tmp)
  if (ret) {
    console.log(ret)
    //全部7则
    ret.data.seven_days_ago = parseFloat(ret.data.seven_days_ago) / 100
    ret.data.thirty_days_ago = parseFloat(ret.data.thirty_days_ago) / 100
    ret.data.ninety_days_ago = parseFloat(ret.data.ninety_days_ago) / 100
    ret.data.this_month = parseFloat(ret.data.this_month) / 100
    ret.data.today = parseFloat(ret.data.today) / 100
    ret.data.yesterday = parseFloat(ret.data.yesterday) / 100
    ret.data.other_days_ago = parseFloat(ret.data.other_days_ago) / 100
    Object.assign(txTJ, ret.data)
  }
}

//开始查询
const onSearch = () => {
  getWithdrawList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getWithdrawList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getWithdrawList()
}
//创建新客户
const onAddCustmer = () => {

}

//处理表格对象操作
const handleOper = (type, row) => {
  console.log(row)
  if (type === '提现成功') {
    ElMessageBox.confirm(
      '该笔订单是否确认提现成功？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        let tmp = {
          id: row.id,
          note: '',
          app_money: row.app_money,
          status: 'Successful Withdrawal'
        }
        console.log(tmp)
        const ret = await updateWithdrawApi(tmp)  //状态， 1：待提现，2：提现成功，3：提现失败
        if (ret) {
          getWithdrawList()

          ElMessage({
            type: 'success',
            message: '操作提现成功',
          })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '操作取消',
        })
      })
  }
  else if (type === '提现失败') {
    ElMessageBox.prompt(
      '该笔订单是否确认提现失败,请输入原因？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async (value) => {

        let tmp = {
          id: row.id,
          note: value.value,
          app_money: row.app_money,
          status: 'Withdrawal Failed'
        }


        const ret = await updateWithdrawApi(tmp)
        if (ret) {
          getWithdrawList()

          ElMessage({
            type: 'success',
            message: '操作提现失败',
          })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '操作取消',
        })

      })
  }
  else if (type === '修改') {
    showEdit.value = true
    selItem.reset()
    Object.assign(selItem, row)

  }
  else if (type == '确定修改') {
    ElMessageBox.confirm(
      '是否确认修改该笔订单？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        let tmp = {
          id: row.id,
          note: '',
          app_money: row.app_money,
        }

        const ret = await updateWithdrawApi(tmp)
        if (ret) {
          getWithdrawList()

          ElMessage({
            type: 'success',
            message: '操作成功',
          })
          showEdit.value = false
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '操作取消',
        })

      })
  }
}

let timmer
onMounted(async () => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  // searchCondition.date[0] = getTodayDate()
  // searchCondition.date[1] = getTodayDate()
  await getWithdrawList()

  //     //启动定时刷新
  // timmer = setInterval(() => {
  //   getWithdrawList()
  // }, 2000);
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});
// 在组件销毁前移除窗口大小变化的监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize);
  clearInterval(timmer)
});

//自动刷新
const autoFresh = ref(false)
const autoFreshChange = () => {
  if (autoFresh.value) {
    timmer = setInterval(() => {
      getWithdrawList()
    }, 2000);
  } else {
    clearInterval(timmer)
  }
}

const showEdit = ref(false)
const selItemdef = {
  user_id: '',
  user_name: '',
  mode: '',
  task_bet: '',
  withdraw_num: '',
  req_money: '',
  cashapp_tag: ''
}
const selItem = reactive({
  ...selItemdef,
  reset() {
    for (let key in this) {
      if (this.hasOwnProperty(key) && !(key in selItemdef) && key != 'reset') {
        delete this[key];
      }
    }
    Object.assign(this, selItemdef)
  }
})

const cellClassName = (cell) => {
  if (cell.column.label == '到账金额') {
    return 'font-bold text-green-400'
  }
  if (cell.column.label == '状态') {
    if (cell.row.status == 'Successful Withdrawal') {
      return 'font-bold text-green-500'
    }
    else if (cell.row.status == 'Withdrawal Failed') {
      return 'font-bold text-red-500'
    }
    else if (cell.row.status == 'Processing Payment') {
      return 'font-bold text-yellow-500'
    }

  }
}

// const formatTimeWithOffset = (timeStr) => {
//   const date = new Date(timeStr);
//   // 获取当前时间的时间戳，加上8小时对应的毫秒数（8 * 60 * 60 * 1000）
//   const offsetTimeStamp = date.getTime() + 13 * 60 * 60 * 1000;
//   const offsetDate = new Date(offsetTimeStamp);
//   // 按照你期望的格式来格式化时间，以下示例格式化为 yyyy-MM-dd HH:mm:ss
//   const year = offsetDate.getFullYear();
//   const month = ('0' + (offsetDate.getMonth() + 1)).slice(-2);
//   const day = ('0' + offsetDate.getDate()).slice(-2);
//   const hours = ('0' + offsetDate.getHours()).slice(-2);
//   const minutes = ('0' + offsetDate.getMinutes()).slice(-2);
//   const seconds = ('0' + offsetDate.getSeconds()).slice(-2);
//   return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
// }
</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
    <div ref="rootRef" class="flex relative">
      <div class="absolute top-8 left-8">
        <!-- <ElButton type="success" @click="()=>{nLastCount = 1}">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton> -->
        <el-switch class="ml-10" v-model="autoFresh" size="large" active-text="启动通知" inactive-text="关闭通知"
          @change="autoFreshChange" />
      </div>
      <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
        <div class="text-center mb-5 font-bold text-red-500 text-[30px]">提现管理</div>

        <el-row class="text-center lineOut1 mb-2" justify="center">
          <el-col :span="3" class="lineOut">
            <el-statistic title="查询日提现" :value="txTJ.other_days_ago" :precision="2"/>
          </el-col>
          <el-col :span="3" class="lineOut">
            <el-statistic title="当日提现" :value="txTJ.today" :precision="2"/>
          </el-col>
          <el-col :span="3" class="lineOut">
            <el-statistic title="昨日提现" :value="txTJ.yesterday" :precision="2"/>
          </el-col>
          <el-col :span="3" class="lineOut">
            <el-statistic title="最近7天提现" :value="txTJ.seven_days_ago" :precision="2"/>
          </el-col>
          <el-col :span="3" class="lineOut">
            <el-statistic title="最近30天提现" :value="txTJ.thirty_days_ago" :precision="2"/>
          </el-col>
          <el-col :span="3" class="lineOut">
            <el-statistic title="最近90天提现" :value="txTJ.ninety_days_ago" :precision="2"/>
          </el-col>
          <el-col :span="3" class="lineOut">
            <el-statistic title="当月提现" :value="txTJ.this_month" :precision="2"/>
          </el-col>
          <!-- <el-col :span="6">
              <el-statistic title="Total Transactions" :value="outputValue" />
          </el-col> -->
        </el-row>
        <div style1="border: 1px solid rgb(143, 143, 143);color:#666;" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
          <div class="inline-flex items-center mr-5 ">
            <div class="w-20 flex-none flex justify-end text-sm">用户ID:</div>
            <el-input size="small" class="ml-4" v-model="searchCondition.user_id" placeholder="" />
          </div>

          <div class="inline-flex items-center  mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.status') }}:</div>
            <el-select size="small" class="ml-4 w-53" v-model="searchCondition.status" placeholder="">
              <el-option
                v-for="item in [{ key: 'Successful Withdrawal', value: '提现成功' }, { key: 'Withdrawal Failed', value: '提现失败' }, { key: 'Processing Payment', value: '等待处理' }]"
                :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </div>
          <div class="inline-flex items-center  mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">时间范围:</div>
            <el-date-picker size="small" class="ml-4 searchItem" v-model="searchCondition.date" type="daterange"
              range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" :clearable="false"
              value-format="YYYY-MM-DD" />
          </div>
          <div class="inline-flex items-center  mr-5 mb-2">
            <ElButton class="ml-4" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton class="mr-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="withdrawData"
          style="width: 100%;color: #666666;" :height1="tableHeight" border stripe :cell-class-name="cellClassName">
          <el-table-column show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60" />
          <el-table-column show-overflow-tooltip prop="withdraw_num" label="提现编号" width="150" />
          <el-table-column show-overflow-tooltip prop="mode" label="模式" />
          <el-table-column show-overflow-tooltip prop="req_money" label="提现金额" width="80">
            <template #default="scope">
              {{ scope.row.req_money / 100 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="app_money" label="到账金额" width="80">
            <template #default="scope">
              {{ scope.row.app_money / 100 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="user_id" label="用户ID" width="80" />
          <el-table-column show-overflow-tooltip prop="user_name" label="用户名称" />
          <el-table-column show-overflow-tooltip prop="status" label="状态" width="100">
            <template #default="scope">
              <span v-if="scope.row.status == 'Successful Withdrawal'">提现成功</span>
              <span v-else-if="scope.row.status == 'Withdrawal Failed'">提现失败</span>
              <span v-else-if="scope.row.status == 'Processing Payment'">等待处理</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="bal_money" label="余额" />
          <el-table-column show-overflow-tooltip prop="ret_coin" label="原始余额" />
          <el-table-column show-overflow-tooltip prop="ret_bet" label="原始下注" />
          <el-table-column show-overflow-tooltip prop="buyer_name" label="完成投注/需要投注" :min-width="160">
            <template #default="scope">
              {{ scope.row.task_bet }} / {{ scope.row.need_bet }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="create_time" label="创建时间" width="170">
            <template #default="scope">
              {{ scope.row.create_time }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="modify_time" label="修改时间" width="170">
            <template #default="scope">
              {{ scope.row.modify_time }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="cashapp_tag" label="CashApp标签" :min-width="200"/>
          <el-table-column align="center" show-overflow-tooltip prop="相关提现次数" label="相关提现次数" />
          <el-table-column show-overflow-tooltip label="提现/充值" :min-width="150">
            <template #default="scope">
              <el-tooltip class="box-item" effect="dark" :content="scope.row.相关账号" placement="top">
                {{ scope.row.相关提现金额 / 100 }} / {{ scope.row.相关充值金额 / 100 }}
              </el-tooltip>

            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="note" label="备注" />

          <el-table-column fixed="right" :label="t('userTable.operate')" width="220"
            v-if="checkPermissionApi('允许操作提现')">
            <template #default="scope">
              <div class="flex items-center justify-center" v-if="scope.row.status == 'Processing Payment'">
                <ElButton :disabled="autoFresh" type="success" size="small" @click="handleOper('提现成功', scope.row)"
                  v-if="scope.row.status == 'Processing Payment'">提现成功</ElButton>
                <ElButton :disabled="autoFresh" type="danger" size="small" @click="handleOper('提现失败', scope.row)"
                  v-if="scope.row.status == 'Processing Payment'">提现失败</ElButton>
                <!-- <ElButton type="text" size="small" @click="handleOper('修改', scope.row)" v-if="scope.row.status == 'Processing Payment'">修改</ElButton> -->
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />


        <el-dialog title="提现" v-model="showEdit" width="700" align-center destroy-on-close>
          <el-form>
            <el-form-item class="titleShow" label="用户ID:">
              {{ selItem.user_id }}
            </el-form-item>
            <el-form-item class="titleShow" label="用户昵称:">
              {{ selItem.user_name }}
            </el-form-item>
            <el-form-item class="titleShow" label="模式:">
              {{ selItem.mode }}
            </el-form-item>
            <el-form-item class="titleShow" label="CashApp标签:">
              <el-input v-model="selItem.cashapp_tag" placeholder="请输入CashApp标签"></el-input>
            </el-form-item>
            <el-form-item class="titleShow" label="提现金额:">
              <el-input v-model="selItem.req_money" placeholder="请输入提现金额"></el-input>
            </el-form-item>

          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="showEdit = false">取消</el-button>
              <el-button type="danger" @click="handleOper('确定修改', selItem)">{{ selItem.id == undefined ? '新增' : '修改'
                }}</el-button>
            </div>
          </template>
        </el-dialog>
      </div>
    </div>
    <!-- <audio ref="audioPlayer" controls>
      <source src="@/assets/notify.mp3" type="audio/mpeg">     
      您的浏览器不支持音频播放
    </audio> -->

  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
  color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
  /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
  border: 1px solid #ccc;
  /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
  padding: 10px;
}

:deep(.el-form-item__label) {
  width: 120px;
  /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
  text-align: right;
  /* 让文本右对齐，使标题看起来更整齐 */
  padding-right: 10px;
  /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

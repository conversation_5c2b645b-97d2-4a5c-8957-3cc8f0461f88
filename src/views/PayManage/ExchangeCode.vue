<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElInputNumber,ElDialog, ElForm, ElFormItem, ElTag, ElCol, ElStatistic, ElIcon, ElMessage, ElMessageBox, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, textProps } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi, delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { useTransition } from '@vueuse/core'
import { addExchangeCodeApi, addPayUrlApi, delExchangeCodeApi, delPayUrlApi, getExchangeCodeList<PERSON>pi, getWithdrawListApi, getWithdrawTJApi, resetAccountOrder, updateExchangeCodeApi, updatePayUrlApi, updateWithdrawApi } from '@/api/laba';
import { cloneDeep } from 'lodash-es';
import { getHashApi, getHashSysListApi, getJiangchiListApi, setHashApi, setHashSyspi, updateJiangchiApi } from '@/api/extra';
import { useAppStore } from '@/store/modules/app'
import { useCache } from '@/hooks/web/useCache'

const appStore = useAppStore()
const { wsCache } = useCache()
const { push } = useRouter()
const { t } = useI18n()


//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})

//配置列表
const exchangeCodeData = reactive([])

const getExchangeCodeList = async (page = 1) => {
    searchCondition.page = page
    let ret = await getExchangeCodeListApi(searchCondition)
    if (ret) {
        console.log(ret)
        if (ret.data) {
            exchangeCodeData.splice(0, exchangeCodeData.length, ...ret.data)
            totleCount.value = parseInt(ret.count)
        }
    }
}


//更新表高度
const updateTableHeight = () => {
    if (userTableRef.value && rootRef.value) {
        tableHeight.value = rootRef.value.clientHeight - 400
    }
}
//浏览器大小变化
const handleWindowResize = () => {
    updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {

}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getExchangeCodeList(val)
}
//创建新客户
const onAddCustmer = () => {

}

//处理表格对象操作
const handleOper = async (type, row) => {
    console.log(row)
    if (type == '确定') {
        if(row.cost == '') {
            ElMessage({
                type:'error',
                message: '兑换金额不能为空',
            });
            return
        }
        if(row.expiry_at == '') {
            ElMessage({
                type:'error',
                message: '有效期不能为空',
            })
            return
        }

        showEdit.value = false
        const info = wsCache.get(appStore.getUserInfo)
        row.agent_id = info.six_id
        const ret = await addExchangeCodeApi(row)
        if (ret) {
            getExchangeCodeList();

            ElMessage({
                type: 'success',
                message: '操作成功',
            });
        }

    }
    else if (type == '删除') {
        ElMessageBox.confirm(
            '是否确认删除该兑换码？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {

                const ret = await delExchangeCodeApi(row)
                if (ret) {
                    getExchangeCodeList();

                    ElMessage({
                        type: 'success',
                        message: '操作成功',
                    });
                    showEdit.value = false;

                }


            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: '操作取消',
                })

            })
    }
}


onMounted(() => {
    updateTableHeight(); // 首次设置表格高度
    window.addEventListener('resize', handleWindowResize);
    getExchangeCodeList()

})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
    window.removeEventListener('resize', handleWindowResize);
});


const showEdit = ref(false)
const selItemdef = {
    cost:1,
    expiry_at:'',
    agent_id:'',
    region:'美东',
    mode:'个人',
}
const selItem = reactive({
    ...selItemdef,
    reset() {
        for (let key in this) {
            if (this.hasOwnProperty(key) && !(key in selItemdef) && key != 'reset') {
                delete this[key];
            }
        }
        Object.assign(this, selItemdef)
    }
})

const onAddConfig = () => {
    showEdit.value = true
    selItem.reset()
}

const onCopyCode = (row) => {
    const input = document.createElement('input');
    input.value = row.code_sn;
    document.body.appendChild(input);
    input.select();
    document.execCommand('Copy');
    document.body.removeChild(input);
    ElMessage.success('复制成功')
}

</script>

<template>
    <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px] ">
        <div ref="rootRef" class="flex relative">
            <div class="absolute top-8 left-8">
                <ElButton type="success" @click="onAddConfig">
                    <Icon icon="fluent-mdl2:people-add" />
                    <div class="pl-2">{{ t('button.add') }}</div>
                </ElButton>
                <ElButton type="warning" @click="getExchangeCodeList">
                    刷新
                </ElButton>
            </div>
            <div class="h-[100%] bg-white p-7 w-[100%]" style="color:#666666">
                <div class="text-center mb-5 font-bold" style="color:#333">兑换码管理</div>
                <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="exchangeCodeData"
                    style="width: 100%;color: #666666;" :height1="tableHeight" border stripe>
                    <el-table-column show-overflow-tooltip prop="id" label="ID" />
                    <el-table-column show-overflow-tooltip prop="code_sn" label="兑换码" >
                        <template #default="scope">
                            <div class="nameStyle" @click="onCopyCode(scope.row)">{{scope.row.code_sn}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="cost" label="金额" />
                    <el-table-column show-overflow-tooltip prop="created_at" label="创建时间" />
                    <el-table-column show-overflow-tooltip prop="expiry_at" label="到期时间" />
                    <el-table-column show-overflow-tooltip prop="status" label="状态" >
                        <template #default="scope">
                            
                            <el-tag v-if="scope.row.status == '待使用'" type="success">
                                {{ scope.row.status }}
                            </el-tag>
                            <el-tag v-if="scope.row.status == '已使用'" type="info">
                                {{ scope.row.status }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="user_id" label="玩家" />
                    <el-table-column show-overflow-tooltip prop="agent_id" label="代理" />
                    <el-table-column show-overflow-tooltip prop="region" label="时区" />
                    <el-table-column show-overflow-tooltip prop="mode" label="类型" />
                    <el-table-column fixed="right" :label="t('userTable.operate')" width="120">
                        <template #default="scope">
                            <div class="flex items-center justify-center">
                                <ElButton type="danger" size="small" @click="handleOper('删除', scope.row)">删除</ElButton>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination class="mt-8 flex justify-end" v-model:current-page="searchCondition.page"
                    v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                    layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </div>


        <el-dialog title="新增" v-model="showEdit" width="700" align-center destroy-on-close>
            <el-form>
                <el-form-item class="titleShow" label="金币:">
                    <ElInputNumber v-model="selItem.cost" placeholder="请输入金币数量" :min="1" :max="20000" />
                </el-form-item>
                <el-form-item class="titleShow" label="有效期:">
                    <el-date-picker v-model="selItem.expiry_at" type="date" placeholder=""
                    format="YYYY/MM/DD" value-format="YYYY-MM-DD" :clearable="false" />
                </el-form-item>
                <el-form-item  class="titleShow" label="时区:">
                    <el-select class="w-53" v-model="selItem.region" placeholder="">
                        <el-option v-for="item in ['美东','中国']" :key="item"
                            :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item  class="titleShow" label="类型:">
                    <el-select class="w-53" v-model="selItem.mode" placeholder="">
                        <el-option v-for="item in ['群体','个人']" :key="item"
                            :label="item" :value="item" />
                    </el-select>
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="showEdit = false">取消</el-button>
                    <el-button type="danger" @click="handleOper('确定', selItem)">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    cursor: pointer;
    color: #00BA80;
}

:deep(.el-row.text-center.el-col) {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
    /* 添加一些内边距，让内容看起来更舒适，可按需调整 */
}

.lineOut {
    border: 1px solid #ccc;
    /* 设置边框样式，这里是1px宽的灰色实线边框，你可以根据需求修改颜色等样式 */
    padding: 10px;
}

:deep(.el-form-item__label) {
    width: 120px;
    /* 设置一个固定的宽度值，你可以根据实际需求调整这个值 */
    text-align: right;
    /* 让文本右对齐，使标题看起来更整齐 */
    padding-right: 10px;
    /* 可以添加一些右边的内边距，让内容和输入框之间有一定间隔 */
}
</style>

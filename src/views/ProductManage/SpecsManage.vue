<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElButton, ElTable, ElTableColumn,ElMessageBox, ElInput, ElForm,ElFormItem,ElMessage,ElCheckbox,ElDropdown,ElDropdownItem,ElDropdownMenu,ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getSpecListApi,addSpecApi,updateSpecApi,delSpesApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { Dialog } from '@/components/Dialog'

const { push } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  id:'',
  name:'',
  page: 1,
  count: 15
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//规格数据源
const specsData = reactive([])
//是否显示规格编辑窗口
const showModifySpecs = ref(false)
//规格编辑窗口标题
const showTitle = ref('')
//规避编辑界面缓存数据
const defaultSpecsData = {
  id:'',
  name:'',
  text:'',
  json:[]
}
const specsDataCache = reactive({
  ...defaultSpecsData,
  reset() {
    Object.assign(this, defaultSpecsData)
  }
})

//查询规格数据
const getSpecsData = async()=>{
  const ret = await getSpecListApi(searchCondition)
  if(ret)
  {
    console.log(ret)
    specsData.splice(0,specsData.length,...ret.data)
    totleCount.value = parseInt( ret.count)
  }
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getSpecsData()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 300
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getSpecsData()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getSpecsData()
}
//创建新规格
const onAddSpecs = ()=>{
  showModifySpecs.value = true
  showTitle.value = t('specs_manage.add')
  specsDataCache.reset()
}

const onSaveSpecs = async()=>{
  if(specsDataCache.id == '') //新增
  {
    const ret = await addSpecApi(specsDataCache)
    if(ret)
    {
      getSpecsData()
      showModifySpecs.value = false
    }
  } 
  else //修改
  {
    const ret = await updateSpecApi(specsDataCache)
    if(ret)
    {
      getSpecsData()
      showModifySpecs.value = false
      ElMessage.success(t('msg.success'))
    }
  }
}

//处理表格对象操作
const handleOper = async(type,row) => {
  console.log(row)
  if(type==='edit'){
    Object.assign(specsDataCache,row)
    showModifySpecs.value = true
    showTitle.value = t('specs_manage.modify')
  }
  else if(type==='del'){

    //确认是否删除规格
    const confirmRes = await ElMessageBox.confirm(t('msg.confirm_del_spec'), t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error'
    }).catch(()=>{
      
    })
    if(confirmRes !== 'confirm')
      return

    //删除规格
    const ret = await delSpesApi({
      ids:[row.id]
    })
    if(ret)
    {
      //提示操作成功
      ElMessage.success(t('msg.delOK'))
      getSpecsData()
    }
  }
}


onMounted(()=>{
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //刷新表格
  getSpecsData()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});
</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
    <div class="absolute top-5 left-10">
      <ElButton type="success" @click="onAddSpecs">
        <Icon icon="carbon:document-add" />
        <div class="pl-2">{{ t('button.add') }}</div>
      </ElButton>
      <ElButton color="#409EFF" plain>
        <Icon icon="clarity:import-line" />
        <div class="pl-2">{{ t('button.import') }}</div>
      </ElButton>
      <ElButton color="#409EFF" plain>
        <Icon icon="carbon:export" />
        <div class="pl-2">{{ t('button.export') }}</div>
      </ElButton>
    </div>
    <div  class="h-[100%] bg-white p-7">
      <div class="text-center mb-5 font-bold">{{ t('specs_manage.manage') }}</div>
      <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('specs_manage.id') }}</div>
          <el-input v-model="searchCondition.id" placeholder="" class="w-200px"/>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('specs_manage.name') }}</div>
          <el-input v-model="searchCondition.name" placeholder="" class="w-200px"/>
        </div>
        
        <div  class="flex justify-end items-center mr-6 mt-8 mb-2">
          <ElButton type="primary" @click="onSearch">
            <Icon icon="ri:phone-find-line" />
            <div class="pl-2">查询</div>
          </ElButton>
          <ElButton type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="pl-2">清除</div>
          </ElButton>          
        </div>
      </div>
      <el-table ref="userTableRef" header-cell-class-name="tableHeader" :data="specsData" style="width: 100%"
        :height="tableHeight" border stripe>
        <el-table-column   prop="id" :label="t('userTable.id')" width="60" />
        <el-table-column   prop="name" :label="t('specs_manage.name')" width="250" />
        <el-table-column   prop="text" :label="t('specs_manage.content')" min-width="250"/>
        <el-table-column   prop="modify_date" :label="t('specs_manage.last_time')" width="200" />
        <el-table-column fixed="right" :label="t('userTable.operate')" width="90">
          <template #default="scope">
            <el-dropdown trigger="click" placement="bottom">
              <span class="el-dropdown-link">
                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleOper('edit', scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('research', scope.row)">{{ t('specs_manage.search_prod') }}</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="flex justify-end mt-4 mb-4"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[15, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>


     <!-- 编辑规格窗口 -->
     <Dialog v-model="showModifySpecs" :title="showTitle" >
      <el-form ref="form" :model="specsDataCache" label-width="100px">
        <el-form-item v-if="specsDataCache.id == ''?false:true" :label="t('specs_manage.id')">
          <div>{{ specsDataCache.id }}</div>
        </el-form-item>
        <el-form-item :label="t('specs_manage.name')">
          <el-input v-model="specsDataCache.name" class="!w-[50%]"/>
        </el-form-item>
        <el-form-item :label="t('specs_manage.content')">
          <el-input v-model="specsDataCache.text" :autosize="{ minRows: 10, maxRows: 10 }"   type="textarea"/>
        </el-form-item>
      </el-form>

        <template #footer>
          <ElButton type="primary" @click="onSaveSpecs">
            {{ t('msg.ok') }}
          </ElButton>
          <ElButton @click="showModifySpecs = false">{{ t('common.channel') }}</ElButton>
        </template>
      </Dialog>



  </div>
</template>

<style lang="less" scoped>
// :deep(.current-row) {
//   background-color: #ffe48d !important;
//   --el-table-current-row-bg-color: #ffe48d !important;
//   --el-table-row-hover-bg-color: #ffe48d;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 120px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}
</style>

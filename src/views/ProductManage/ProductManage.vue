<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElUpload,ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getCategListApi, addCategApi, updateCategApi, delCategApi,getProductListApi, updateProductApi,delProductApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { RightMenu } from '@/components/RightMenu'
import { Dialog } from '@/components/Dialog'
import { checkPermissionApi, closeOneTagByPath, decodeString, downloadFile } from '@/api/tool'
import { computed,nextTick } from 'vue';
import { useCache } from '@/hooks/web/useCache'
import { DialogProduct } from '@/components/DialogProduct';
import { DialogFileList } from "@/components/DialogFileList";
import { DialogBomStruct } from '@/components/DialogBomStruct'
import { getOssSignApi, ossUpload } from '@/api/oss';
import { useAppStore } from '@/store/modules/app'
import { importPdtApi } from  '@/api/task'
import { DialogImportTask } from '@/components/DialogImportTask'
import { exportPdtListApi } from '@/api/extra';
import { cloneDeep } from 'lodash-es';

const appStore = useAppStore()
const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

//分类树
const categTree = ref()
//当前选中分类节点
const currentCatg = ref('')
//当前选中分类节点详细信息
const currentCatgData = ref({})
//分类树数据源
const categData = reactive([])
//分类树默认属性
const defaultProps = {
  children: 'sub_categ',
  label: 'name',
}
//分类树默认展开节点
const expandCateg = reactive([])
//分类树右键菜单classname
const categMenuClassName = 'categ_menu'
//分类右键菜单
const menuCateg = reactive([
  {
    icon: "icon-park-outline:add",
    name: 'add_product',
    title: t('product_manage.add_product')
  },
  {
    icon: "icon-park-outline:add",
    name: 'categ_param_set',
    title: t('product_manage.categ_param_set')
  },
  {
    icon: '',
    name: '分割线',
    title: ''
  },
  {
    icon: "icon-park-outline:add",
    name: 'add_categ',
    title: t('product_manage.add_categ')
  },
  {
    icon: "icon-park-outline:add",
    name: 'update_categ',
    title: t('product_manage.update_categ')
  },
  {
    icon: "icon-park-outline:add",
    name: 'delete_categ',
    title: t('product_manage.delete_categ')
  },
])
//分类编码器设置窗口控制
const showEncoderWin = ref(false)
//编码器设置
const categEncoderSet = reactive(['关闭', '', 1, 8])

//产品数据源
const productData = reactive([])

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  categ:'',
  nick:'',
  name:'',
  specs:'',
  brand:'',
  status:'',
  nick_brif:'',
  note:'',
  buy_price_begin:'',
  buy_price_end:'',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//查询产品分类树
const getCategList = async () => {
  const ret = await getCategListApi({
    page: 1,
    count: 10000
  })
  if (ret) {
    console.log(ret)
    console.log('当前选中')

    //设置默认选中
    const lastSelCateg = wsCache.get('last_sel_categ') || {}
    console.log(categTree.value)
    console.log(lastSelCateg.id)
    currentCatgData.value = lastSelCateg
    currentCatg.value = lastSelCateg.id
    searchCondition.categ = lastSelCateg.id == undefined ? '' : lastSelCateg.id
    if(lastSelCateg.id != undefined)
    {
      nextTick(()=>{
        categTree.value.setCurrentNode(lastSelCateg)
        console.log(categTree.value?.getCurrentNode())
      })
    }


    categData.splice(0, categData.length, ret.data.all_categs)
    //设置默认展开
    expandCateg.splice(0, expandCateg.length, ret.data.all_categs.id)
    if (currentCatg.value) {
      expandCateg.push(currentCatg.value)
    }
  }
}
//分类树点击左键
const leftClick = (data) => {
  currentCatgData.value = data
  searchCondition.categ = data.id
  //更新最后一次选中
  wsCache.set('last_sel_categ',data)
  getProductList()
}

//分类树点击右键菜单
const rightClick = (event, data) => {
  currentCatg.value = data.id
  currentCatgData.value = data
  //隐藏之前的菜单
  hideRightMenu()

  //如果是未分组人员行不允许弹出菜单
  if (data.name == '未分组人员') {
    event.preventDefault();
    return
  }


  const menu = document.querySelector("#" + categMenuClassName) as HTMLElement;
  if (!menu) return;

  event.preventDefault();
  menu.style.left = `${event.pageX - 200}px`;
  menu.style.top = `${event.pageY - 200}px`;
  menu.style.display = "block";
  menu.style.zIndex = "1000";

  document.addEventListener("click", hideRightMenu);
}
//隐藏右键菜单
const hideRightMenu = () => {
  const menu = document.querySelector("#" + categMenuClassName) as HTMLElement;
  menu!.style.display = "none";
  document.removeEventListener("click", hideRightMenu);
}
//处理分类树菜单消息
const handleMenuEvent = (item) => {
  console.log(item)
  hideRightMenu()
  //添加/修改分类
  if (item === 'add_categ' || item === 'update_categ') {
    ElMessageBox.prompt(t('msg.inputCategName'), t('title.notify'),
      {
        confirmButtonText: t('button.ok'),
        cancelButtonText: t('button.cancel'),
        inputErrorMessage: t('msg.inputCategName'),
        inputValidator: (val) => {
          if (val === null || val.length < 1) {
            return false;
          }
        }
      })
      .then(async ({ value }) => {
        if (item === 'add_categ') {
          const ret = await addCategApi({
            parent_id: currentCatg.value,
            name: value,
            note: ''
          })
          if (ret) {
            getCategList()
            ElMessage({
              type: 'success',
              message: t('msg.success'),
            })
          }
        }
        else {
          const ret = await updateCategApi({
            ids: [currentCatg.value],
            name: value,
            note: ''
          })
          if (ret) {
            getCategList()
            ElMessage({
              type: 'success',
              message: t('msg.success'),
            })
          }
        }


      })
  }
  //删除分类
  else if (item === 'delete_categ') {
    ElMessageBox.confirm(
      t('msg.delCateg'),
      'Warning',
      {
        confirmButtonText: t('button.ok'),
        cancelButtonText: t('button.cancel'),
        type: 'warning',
      }
    )
      .then(async () => {
        const ret = await delCategApi({
          ids: [currentCatg.value],
        })
        if (ret) {
          getCategList()
          ElMessage({
            type: 'success',
            message: t('msg.success'),
          })
        }



      })
  }
  //设置分类参数
  else if (item === 'categ_param_set') {
    showEncoderWin.value = true
    categEncoderSet.splice(0, categEncoderSet.length, ...currentCatgData.value.pdt_num_role)
    console.log(categEncoderSet)
  }
  else if(item === 'add_product')
  {
    onAddProduct()
  }
}
//计算编码展示样例
const categEncodeDemo = computed(() => {
  return categEncoderSet[1] + '0'.repeat(categEncoderSet[3])
})

//修改分类编码器格式
const onUpdateCategEncode = async () => {
  showEncoderWin.value = false
  const ret = await updateCategApi({
    ids: [currentCatgData.value.id],
    pdt_num_role: [...categEncoderSet],
    pdt_def_types: [...currentCatgData.value.pdt_def_types],
    pdt_def_digits: [...currentCatgData.value.pdt_def_digits],
  })
  if (ret) {
    ElMessage({
      type: 'success',
      message: t('msg.success'),
    })
    getCategList()
  }
}


//开始查询
const onSearch = () => {
  getProductList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

const nCurPage = ref(1)
//page控件发生切换
const handleSizeChange = (val: number) => {
  nCurPage.value = val
  getProductList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  nCurPage.value = val
  getProductList(val)
}
//处理表格对象操作
const handleOper = (type, item) => {
  //编辑产品
  if(type === 'edit' || type === 'info')
  {
    // push({
    //   path: '/productmanage/addproduct',
    //   query:{
    //       id:item.id,
    //       categ: JSON.stringify(currentCatgData.value),
    //       type:type
    //   }
    // })
    curID.value = item.id
    curType.value = type
    curCatag.value = JSON.stringify(currentCatgData.value)
    onShowAddProduct()
  }
  else if(type === 'stop') //停用
  {
    ElMessageBox.confirm(t('msg.confirm_stop')+'--> '+item.nick, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'warning',
    }
    ).then(async () => {
      console.log(item)
      item.status = '停用'
      const ret = await updateProductApi(item)
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getProductList()
      }
    }
    ).catch(() => {})
  }
  else if(type === 'start') //启用
  {
    ElMessageBox.confirm(t('msg.confirm_start')+'--> '+item.nick, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'warning',
    }
    ).then(async () => {
      console.log(item)
      item.status = '正常'
      const ret = await updateProductApi(item)
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getProductList()
      }
    }
    ).catch(() => {})
  }
  else if(type === 'del') //启用
  {
    ElMessageBox.confirm(t('msg.confirm_del')+'--> '+item.nick, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const ret = await delProductApi({
        ids: [item.id],
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getCategList()
        getProductList()
      }
    }
    ).catch(() => {})
  }
  else if(type === 'BOM反查')
  {
    push({
      path: '/productmanage/pdtbommanage',
      query:{
        wl_name:item.name
      }
    })
  }
  else if(type === 'copy') //复制产品
  {
    closeOneTagByPath('/productmanage/addproduct')
    push({
    path: '/productmanage/addproduct',
    query:{
        id:'',
        categ: JSON.stringify(currentCatgData.value),
        copy_id:item.id
    },
    
  })
  }
}

//显示进入新增产品界面
const curID = ref('')
const curType = ref('')
const curCatag = ref('{}')
const showAddProduct = ref(false)
const onShowAddProduct = ()=>{
  showAddProduct.value = true
}



//进入新增产品界面
const onAddProduct = ()=>{
  push({
    path: '/productmanage/addproduct',
    query:{
        id:'',
        categ: JSON.stringify(currentCatgData.value),
    },
    
  })
  // curID.value = ''
  // curType.value = ''
  // curCatag.value = JSON.stringify(currentCatgData.value)
  // onShowAddProduct()
}

//查询产品列表
const getProductList = async (page = 1) => {
  const lastSelCateg = wsCache.get('last_sel_categ') || {}
  searchCondition.categ = lastSelCateg.id
  searchCondition.page = nCurPage.value
  const ret = await getProductListApi(searchCondition)
  if(ret)
  {
    productData.splice(0,productData.length, ...ret.data)
    for(let pdt of productData)
    {
      //保护字段
      if(pdt.file_list == undefined)
      {
          pdt.file_list = []
      }
    }
    console.log(productData)
    totleCount.value = parseInt(ret.count)
  }
}

onMounted(() => {

  //更新分类树
  getCategList()

  //更新产品列表
  getProductList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {

});




//--------------中间拖拽调整大小-------------
const isResizing = ref(false);
const startX = ref(0);
const leftWidth = ref(15);
const rightWidth = ref(85);

const startResize = (event: MouseEvent) => {
  event.preventDefault(); // 阻止默认行为
  isResizing.value = true;
  startX.value = event.clientX;

  document.addEventListener('mousemove', resize);
  document.addEventListener('mouseup', stopResize);
};

const resize = (event: MouseEvent) => {
  if (isResizing.value) {
    event.preventDefault(); // 阻止默认行为
    const deltaX = event.clientX - startX.value;
    leftWidth.value += (deltaX / window.innerWidth) * 100;
    rightWidth.value -= (deltaX / window.innerWidth) * 100;
    startX.value = event.clientX;
  }
};

const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener('mousemove', resize);
  document.removeEventListener('mouseup', stopResize);
};


const getPdtStatus = (pdt)=>{
  if(pdt.status === '停用')
    return 'pdt_stop'
  else
    return ''
}


//显示文件列表
const curSelItem = ref({id:'0',bom_id:'0',file_list:[]})
const curSelFileType = ref('文件')
const showFileList = ref(false)
const onShowFileList = (item,type)=>{
  showFileList.value = true
  console.log(item)
  curSelItem.value = item
  curSelFileType.value = type 
}
const onUpdateFileList = async(list)=>{
  if(curSelFileType.value === '文件')
  {
    curSelItem.value.file_list = [...list]
  }

  //更新文件列表
  const ret =await updateProductApi(curSelItem.value)
  if(ret)
  {
      ElMessage.success('更新文件成功！')
      getProductList()
  }
}


//显示BOM结构图
const showBomStruct = ref(false)
const onShowBomStruct = (item)=>{
    curSelItem.value = item
    if(curSelItem.value.bom_id == "")
    {
        ElMessage.warning("当前产品没有创建BOM")
        return
    }
    

    push({
      path: '/productmanage/showbomstructpdt',
      query:{
      bomid:curSelItem.value.bom_id,
      nick:curSelItem.value.nick
    }})
    

}
//产品导入
const onImportPdt = ()=>{

}
//正在上传
const bShowTaskDlg = ref(false)
const onShowTaskDlg = async()=>{
  bShowTaskDlg.value = true
}
const loading = ref(false)
const uploadImg = async(file) => {
  const info = wsCache.get(appStore.getUserInfo)
  console.log(info)

    let path = 'import/'+info.username+'/'
    
    const ret =await getOssSignApi({upload_dir:path})
    if(ret)
    {    
        loading.value = true
        const end = await ossUpload(ret.data.token,file.file,path,(pro)=>{
            console.log('pppp',pro)
            
        })  
        loading.value = false   
        // fileData.push({
        //     name:file.file.name,
        //     url:end.url
        // })
        //上传完成调用导入
        const imp = await importPdtApi({
          url:end.url
        })
        if(imp)
        {
            ElMessage.success("上传文件成功，等待系统导入！")
            console.log('上传完成',end.url)
            onShowTaskDlg()
        }

    }
}




const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    const tmp = cloneDeep(searchCondition)


    const ret = await exportPdtListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}
</script>

<template>
  <div ref="rootRef" class="flex absolute top-5 right-5 left-5 bottom-5">
    <!-- 左侧分类栏 -->
    <div class="bg-white p-2 overflow-y-auto" :style="{ width: leftWidth + '%'}" >
      <el-tree class="h-[100%] overflow-x-scroll" ref="categTree" :data="categData" :props="defaultProps" :default-expanded-keys="expandCateg" node-key="id"
        @node-contextmenu="rightClick" @node-click="leftClick" highlight-current :current-node-key="currentCatg"
        :expand-on-click-node="false" :render-after-expand="true">
        <template #default="{ node }">
          <Icon icon="bx:category" />
          <div class="pl-2">{{ node.data.name + (node.data.total_product_count > 0 ? (" " + node.data.total_product_count) : '') }}
          </div>
        </template>
      </el-tree>
      <RightMenu :id="categMenuClassName" :menuDate="menuCateg" @on-menu-event="handleMenuEvent" />
    </div>
    <!-- <div class="w-2"></div> -->
    <div class="drag w-2  bg-gray-100 " style="cursor: col-resize;" @mousedown="startResize"></div> 
    <!-- 右侧产品列表 -->
    <div  class="relative !bg-white overflow-y-auto" :style="{ width: rightWidth + '%'}">
      <div class="absolute top-6 left-10 flex z-1">
        <ElButton class="mr-3" type="success" @click="onAddProduct">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
        <!-- <ElButton color="#409EFF" plain @click='onImportPdt'>
          <Icon icon="clarity:import-line" />
          <div class="pl-2">{{ t('button.import') }}</div>
        </ElButton> -->
        <el-upload
        class='mr-3'
        :http-request="(file) => uploadImg(file)"
        :auto-upload="true"
        :show-file-list = 'false'
        >
          <template #trigger>
            <el-button color="#409EFF" plain :loading="loading" :disabled="loading">
              <Icon icon="clarity:import-line" />
              {{ loading?'上传中..':'导入' }} 
            </el-button>
          </template>
        </el-upload>
        <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
            <Icon icon="carbon:export" />
            <div class="pl-2">{{ t('button.export') }}</div>
        </ElButton>
      </div>
      <div class="h-[100%] bg-white p-7" style="overflow-x:hidden;">
        <div class="text-center mb-5 font-bold">{{ t('product_manage.product_manage') }}</div>
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-6 mb-2">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input v-model="searchCondition.nick" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-6 mb-2">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input v-model="searchCondition.name" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-6 mb-2">
            <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
            <el-input v-model="searchCondition.specs" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-6 mb-2">
            <div class="searchTitle">{{ t('product_manage.brand') }}</div>
            <el-input v-model="searchCondition.brand" placeholder="" size="small" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-6 mb-2">
            <div class="searchTitle">{{ t('product_manage.status') }}</div>
            <el-select class="searchItem" v-model="searchCondition.status" placeholder="请选择" size="small" >
              <el-option v-for="item in ['正常','停用']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-if="senior" class="inline-flex items-center ml-6 mb-2">
            <div class="searchTitle">{{ t('product_manage.short_name') }}</div>
            <el-input v-model="searchCondition.nick_brif" placeholder="" size="small" class="searchItem" />
          </div>
          <div v-if="senior" class="inline-flex items-center ml-6 mb-2">
            <div class="searchTitle">{{ t('product_manage.help_name') }}</div>
            <el-input v-model="searchCondition.note" placeholder="" size="small" class="searchItem" />
          </div>

          <div v-if="senior" class="inline-flex items-center ml-6 mb-2">
            <div class="searchTitle">{{ t('product_manage.buy_price') }}</div>
            <el-input v-model="searchCondition.buy_price_begin" placeholder="" size="small" class="!w-[60px]" />
            <div class="searchTitle !w-32px">到</div>
            <el-input v-model="searchCondition.buy_price_end" placeholder="" size="small" class="!w-[60px]" />
          </div>
          <div  class="flex justify-end items-center mr-6 mt-8 mb-2">
            <el-checkbox  :label="t('customer.senior')" v-model="senior" size="small"/>
            <ElButton class="ml-5" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 产品列表 -->
        <div>
            <!-- 表头 -->
            <div class="flex header headerBk">
              <div class="w-[5%] !min-w-[50px] font-bold">全选</div>
              <div class="w-[25%] !min-w-[250px] font-bold">产品名称</div>
              <div class="w-[15%] min-w-[150px] font-bold">规格</div>
              <div class="w-[10%] min-w-[100px] font-bold">简称</div>
              <div class="w-[10%] min-w-[100px] font-bold">助记码</div>
              <div class="w-[5%] min-w-[60px] font-bold">单位</div>
              <div class="w-[10%] min-w-[120px] font-bold">所属分类</div>
              <div class="w-[10%] !min-w-[150px] font-bold">价格</div>
              <div class="w-[10%] min-w-[60px] font-bold">状态</div>
              <div class="w-[10%] min-w-[80px] font-bold">操作</div>
            </div>
            <!-- 表内容 -->
            <div class="mt-2" v-for="item in productData" :key="item.id">
              <!-- 内容头 -->
              <div>
                <div class="bg-light-500 p-2 flex flex-nowrap text-[13px]">
                  <div class="w-[35%] min-w-[300px]">产品编号 {{ item.name }}</div>
                  <div class="flex justify-center items-center min-w-[300px]">
                    <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin-right: 5px;" icon="solar:file-bold" />
                    <div class="cursor-pointer" @click="onShowFileList(item,'文件')">文件({{ item.file_list.length }})</div>
                    <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin:0 5px 0 50px;" icon="mdi:material" />
                    <div class="cursor-pointer" @click="onShowBomStruct(item)">BOM  ({{ item.bom_id==''?' 0 ':' 1 ' }})</div>
                  </div>
                  <!-- 靠右的其他信息 -->
                  <div class="ml-auto flex justify-center items-center min-w-[400px]">
                    <div>
                      最后编辑时间: {{ item.modify_date }}
                    </div>
                    <!-- <Icon style="scale: 1.1; margin:0 5px 0 50px;" icon="ic:baseline-qrcode" />
                    <Icon style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);" icon="material-symbols:print-outline" />
                    <Icon style="scale: 1.1; margin:0 5px 0 10px;" icon="uil:copy" /> -->
                  </div>
                </div>
              </div>
              <!-- 内容体 -->
              <div class="flex content">
                <!-- 产品名 -->
                <div class="w-[30%] !min-w-[300px]" :class="getPdtStatus(item)">
                  <div class="flex justify-start items-start w-[100%]">
                    <el-image v-if="item.pics.length>0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" :src="item.pics[0].url" />
                    <el-image v-if="item.pics.length<=0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" src="/nopic.jpg" />
                    <div class="ml-2 inline-block text-left max-w-[100%]">
                      <div style="white-space: normal;" class="nameStyle" @click="handleOper('info', item)">{{ item.nick }}</div>
                    </div>
                  </div>
                </div>
                <!-- 规格 -->
                <div class="w-[15%] min-w-[150px]" :class="getPdtStatus(item)">
                  <div v-if="item.specs != ''" class="inline-block text-left max-w-[100%]" style="white-space: normal;">        
                    <el-tooltip
                      v-if="item.specs_name!='自定义规格'"
                      class="box-item"
                      effect="dark"
                      :content="item.specs_text"
                      placement="bottom"
                    >
                      <el-tag style="white-space: normal;" type="success" effect="dark">{{ item.specs_name=='自定义规格'?item.specs_text:item.specs_name }}</el-tag>
                    </el-tooltip>
                    <div v-if="item.specs_name=='自定义规格'" style="font-size: 12px;">{{item.specs_text}}</div>
                  </div>
                </div>
                <div class="w-[10%] min-w-[100px]" :class="getPdtStatus(item)">{{ item.nick_brif }}</div>
                <div class="w-[10%] min-w-[100px]" :class="getPdtStatus(item)">{{ item.note }}</div>
                <div class="w-[5%] min-w-[60px]" :class="getPdtStatus(item)">{{ item.base_unit }}</div>
                <div class="w-[10%] min-w-[120px]" :class="getPdtStatus(item)">{{ item.categ_name }}</div>
                <div class="w-[10%] !min-w-[150px] flex flex-col items-start !justify-start" :class="getPdtStatus(item)">
                  <div>销售价税前:{{  checkPermissionApi('产品销售价查看')?item.sell_price_bef_tax:'*' }}</div>
                  <div>销售价税后:{{ checkPermissionApi('产品销售价查看')?item.sell_price_aft_tax:'*' }}</div>
                  <div>采购价税前:{{ item.buy_price_bef_tax }}</div>
                  <div>采购价税后:{{ item.buy_price_aft_tax }}</div>
                </div>
                <div class="w-[10%] min-w-[60px]" :class="getPdtStatus(item)">{{ item.status }}</div>
                <div class="w-[10%] min-w-[80px] flex justify-center items-center" :class="getPdtStatus(item)">
                  <el-dropdown trigger="click" placement="bottom">
                    <span class="el-dropdown-link">
                      <ElButton size='small' type="primary">{{ t('userTable.operate') }}</ElButton>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>          
                        <el-dropdown-item @click="handleOper('copy', item)">复制</el-dropdown-item> 
                        <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.detail') }}</el-dropdown-item>              
                        <el-dropdown-item @click="handleOper('edit', item)">{{ t('userOpt.edit') }}</el-dropdown-item>
                        <el-dropdown-item v-if="item.status == '正常'" @click="handleOper('stop', item)">{{ t('product_manage.status_stop') }}</el-dropdown-item>
                        <el-dropdown-item v-if="item.status == '停用'" @click="handleOper('start', item)">{{ t('product_manage.status_start') }}</el-dropdown-item>
                        <el-dropdown-item @click="handleOper('BOM反查', item)">{{ t('product_manage.bom_research') }}</el-dropdown-item>
                        <el-dropdown-item @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
        </div>
        <el-pagination
        class="flex justify-end mt-4 mb-4"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[10, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
          />
        <!-- <div class="h-[300px]"></div> -->
      </div>




      <!-- 设置编码器弹窗 -->
      <Dialog v-model="showEncoderWin" :title="t('title.categ_param')" :max-height="300">
        <div class="flex justify-center flex-col align-middle">
          <div class="flex mb-2">
            <div class="flex" style="align-items: center;">是否打开自动编码</div>
            <el-radio-group v-model="categEncoderSet[0]" class=" ml-5">
              <el-radio label="启用">启用</el-radio>
              <el-radio label="关闭">关闭</el-radio>
            </el-radio-group>
          </div>
          <el-table v-if="categEncoderSet[0] == '启用'" :data="['']" style="width: 100%; margin-bottom: 20px"
            header-cell-class-name="tableHeader" border>
            <el-table-column show-overflow-tooltip fixed prop="id" :label="t('product_manage.categ')">
              <template #default>
                <div>{{ currentCatgData.name }}</div>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed prop="username" :label="t('product_manage.def_header')">
              <template #default="scope">
                <el-input class="!w-[100%]" v-model="categEncoderSet[1]" :placeholder="scope.row" />
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed prop="resident_name" :label="t('product_manage.cur_index')">
              <template #default="scope">
                <el-input class="!w-[100%]" v-model.number="categEncoderSet[2]" :placeholder="scope.row" />
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed prop="resident_name" :label="t('product_manage.show_rule')">
              <template #default>
                <div>{{ categEncodeDemo }}</div>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip fixed prop="resident_name" :label="t('product_manage.tail_len')">
              <el-select v-model="categEncoderSet[3]" placeholder="Select">
                <el-option v-for="item in 10" :key="item" :label="item" :value="item" />
              </el-select>
            </el-table-column>
          </el-table>

          <el-descriptions class="flex-1 mt-2" :column="1" border>
            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle"
              :label="t('product_manage.def_type')">
              <el-checkbox-group v-model="currentCatgData.pdt_def_types">
                <el-checkbox label="销售产品" />
                <el-checkbox label="采购产品" />
                <el-checkbox label="生产产品" />
                <el-checkbox label="半成品" />
                <el-checkbox label="物料" />
                <el-checkbox label="委外产品" />
              </el-checkbox-group>
            </el-descriptions-item>
            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle"
              :label="t('product_manage.price_acc')">
              <el-select v-model="currentCatgData.pdt_def_digits[0]" placeholder="Select">
                <el-option v-for="item in [2, 3, 4, 5, 6]" :key="item" :label="'小数点后' + item + '位'" :value="item" />
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle"
              :label="t('product_manage.unit_acc')">
              <el-select v-model="currentCatgData.pdt_def_digits[1]" placeholder="Select">
                <el-option v-for="item in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]" :key="item" :label="item + '位小数'" :value="item" />
              </el-select>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <template #footer>
          <ElButton type="primary" @click="onUpdateCategEncode">
            {{ t('msg.ok') }}
          </ElButton>
          <ElButton @click="showEncoderWin = false">{{ t('common.channel') }}</ElButton>
        </template>
      </Dialog>
    </div>

    <DialogProduct :id="curID" :type="curType" :categ="curCatag" :exinfo="{}"  :title="t('product_manage.add_product')" v-model:show="showAddProduct" @on-submit="getProductList(1)"/>

    <DialogFileList :path="'file/pdt/'+curSelItem.name+'/'" v-model:show="showFileList" :files="curSelItem.file_list" @on-update="onUpdateFileList"/>

    <DialogBomStruct v-model:show="showBomStruct" :bomid="curSelItem.bom_id"/>
    <DialogImportTask v-model:show="bShowTaskDlg" :mod="'pdt'" :cmd="'import_list'"/>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e1f3d8 !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  color: #333;
}
.headerBk{
  background-color: #fff;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

//解决tree没有横向滚动条问题
:deep(.el-tree>.el-tree-node) {
    display: inline-block;
    min-width: 100%;
  }

.pdt_stop{
  background-color: rgb(224, 224, 224);
}

</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElInputNumber, ElUpload, ElTag, ElButton, ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio, ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted,onBeforeUnmount,watch } from 'vue'
import { useRouter } from 'vue-router'
import { getCategListApi, addCategApi, updateCategApi, delCategApi, getProductListApi, updateProductApi, delProductApi } from '@/api/product'
import { nextTick } from 'vue';
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getHashApi } from '@/api/extra';
import { Inputex } from '@/components/Inputex'
import { DialogDept } from '@/components/DialogDept'
import { InputExNum } from '@/components/InputexNum'

const appStore = useAppStore()
const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

//分类树
const categTree = ref()
//当前选中分类节点
const currentCatg = ref('')
//当前选中分类节点详细信息
const currentCatgData = ref({})
//分类树数据源
const categData = reactive([])
//分类树默认属性
const defaultProps = {
    children: 'sub_categ',
    label: 'name',
}
//分类树默认展开节点
const expandCateg = reactive([])
//产品数据源
const productData = reactive([])

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    categ: '',
    nick: '',
    name: '',
    specs: '',
    brand: '',
    status: '',
    nick_brif: '',
    note: '',
    buy_price_begin: '',
    buy_price_end: '',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//查询产品分类树
const getCategList = async () => {
    const ret = await getCategListApi({
        page: 1,
        count: 10000
    })
    if (ret) {
        console.log(ret)
        console.log('当前选中')

        //设置默认选中
        const lastSelCateg = wsCache.get('last_sel_categ') || {}
        console.log(categTree.value)
        console.log(lastSelCateg.id)
        currentCatgData.value = lastSelCateg
        currentCatg.value = lastSelCateg.id
        searchCondition.categ = lastSelCateg.id == undefined ? '' : lastSelCateg.id
        if (lastSelCateg.id != undefined) {
            nextTick(() => {
                categTree.value.setCurrentNode(lastSelCateg)
                console.log(categTree.value?.getCurrentNode())
            })
        }


        categData.splice(0, categData.length, ret.data.all_categs)
        //设置默认展开
        expandCateg.splice(0, expandCateg.length, ret.data.all_categs.id)
        if (currentCatg.value) {
            expandCateg.push(currentCatg.value)
        }
    }
}
//分类树点击左键
const leftClick = (data) => {
    currentCatgData.value = data
    searchCondition.categ = data.id
    //更新最后一次选中
    wsCache.set('last_sel_categ', data)
    getProductList()
}


//开始查询
const onSearch = () => {
    getProductList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

const nCurPage = ref(1)
//page控件发生切换
const handleSizeChange = (val: number) => {
    nCurPage.value = val
    getProductList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    nCurPage.value = val
    getProductList(val)
}
//处理表格对象操作
const handleOper = (type, item) => {

}

//查询产品列表
const getProductList = async (page = 1) => {
    const lastSelCateg = wsCache.get('last_sel_categ') || {}
    searchCondition.categ = lastSelCateg.id
    searchCondition.page = nCurPage.value
    const ret = await getProductListApi(searchCondition)
    if (ret) {
        selProps.value.splice(0, selProps.value.length)
        productData.splice(0, productData.length, ...ret.data)
        for (let pdt of productData) {
            //保护字段
            if (pdt.file_list == undefined) {
                pdt.file_list = []
            }
            if(pdt.props.length >=6)
            {
                pdt.props_all = true
                selAll.value = true
            }
            else
            {
                pdt.props_all = false
            }

            //如果pdt.props中包含 销售产品
            if (pdt.props.includes('销售产品')) {
                selAll.value = true
                if(!selProps.value.includes('销售产品'))
                {
                    selProps.value.push('销售产品')
                }
            }
            if (pdt.props.includes('采购产品')) {
                selAll.value = true
                if(!selProps.value.includes('采购产品'))
                {
                    selProps.value.push('采购产品')
                }
            }
            if (pdt.props.includes('采购产品')) {
                selAll.value = true
                if(!selProps.value.includes('采购产品'))
                {
                    selProps.value.push('采购产品')
                }
            }
            if (pdt.props.includes('生产产品')) {
                selAll.value = true
                if(!selProps.value.includes('生产产品'))
                {
                    selProps.value.push('生产产品')
                }
            }
            if (pdt.props.includes('半成品')) {
                selAll.value = true
                if(!selProps.value.includes('半成品'))
                {
                    selProps.value.push('半成品')
                }
            }
            if (pdt.props.includes('物料')) {
                selAll.value = true
                if(!selProps.value.includes('物料'))
                {
                    selProps.value.push('物料')
                }
            }
            if (pdt.props.includes('委外产品')) {
                selAll.value = true
                if(!selProps.value.includes('委外产品'))
                {
                    selProps.value.push('委外产品')
                }
            }

        }
        console.log(productData)
        totleCount.value = parseInt(ret.count)
    }
    tableHeight.value = document.getElementById('mainscroll')?.clientHeight-spanH.value

    // setTimeout(()=>{
    //        // 获取 id 为 "mytable" 的 el-table 元素
    //        const table = document.getElementById('mytable');

    //     // 在该 el-table 元素下查找所有的 input 元素
    //     const inin = table.querySelectorAll('input');
    //     console.log('找到',inin)
    //     // 遍历所有的 input 元素
    //     inin.forEach(input => {
    //     // 在这里可以对 input 元素进行操作，例如移除事件监听器
            
    //         const hander = input.addEventListener('input',()=>{})
    //         input.removeEventListener('input', hander);
    //         console.log('11111')
    //     });
    // },5000)
 
}

const spanH = ref(340)

onMounted(async () => {

    //更新分类树
    getCategList()

    await getBaseUnitData()
    //更新产品列表
    getProductList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });
    tableHeight.value = document.getElementById('mainscroll')?.clientHeight-spanH.value
    window.addEventListener('resize', adjustScrollerHeight)


})

//基本计量单位数据源
const baseUnitData = reactive([
    { name: 'PCS' },
    { name: '个' },
    { name: '件' },
    { name: '公斤' },
    { name: '台' },
    { name: '套' },
    { name: 'KG' },
    { name: 'G' },
    { name: '吨' }
]);
const getBaseUnitData = async () => {
    const ret = await getHashApi({
        name: 'baseunit',
        page: 1,
        count: 10000
    })
    if (ret) {
        baseUnitData.splice(0, baseUnitData.length, ...ret.data.json)
    }
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', adjustScrollerHeight)
})

watch(productData, () => {
    tableHeight.value = document.getElementById('mainscroll')?.clientHeight-spanH.value
})
const adjustScrollerHeight = () => {
    tableHeight.value = document.getElementById('mainscroll')?.clientHeight-spanH.value
}

//--------------中间拖拽调整大小-------------
const isResizing = ref(false);
const startX = ref(0);
const leftWidth = ref(15);
const rightWidth = ref(85);

const startResize = (event: MouseEvent) => {
    event.preventDefault(); // 阻止默认行为
    isResizing.value = true;
    startX.value = event.clientX;

    document.addEventListener('mousemove', resize);
    document.addEventListener('mouseup', stopResize);
};

const resize = (event: MouseEvent) => {
    if (isResizing.value) {
        event.preventDefault(); // 阻止默认行为
        const deltaX = event.clientX - startX.value;
        leftWidth.value += (deltaX / window.innerWidth) * 100;
        rightWidth.value -= (deltaX / window.innerWidth) * 100;
        startX.value = event.clientX;
    }
};

const stopResize = () => {
    isResizing.value = false;
    document.removeEventListener('mousemove', resize);
    document.removeEventListener('mouseup', stopResize);
};


const getPdtStatus = (pdt) => {
    if (pdt.status === '停用')
        return 'pdt_stop'
    else
        return ''
}

const loading = ref(false)
const onSave = async () => {
    ElMessageBox.confirm(
      '是否保存修改',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {
        for (let one of productData) {
            if (one.nick == '') {
                ElMessage.error('产品名称不能为空！')
                return
            }
        }
        loading.value = true
        for (let one of productData) {
            if(one.bModify)
                await updateProductApi(one)
        }
        ElMessage.success('批量修改完成！')
        loading.value = false
        getProductList()

      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })

}

const onSelPropsAll = (row, value) => {
    console.log(row, value)
    if(value)
    {
        row.props = []
        row.props.push('销售产品', '采购产品', '生产产品', '半成品', '物料', '委外产品')
    }
    else
    {
        row.props = []
    }
    row.bModify = true
}
const onChangeInput = (row,att,value)=>{
    row[att] = value
    row.bModify = true
}

const tableHeight = ref(600)

const selAll = ref(false)
const selProps = ref([])
const onSelectAll = (item) => {
    productData.forEach(one => {
        one.bModify = true
        if (item) {
            one.props.push('销售产品', '采购产品', '生产产品', '半成品', '物料', '委外产品')
            one.props_all = true
        }
        else {
            one.props = []
            one.props_all = false
        }
        
    })
    if(item)
    {
        selProps.value = ['销售产品', '采购产品', '生产产品', '半成品', '物料', '委外产品']
    }
    else
    {
        selProps.value = []
    }
    selAll.value = item
    
}
const onSelectAllSub = (item) => {
    let all = ['销售产品', '采购产品', '生产产品', '半成品', '物料', '委外产品']
    console.log(item)
    productData.forEach(one => {
        

        for (let tmp of all)
        {
            if (selProps.value.includes(tmp)) {
                console.log('包含',tmp)
                if (!one.props.includes(tmp)) {
                    console.log('one没有',tmp)
                    one.props.push(tmp)
                    one.bModify = true
                }
                else {
                    console.log('one已经有了',tmp)
                }
            }
            else
            {
                console.log('不包含',tmp)
                if (one.props.includes(tmp)) {
                    one.props.splice(one.props.indexOf(tmp), 1)
                    one.bModify = true
                }
            }
        }

        
    })

}

</script>

<template>
    <div ref="rootRef" class="flex absolute top-5 right-5 left-5 bottom-5">
        <!-- 左侧分类栏 -->
        <div class="bg-white p-2 overflow-y-auto" :style="{ width: leftWidth + '%' }">
            <el-tree class="h-[100%] overflow-x-scroll" ref="categTree" :data="categData" :props="defaultProps"
                :default-expanded-keys="expandCateg" node-key="id" @node-click="leftClick" highlight-current
                :current-node-key="currentCatg" :expand-on-click-node="false" :render-after-expand="true">
                <template #default="{ node }">
                    <Icon icon="bx:category" />
                    <div class="pl-2">{{ node.data.name + (node.data.total_product_count > 0 ? (" " +
                        node.data.total_product_count) : '') }}
                    </div>
                </template>
            </el-tree>
        </div>
        <!-- <div class="w-2"></div> -->
        <div class="drag w-2  bg-gray-100 " style="cursor: col-resize;" @mousedown="startResize"></div>
        <!-- 右侧产品列表 -->
        <div class="relative !bg-white overflow-y-auto" :style="{ width: rightWidth + '%' }">

            <div class="h-[100%] bg-white p-2" style="overflow-x:hidden;">
                <div class="text-center mb-5 font-bold">批量编辑</div>
                <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-2 bg-light-200">
                    <!-- 检索条件 -->
                    <div class="inline-flex items-center ml-6 mb-2">
                        <div class="searchTitle">{{ t('product_manage.name') }}</div>
                        <el-input v-model="searchCondition.nick" placeholder="" size="small" class="searchItem" />
                    </div>
                    <div class="inline-flex items-center ml-6 mb-2">
                        <div class="searchTitle">{{ t('product_manage.id') }}</div>
                        <el-input v-model="searchCondition.name" placeholder="" size="small" class="searchItem" />
                    </div>
                    <div class="inline-flex items-center ml-6 mb-2">
                        <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
                        <el-input v-model="searchCondition.specs" placeholder="" size="small" class="searchItem" />
                    </div>
                    <div class="inline-flex items-center ml-6 mb-2">
                        <div class="searchTitle">{{ t('product_manage.brand') }}</div>
                        <el-input v-model="searchCondition.brand" placeholder="" size="small" class="searchItem" />
                    </div>

                    <div class="flex justify-end items-center mr-6 mt-1 mb-2">
                        <ElButton class="ml-5" type="primary" @click="onSearch">
                            <Icon icon="ri:phone-find-line" />
                            <div class="pl-2">查询</div>
                        </ElButton>
                        <ElButton type="warning" @click="onClear">
                            <Icon icon="ant-design:clear-outlined" />
                            <div class="pl-2">清除</div>
                        </ElButton>
                    </div>
                </div>
                <div class="flex justify-center items-center mb-2">
                    <ElButton type="success" @click="onSave">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="pl-2">保存</div>
                    </ElButton>
                </div>
                <!-- 产品列表 -->
                <el-table scrollbar-always-on id="mytable" row-key="id" :height="tableHeight" v-loading="loading" ref="userTableRef1"
                    header-cell-class-name="tableHeader" :data="productData" style="width: 100%;color: #666666;" border
                    stripe>
                    <el-table-column show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60" >
                        <template #default="scope">
                            {{ scope.$index + 1}}
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="name" :label="'产品编号'" width="110" />
                    <el-table-column show-overflow-tooltip prop="nick" :label="'产品名称'" width="200">
                        <template #default="scope">
                            <inputex :inputvalue="scope.row.nick" @change="onChangeInput(scope.row,'nick',$event)"/>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="specs" :label="'规格'" width="200">
                        <template #default="scope">
                            <!-- <el-input v-model="scope.row.specs" size="small" /> -->
                            <inputex :inputvalue="scope.row.specs" @change="onChangeInput(scope.row,'specs',$event)"/>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="brand" :label="'产品品牌'">
                        <template #default="scope">
                            <!-- <el-input v-model="scope.row.brand" size="small" /> -->
                            <inputex :inputvalue="scope.row.brand" @change="onChangeInput(scope.row,'brand',$event)"/>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="nick_brif" :label="'简称'">
                        <template #default="scope">
                            <!-- <el-input v-model="scope.row.nick_brif" size="small" /> -->
                            <inputex :inputvalue="scope.row.nick_brif" @change="onChangeInput(scope.row,'nick_brif',$event)"/>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="note" :label="'助记码'">
                        <template #default="scope">
                            <!-- <el-input v-model="scope.row.note" size="small" /> -->
                            <inputex :inputvalue="scope.row.note" @change="onChangeInput(scope.row,'note',$event)"/>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="base_unit" :label="'单位'" width="110">
                        <template #default="scope">
                            <el-select size="small" v-model="scope.row.base_unit" placeholder="Select" @change="onChangeInput(scope.row,'base_unit',$event)">
                                <el-option v-for="item in baseUnitData" :key="item.name" :label="item.name"
                                    :value="item.name" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" :label="'销售'">
                        <el-table-column show-overflow-tooltip prop="note" :label="'税前价格'">
                            <template #default="scope">
                                <!-- <el-input-number class="!w-[100%]" :controls="false" :min="0" v-model="scope.row.sell_price_bef_tax" size="small" /> -->
                                <InputExNum  :inputvalue="scope.row.sell_price_bef_tax" @change="onChangeInput(scope.row,'sell_price_bef_tax',$event)"/>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip prop="note" :label="'税后价格'">
                            <template #default="scope">
                                <!-- <el-input-number class="!w-[100%]" :controls="false" :min="0" v-model="scope.row.sell_price_aft_tax" size="small" /> -->
                                <InputExNum :inputvalue="scope.row.sell_price_aft_tax" @change="onChangeInput(scope.row,'sell_price_aft_tax',$event)"/>
                            </template>
                        </el-table-column>

                    </el-table-column>
                    <el-table-column align="center" :label="'采购'">
                        <el-table-column show-overflow-tooltip prop="note" :label="'税前价格'">
                            <template #default="scope">
                                <!-- <el-input-number class="!w-[100%]" :controls="false" :min="0" v-model="scope.row.buy_price_bef_tax" size="small" /> -->
                                <InputExNum  :inputvalue="scope.row.buy_price_bef_tax" @change="onChangeInput(scope.row,'buy_price_bef_tax',$event)"/>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip prop="note" :label="'税后价格'">
                            <template #default="scope">
                                <!-- <el-input-number class="!w-[100%]" :controls="false" :min="0" v-model="scope.row.buy_price_aft_tax" size="small" /> -->
                                <InputExNum :inputvalue="scope.row.buy_price_aft_tax" @change="onChangeInput(scope.row,'buy_price_aft_tax',$event)"/>
                            </template>
                        </el-table-column>

                    </el-table-column>
                    <!-- <el-table-column align="center" :label="'委外'">
                        <el-table-column show-overflow-tooltip prop="note" :label="'税前价格'">
                            <template #default="scope">
                               
                                <InputExNum  :inputvalue="scope.row.oem_price_bef_tax" @change="onChangeInput(scope.row,'oem_price_bef_tax',$event)"/>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip prop="note" :label="'税后价格'">
                            <template #default="scope">
                               
                                <InputExNum :inputvalue="scope.row.oem_price_aft_tax" @change="onChangeInput(scope.row,'oem_price_aft_tax',$event)"/>
                            </template>
                        </el-table-column>
                    </el-table-column> -->
                    <el-table-column align="center" :label="'分类'" width="700">
                        <template #header>
                            <div class="w-[100%] flex ">
                            <el-checkbox class="!mr-5" label="全选" v-model="selAll"
                                @change="onSelectAll" />
                            <el-checkbox-group v-model="selProps" @change="onSelectAllSub">
                                <el-checkbox label="销售产品" />
                                <el-checkbox label="采购产品" />
                                <el-checkbox label="生产产品" />
                                <el-checkbox label="半成品" />
                                <el-checkbox label="物料" />
                                <el-checkbox label="委外产品" />
                            </el-checkbox-group>
                            </div>
                        </template>
                        <template #default="scope">
                            <div class="w-[100%] flex ">
                                <el-checkbox class="!mr-5" label="全选" v-model="scope.row.props_all"
                                    @change='onSelPropsAll(scope.row, $event)' />
                                <el-checkbox-group v-model="scope.row.props" @change="()=>{scope.row.bModify = true}">
                                    <el-checkbox label="销售产品" />
                                    <el-checkbox label="采购产品" />
                                    <el-checkbox label="生产产品" />
                                    <el-checkbox label="半成品" />
                                    <el-checkbox label="物料" />
                                    <el-checkbox label="委外产品" />
                                </el-checkbox-group>
                            </div>
                            
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination class="flex justify-end mt-4 mb-4" v-model:current-page="searchCondition.page"
                    v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                    layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
                <!-- <div class="h-[300px]"></div> -->
            </div>

        </div>
    </div>
</template>

<style lang="less" scoped>
.nameStyle {
    color: rgb(60, 60, 255);
    cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #e1f3d8 !important;
}

.searchItem {
    width: 140px;
}

//自定义表格
.header {
    display: flex;
    border: 1px solid #e2e2e2;
    border-collapse: collapse;
    width: 100%;
    color: var(--el-text-color-regular);
    font-size: 15px;
    color: #333;
}

.headerBk {
    background-color: #fff;
}

.content {
    &:extend(.header);
    font-size: 14px;
}

.header>div,
.content>div {
    display: flex;
    border-right: 1px solid #e2e2e2;
    padding: 10px;
    // max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    padding: 5px;
    justify-content: center;
    min-width: 50px;
    max-height: 90px;
    font-size: 13px;
}

.header>div:last-child,
.content>div:last-child {
    border-right: none;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

//解决tree没有横向滚动条问题
:deep(.el-tree>.el-tree-node) {
    display: inline-block;
    min-width: 100%;
}

.pdt_stop {
    background-color: rgb(224, 224, 224);
}
</style>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import {ElDatePicker, ElTag,ElTooltip,ElRow, ElCol, ElButton,ElImage,ElAffix,ElPagination,ElDrawer,ElSelect,ElInput,ElOption, ElMessage, } from 'element-plus';
import 'element-plus/dist/index.css';
import { useRouter } from 'vue-router';
import { useI18n } from '@/hooks/web/useI18n';
import { cloneDeep } from 'lodash-es';
import { getOemOrderListApi, getPurchaseListApi } from '@/api/product';
import { checkPermissionApi, getMoneyFlag } from '@/api/tool';
const { t } = useI18n();

const { currentRoute, push, back } = useRouter();
const isLoading = ref(false);

//总条数
const totleCount = ref(0);
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state: '',
  oem_order_num: '',
  任务编号: '',
  产品编号: '',
  产品名称: '',
  发料比例: '',
  产品规格: '',
  受托商: '',
  收货状态: '',
  关联类型: '',
  关联单号: '',
  委外数量0: '',
  委外数量1: '',
  委外单价0: '',
  委外单价1: '',
  下单人员: '',
  支付方式: '',
  税率: '',
  备注: '',
  下单日期: ['', ''],
  交货日期: ['', ''],
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition);
  }
});

//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])
//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])

const orderData  = reactive([]);

const 总委外数量 = ref(0)
//查询委外单列表
const getOemOrderList = async (page = 1) => {
  searchCondition.page = page

  let tmp = cloneDeep(searchCondition)
  delete tmp.委外数量0
  delete tmp.委外数量1
  delete tmp.委外单价0
  delete tmp.委外单价1

  tmp.委外数量 = searchCondition.委外数量0 + ',' + searchCondition.委外数量1
  tmp.委外单价 = searchCondition.委外单价0 + ',' + searchCondition.委外单价1
  tmp.下单日期 = searchCondition.下单日期[0] + ',' + searchCondition.下单日期[1]
  tmp.交货日期 = searchCondition.交货日期[0] + ',' + searchCondition.交货日期[1]

  isLoading.value = true
  orderData.splice(0, orderData.length)
  const ret = await getOemOrderListApi(tmp)
  if (ret) {
    orderData.splice(0, orderData.length, ...ret.data)
    console.log(orderData)
    totleCount.value = parseInt(ret.count)
    for (let one of orderData) {
      one.checked = false
      for(let pdt of one.pdt_list)
      {
        //修复一下总价
        pdt.总价 = pdt.委外数量 * pdt.oem_price_aft_tax
      }
    }
    总委外数量.value = 0
    orderData.forEach(item=>{
      总委外数量.value += (parseFloat(item.委外数量)+parseFloat(item.委外备品数量))
    })
  }
  isLoading.value = false
}


//开始查询
const onSearch = () => {
  getOemOrderList();
};
//清除条件
const onClear = () => {
  searchCondition.reset();
};
//page控件发生切换
const handleSizeChange = (val: number) => {
  getOemOrderList();
};
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getOemOrderList(val);
};

//审核模式
const bCheckMode = ref(false);
onMounted(() => {

if (currentRoute.value.name === "OemOrderCheck") {
    bCheckMode.value = true
    searchCondition.fsm_cur_state = '等待审核'
  }
  if (currentRoute.value.query.oem_order_num != undefined)
    searchCondition.oem_order_num = currentRoute.value.query.oem_order_num
  getOemOrderList()

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

});


const bShowSearch = ref(false)
const onShowSearch = () => {
  bShowSearch.value = true
}

const freshPage = () => {
  window.location.reload()
}


//处理表格对象操作
const handleOper = (type, item, param = null) => {

  //编辑产品
  if (type === 'edit' || type === 'info' || type === 'check') {
    if (type === 'info' && !checkPermissionApi('委外订单明细查看')) {
      ElMessage.error('无权限1')
      return
    }
    if (type === 'edit' && !checkPermissionApi('委外订单修改')) {
      ElMessage.error('无权限2')
      return
    }

    if (type == 'check') {
      type = 'info'
    }

    push({
      path: '/mobile_oemmanage/addoemorder',
      query: {
        id: item.id,
        type: type,
        cmd: bCheckMode.value ? '审核' : ''
      }
    })
  }
}

</script>

<template>
  <div class="container">
    <el-affix :offset="10">
      <div class="w-[110%] flex bg-gray-200 p-2 mt-[-10px] ml-[-10px] mr-[20px] opacity-95">
        <el-button @click="back">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          {{ t('common.back') }}
        </el-button>
        <el-button @click="freshPage">
          <Icon icon="solar:refresh-broken" />
          刷新
        </el-button>
        <el-button plain type="primary" class="!ml-auto mr-5" @click="onShowSearch">
          <Icon icon="mdi:filter-outline" class="mr-5px" />
          条件
        </el-button>
      </div>
    </el-affix>
    <div class="header">
      <h1>委外订单列表</h1>
    </div>
    <el-row :gutter="10">
      <el-col :span="24" v-for="(item, index) in orderData" :key="index" class="menu-item flex flex-col">
        <div class="flex w-[100%] items-center p-1 mb-1">
          <Icon icon="mdi:company" />
          <span class="ellipsis ml-5px font-bold text-lg">{{ checkPermissionApi('受托商名称显示')?item.parter_nick:'***' }}</span>
          <div class="rounded pl-1 pr-1 ml-auto text-sm" style="color: #fff;"
          :class="{ 'title_create': ['订单创建', '等待审核', '等待修改', '等待提交'].includes(item.fsm_cur_state), 'title_ok': ['已入库', '已关闭', '已拒绝', '已审核'].includes(item.fsm_cur_state), 'title_wait': item.fsm_cur_state === '等待修改' }">
          {{ item.fsm_cur_state }}
          </div>
        </div>
        <hr class="w-full border-gray-300 mb-1"/>
        <div class="flex text-sm mb-1">
          <span class="mr-5">委外日期:{{ item.create_date.split(' ')[0] }}</span>
          <span>委外单号:{{ item.oem_order_num }}</span>
        </div>
        <div class="flex text-sm">
          <span class="mr-5">委外人员:{{ item.oem_man_name }}</span>
          <span>跟单人员:{{ item.follow_man_name }}</span>
        </div>
        <!-- 产品列表 -->
        <div>
          <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex justify-start items-center w-[100%] p-1">
            <el-image v-if="pdt.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" :src="pdt.pics[0].url" />
            <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" src="/nopic.jpg" />
            <div class="inline-block text-left w-[80%] text-[13px]">
                <div class="flex">
                <div class="extxt mr-5 mb-1 flex flex-col">
                    <span>单号:</span>
                    <span>{{ pdt.子任务单号 }}</span>
                </div>
                <div v-if="pdt.sell_order_num!=''" style="color: rgb(81, 141, 146);" class="extxt  cursor-pointer flex flex-col">
                    <span>销售单:</span>
                    <span>{{ pdt.sell_order_num }}</span>
                </div>
                <div v-if="pdt.oem_order_num!=''" style="color: rgb(81, 141, 146);" class="extxt  cursor-pointer flex flex-col">
                    <span>委外单:</span>
                    <span v-if="pdt.oem_order_num.indexOf(',')<0">{{ pdt.oem_order_num }}</span>
                    <el-tooltip
                    v-if="pdt.oem_order_num.indexOf(',')>=0"
                    class="box-item"
                    effect="dark"
                    :content="pdt.父子任务单号"
                    placement="bottom"
                    >
                    <el-tag type="warning" effect="dark">组单</el-tag>
                    
                    </el-tooltip>
                </div>
                </div>

                <div style="white-space: normal;" class="mb-1 nameStyle" >{{ '['+pdt.name+']'+pdt.nick }}</div>
            </div>



            
            <div class="flex flex-col text-[12px] flex-1">
              <span class="mb-1" v-show="checkPermissionApi('委外订单价格显示')">{{ getMoneyFlag(item.money_type)+ parseFloat(pdt.oem_price_aft_tax.toFixed(4)) }}</span>
              <span class="mb-1"> x{{ pdt.委外数量 }}</span>
              <span class="text-[11px] text-gray-500"> x备:{{ pdt.委外备品数量 }}</span>
            </div>
          </div>
        </div>
        <!-- <div class="flex text-sm">
          <span class="ml-auto text-red-800">合计:{{getMoneyFlag(item.money_type)+ item.合计金额+' ' }}</span>
        </div> -->
        <hr class="w-full border-gray-300 mb-1"/>
        <div class="flex text-sm mt-2 justify-center">
          <el-button  size="small" type="danger" plain  class="mr-2" v-if="bCheckMode" @click="handleOper('check', item)">
            <Icon icon="ic:baseline-find-replace" class="mr-5px" />
            审核
          </el-button>
          <el-button v-if="!bCheckMode" size="small" type="primary" plain  class="mr-5" @click="handleOper('info', item)">
            <Icon icon="ic:baseline-find-replace" class="mr-5px" />
            查看
          </el-button>
        </div>
      </el-col>
    </el-row>
    <!-- <el-affix position="bottom" :offset="65"> -->
      <div class="flex mt-5 items-center w-[100%] bg-gray-200 p-2  opacity-95">
        <el-pagination
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          size="small"
          background
          layout="prev, pager, next"
          :pager-count="5"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    <!-- </el-affix> -->
  </div>

  <el-drawer v-model="bShowSearch" :size="'80%'" :direction="'rtl'">
    <template #header>
      <h4>筛选</h4>
    </template>
    <template #default>
        <div>
          <div class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">{{ t('purchase.check_status') }}</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择">
            <el-option v-for="item in ['订单创建', '等待审核', '等待修改', '等待提交', '已审核', '已拒绝', '已关闭']" :key="item" :label="item"
              :value="item" />
          </el-select>
        </div>
        <div class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">委外单号</div>
          <el-input size="small" v-model.lazy="searchCondition.oem_order_num" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">任务编号</div>
          <el-input size="small" v-model.lazy="searchCondition.任务编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('product_manage.id') }}</div>
          <el-input size="small" v-model="searchCondition.产品编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('product_manage.name') }}</div>
          <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">发料比例</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.发料比例" placeholder="请选择">
            <el-option v-for="item in ['未发料+部分发料', '未发料', '部分发料', '完全发料', '超量发料']" :key="item" :label="item"
              :value="item" />
          </el-select>
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品规格</div>
          <el-input size="small" v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">受托商</div>
          <el-input size="small" v-model="searchCondition.受托商" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">收货状态</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.收货状态" placeholder="请选择">
            <el-option v-for="item in ['未收货+部分收货', '未收货', '部分收货', '完全收货', '超量收货']" :key="item" :label="item"
              :value="item" />
          </el-select>
        </div>
        <div  class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">关联类型</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.关联类型" placeholder="请选择">
            <el-option v-for="item in ['销售', '委外']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">关联单号</div>
          <el-input size="small" v-model="searchCondition.关联单号" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">委外数量</div>
          <el-input size="small" v-model="searchCondition.委外数量0" placeholder="" class="!w-[60px]" type="number" />
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small" v-model="searchCondition.委外数量1" placeholder="" class="!w-[60px]" type="number" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">委外单价</div>
          <el-input size="small" v-model="searchCondition.委外单价0" placeholder="" class="!w-[60px]" type="number" />
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small" v-model="searchCondition.委外单价1" placeholder="" class="!w-[60px]" type="number" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">下单人员</div>
          <el-input size="small" v-model="searchCondition.下单人员" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">支付方式</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.支付方式" placeholder="">
            <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">税率</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.税率" placeholder="">
            <el-option v-for="item in ['不含税', ...Array.from({ length: 17 }, (_, i) => `${i + 1}%`)]" :key="item"
              :label="item" :value="item" />
          </el-select>
        </div>
        <div  class="inline-flex items-center mb-1 mb-1">
          <div class="searchTitle">备注</div>
          <el-input size="small" v-model="searchCondition.备注" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">下单日期</div>
          <el-date-picker size="small" class="searchItem" v-model="searchCondition.下单日期" type="daterange"
            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
        <div  class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">交货日期</div>
          <el-date-picker size="small" class="searchItem" v-model="searchCondition.交货日期" type="daterange"
            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>



        </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="searchCondition.reset()">清除</el-button>
        <el-button type="primary" @click="getOemOrderList();bShowSearch = false">查询</el-button>
      </div>
    </template>
  </el-drawer>


</template>

<style lang="less" scoped>
.container {
  padding: 1px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  margin: 1px 0 20px; /* 调整标题栏的上下边距 */
  color: #333;
}

.back-button {
  font-size: 24px;
  margin-bottom: 20px; /* 调整按钮与标题栏之间的距离 */
}

.el-row {
  margin-bottom: 10px; /* 纵向间距 */
}

.el-row:last-child {
  margin-bottom: 0;
}

.menu-item {
  margin-right: 0;
  margin-bottom: 10px;
  justify-content: flex-start; /* 调整对齐方式 */
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;

  .icon {
    font-size: 50px;
    color: #fff;
    margin-right: 20px; /* 调整图标和标题之间的间距 */
    padding: 10px; /* 调整图标内边距 */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 18px;
    color: #333;
    font-weight: 500;
  }

  .ellipsis {
    max-width: 70%; /* 限制最大宽度 */
    white-space: nowrap; /* 防止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 添加省略号 */
  }
}

//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt{ //未收货
  background-color: #79bbff;
  // border: #d9d9d9 1px solid;
}
.title_partial{ //部分收货
  background-color: #eebe77;
}
.title_ok{ //完全收货
  background-color: #95d475;
}
.title_over{//超量收货
  background-color: #FFC0CB;
}
.title_create{ //已创建
  color: #409EFF;
  background-color: color-mix(in oklch, #409EFF, transparent 80%);
}
.title_checked{ //已审核
  color: #67C23A;
  background-color: color-mix(in oklch, #67C23A, transparent 80%);
}
.title_wait{//等待修改
  background-color: #FFC0CB;
  background-color: color-mix(in oklch, #FFC0CB, transparent 80%);
}
.title_ok2{ //完全收货
  background-color: #95d475;
  background-color: color-mix(in oklch, #95d475, transparent 80%);
}


.searchItem{
  width: 150px;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}
</style>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import { ElPopconfirm,ElCard, ElCol,ElCollapse,ElCollapseItem, ElButton,ElImage,ElAffix,ElPagination,ElDrawer,ElSelect,ElInput,ElOption, ElMessage, FormInstance, FormRules, } from 'element-plus';
import 'element-plus/dist/index.css';
import { useRouter } from 'vue-router';
import { useI18n } from '@/hooks/web/useI18n';
import { cloneDeep } from 'lodash-es';
import { addSaleApi, getProductListApi, getSaleInfoApi, getSaleListApi, updateSaleApi } from '@/api/product';
import { checkFormRule, checkPermissionApi, closeOneTagByName, closeOneTagByPath, getMoneyFlag, getTodayDate } from '@/api/tool';
import { getDepartmentListApi } from '@/api/usermanage';
import { getSaleNewnumApi } from '@/api/sale';
import { useAppStore } from '@/store/modules/app';
import { useCache } from '@/hooks/web/useCache';


const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    buyer_nick: [{ required: true, message: t('msg.noCustom'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})

const activeItems = ref(['1', '2'])


//销售单数据
const saleData = reactive(
    {
    "sell_order_num": "",
    "diy_order_num": "",
    "buy_man_id": "",
    "is_same_delivery": 0,
    "delivery_date": "",
    "sell_man_id": "",
    "follow_man_id": "",
    "sell_dept_id": "",
    "sell_date": "",
    "note": "",
    "express_fee": 0.00,
    "other_fee": 0.00,
    "money_type": "人民币",
    "pay_type": "月结",
    "delivery_type": "",
    "express_type": "",
    "setup_type": "",
    "pack_type": "",
    "receive_man_id": "",
    "pdt_list": [],
    "remark": "",
    tax_type:'',
    tax_rate:'',
    source:'',
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:['保存']
    }, //审核决策
    fsm_cur_state:'订单创建',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[],
    deliver_date: '',
    
    create_date: '',
    sell_man_name: '',
    follow_man_name:'',
})



//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])
//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])

//联系人
const receive_users = reactive([])

//treeProp属性
const treeProps = {
    multiple: true,
    checkStrictly: true,
    value:'id', 
    children: 'sub_dept',
    label: 'name',
    emitPath:false
}

//获取最新ID
const onChangeID = async()=>{
    const ret = await getSaleNewnumApi()
    if(ret)
    {
        console.log(ret)
        saleData.sell_order_num = ret.data.new_id
    }
}



//查询产品绑定的查询结果
const searchPdtData = reactive<any>([])
//查询产品列表根据编号
const getProductList = async (val='') => {
  const ret = await getProductListApi({
    name:val,
    nick:val,
    _or:true,
    prop_list:['销售产品'],
    status:'正常',
    page: 1,
    count:30,
  })
  if(ret)
  {
    
    searchPdtData.splice(0,searchPdtData.length, ...ret.data)
    // if(searchPdtData.length >0)
    // {
    //     setCurrent(searchPdtData[0])
    // }
  }
}

//根据产品列表中数量计算销售总数
const totleSale = computed(() => {
console.log('111')
    let totle = 0
    for(const item of saleData.pdt_list)
    {
        if(item.销售数量 == undefined || item.id == undefined)
            continue
        totle += parseFloat(item.销售数量)
        totle += parseFloat(item.销售备品数量)
    }
    return totle
})

//根据产品列表计算销售 总价
const totleSalePrice = computed(() => {
    
    let totle = 0
    let span = 0
    for(const item of saleData.pdt_list)
    {
        if(item.总价 == undefined || item.id == undefined)
            continue
        if(item.sell_buy_digit != undefined)
            span = parseInt(item.sell_buy_digit)
        totle += parseFloat(item.总价)
    }
    
    return  parseFloat(totle.toFixed(span))
})

//计算合计费用(包含运费和其他费用)
const totleSalePriceAll = computed(() => {
    return totleSalePrice.value + parseFloat(saleData.express_fee.toString())+parseFloat(saleData.other_fee.toString())
})


//重新计算产品相关数据
const reComputePdtInfo = (pdt,mode) => {
    pdt.销售数量 = pdt.销售数量 == ''?0:pdt.销售数量
    pdt.销售备品数量 = pdt.销售备品数量 == ''?0:pdt.销售备品数量
    pdt.sell_price_bef_tax = pdt.sell_price_bef_tax == ''?0:pdt.sell_price_bef_tax
    pdt.sell_price_aft_tax = pdt.sell_price_aft_tax == ''?0:pdt.sell_price_aft_tax
    pdt.大模具费 = pdt.大模具费 == ''?0:pdt.大模具费


    pdt.大模具费 = parseFloat(pdt.大模具费)
    console.log('1111',pdt.大模具费)

    if(mode == '销售数量')
    {
        //根据销售数量计算总价 总价等于税后单价乘以销售数量
        //重新计算税后价格
       // pdt.sell_price_aft_tax = parseFloat(pdt.sell_price_bef_tax)+ parseFloat(pdt.sell_price_bef_tax)*pdt.发票税率/100
        // pdt.sell_price_bef_tax = parseFloat((pdt.sell_price_aft_tax/(1+pdt.发票税率/100)).toFixed(pdt.sell_buy_digit))
        pdt.总价 = parseFloat((pdt.sell_price_aft_tax*pdt.销售数量+pdt.大模具费).toFixed(pdt.sell_buy_digit))

        //更新默认备品数
       // pdt.销售备品数量 = parseFloat(pdt.销售数量*0.01)
    }
    else if(mode == '销售备品数量')
    {
                //根据销售数量计算总价 总价等于税后单价乘以销售数量
        //重新计算税后价格
        // pdt.sell_price_aft_tax = parseFloat(pdt.sell_price_bef_tax)+ parseFloat(pdt.sell_price_bef_tax)*pdt.发票税率/100
        pdt.总价 = parseFloat((pdt.sell_price_aft_tax*pdt.销售数量+pdt.大模具费).toFixed(pdt.sell_buy_digit))

    }
    if(mode == '大模具费')
    {
        pdt.总价 = parseFloat((pdt.sell_price_aft_tax*pdt.销售数量+pdt.大模具费).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '税前单价')
    {
        //根据税前单价和税率计算出税后单价         
        pdt.sell_price_aft_tax = parseFloat(pdt.sell_price_bef_tax)+ parseFloat(pdt.sell_price_bef_tax)*pdt.发票税率/100
        //计算出总价
        pdt.总价 = parseFloat((pdt.sell_price_aft_tax*pdt.销售数量+pdt.大模具费).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '税后单价')
    {
        //根据税后单价和税率计算出税前单价         
        pdt.sell_price_bef_tax = parseFloat((pdt.sell_price_aft_tax/(1+pdt.发票税率/100)).toFixed(pdt.sell_buy_digit))
        console.log('--',parseFloat(pdt.sell_price_aft_tax),(1+pdt.发票税率/100))
        //计算出总价
        pdt.总价 = parseFloat((pdt.sell_price_aft_tax*pdt.销售数量+pdt.大模具费).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '折扣')
    {
        //根据折扣重新计算总价
        pdt.总价 = parseFloat((pdt.sell_price_aft_tax*pdt.销售数量+pdt.大模具费).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '发票税率')
    {
        //根据发票类型调整税率数
        if(!['普票','专票','电子票'].includes(pdt.发票类型))
        {
            pdt.发票税率 = 0
            //重新调整税后价格
            pdt.sell_price_aft_tax = pdt.sell_price_bef_tax
        }
        //重新计算税后价格
        //pdt.sell_price_aft_tax = parseFloat(pdt.sell_price_bef_tax)+ parseFloat(pdt.sell_price_bef_tax)*pdt.发票税率/100
        pdt.sell_price_bef_tax = parseFloat((pdt.sell_price_aft_tax/(1+pdt.发票税率/100)).toFixed(pdt.sell_buy_digit))
        //重新计算总价
        pdt.总价 = parseFloat((pdt.sell_price_aft_tax*pdt.销售数量+pdt.大模具费).toFixed(pdt.sell_buy_digit))
    }
    else if(mode == '总价')
    {
        //根据总价重新调整税前税后价格
        pdt.sell_price_aft_tax = parseFloat(((pdt.总价-pdt.大模具费)/pdt.销售数量).toFixed(pdt.sell_buy_digit))
        pdt.sell_price_bef_tax = parseFloat(((pdt.总价-pdt.大模具费)/((1+pdt.发票税率/100)*pdt.销售数量)).toFixed(pdt.sell_buy_digit))
    }

    pdt.销售数量 = parseFloat(pdt.销售数量)
    pdt.销售备品数量 = parseFloat(pdt.销售备品数量)
    pdt.sell_price_bef_tax = parseFloat(pdt.sell_price_bef_tax)
    pdt.sell_price_aft_tax = parseFloat(pdt.sell_price_aft_tax)
    pdt.大模具费 = parseFloat(pdt.大模具费)
}



onMounted(async () => {    
    getDepartmentTreeInfo()
 
    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('sale.add')
        onChangeID()

        //新建需要默认可以提审
        const tmp = [...saleData.fsm_can_trig_data.操作触发,'提交审核']
        saleData.fsm_can_trig_data.操作触发 = tmp

        //设置默认人员部门
        const info = wsCache.get(appStore.getUserInfo)
        saleData.sell_man_id = info.id
        saleData.sell_man_name = info.resident_name
        saleData.follow_man_id = info.id
        saleData.follow_man_name = info.resident_name
        saleData.sell_dept_id = info.depts[0]
        //默认销售日期为今天
        saleData.sell_date = getTodayDate()

        //是否是售前转销售任务
        if(currentRoute.value.query.tosell == '1')
        {
            saleData.source = currentRoute.value.query.source
            const arPdtNum = wsCache.get('tosell')
            console.log(arPdtNum)
            //添加pdt到列表
            for(let pdt of arPdtNum)
            {
                const ret = await getProductListApi({
                    name:pdt.pdt_name,
                    page: 1,
                    count:20,
                })
                if(ret)
                {
                    if(ret.data.length>0)
                    {
                        handleCurrentSelectPdt(ret.data[0],'',true)
                        if(saleData.source == '售前转销售')
                            ret.data[0].sell_offer_num = pdt.sell_offer_num

                        ret.data[0].销售数量 = pdt.pdt_amount
                        ret.data[0].标识 = pdt.标识   
                                 
                    }
                }
            }
            //添加客户信息到订单
            saleData.buyer_nick = currentRoute.value.query.buyer_nick
            saleData.buyer_id = currentRoute.value.query.buyer_id
        }


        //追加默认行
        saleData.pdt_list.push({总价:'0'}) 
    }
    else
    {        
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('sale.look')
        }
        else
        {
            title.value = t('sale.edit')
        }


        
        //查询产品信息 
        const ret = await getSaleInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(saleData, ret.data)


            saleData.pdt_list = ret.data.pdt_list;
            saleData.pdt_list.push({总价:'0'}) 
        }

        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }
})


//产品查找的输入
const txtSearch = ref('')
//点击了查找产品input
const onClickSearchPdt = ()=>{
    console.log('点了')
    getProductList(txtSearch.value)
    showProductSel.value = true
    // 添加全局点击事件监听器
    window.addEventListener('click', handleGlobalClick);
}
const handleGlobalClick = (event)=>{

    const elTableContainers = document.querySelectorAll('.el-table-container');      
    let clickedInsideTable = false;
    for (const container of elTableContainers) {
        if (container.contains(event.target)) {
            // 点击的元素在 el-table 容器内部
            clickedInsideTable = true;
            break;
        }
    }
        
    if (!clickedInsideTable) {
        // 判断点击位置是否在输入框和浮动窗口外部
        // const isOutsideClick = !inputElement.contains(event.target) && !floatWindowElement.contains(event.target);
        const bIn = (event.target as HTMLElement).classList.contains('el-input__inner')
        // 根据判断结果来决定是否隐藏浮动窗口
        if(!bIn)
        {
            showProductSel.value = false
        }
    }
}
//处理输入
const delaytime =ref<NodeJS.Timeout | null>(null); 
const onInputSearch =(val)=>{
    showProductSel.value = true
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)
    
    delaytime.value = setTimeout(() => {
      // 输入停止后触发的方法
      getProductList(val)
    }, 200) 
}


//显示隐藏选择客户弹窗
const showSelCusDlg = ref(false)
const onSelCustom = ()=>{
    showSelCusDlg.value = true
}
//选择客户回调
const onSelCusCallback = (id,name,type,customer)=>{
    console.log(id,name,type,customer)

    //检测是否配置了税号，
    if(customer.corp_taxnum == undefined || customer.corp_taxnum == '')
    {
        ElMessage.error('请先配置客户 （'+customer.buyer_nick+'） 的税号！')
        return
    }


    saleData.buyer_id = id
    saleData.buyer_name = name
    saleData.buyer_nick = customer.type == '公司'?customer.corp_name:customer.buyer_nick
    saleData.tax_type = customer.tax_type == undefined?'普票':customer.tax_type
    saleData.tax_rate = customer.tax_rate ==''?0:(customer.tax_rate=='不含税'?0:(customer.tax_rate.replace('%','')))
    console.log(saleData)

    //同步更新所有产品税率
    for(let item of saleData.pdt_list)
    {
        item.发票类型 = saleData.tax_type
        item.发票税率 =  parseInt(saleData.tax_rate)
        reComputePdtInfo(item,'发票税率')
    }
    //如果没有联系人列表则使用默认联系人
    if(customer.corp_linkman.length == 0 && customer.phone1 !='')
    {//mainer_name phone1
        customer.corp_linkman.push({
            name:customer.mainer_name,
            phone:customer.phone1
        })
    }

    //更新联系人
    Object.assign(receive_users,customer.corp_linkman)
    //复位已选择的客户
    saleData.receive_man_id = ""
}


//显示隐藏选择销售员窗口变量
const showSelSaleUserDlg = ref(false)
//显示选择销售员弹窗
const onSelSale = ()=>{
    showSelSaleUserDlg.value = true
}
//选择销售员回调
const onSelSaleCallback = (id,name)=>{
    console.log(id,name)
    saleData.sell_man_id = id
    saleData.sell_man_name = name
}

//显示隐藏选择跟单员窗口变量
const showSelFollowerUserDlg = ref(false)
//显示选择跟单员弹窗
const onSelFollower = ()=>{
    showSelFollowerUserDlg.value = true
}
//选择跟单员回调
const onSelFollowerCallback = (id,name)=>{
    console.log(id,name)
    saleData.follow_man_id = id
    saleData.follow_man_name = name
}

//部门数据源
const deptData = reactive([])
//查询组织信息
const getDepartmentTreeInfo = async () => {
  const ret = await getDepartmentListApi({})
  console.log(ret)
  if (ret) {

    //deptData.splice(0, deptData.length, ...data)
    deptData.splice(0, deptData.length, ...ret.data.all_depts)
    console.log(deptData.length)
  }
}

//勾选使用统一交付时间事件
const onChangeDeliveryTime = (val)=>{
    if(val) //勾选
    {
        if(saleData.delivery_date == "")
        {
            saleData.delivery_date = getTodayDate()
        }
    }
}

//显示隐藏选择产品浮窗
const showProductSel = ref(false)
//选择了某一个产品
const handleCurrentSelectPdt = (item,old,fource = false) =>{
    if( item == undefined)
    {
        return
    }

    nCurSelPdtID.value = item.id
    if(!fource){
        return
    }
    txtSearch.value = ''
    
    setTimeout(() => {
        const inputElement = document.querySelector('.input-search input');
        inputElement?.focus()
        onClickSearchPdt()
    },500)
       
    


    //增加属性    
    item.材质 = '',
    item.类型 = '正常'
    item.库存数量 = 0
    item.可用数量 = 0
    item.销售数量 = 1
    item.销售备品数量 = 0
    item.交货日期 = saleData.delivery_date == ''? getTodayDate():saleData.delivery_date
    item.大模具费 = 0
    item.发票类型 = '专票'
    item.发票税率 = 0
    item.总价 = 0
    item.标识 = getGUID(10)
    item.销售备注 = ''
    item.locked_list = []
    item.sell_offer_num = ''

    //设置默认价格
    item.sell_price_bef_tax = item.sell_price_bef_tax
    item.sell_price_aft_tax = item.sell_price_aft_tax

    //如果已经选择了客户则自动同步税率
    if(saleData.tax_rate != undefined)
    {
        item.发票类型 = saleData.tax_type
        item.发票税率 = saleData.tax_rate
    }


    //更新产品价格
    reComputePdtInfo(item,'销售数量')

    //构造一行产品
    saleData.pdt_list.splice(-1,0,item)
    //隐藏浮窗
    showProductSel.value = false    

    console.log(saleData)
    
}

//删除某一个产品
const onDelPdt = (index)=>{
    saleData.pdt_list.splice(index,1)
}

//修改产品类型
const onChangeType = (item)=>{
    if(item.类型 == '正常')
    {
        item.sell_price_bef_tax = item.备用税前
        item.sell_price_aft_tax = item.备用税后
        item.大模具费 = item.备用大模具费
        reComputePdtInfo(item,'销售数量')
    }
    else
    {
        item.备用税前 = item.sell_price_bef_tax
        item.备用税后 = item.sell_price_aft_tax
        item.备用大模具费 = item.大模具费
        item.sell_price_bef_tax = 0
        item.sell_price_aft_tax = 0
        item.大模具费 = 0
        reComputePdtInfo(item,'销售数量')

    }
}

//校验pdt
const checkPdt = ()=>{
    if(saleData.pdt_list.length <= 1)
    {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}

//提交审核意见
const handleCheck = async(btn)=>{
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    if(saleData.pay_type == "")
    {
        ElMessage.error('请选择付款方式！')
        return
    }
    //处理下空数据问题
    for(let pdt of saleData.pdt_list)
    {
        pdt.销售数量 = pdt.销售数量 == ''?0:pdt.销售数量
        pdt.销售备品数量 = pdt.销售备品数量 == ''?0:pdt.销售备品数量
        pdt.sell_price_bef_tax = pdt.sell_price_bef_tax == ''?0:pdt.sell_price_bef_tax
        pdt.sell_price_aft_tax = pdt.sell_price_aft_tax == ''?0:pdt.sell_price_aft_tax
        pdt.大模具费 = pdt.大模具费 == ''?0:pdt.大模具费
    }


    const info = wsCache.get(appStore.getUserInfo)
    saleData.fsm_exe_man_name = info.resident_name
    saleData.fsm_exe_trig = btn
    const data = cloneDeep(saleData)
    data.pdt_list.splice(-1,1)

    if(!checkPdt())
    {
        return 
    }


    if(saleData.id == undefined)
    {
        const ret = await addSaleApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.newPurchaseSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        const ret =await updateSaleApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.updatePurchaseSuccess'))

            if(btn == '恢复')
            {
                //查询产品信息 
                const ret = await getSaleInfoApi({
                    id:currentRoute.value.query.id,
                    page:1,
                    count:100
                })
                if(ret)
                {
                    console.log(ret)
                    Object.assign(saleData, ret.data)


                    saleData.pdt_list = ret.data.pdt_list;
                    saleData.pdt_list.push({总价:'0'}) 
                }
            }
            else{
                baskFront()
            }
            
        }
    }
}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = ()=>{
    showCheckHisDlg.value = true
}

//返回上一页
const baskFront = ()=>{
    back()
}

// 使用 computed 定义计算属性
const sameDay = computed({
  get: () => (saleData.delivery_date === '' ? false : true),
  set: (value) => {
    if (value) {
      saleData.delivery_date = getTodayDate();
      //更新下面列表
      saleData.pdt_list.forEach((item, index) => {
          item.交货日期 = saleData.delivery_date;
      })
    } else {
      saleData.delivery_date = '';
    }
    console.log('->',saleData.delivery_date);
  },
});

const onChangeDeliverData = (value)=>{
    saleData.pdt_list.forEach((item, index) => {
          item.交货日期 = saleData.delivery_date;
      })
}



const bShowSearch = ref(false)

const freshPage = () => [
  window.location.reload()
]
</script>

<template>
  <div class="container text-[14px]">
    <el-affix :offset="10">
      <div class="w-[110%] flex bg-gray-200 p-2 mt-[-10px] ml-[-10px] mr-[20px] opacity-95 items-center">
        <el-button @click="back">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          {{ t('common.back') }}
        </el-button>
        <div class="!ml-auto !mr-auto">
          {{saleData.fsm_cur_state}}
        </div>
        <el-button class="!ml-auto mr-5" @click="freshPage">
          <Icon icon="solar:refresh-broken" />
          刷新
        </el-button>
      </div>
    </el-affix>

    <el-card id="check" shadow="never" v-if="saleData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%] mt-4 mb-4">
        审核原因：
        <el-input v-model="saleData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        <div class="mt-4 flex justify-end">
            <ElButton v-show="saleData.fsm_can_trig_data.审核触发.includes('同意')" type="success" @click="handleCheck('同意')" >同意</ElButton>
            <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                <template #reference>
                    <ElButton v-show="saleData.fsm_can_trig_data.审核触发.includes('驳回')" type="warning" >驳回</ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                <template #reference>
                    <ElButton v-show="saleData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                </template>
            </el-popconfirm>  
        </div>
    </el-card>



    <div class="header">
      <h1>{{ title }}</h1>
    </div>

    <div class="flex justify-center items-center border-b border-t pt-2 pb-2 border-gray-400">
      <div class="flex-1">
        {{ saleData.buyer_nick }}
      </div>
      <div class="flex-1 text-center ">
        单号:{{ saleData.sell_order_num }}
      </div>
    </div>
    <div class="flex justify-center items-center border-b  pt-2 pb-2 border-gray-400">
      <div class="flex-1 bo">
        客户单号:{{ saleData.diy_order_num }}
      </div>
      <div class="flex-1 text-center">
        创建日期:{{ saleData.create_date.split(' ')[0]  }}
      </div>
    </div>
    <div class="flex justify-center items-center border-b  pt-2 pb-2 border-gray-400">
      <div class="flex-1">
        币种:{{ saleData.money_type }}
      </div>
      <div class="flex-1 text-center">
        交货日期:{{ saleData.delivery_date == ''?'见明细':saleData.delivery_date }}
      </div>
    </div>
    <div class="flex justify-center items-center border-b  pt-2 pb-2 border-gray-400">
      <div class="flex-1">
        销售:{{ saleData.sell_man_name }}
      </div>
      <div class="flex-1 text-center">
        跟单:{{ saleData.follow_man_name }}
      </div>
    </div>
    <el-collapse v-model="activeItems" class="mt-2">
      <el-collapse-item name="1">
        <template #title>
          <div class='title'>
            销售商品:
            <span class="text-[13px] text-red-600 ml-3" v-show="checkPermissionApi('销售订单价格显示')">总价:{{ totleSalePrice }}</span>
            
          </div>
        </template>
        <div class="border border-gray-300">
          <div v-for="pdt in saleData.pdt_list.filter(pdt => pdt.id !== undefined)" :key="pdt.id"  class="flex justify-start items-center w-[100%] p-1 border-b border-gray-400">
            <el-image v-if="pdt.pics?.length>0"  class="object-fill w-[50px] h-[50px] min-w-[50px] cursor-pointer" :src="pdt.pics[0].url" />
            <el-image v-if="pdt.pics?.length<=0"  class="object-fill w-[50px] h-[50px] min-w-[50px] cursor-pointer" src="/nopic.jpg" />
            <div class="ml-2 inline-block text-left w-[80%] ">
              <div style="white-space: normal;font-size:'12px'" class="nameStyle" >{{ '['+pdt.name+']'+pdt.nick }}</div>
              <div v-if="pdt.sell_offer_num!=''" style="color: rgb(81, 141, 146);" class="cursor-pointer" >售前单:{{pdt.sell_offer_num}}</div>
              <div class="flex text-[13px] mt-1">
                <span class="text-red-400 mr-2" v-show="checkPermissionApi('销售订单价格显示')">{{ getMoneyFlag(saleData.money_type)+parseFloat(pdt.sell_price_aft_tax.toFixed(4))}}</span>{{'x '+pdt.销售数量 +' 备:'+pdt.销售备品数量 +'  '+pdt.base_unit }}
              </div>
              <div class="ex_text mt-1">
                <span>交货日期:</span>
                {{ pdt.交货日期 }}
              </div>
            </div>
            <div class="text-red-600 text-right flex-1" v-if="saleData.pdt_list.length>1 " v-show="checkPermissionApi('销售订单价格显示')">
                {{getMoneyFlag(saleData.money_type)+ pdt.总价 }}
            </div>  
          </div>
          <div class=" text-blue-500 ml-3 mt-2 text-left">总数量:{{ totleSale }}</div>
        </div>
      </el-collapse-item>
      <el-collapse-item name="2">
        <template #title>
          <div class='title'>
            备注:
          </div>
        </template>
        <div>
          {{  saleData.note }}
        </div>
      </el-collapse-item>
      <el-collapse-item name="3">
        <template #title>
          <div class='title'>
            合计金额:
            <span class="text-[13px] text-red-600 ml-3" v-show="checkPermissionApi('销售订单价格显示')">总价:{{ totleSalePrice }}</span>
          </div>
        </template>
        <div>
          <div class="flex justify-center items-center">
            <div class="flex-1">
              运费:{{ saleData.express_fee }}
            </div>
            <div class="flex-1 text-center">
              其他费用:{{ saleData.other_fee }}
            </div>
          </div>
          <div class="flex justify-center items-center ">
            <div class="flex-1">
              付款方式:{{ saleData.pay_type }}
            </div>
            <div class="flex-1 text-center" v-show="checkPermissionApi('销售订单价格显示')">
              产品合计:{{ totleSalePrice }}
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>


<!-- 
    <el-row :gutter="10">
      <el-col :span="24" v-for="(item, index) in saleData" :key="index" class="menu-item flex flex-col">



      </el-col>
    </el-row> -->

  </div>

  <el-drawer v-model="bShowSearch" :size="'80%'" :direction="'btt'">
    <template #header>
      <h4>筛选</h4>
    </template>
    <template #default>
      <div>12</div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="searchCondition.reset()">清除</el-button>
        <el-button type="primary" @click="getSaleList();bShowSearch = false">查询</el-button>
      </div>
    </template>
  </el-drawer>


</template>

<style lang="less" scoped>
.container {
  padding: 1px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  margin: 1px 0 20px; /* 调整标题栏的上下边距 */
  color: #333;
}

.back-button {
  font-size: 24px;
  margin-bottom: 20px; /* 调整按钮与标题栏之间的距离 */
}

.el-row {
  margin-bottom: 10px; /* 纵向间距 */
}

.el-row:last-child {
  margin-bottom: 0;
}

.menu-item {
  margin-right: 0;
  margin-bottom: 10px;
  justify-content: flex-start; /* 调整对齐方式 */
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;

  .icon {
    font-size: 50px;
    color: #fff;
    margin-right: 20px; /* 调整图标和标题之间的间距 */
    padding: 10px; /* 调整图标内边距 */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 18px;
    color: #333;
    font-weight: 500;
  }

  .ellipsis {
    max-width: 70%; /* 限制最大宽度 */
    white-space: nowrap; /* 防止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 添加省略号 */
  }
}

//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt{ //未收货
  background-color: #79bbff;
  // border: #d9d9d9 1px solid;
}
.title_partial{ //部分收货
  background-color: #eebe77;
}
.title_ok{ //完全收货
  background-color: #95d475;
}
.title_over{//超量收货
  background-color: #FFC0CB;
}
.title_create{ //已创建
  color: #409EFF;
  background-color: color-mix(in oklch, #409EFF, transparent 80%);
}
.title_checked{ //已审核
  color: #67C23A;
  background-color: color-mix(in oklch, #67C23A, transparent 80%);
}
.title_wait{//等待修改
  background-color: #FFC0CB;
  background-color: color-mix(in oklch, #FFC0CB, transparent 80%);
}
.title_ok2{ //完全收货
  background-color: #95d475;
  background-color: color-mix(in oklch, #95d475, transparent 80%);
}


.searchItem{
  width: 150px;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}



//----------------------------
//标题设置
.title {
  font-weight: 900;
  font-size: large;
  margin-left: 20px;
}

//标题前提示
.title::before {
  content: 'I';
  color: red;
  margin-right: 10px;
}

:deep(.el-collapse-item__header) {
  height: 40px; /* 你想要设置的高度 */
  line-height: 40px; /* 确保内容垂直居中 */
  background-color: #eeeeee;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
</style>

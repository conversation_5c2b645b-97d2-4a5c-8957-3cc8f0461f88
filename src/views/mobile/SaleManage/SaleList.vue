<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElRow, ElCol, ElButton,ElImage,ElAffix,ElPagination,ElDrawer,ElSelect,ElInput,ElOption, ElMessage, } from 'element-plus';
import 'element-plus/dist/index.css';
import { useRouter } from 'vue-router';
import { useI18n } from '@/hooks/web/useI18n';
import { cloneDeep } from 'lodash-es';
import { getSaleListApi } from '@/api/product';
import { checkPermissionApi, getMoneyFlag } from '@/api/tool';
const { t } = useI18n();

const { currentRoute, push, back } = useRouter();
const isLoading = ref(false);
const 总销售数量 = ref(0);
//总条数
const totleCount = ref(0);
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state:'',
  sell_order_num:'',
  sell_offer_num:'',
  pdt_name:'',
  pdt_nick:'',
  sell_man_name:'',
  follow_man_name:'',
  buyer_nick:'',
  status:'',
  产品编码:'',
  产品名称:'',
  客户名称:'',
  出库状态:'',
  销售人员:'',
  跟单人员:'',
  产品品牌:'',
  产品规格:'',
  是否收款:'',
  客户订单号:'',
  销售价格0:'',
  销售价格1:'',
  是否含税:'',
  支付方式:'',
  开票标识:'',
  结算币种:'',
  产品数量0:'',
  产品数量1:'',
  产品单价0:'',
  产品单价1:'',
  订单金额0:'',
  订单金额1:'',
  发票金额0:'',
  发票金额1:'',
  销售备注:'',
  收货人员:'',
  收货号码:'',
  备货状态:'',
  下单日期:['',''],
  交货日期:['',''],
  产品分类:'',
  page: 1,
  count: 10
};
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition);
  }
});

//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])
//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])

const saleData = reactive([]);

//查询销售单列表
const getSaleList = async (page = 1) => {
  searchCondition.page = page;
  let tmp = cloneDeep(searchCondition);
  delete tmp.销售价格0;
  delete tmp.销售价格1;
  delete tmp.产品数量0;
  delete tmp.产品数量1;
  delete tmp.产品单价0;
  delete tmp.产品单价1;
  delete tmp.订单金额0;
  delete tmp.订单金额1;
  delete tmp.发票金额0;
  delete tmp.发票金额1;
  tmp.销售价格 = searchCondition.销售价格0+','+searchCondition.销售价格1;
  tmp.产品数量 = searchCondition.产品数量0+','+searchCondition.产品数量1;
  tmp.产品单价 = searchCondition.产品单价0+','+searchCondition.产品单价1;
  tmp.订单金额 = searchCondition.订单金额0+','+searchCondition.订单金额1;
  tmp.发票金额 = searchCondition.发票金额1+','+searchCondition.发票金额1;
  tmp.下单日期 = searchCondition.下单日期[0]+','+searchCondition.下单日期[1];
  tmp.交货日期 = searchCondition.交货日期[0]+','+searchCondition.交货日期[1];
  isLoading.value = true;
  saleData.splice(0, saleData.length);
  const ret = await getSaleListApi(tmp);
  if(ret) {
    saleData.splice(0, saleData.length, ...ret.data);
    console.log(saleData);
    totleCount.value = parseInt(ret.count);
    总销售数量.value = 0;
    saleData.forEach(item => {
      总销售数量.value += (parseFloat(item.销售数量) + parseFloat(item.销售备品数量));
    });
  }
  isLoading.value = false;
};

//开始查询
const onSearch = () => {
  getSaleList();
};
//清除条件
const onClear = () => {
  searchCondition.reset();
};
//page控件发生切换
const handleSizeChange = (val: number) => {
  getSaleList();
};
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getSaleList(val);
};

//审核模式
const bCheckMode = ref(false);
onMounted(() => {

  if(currentRoute.value.name === "PMCSaleCheck") {
    bCheckMode.value = true;
    searchCondition.fsm_cur_state = '等待PMC交期审核';
  }
  if(currentRoute.value.name === "ProductMgrSaleCheck") {
    bCheckMode.value = true;
    searchCondition.fsm_cur_state = '等待生产经理交期审核';
  }
  if(currentRoute.value.name === "ProjectMgrSaleCheck") {
    bCheckMode.value = true;
    searchCondition.fsm_cur_state = '等待业务部门经理审核';
  }

  if(currentRoute.value.query.sell_order_num != undefined)
    searchCondition.sell_order_num = currentRoute.value.query.sell_order_num;
  getSaleList();  

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

});


const bShowSearch = ref(false)
const onShowSearch = () => {
  bShowSearch.value = true
}

const freshPage = () => {
  window.location.reload()
}


//处理表格对象操作
const handleOper = (type, item, param = null) => {

  //编辑产品
  if (type === 'edit' || type === 'info' || type === 'check') {
    if (type === 'info' && !checkPermissionApi('销售订单明细查看')) {
      ElMessage.error('无权限1')
      return
    }
    if (type === 'edit' && !checkPermissionApi('销售订单修改')) {
      ElMessage.error('无权限2')
      return
    }

    if (type == 'check') {
      type = 'info'
    }

    push({
      path: '/mobile_salemanage/addsale',
      query: {
        id: item.id,
        type: type,
        cmd: bCheckMode.value ? '审核' : ''
      }
    })
  }
}

</script>

<template>
  <div class="container">
    <el-affix :offset="10">
      <div class="w-[110%] flex bg-gray-200 p-2 mt-[-10px] ml-[-10px] mr-[20px] opacity-95">
        <el-button @click="back">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          {{ t('common.back') }}
        </el-button>
        <el-button @click="freshPage">
          <Icon icon="solar:refresh-broken" />
          刷新
        </el-button>
        <el-button plain type="primary" class="!ml-auto mr-5" @click="onShowSearch">
          <Icon icon="mdi:filter-outline" class="mr-5px" />
          条件
        </el-button>
      </div>
    </el-affix>
    <div class="header">
      <h1>销售订单列表</h1>
    </div>
    <el-row :gutter="10">
      <el-col :span="24" v-for="(item, index) in saleData" :key="index" class="menu-item flex flex-col">
        <div class="flex w-[100%] items-center p-1 mb-1">
          <Icon icon="mdi:company" />
          <span class="ellipsis ml-5px font-bold text-lg">{{ item.buyer_nick }}</span>
          <div class="rounded pl-1 pr-1 ml-auto text-sm" style="color: #fff;"
            :class="{'title_unreceipt': item.发货状态 === '未发货', 'title_partial': item.发货状态 === '部分发货', 'title_ok': item.发货状态 === '完全发货', 'title_over': item.发货状态 === '超量发货'}">
            {{ item.发货状态}}
          </div>
        </div>
        <hr class="w-full border-gray-300 mb-1"/>
        <div class="flex text-sm mb-1">
          <span class="mr-5">销售日期:{{ item.create_date.split(' ')[0] }}</span>
          <span>销售单号:{{ item.sell_order_num }}</span>
        </div>
        <div class="flex text-sm">
          <span class="mr-5">销售人员:{{ item.sell_man_name }}</span>
          <span>跟单人员:{{ item.follow_man_name }}</span>
        </div>
        <!-- 产品列表 -->
        <div>
          <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex justify-start items-center w-[100%] p-1">
            <el-image v-if="pdt.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" :src="pdt.pics[0].url" />
            <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" src="/nopic.jpg" />
            <div class="ml-2 inline-block text-left w-[80%] ">
              <div style="white-space: normal;font-size:'12px'" class="nameStyle" >{{ '['+pdt.name+']'+pdt.nick }}</div>
              <div v-if="pdt.sell_offer_num!=''" style="color: rgb(81, 141, 146);" class="cursor-pointer" >售前单:{{pdt.sell_offer_num}}</div>
              <div class="flex text-sm">
                <div class="mr-2 border text-green-700 border-green-700 pl-1 pr-1 cursor-pointer rounded" style="font-size: smaller;" >锁定:{{ pdt.锁定数量 }}</div>
                <div class="mr-2 border text-red-700 border-red-800 pl-1 pr-1 cursor-pointer rounded" style="font-size: smaller;">备货:{{ pdt.备货数量 }}</div>
                <div class="mr-2 border text-red-500 border-red-500 pl-1 pr-1 cursor-pointer rounded" style="font-size: smaller;">缺货:{{ pdt.缺口数量 }}</div>
                <!-- pdt.未发货-pdt.锁定数量-pdt.备货数量 -->
              </div>
            </div>
            <div class="flex flex-col text-[12px]">
              <span class="mb-1" v-show="checkPermissionApi('销售订单价格显示')">{{ getMoneyFlag(item.money_type)+pdt.sell_price_aft_tax }}</span>
              <span class="mb-1"> x{{ pdt.销售数量 }}</span>
              <span class="text-[11px] text-gray-500"> x备:{{ pdt.销售备品数量 }}</span>
            </div>
          </div>
        </div>
        <div class="flex text-sm">
          <span class="ml-auto text-red-800" v-show="checkPermissionApi('销售订单价格显示')">合计:{{getMoneyFlag(item.money_type)+ item.合计金额+' ' }}</span>
        </div>
        <hr class="w-full border-gray-300 mb-1"/>
        <div class="flex text-sm mt-2 justify-center">
          <el-button  size="small" type="danger" plain  class="mr-2" v-if="item.fsm_cur_state.indexOf('审核')>=0 && currentRoute.name != 'SaleManage' " @click="handleOper('check', item)">
            <Icon icon="ic:baseline-find-replace" class="mr-5px" />
            审核
          </el-button>
          <el-button size="small" type="primary" plain  class="mr-5" @click="handleOper('info', item)">
            <Icon icon="ic:baseline-find-replace" class="mr-5px" />
            查看
          </el-button>
        </div>
      </el-col>
    </el-row>
    <!-- <el-affix position="bottom" :offset="65"> -->
      <div class="flex mt-5 items-center w-[100%] bg-gray-200 p-2  opacity-95">
        <el-pagination
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          size="small"
          background
          layout="prev, pager, next"
          :pager-count="5"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    <!-- </el-affix> -->
  </div>

  <el-drawer v-model="bShowSearch" :size="'80%'" :direction="'rtl'">
    <template #header>
      <h4>筛选</h4>
    </template>
    <template #default>
      <div>
        <div class="inline-flex items-center ml-1" >
          <div class="searchTitle">订单状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择"  >
              <el-option v-for="item in ['等待提交', '等待PMC交期审核', '等待生产经理交期审核', '等待业务部门经理审核', '等待修改', '已审核', '已拒绝', '已关闭', '已删除']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('sale.name') }}</div>
            <el-input size="small"   v-model="searchCondition.sell_order_num" placeholder=""  class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">售前单号</div>
            <el-input size="small"   v-model="searchCondition.sell_offer_num" placeholder=""  class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input size="small"  v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input size="small"  v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('customer.name') }}</div>
            <el-input size="small"  v-model="searchCondition.客户名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">出库状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.出库状态" placeholder="请选择" >
              <el-option v-for="item in ['未出库','部分出库','完全出库','超量出库','出库且退货']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">销售人员</div>
              <el-input size="small"   v-model="searchCondition.sell_man_name" placeholder=""  class="!w-[100px]" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">跟单人员</div>
              <el-input size="small"   v-model="searchCondition.follow_man_name" placeholder=""  class="!w-[100px]" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品品牌</div>
            <el-input size="small"  v-model="searchCondition.产品品牌" placeholder="" class="searchItem" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品规格</div>
            <el-input size="small"  v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">是否收款</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.是否收款" placeholder="" >
              <el-option v-for="item in ['未收款','完全收款','部分收款']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>

          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('sale.cus_sef_id') }}</div>
            <el-input size="small"  v-model="searchCondition.客户订单号" placeholder="" class="searchItem" />
          </div>

          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('sale.price') }}</div>
            <el-input size="small"  v-model="searchCondition.销售价格0" placeholder="" class="!w-[60px]" type="number">
              <!-- <template #append>
                元
              </template> -->
            </el-input>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.销售价格1" placeholder="" class="!w-[60px]" type="number">
              <!-- <template #append>
                元
              </template> -->
            </el-input>
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">是否含税</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.是否含税" placeholder="" >
              <el-option v-for="item in ['不含税','含税']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">支付方式</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.支付方式" placeholder="" >
              <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <!-- <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">开票标识</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.开票标识" placeholder="" >
              <el-option v-for="item in ['不开发票','未开发票','部分开票','完全开票']" :key="item" :label="item" :value="item" />
            </el-select>
          </div> -->
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品数量</div>
            <el-input size="small"  v-model="searchCondition.产品数量0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.产品数量1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">结算币种</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.结算币种" placeholder="" >
              <el-option v-for="item in moneyTypeData" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品单价</div>
            <el-input size="small"  v-model="searchCondition.产品单价0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.产品单价1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">订单金额</div>
            <el-input size="small"  v-model="searchCondition.订单金额0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.订单金额1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">发票金额</div>
            <el-input size="small"  v-model="searchCondition.发票金额0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.发票金额1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">销售备注</div>
            <el-input size="small"  v-model="searchCondition.销售备注" placeholder="" class="searchItem" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">收货人员</div>
            <el-input size="small"  v-model="searchCondition.收货人员" placeholder="" class="searchItem" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">收货号码</div>
            <el-input size="small"  v-model="searchCondition.收货号码" placeholder="" class="searchItem" />
          </div>
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">备货状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.备货状态" placeholder="" >
              <el-option v-for="item in ['未备货','备货中(无缺口)','备货中(有缺口)','备货完成(可发货)']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="searchCondition.reset()">清除</el-button>
        <el-button type="primary" @click="getSaleList();bShowSearch = false">查询</el-button>
      </div>
    </template>
  </el-drawer>


</template>

<style lang="less" scoped>
.container {
  padding: 1px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  margin: 1px 0 20px; /* 调整标题栏的上下边距 */
  color: #333;
}

.back-button {
  font-size: 24px;
  margin-bottom: 20px; /* 调整按钮与标题栏之间的距离 */
}

.el-row {
  margin-bottom: 10px; /* 纵向间距 */
}

.el-row:last-child {
  margin-bottom: 0;
}

.menu-item {
  margin-right: 0;
  margin-bottom: 10px;
  justify-content: flex-start; /* 调整对齐方式 */
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;

  .icon {
    font-size: 50px;
    color: #fff;
    margin-right: 20px; /* 调整图标和标题之间的间距 */
    padding: 10px; /* 调整图标内边距 */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 18px;
    color: #333;
    font-weight: 500;
  }

  .ellipsis {
    max-width: 70%; /* 限制最大宽度 */
    white-space: nowrap; /* 防止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 添加省略号 */
  }
}

//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt{ //未收货
  background-color: #79bbff;
  // border: #d9d9d9 1px solid;
}
.title_partial{ //部分收货
  background-color: #eebe77;
}
.title_ok{ //完全收货
  background-color: #95d475;
}
.title_over{//超量收货
  background-color: #FFC0CB;
}
.title_create{ //已创建
  color: #409EFF;
  background-color: color-mix(in oklch, #409EFF, transparent 80%);
}
.title_checked{ //已审核
  color: #67C23A;
  background-color: color-mix(in oklch, #67C23A, transparent 80%);
}
.title_wait{//等待修改
  background-color: #FFC0CB;
  background-color: color-mix(in oklch, #FFC0CB, transparent 80%);
}
.title_ok2{ //完全收货
  background-color: #95d475;
  background-color: color-mix(in oklch, #95d475, transparent 80%);
}


.searchItem{
  width: 150px;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}
</style>

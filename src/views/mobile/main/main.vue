<script setup lang="ts">
import { ref } from 'vue';
import { ElRow, ElCol } from 'element-plus';
import 'element-plus/dist/index.css';
import { useRouter } from 'vue-router'
const { currentRoute, push } = useRouter()

const menuItems = ref([
  // { icon: 'streamline:information-desk-customer', title: '客户管理', color: '#FF6B6B' },
  { icon: 'hugeicons:sale-tag-02', title: '销售管理', color: '#FFD93D' ,path:'/mobile_salemanage'},
  { icon: 'f7:purchased-circle', title: '采购管理', color: '#6BCB77' ,path:'/mobile_purchasemanage'},
  { icon: 'emojione-monotone:outbox-tray', title: '委外管理', color: '#AB47BC' ,path:'/mobile_oemmanage'},
  // { icon: 'iconamoon:store-duotone', title: '仓库管理', color: '#4D96FF' },
  // { icon: 'icon-park-outline:finance', title: '财务管理', color: '#AFB42B' },
  // { icon: 'octicon:project-roadmap-16', title: '工程管理', color: '#26A69A' },
  // { icon: 'mdi:beaker-check-outline', title: '品质管理', color: '#5C6BC0' },
  // { icon: 'icon-park-outline:config', title: '系统管理', color: '#FFCA28' },
]);

const handleMenuClick = (path) => {
  if(path != undefined)
    push({ path: path });
};
</script>

<template>
  <div class="container">
    <div class="header">
      <h1>文博ERP移动版</h1>
    </div>
    <el-row :gutter="10">
      <el-col :span="24" v-for="(item, index) in menuItems" :key="index" class="menu-item " @click="handleMenuClick(item.path)">
        <Icon :icon="item.icon" :style="{ backgroundColor: item.color, width: '40px', height: '40px', borderRadius: '50%' ,marginRight: '10px',marginLeft:'20px' }"/>
        <div class="title">{{ item.title }}</div>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="less" scoped>
.container {
  padding: 1px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  margin: 30px 0;
  color: #333;
}

.el-row {
  margin-bottom: 10px; /* 纵向间距 */
}

.el-row:last-child {
  margin-bottom: 0;
}

.menu-item {
  margin-right: 0;
  margin-bottom: 10px;
  display: flex !important;
  flex-direction: row; /* 更改为水平方向 */
  align-items: center;
  justify-content: flex-start; /* 调整对齐方式 */
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;

  // &:hover {
  //   transform: translateY(-5px);
  //   box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  // }

  .icon {
    font-size: 50px;
    color: #fff;
    margin-right: 20px; /* 调整图标和标题之间的间距 */
    padding: 10px; /* 调整图标内边距 */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 18px;
    color: #333;
    font-weight: 500;
  }
}
</style>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import {ElDatePicker, ElTag,ElTooltip,ElRow, ElCol, ElButton,ElImage,ElAffix,ElPagination,ElDrawer,ElSelect,ElInput,ElOption, ElMessage, } from 'element-plus';
import 'element-plus/dist/index.css';
import { useRouter } from 'vue-router';
import { useI18n } from '@/hooks/web/useI18n';
import { cloneDeep } from 'lodash-es';
import { getPurchaseListApi } from '@/api/product';
import { checkPermissionApi, getMoneyFlag } from '@/api/tool';
const { t } = useI18n();

const { currentRoute, push, back } = useRouter();
const isLoading = ref(false);
const 总销售数量 = ref(0);
//总条数
const totleCount = ref(0);
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state:'',
  buy_order_num: '',
  supplier_nick:'',
  产品编码:'',
  产品名称:'',
  供应商:'',
  订单状态:'',
  收货状态:'',
  供应商单号:'',
  产品规格:'',
  采购人员:'',
  下单日期:['',''],
  交货日期:['',''],
  支付方式:'',
  收票标识:'',
  产品品牌:'',
  销售单:'',
  委外单:'',
  是否付款:'',
  采购备注:'',
  采购件数0:'',
  采购件数1:'',
  采购单价0:'',
  采购单价1:'',
  采购部门:'',
  订单总价0:'',
  订单总价1:'',
  跟单人员:'',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition);
  }
});

//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])
//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])

const purchaseData  = reactive([]);
const 总采购数量 = ref(0)
//查询采购单列表
const getPurchaseList = async (page = 1) => {
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  delete tmp.采购件数0
  delete tmp.采购件数1
  delete tmp.采购单价0
  delete tmp.采购单价1
  delete tmp.订单总价0
  delete tmp.订单总价1
  
  tmp.采购件数 = searchCondition.采购件数0+','+searchCondition.采购件数1
  tmp.采购单价 = searchCondition.采购单价0+','+searchCondition.采购单价1
  tmp.订单总价 = searchCondition.订单总价0+','+searchCondition.订单总价1
  tmp.下单日期 = searchCondition.下单日期[0]+','+searchCondition.下单日期[1]
  tmp.交货日期 = searchCondition.交货日期[0]+','+searchCondition.交货日期[1]

  isLoading.value = true
  purchaseData.splice(0,purchaseData.length)

  const ret = await getPurchaseListApi(tmp)
  if(ret)
  {
    purchaseData.splice(0,purchaseData.length, ...ret.data)
    totleCount.value = parseInt(ret.count)
    总采购数量.value = 0
    purchaseData.forEach(item=>{
      总采购数量.value += (parseFloat(item.采购数量)+parseFloat(item.采购备品数量))


      for(let one of item.pdt_list)
      {
        if(isNaN(Number(one.发票税率)))
        {
          console.error('有问题:',item.id,one.发票税率)
        }
        //修复字符串到数字
        one.buy_price_aft_tax = Number(one.buy_price_aft_tax)
      }
    })
  }
  isLoading.value = false
}

//开始查询
const onSearch = () => {
  getPurchaseList();
};
//清除条件
const onClear = () => {
  searchCondition.reset();
};
//page控件发生切换
const handleSizeChange = (val: number) => {
  getPurchaseList();
};
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getPurchaseList(val);
};

//审核模式
const bCheckMode = ref(false);
onMounted(() => {

if(currentRoute.value.name === "PurchaseCheck")
  {
    bCheckMode.value = true
    searchCondition.fsm_cur_state = '等待审核'
  }
  if(currentRoute.value.query.buy_order_num != undefined)
        searchCondition.buy_order_num = currentRoute.value.query.buy_order_num

  getPurchaseList();  

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

});


const bShowSearch = ref(false)
const onShowSearch = () => {
  bShowSearch.value = true
}

const freshPage = () => {
  window.location.reload()
}


//处理表格对象操作
const handleOper = (type, item, param = null) => {

  //编辑产品
  if (type === 'edit' || type === 'info' || type === 'check') {
    if (type === 'info' && !checkPermissionApi('采购订单明细查看')) {
      ElMessage.error('无权限1')
      return
    }
    if (type === 'edit' && !checkPermissionApi('采购订单修改')) {
      ElMessage.error('无权限2')
      return
    }

    if (type == 'check') {
      type = 'info'
    }

    push({
      path: '/mobile_purchasemanage/addpurchase',
      query: {
        id: item.id,
        type: type,
        cmd: bCheckMode.value ? '审核' : ''
      }
    })
  }
}

</script>

<template>
  <div class="container">
    <el-affix :offset="10">
      <div class="w-[110%] flex bg-gray-200 p-2 mt-[-10px] ml-[-10px] mr-[20px] opacity-95">
        <el-button @click="back">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          {{ t('common.back') }}
        </el-button>
        <el-button @click="freshPage">
          <Icon icon="solar:refresh-broken" />
          刷新
        </el-button>
        <el-button plain type="primary" class="!ml-auto mr-5" @click="onShowSearch">
          <Icon icon="mdi:filter-outline" class="mr-5px" />
          条件
        </el-button>
      </div>
    </el-affix>
    <div class="header">
      <h1>采购订单列表</h1>
    </div>
    <el-row :gutter="10">
      <el-col :span="24" v-for="(item, index) in purchaseData" :key="index" class="menu-item flex flex-col">
        <div class="flex w-[100%] items-center p-1 mb-1">
          <Icon icon="mdi:company" />
          <span class="ellipsis ml-5px font-bold text-lg">{{ checkPermissionApi('供应商名称显示')?item.supplier_nick:'***' }}</span>
          <div class="rounded pl-1 pr-1 ml-auto text-sm" style="color: #fff;"
            :class="{'title_unreceipt': item.收货状态 === '未收货', 'title_partial': item.收货状态 === '部分收货', 'title_ok': item.收货状态 === '完全收货', 'title_over': item.收货状态 === '超量收货'}">
            {{ item.收货状态}}
          </div>
        </div>
        <hr class="w-full border-gray-300 mb-1"/>
        <div class="flex text-sm mb-1">
          <span class="mr-5">采购日期:{{ item.create_date.split(' ')[0] }}</span>
          <span>采购单号:{{ item.buy_order_num }}</span>
        </div>
        <div class="flex text-sm">
          <span class="mr-5">采购人员:{{ item.buy_man_name }}</span>
          <span>跟单人员:{{ item.follow_man_name }}</span>
        </div>
        <!-- 产品列表 -->
        <div>
          <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex justify-start items-center w-[100%] p-1">
            <el-image v-if="pdt.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" :src="pdt.pics[0].url" />
            <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" src="/nopic.jpg" />
            <div class="ml-2 inline-block text-left w-[80%] text-[14px]">
                <div class="flex">
                    <div v-if="pdt.sell_order_num!=''" style="color: rgb(81, 141, 146);" class="extxt mr-5 mb-1  cursor-pointer flex ">
                    <span>销售单:</span>
                    <span v-if="pdt.sell_order_num.indexOf(',')<0">{{ pdt.sell_order_num }}</span>
                    <el-tooltip
                        v-if="pdt.sell_order_num.indexOf(',')>=0"
                        class="box-item"
                        effect="dark"
                        :content="pdt.sell_order_num"
                        placement="bottom"
                    >
                        <el-tag type="info" effect="dark">组合备料</el-tag>
                    
                    </el-tooltip>
                    </div>
                    <div v-if="pdt.oem_order_num!=''" style="color: rgb(81, 141, 146);" class="extxt  cursor-pointer flex items-center">
                    <span>委外单:</span>
                    <span v-if="pdt.oem_order_num.indexOf(',')<0">{{ pdt.oem_order_num +'  ['+pdt.父子任务单号+']'}}</span>
                    <el-tooltip
                        v-if="pdt.oem_order_num.indexOf(',')>=0"
                        class="box-item"
                        effect="dark"
                        :content="pdt.oem_order_num"
                        placement="bottom"
                    >
                        <el-tag class="ml-2" type="warning" effect="dark">组单</el-tag>
                    
                    </el-tooltip>
                    <el-tooltip
                        v-if="pdt.oem_order_num.indexOf(',')>=0"
                        class="box-item"
                        effect="dark"
                        :content="pdt.父子任务单号"
                        placement="bottom"
                    >
                        <el-tag class="ml-2" type="info" effect="dark">子单</el-tag>
                    
                    </el-tooltip>
                    </div>
                    <!-- <div v-if="pdt.oem_order_num!=''" class="extxt mr-5 mb-1 flex flex-col">
                    <span>子单号:</span>
                    <span>{{ pdt.父子任务单号 }}</span>
                    </div> -->
                </div>

                <div style="white-space: normal;" class="mb-1 nameStyle" >{{ '['+pdt.name+']'+pdt.nick }}</div>
                <div class="ex_text">{{ pdt.specs_text }}</div>
            </div>



            
            <div class="flex flex-col text-[12px] flex-1">
              <span class="mb-1" v-show="checkPermissionApi('采购订单价格显示')">{{ getMoneyFlag(item.money_type)+ parseFloat(pdt.buy_price_aft_tax.toFixed(4)) }}</span>
              <span class="mb-1"> x{{ pdt.采购数量 }}</span>
              <span class="text-[11px] text-gray-500"> x备:{{ pdt.采购备品数量 }}</span>
            </div>
          </div>
        </div>
        <div class="flex text-sm">
          <span class="ml-auto text-red-800" v-show="checkPermissionApi('采购订单价格显示')">合计:{{getMoneyFlag(item.money_type)+ item.合计金额+' ' }}</span>
        </div>
        <hr class="w-full border-gray-300 mb-1"/>
        <div class="flex text-sm mt-2 justify-center">
          <el-button  size="small" type="danger" plain  class="mr-2" v-if="bCheckMode" @click="handleOper('check', item)">
            <Icon icon="ic:baseline-find-replace" class="mr-5px" />
            审核
          </el-button>
          <el-button v-if="!bCheckMode" size="small" type="primary" plain  class="mr-5" @click="handleOper('info', item)">
            <Icon icon="ic:baseline-find-replace" class="mr-5px" />
            查看
          </el-button>
        </div>
      </el-col>
    </el-row>
    <!-- <el-affix position="bottom" :offset="65"> -->
      <div class="flex mt-5 items-center w-[100%] bg-gray-200 p-2  opacity-95">
        <el-pagination
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          size="small"
          background
          layout="prev, pager, next"
          :pager-count="5"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    <!-- </el-affix> -->
  </div>

  <el-drawer v-model="bShowSearch" :size="'80%'" :direction="'rtl'">
    <template #header>
      <h4>筛选</h4>
    </template>
    <template #default>
      <div>
        <!-- 检索条件 -->
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.check_status') }}</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择" >
            <el-option v-for="item in ['订单创建', '等待审核', '等待修改', '等待提交', '已审核', '已拒绝', '已关闭']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.id') }}</div>
          <el-input size="small"   v-model="searchCondition.buy_order_num" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input size="small"  v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input size="small"  v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
          </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.supplier') }}</div>
          <el-input size="small"  v-model="searchCondition.supplier_nick" placeholder="" class="searchItem" />
        </div>
        <!-- <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.status') }}</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.订单状态" placeholder="请选择" >
            <el-option v-for="item in ['进行中+已完成','进行中','已完成','已暂停','已停止']" :key="item" :label="item" :value="item" />
          </el-select>
        </div> -->
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.receipt_status') }}</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.收货状态" placeholder="请选择" >
            <el-option v-for="item in ['未收货','部分收货','完全收货','超量收货','收货且退货','未收货+部分收货']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.supplier_sup_id') }}</div>
          <el-input size="small"  v-model="searchCondition.供应商单号" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品规格</div>
          <el-input size="small"  v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购人员</div>
          <el-input size="small"  v-model="searchCondition.采购人员" placeholder="" class="searchItem" />
        </div>

        <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">支付方式</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.支付方式" placeholder="" >
              <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
            </el-select>
        </div>
        <!-- <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">收票标识</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.收票标识" placeholder="" >
            <el-option v-for="item in ['不收发票','未收发票','部分收票','完全收票']" :key="item" :label="item" :value="item" />
          </el-select>
        </div> -->
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品品牌</div>
          <el-input size="small"  v-model="searchCondition.产品品牌" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">关联销售单</div>
          <el-input size="small"  v-model="searchCondition.销售单" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">关联委外单</div>
          <el-input size="small"  v-model="searchCondition.委外单" placeholder="" class="searchItem" />
        </div>

        <!-- <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">是否付款</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.是否付款" placeholder="" >
            <el-option v-for="item in ['未付款','部分付款','完全付款','未付款+部分付款']" :key="item" :label="item" :value="item" />
          </el-select>
        </div> -->
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购备注</div>
          <el-input size="small"  v-model="searchCondition.采购备注" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购件数</div>
          <el-input size="small"  v-model="searchCondition.采购件数0" placeholder="" class="!w-[60px]" type="number"/>
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small"  v-model="searchCondition.采购件数1" placeholder="" class="!w-[60px]" type="number" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购单价</div>
          <el-input size="small"  v-model="searchCondition.采购单价0" placeholder="" class="!w-[60px]" />
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small"  v-model="searchCondition.采购单价1" placeholder="" class="!w-[60px]" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购部门</div>
          <el-input size="small"  v-model="searchCondition.采购部门" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">订单总价</div>
          <el-input size="small"  v-model="searchCondition.订单总价0" placeholder="" class="!w-[60px]" />
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small"  v-model="searchCondition.订单总价1" placeholder="" class="!w-[60px]" />
        </div>
        <div  class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">跟单人员</div>
          <el-input size="small"  v-model="searchCondition.跟单人员" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">下单日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.下单日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
        <div  class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">交货日期</div>
          <el-date-picker size="small"  class="searchItem" v-model="searchCondition.交货日期" type="daterange" range-separator="To"
            start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="searchCondition.reset()">清除</el-button>
        <el-button type="primary" @click="getPurchaseList();bShowSearch = false">查询</el-button>
      </div>
    </template>
  </el-drawer>


</template>

<style lang="less" scoped>
.container {
  padding: 1px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  margin: 1px 0 20px; /* 调整标题栏的上下边距 */
  color: #333;
}

.back-button {
  font-size: 24px;
  margin-bottom: 20px; /* 调整按钮与标题栏之间的距离 */
}

.el-row {
  margin-bottom: 10px; /* 纵向间距 */
}

.el-row:last-child {
  margin-bottom: 0;
}

.menu-item {
  margin-right: 0;
  margin-bottom: 10px;
  justify-content: flex-start; /* 调整对齐方式 */
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;

  .icon {
    font-size: 50px;
    color: #fff;
    margin-right: 20px; /* 调整图标和标题之间的间距 */
    padding: 10px; /* 调整图标内边距 */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 18px;
    color: #333;
    font-weight: 500;
  }

  .ellipsis {
    max-width: 70%; /* 限制最大宽度 */
    white-space: nowrap; /* 防止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 添加省略号 */
  }
}

//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt{ //未收货
  background-color: #79bbff;
  // border: #d9d9d9 1px solid;
}
.title_partial{ //部分收货
  background-color: #eebe77;
}
.title_ok{ //完全收货
  background-color: #95d475;
}
.title_over{//超量收货
  background-color: #FFC0CB;
}
.title_create{ //已创建
  color: #409EFF;
  background-color: color-mix(in oklch, #409EFF, transparent 80%);
}
.title_checked{ //已审核
  color: #67C23A;
  background-color: color-mix(in oklch, #67C23A, transparent 80%);
}
.title_wait{//等待修改
  background-color: #FFC0CB;
  background-color: color-mix(in oklch, #FFC0CB, transparent 80%);
}
.title_ok2{ //完全收货
  background-color: #95d475;
  background-color: color-mix(in oklch, #95d475, transparent 80%);
}


.searchItem{
  width: 150px;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}
</style>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import { ElPopconfirm,ElCard, ElCol,ElCollapse,ElCollapseItem, ElButton,ElImage,ElAffix,ElPagination,ElDrawer,ElSelect,ElInput,ElOption, ElMessage, FormInstance, FormRules, } from 'element-plus';
import 'element-plus/dist/index.css';
import { useRouter } from 'vue-router';
import { useI18n } from '@/hooks/web/useI18n';
import { cloneDeep } from 'lodash-es';
import { addPurchaseApi, addSaleApi, getProductInfoApi, getProductListApi, getPurchaseInfoApi, getPurchaseNewnumApi, getSaleInfoApi, getSaleListApi, updatePurchase<PERSON>pi, updateSaleApi } from '@/api/product';
import { checkFormRule, checkPermissionApi, closeOneTagByName, closeOneTagByPath, getGUID, getMoneyFlag, getTodayDate } from '@/api/tool';
import { getDepartmentListApi } from '@/api/usermanage';
import { getSaleNewnumApi } from '@/api/sale';
import { useAppStore } from '@/store/modules/app';
import { useCache } from '@/hooks/web/useCache';
import { getSupplierListApi } from '@/api/customer';


const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    buyer_nick: [{ required: true, message: t('msg.noCustom'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})

const activeItems = ref(['1', '2'])



//采购单数据
const purchaseData = reactive(
{
    "supplier_id": "",
    supplier_name:'',
    supplier_nick:'',
    "buy_order_num": "",
    "supplier_order_num": "",
    "money_type": "人民币",
    "delivery_date": "",
    "buy_man_id": "",
    buy_man_name:'',
    "follow_man_id": "",
    follow_man_name:'',
    "buy_dept_id": "",
    "create_date": "",
    "modify_date": "",
    "pdt_list": [],
    "note": "",
    "express_fee": 0.00,
    "other_fee": 0.00,
    "pay_type": "月结",
    business_date:'', //业务日期
    is_same_delivery:0,
    合计费用:0,
    source:'',
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:['提交']
    }, //审核决策
    fsm_cur_state:'订单创建',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)

const is_same_delivery_BOOL = computed({
    get: () =>{
        return purchaseData.is_same_delivery == 0?false:true
    },
    set:(value)=>{
        purchaseData.is_same_delivery = value?1:0
    }
}
)

//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])
//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])

//treeProp属性
const treeProps = {
    multiple: true,
    checkStrictly: true,
    value:'id', 
    children: 'sub_dept',
    label: 'name',
    emitPath:false
}

//获取最新ID
const onChangeID = async()=>{
    const ret = await getPurchaseNewnumApi()
    if(ret)
    {
        console.log(ret)
        purchaseData.buy_order_num = ret.data.new_id
    }
}


//输入发生变化时处理,要支持延时处理
const delaytime =ref<NodeJS.Timeout | null>(null); 
const onItemInput = (val)=>{
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)
    
    delaytime.value = setTimeout(() => {
      // 输入停止后触发的方法
      getProductList(val)
    }, 200) 
} 


//查询产品绑定的查询结果
const searchPdtData = reactive<any>([])
//查询产品列表根据编号
const getProductList = async (val='') => {
  const ret = await getProductListApi({
    name:val,
    nick:val,
    _or:true,
    prop_list:['采购产品','物料'],
    status:'正常',
    page: 1,
    count:30,
  })
  if(ret)
  {
    searchPdtData.splice(0,searchPdtData.length, ...ret.data)
    //if(searchPdtData.length >0)
    // {
    //     setCurrent(searchPdtData[0])
    // }
  }
}

//根据产品列表中数量计算采购总数
const totlePurchase = computed(() => {

    let totle = 0
    for(const item of purchaseData.pdt_list)
    {
        if(item.采购数量 == undefined || item.id == undefined)
            continue
        totle += parseFloat(item.采购数量)
        totle += parseFloat(item.采购备品数量)
    }
    return totle
})

//根据产品列表计算采购总价
const totlePurchasePrice = computed(() => {
    
    let totle = 0
    let span = 0
    for(const item of purchaseData.pdt_list)
    {
        if(item.总价 == undefined || item.id == undefined)
            continue
        if(item.sell_buy_digit != undefined)
            span = parseFloat(item.sell_buy_digit)
        totle += parseFloat(item.总价)
    }
    
    return  parseFloat(totle.toFixed(span))
})

//计算合计费用(包含运费和其他费用)
const totlePurchasePriceAll = computed(() => {
    return totlePurchasePrice.value + parseFloat(purchaseData.express_fee)+parseFloat(purchaseData.other_fee)
})

//计算备品价格
const countBakPrice = (pdt)=>{
    //备品数量占采购数量的3%以内不算钱，超过3%最多只计算1%的钱
    // if(pdt.采购备品数量<=pdt.采购数量*0.03)
    // {
    //     return 0
    // }
    // else
    // {
    //     return Math.min(parseFloat((pdt.采购备品数量*pdt.buy_price_aft_tax*pdt.折扣).toFixed(pdt.sell_buy_digit)) , parseFloat((pdt.采购数量*0.01*pdt.buy_price_aft_tax*pdt.折扣).toFixed(pdt.sell_buy_digit)))
    // }
    return 0
}



//重新分配PDT中的来源数量  销售备料
const reComputeSourceCountSell = (pdt) => {
    //组合PDT 才需要调整
    console.log(purchaseData.source)
    console.log('pdt.原始需求数量', pdt.原始需求数量, pdt)
    if(pdt.原始需求数量.indexOf(',')>=0)
    {
        let nTotle = parseFloat(pdt.采购数量) + parseFloat(pdt.采购备品数量)
        let arCount:string[] = pdt.原始需求数量.split(',')
        let arSourceTotle = 0
        //计算总需求
        for(const item of arCount)
        {
            arSourceTotle += parseFloat(item)
        }
        if (nTotle < arSourceTotle) {
            //需要按照从前往后的顺序来分nTotle，每份不超过原始需求数量中对应每一份的数量
            let arOut:string[] = []
            for(let one of arCount)
            {
                if(nTotle>0)
                {
                    if(nTotle>=parseFloat(one))
                    {
                        arOut.push(one)
                        nTotle -= parseFloat(one)
                    }
                    else
                    {
                        arOut.push(nTotle)
                        nTotle = 0
                    }
                }
            }
            pdt.需求数量 = arOut.join(',')
            console.log('调整后:',pdt.需求数量)
        }
        else
        {
            //还原默认
            pdt.需求数量 = pdt.原始需求数量            
            console.log('不需要调整:',pdt.采购数量,pdt.采购备品数量,nTotle,arSourceTotle)
        }

        //先还原
        pdt.sell_order_num = pdt.原始sell_order_num
        pdt.sell_order_sub = pdt.原始sell_order_sub
        pdt.父子任务单号 = pdt.原始父子任务单号
        pdt.标识 = pdt.原始标识

        //pdt.sell_order_num,pdt.sell_order_sub 对应的字符传逗号分割的数量要和 pdt.需求数量  保持一直 后面多的删除
        let arSell:string[] = pdt.sell_order_num.split(',')
        let arSub: string[] = pdt.sell_order_sub.split(',')
        let arZRWDH: string[] = pdt.父子任务单号.split(',')
        let arNeed: string[] = pdt.需求数量.split(',')
        let arBS:string[] = pdt.标识.split(',')
        //arSell arsUB 数组slice前arNeed长度 的内容
        let arSellOut:string[] = arSell.slice(0,arNeed.length)
        let arSubOut: string[] = arSub.slice(0, arNeed.length)
        let arZRWDHOut: string[] = arZRWDH.slice(0, arNeed.length)
        let arBSOut:string[] = arBS.slice(0,arNeed.length)

        console.log('arSellOut',arNeed.length,arSell.slice(0,arNeed.length))

        pdt.sell_order_num = arSellOut.join(',')
        pdt.sell_order_sub = arSubOut.join(',')
        pdt.父子任务单号 = arZRWDHOut.join(',')
        pdt.标识 = arBSOut.join(',')
    }
    else  //针对非组单也需要调整数量
    {
        let nTotle = parseFloat(pdt.采购数量) + parseFloat(pdt.采购备品数量)
        if(nTotle < parseFloat(pdt.原始需求数量))
        {
            pdt.需求数量 = nTotle 
        }
        else
        {
            pdt.需求数量 = pdt.原始需求数量
        }
    }
}



const reComputeSourceCount = (pdt) => {
    //组合PDT 才需要调整
    console.log('pdt.原始需求数量', pdt.原始需求数量, pdt)
    if(pdt.原始需求数量.indexOf(',')>=0)
    {
        let nTotle = parseFloat(pdt.采购数量) + parseFloat(pdt.采购备品数量)
        let arCount:string[] = pdt.原始需求数量.split(',')
        let arSourceTotle = 0
        //计算总需求
        for(const item of arCount)
        {
            arSourceTotle += parseFloat(item)
        }
        if (nTotle < arSourceTotle) {
            //需要按照从前往后的顺序来分nTotle，每份不超过原始需求数量中对应每一份的数量
            let arOut:string[] = []
            for(let one of arCount)
            {
                if(nTotle>0)
                {
                    if(nTotle>=parseFloat(one))
                    {
                        arOut.push(one)
                        nTotle -= parseFloat(one)
                    }
                    else
                    {
                        arOut.push(nTotle)
                        nTotle = 0
                    }
                }
            }
            pdt.需求数量 = arOut.join(',')
            console.log('调整后:',pdt.需求数量)
        }
        else
        {
            //还原默认
            pdt.需求数量 = pdt.原始需求数量            
            console.log('不需要调整:',pdt.采购数量,pdt.采购备品数量,nTotle,arSourceTotle)
        }

        //先还原
        pdt.oem_order_num = pdt.原始oem_order_num
        pdt.oem_order_sub = pdt.原始oem_order_sub
        pdt.父子任务单号 = pdt.原始父子任务单号
        pdt.标识 = pdt.原始标识

        //pdt.oem_order_num,pdt.oem_order_sub 对应的字符传逗号分割的数量要和 pdt.需求数量  保持一直 后面多的删除
        let arSell:string[] = pdt.oem_order_num.split(',')
        let arOem: string[] = pdt.oem_order_sub.split(',')
        let arZRWDH: string[] = pdt.父子任务单号.split(',')
        let arNeed: string[] = pdt.需求数量.split(',')
        let arBS:string[] = pdt.标识.split(',')
        //arSell arOem 数组slice前arNeed长度 的内容
        let arSellOut:string[] = arSell.slice(0,arNeed.length)
        let arOemOut: string[] = arOem.slice(0, arNeed.length)
        let arZRWDHOut: string[] = arZRWDH.slice(0, arNeed.length)
        let arBSOut:string[] = arBS.slice(0,arNeed.length)

        console.log('arSellOut',arNeed.length,arSell.slice(0,arNeed.length))

        pdt.oem_order_num = arSellOut.join(',')
        pdt.oem_order_sub = arOemOut.join(',')
        pdt.父子任务单号 = arZRWDHOut.join(',')
        pdt.标识 = arBSOut.join(',')
    }
    else  //针对非组单也需要调整数量
    {
        let nTotle = parseFloat(pdt.采购数量) + parseFloat(pdt.采购备品数量)
        if(nTotle < parseFloat(pdt.原始需求数量))
        {
            pdt.需求数量 = nTotle 
        }
        else
        {
            pdt.需求数量 = pdt.原始需求数量
        }
    }
}



const reComputePdtInfo = (pdt,mode) => {
    pdt.采购数量 = pdt.采购数量 == ''?0:pdt.采购数量
    pdt.采购备品数量 = pdt.采购备品数量 == ''?0:pdt.采购备品数量
    pdt.buy_price_bef_tax = (pdt.buy_price_bef_tax == ''||pdt.buy_price_bef_tax == null)?0:pdt.buy_price_bef_tax
    pdt.buy_price_aft_tax = (pdt.buy_price_aft_tax == '' ||pdt.buy_price_aft_tax == null)?0:pdt.buy_price_aft_tax
    pdt.折扣 = pdt.折扣 == ''?1:pdt.折扣

    if (isNaN(Number(pdt.发票税率))) {
        pdt.发票税率 = 0
    }

    if(mode == '采购数量')
    {
        //调整默认备品数量
       // pdt.采购备品数量 = parseFloat(pdt.采购数量*0.01)

        //根据采购数量计算总价 总价等于税后单价乘以采购数量
        //重新计算税后价格
        // pdt.buy_price_aft_tax = parseFloat(pdt.buy_price_bef_tax)+ parseFloat(pdt.buy_price_bef_tax)*pdt.发票税率/100
        pdt.总价 = parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣).toFixed(pdt.sell_buy_digit))+countBakPrice(pdt)

        //调整来源中的数量
        if(purchaseData.source === '销售转采购物料')
            reComputeSourceCountSell(pdt)
        else if(purchaseData.source .indexOf('委外转采购')>=0)
            reComputeSourceCount(pdt)
    }
    else if(mode  == '采购备品数量')
    {
        pdt.总价 = parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣).toFixed(pdt.sell_buy_digit))+countBakPrice(pdt)
        //调整来源中的数量
        if(purchaseData.source === '销售转采购物料')
            reComputeSourceCountSell(pdt)
        else if(purchaseData.source .indexOf('委外转采购')>=0)
            reComputeSourceCount(pdt)
    }
    else if(mode == '税前单价')
    {
        //根据税前单价和税率计算出税后单价         
        pdt.buy_price_aft_tax = parseFloat(pdt.buy_price_bef_tax)+ parseFloat(pdt.buy_price_bef_tax)*pdt.发票税率/100

        //计算备品价格

        //计算出总价
        pdt.总价 = parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣).toFixed(pdt.sell_buy_digit))+countBakPrice(pdt)
    }
    else if(mode == '税后单价')
    {
        //根据税后单价和税率计算出税前单价         
        pdt.buy_price_bef_tax = parseFloat((pdt.buy_price_aft_tax/(1+pdt.发票税率/100)).toFixed(pdt.sell_buy_digit))
        console.log('--',parseFloat(pdt.buy_price_aft_tax),(1+pdt.发票税率/100))
        //计算出总价
        pdt.总价 = parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣).toFixed(pdt.sell_buy_digit))+countBakPrice(pdt)
    }
    else if(mode == '折扣')
    {
        //根据折扣重新计算总价
        pdt.总价 = parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣).toFixed(pdt.sell_buy_digit))+countBakPrice(pdt)
    }
    else if(mode == '发票税率')
    {
        //根据发票类型调整税率数
        if(!['普票','专票','电子票'].includes(pdt.发票类型))
        {
            pdt.发票税率 = 0
            //重新调整税后价格
            pdt.buy_price_aft_tax = pdt.buy_price_bef_tax
        }
        //重新计算税后价格
        // pdt.buy_price_aft_tax = parseFloat(pdt.buy_price_bef_tax)+ parseFloat(pdt.buy_price_bef_tax)*pdt.发票税率/100
        pdt.buy_price_bef_tax = parseFloat((pdt.buy_price_aft_tax/(1+pdt.发票税率/100)).toFixed(pdt.sell_buy_digit))
        //重新计算总价
        pdt.总价 = parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣).toFixed(pdt.sell_buy_digit))+countBakPrice(pdt)
    }
    else if(mode == '总价')
    {
        //根据总价重新调整税前税后价格
        pdt.buy_price_aft_tax = parseFloat((pdt.总价/(parseFloat(pdt.采购数量)+parseFloat(pdt.采购备品数量))).toFixed(pdt.sell_buy_digit))
        pdt.buy_price_bef_tax = parseFloat((pdt.总价/((1+pdt.发票税率/100)*(parseFloat(pdt.采购数量)+parseFloat(pdt.采购备品数量)))).toFixed(pdt.sell_buy_digit))
    }

    pdt.采购数量 = parseFloat(pdt.采购数量, 10)
    pdt.采购备品数量 = parseFloat(pdt.采购备品数量, 10)
    pdt.buy_price_bef_tax = parseFloat(pdt.buy_price_bef_tax, 10)
    pdt.buy_price_aft_tax = parseFloat(pdt.buy_price_aft_tax, 10)
    pdt.折扣 = parseFloat(pdt.折扣, 10)
}



onMounted(async () => {    
    getDepartmentTreeInfo()
 
    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('purchase.add')
        

        //新建需要默认可以提审
        // const tmp = [...purchaseData.fsm_can_trig_data.操作触发,'提交审核']
        const tmp = [...purchaseData.fsm_can_trig_data.操作触发]
        purchaseData.fsm_can_trig_data.操作触发 = tmp

        //设置默认人员部门
        const info = wsCache.get(appStore.getUserInfo)
        purchaseData.buy_man_id = info.id
        purchaseData.buy_man_name = info.resident_name
        purchaseData.follow_man_id = info.id
        purchaseData.follow_man_name = info.resident_name
        purchaseData.buy_dept_id = info.depts[0]
        //默认销售日期为今天
        purchaseData.business_date = getTodayDate()
        

        //是否有销售转采购任务
        if(currentRoute.value.query.tobuy == '1')
        {
            purchaseData.source = currentRoute.value.query.source
            console.log('----',purchaseData.source,)
            const arPdtNum = wsCache.get('tobuy')
            console.log('===',arPdtNum)



            //添加pdt到列表
            for(const pdt of arPdtNum)
            {
                const ret = await getProductInfoApi({
                    name:pdt.pdt_name,
                    page: 1,
                    count:20,
                })
                if(ret)
                {

                    //如果有多个找到匹配的
                    let find =ret.data

                    handleCurrentSelectPdt(find,'',true)
                    if(purchaseData.source == '销售转采购')
                    {
                        if(pdt.销售备品数量 == undefined)
                            pdt.销售备品数量 = 0
                        find.sell_order_num = pdt.sell_order_num
                        find.需求数量 = pdt.当前需求
                        find.采购数量 = pdt.当前需求-pdt.销售备品数量
                        find.采购备品数量 = pdt.销售备品数量


                        //没有订单号表示不关联
                        if(pdt.sell_order_num == '')
                        {
                            find.标识 = getGUID(5)  
                            find.sell_order_sub = getGUID(5)
                            purchaseData.source = ''
                        }
                        else
                        {
                            find.标识 = pdt.标识   
                            find.sell_order_sub = pdt.sell_order_sub
                            find.父子任务单号 = pdt.子任务单号
                        }
                        //用户需求来源订单号为采购单号
                        if(arPdtNum[0].sell_order_num.indexOf(',') > 0)
                        {
                            arPdtNum[0].buy_order_num =  arPdtNum[0].sell_order_num.split(',')[0]
                        }
                        else
                            purchaseData.buy_order_num = arPdtNum[0].sell_order_num
                    }
                    if(purchaseData.source .indexOf('委外转采购')>=0)
                    {
                        if(pdt.委外备品数量 == undefined)
                            pdt.委外备品数量 = 0
                        find.oem_order_num = pdt.oem_order_num
                        find.需求数量 = pdt.需求数量
                        find.采购数量 = pdt.当前需求-pdt.委外备品数量
                        find.采购备品数量 = pdt.委外备品数量

                        find.原始需求数量 = pdt.需求数量
                        find.原始oem_order_num = pdt.oem_order_num
                        find.原始oem_order_sub = pdt.oem_order_sub
                        find.原始父子任务单号 = pdt.子任务单号
                        find.原始标识 = pdt.标识

                        find.物料总用量 = pdt.物料总用量

                        //没有订单号表示不关联
                        if(pdt.oem_order_num == '')
                        {
                            find.标识 = getGUID(5)  
                            find.oem_order_sub = getGUID(5)
                            purchaseData.source = ''
                        }
                        else
                        {
                            find.标识 = pdt.标识   
                            find.oem_order_sub = pdt.oem_order_sub
                            find.父子任务单号 = pdt.子任务单号
                        }
                        //用户需求来源订单号为采购单号
                        if(arPdtNum[0].oem_order_num.indexOf(',') > 0)
                            purchaseData.buy_order_num = arPdtNum[0].oem_order_num.split(',')[0]
                        else
                            purchaseData.buy_order_num = arPdtNum[0].oem_order_num
                    }                            
                    if(purchaseData.source == '销售转采购物料')
                    {
                        if(pdt.销售备品数量 == undefined)
                            pdt.销售备品数量 = 0
                        find.sell_order_num = pdt.sell_order_num
                        find.需求数量 = pdt.需求数量
                        find.采购数量 = pdt.当前需求-pdt.销售备品数量
                        find.采购备品数量 = pdt.销售备品数量
                        find.订单来源 = pdt.订单来源


                        find.原始需求数量 = pdt.需求数量
                        find.原始sell_order_num = pdt.sell_order_num
                        find.原始sell_order_sub = pdt.sell_order_sub
                        find.原始父子任务单号 = pdt.子任务单号
                        find.原始标识 = pdt.标识

                        find.物料总用量 = pdt.物料总用量  //传递销售单物料用量， 用于服务器计算 需求总量 （备料时打印需求）

                        //没有订单号表示不关联
                        if(pdt.sell_order_num == '')
                        {
                            find.标识 = getGUID(5)  
                            find.sell_order_sub = getGUID(5)
                            purchaseData.source = ''
                        }
                        else
                        {
                            find.标识 = pdt.标识   
                            find.sell_order_sub = pdt.sell_order_sub
                            find.父子任务单号 = pdt.子任务单号
                        }
                        //用户需求来源订单号为采购单号
                        if(arPdtNum[0].sell_order_num.indexOf(',') > 0)
                        {
                            arPdtNum[0].buy_order_num =  arPdtNum[0].sell_order_num.split(',')[0]
                        }
                        else
                            purchaseData.buy_order_num = arPdtNum[0].sell_order_num
                    }


                                
                    reComputePdtInfo(find,'采购备品数量')
                    console.log('=========11',find.标识)
                
                }
            }
        }

        if(purchaseData.buy_order_num == '')
        {
            await onChangeID()
        }

        console.log(purchaseData.pdt_list)

        //追加默认行
        purchaseData.pdt_list.push({总价:'0'}) 
    }
    else
    {        
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('purchase.look')
        }
        else
        {
            title.value = t('purchase.modify')
        }
        
        //查询产品信息 
        const ret = await getPurchaseInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(purchaseData, ret.data)


            // //更新供应商信息
            const ret2 = await getSupplierListApi({
                ids:[purchaseData.supplier_id],
                page:1,
                count:100
            });
            if (ret2) {
                //更新供应商发票和税率
                if(ret2.data.length>0)
                {
                    purchaseData.tax_type = ret2.data[0].tax_type=='纸质专票'?'专票':ret2.data[0].tax_type
                    purchaseData.tax_rate = ret2.data[0].tax_rate=='不含税'?0:ret2.data[0].tax_rate.replace('%','')
                }

            }


            purchaseData.pdt_list = ret.data.pdt_list;
            purchaseData.pdt_list.push({总价:'0'}) 
        }

        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
        
    }
})


//产品查找的输入
const txtSearch = ref('')
//点击了查找产品input
const onClickSearchPdt = ()=>{
    console.log('点了')
    getProductList(txtSearch.value)
    showProductSel.value = true
    // 添加全局点击事件监听器
    window.addEventListener('click', handleGlobalClick);
}
const handleGlobalClick = (event)=>{

    const elTableContainers = document.querySelectorAll('.el-table-container');      
    let clickedInsideTable = false;
    for (const container of elTableContainers) {
        if (container.contains(event.target)) {
            // 点击的元素在 el-table 容器内部
            clickedInsideTable = true;
            break;
        }
    }
        
    if (!clickedInsideTable) {
        // 判断点击位置是否在输入框和浮动窗口外部
        // const isOutsideClick = !inputElement.contains(event.target) && !floatWindowElement.contains(event.target);
        const bIn = (event.target as HTMLElement).classList.contains('el-input__inner')
        // 根据判断结果来决定是否隐藏浮动窗口
        if(!bIn)
        {
            showProductSel.value = false
        }
    }
}
//处理输入
const onInputSearch =(val)=>{
    showProductSel.value = true
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)
    
    delaytime.value = setTimeout(() => {
      // 输入停止后触发的方法
      getProductList(val)
    }, 200) 
}


//显示隐藏选择供应商弹窗
const showSelSupplierDlg = ref(false)
const onSelSupplier = ()=>{
    showSelSupplierDlg.value = true
}
//选择供应商回调
const onSelSupplierCallback = (id,name,nick,tax_type,tax_rate:string)=>{
    console.log(id,name,nick,tax_type,tax_rate)
    purchaseData.supplier_id = id
    purchaseData.supplier_name = name
    purchaseData.supplier_nick = nick
    purchaseData.tax_type = tax_type=='纸质专票'?'专票':tax_type
    purchaseData.tax_rate = tax_rate=='不含税'?0:(tax_rate.replace('%',''))
    console.log(purchaseData)

    //同步更新所有产品税率
    for(let item of purchaseData.pdt_list)
    {
        item.发票类型 = purchaseData.tax_type
        item.发票税率 =  parseInt(purchaseData.tax_rate)
        reComputePdtInfo(item,'发票税率')
    }
}


//显示隐藏选择采购员窗口变量
const showSelPurchaseUserDlg = ref(false)
//显示选择采购员弹窗
const onSelPurchase = ()=>{
    showSelPurchaseUserDlg.value = true
}
//选择采购员回调
const onSelPurchaseCallback = (id,name)=>{
    console.log(id,name)
    purchaseData.buy_man_id = id
    purchaseData.buy_man_name = name
}

//显示隐藏选择跟单员窗口变量
const showSelFollowerUserDlg = ref(false)
//显示选择跟单员弹窗
const onSelFollower = ()=>{
    showSelFollowerUserDlg.value = true
}
//选择跟单员回调
const onSelFollowerCallback = (id,name)=>{
    console.log(id,name)
    purchaseData.follow_man_id = id
    purchaseData.follow_man_name = name
}

//部门数据源
const deptData = reactive([])
//查询组织信息
const getDepartmentTreeInfo = async () => {
  const ret = await getDepartmentListApi({})
  console.log(ret)
  if (ret) {

    //deptData.splice(0, deptData.length, ...data)
    deptData.splice(0, deptData.length, ...ret.data.all_depts)
    console.log(deptData.length)
  }
}

//勾选使用统一交付时间事件
const onChangeDeliveryTime = (val)=>{
    if(val) //勾选
    {
        if(purchaseData.delivery_date == "")
        {
            purchaseData.delivery_date = getTodayDate()
        }
    }
}

//显示隐藏选择产品浮窗
const showProductSel = ref(false)
//选择了某一个产品
const handleCurrentSelectPdt = (item,old,fource = false) =>{
    if( item == undefined)
    {
        return
    }

    nCurSelPdtID.value = item.id
    if(!fource){
        return
    }
    txtSearch.value = ''
    
    setTimeout(() => {
        const inputElement = document.querySelector('.input-search input');
        inputElement?.focus()
        onClickSearchPdt()
    },500)
       
    
  
  
    //增加属性
   // item.当前库存 = ''
    item.采购数量 = 1
    item.折扣 = 1
    item.发票类型 = '专票'
    item.发票税率 = 0
    item.总价 = 0
    item.交货日期 = purchaseData.delivery_date == ''? getTodayDate():purchaseData.delivery_date
    item.类型 = '正常'
    item.供应商产品编码 = ''
    item.已收货 = 0
    item.未收货 = 0
    item.已退货 = 0
    item.已质检 = 0
    item.已入库 = 0
    item.标识 = getGUID(10)
    item.sell_order_num = ''
    item.采购备品数量 = 0
    item.原始需求数量 = ''
    item.原始父子任务单号 = ''
    item.原始oem_order_num = ''
    item.原始oem_order_sub = ''
    item.原始标识 = ''
    item.原始sell_order_num = ''
    item.原始sell_order_sub = ''

    //如果已经选择了供应商则自动同步税率
    if(purchaseData.tax_rate != undefined)
    {
        item.发票类型 = purchaseData.tax_type
        item.发票税率 = purchaseData.tax_rate =='不含税'?0:purchaseData.tax_rate
    }


    //更新产品价格
    reComputePdtInfo(item,'采购数量')

    //构造一行产品
    purchaseData.pdt_list.splice(-1,0,item)
    //隐藏浮窗
    showProductSel.value = false

    

    console.log(purchaseData)

    
}

//删除某一个产品
const onDelPdt = (index) => {
    if(purchaseData.pdt_list[index].已收货>0)
    {
        ElMessage.error('已收货的产品不能删除!')
        return
    }
    purchaseData.pdt_list.splice(index,1)
}


//校验pdt
const checkPdt = ()=>{
    if(purchaseData.pdt_list.length <= 1)
    {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}

//提交审核意见
const handleCheck = async(btn)=>{
    console.log(11)
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    const data = cloneDeep(purchaseData)
    if(!checkPdt())
    {
        return
    }
    data.pdt_list.splice(-1,1)

    //处理下空数据问题
    for(let pdt of data.pdt_list)
    {
        pdt.采购数量 = pdt.采购数量 == ''?0:pdt.采购数量
        pdt.采购备品数量 = pdt.采购备品数量 == ''?0:pdt.采购备品数量
        pdt.buy_price_bef_tax = pdt.buy_price_bef_tax == ''?0:pdt.buy_price_bef_tax
        pdt.buy_price_aft_tax = pdt.buy_price_aft_tax == ''?0:pdt.buy_price_aft_tax
        pdt.折扣 = pdt.折扣 == ''?1:pdt.折扣
    }


    const info = wsCache.get(appStore.getUserInfo)
    data.fsm_exe_man_name = info.resident_name
    data.fsm_exe_trig = btn
    
    if(data.source == '销售转采购')
    {
        closeOneTagByPath('/salemanage/saledemand')
    }
    else if(data.source.indexOf('委外转采购')>=0)
    {
        closeOneTagByPath('/oemmanage/wlneedinfo')
        closeOneTagByPath('/oemmanage/wlneedlist')
    }
    if(data.source == '销售转采购物料')
    {
        closeOneTagByPath('/salemanage/sellwlprepare')
    }
    

    if(data.id == undefined)
    {
        const ret = await addPurchaseApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.newPurchaseSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        const ret =await updatePurchaseApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.updatePurchaseSuccess'))

            if(btn == '恢复')
            {
                //查询产品信息 
                const ret = await getPurchaseInfoApi({
                    id:currentRoute.value.query.id,
                    page:1,
                    count:100
                })
                if(ret)
                {
                    console.log(ret)
                    Object.assign(data, ret.data)


                    // //更新供应商信息
                    const ret2 = await getSupplierListApi({
                        ids:[data.supplier_id],
                        page:1,
                        count:100
                    });
                    if (ret2) {
                        //更新供应商发票和税率
                        data.tax_type = ret2.data[0].tax_type
                        data.tax_rate = ret2.data[0].tax_rate.replace('%','')
                    }


                    data.pdt_list = ret.data.pdt_list;
                    data.pdt_list.push({总价:'0'}) 
                }
            }
            else    
            {
                baskFront()
            }
            
        }
    }
}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = ()=>{
    showCheckHisDlg.value = true
}


//返回上一页
const baskFront = ()=>{
    back()
}


// 使用 computed 定义计算属性
const sameDay = computed({
  get: () => (purchaseData.delivery_date === '' ? false : true),
  set: (value) => {
    if (value) {
        purchaseData.delivery_date = getTodayDate();
      //更新下面列表
      purchaseData.pdt_list.forEach((item, index) => {
          item.交货日期 = purchaseData.delivery_date;
      })
    } else {
        purchaseData.delivery_date = '';
    }
    console.log('->',purchaseData.delivery_date);
  },
});

const onChangeDeliverData = (value)=>{
    purchaseData.pdt_list.forEach((item, index) => {
          item.交货日期 = purchaseData.delivery_date;
      })
}







const bShowSearch = ref(false)

const freshPage = () => [
  window.location.reload()
]
</script>

<template>
  <div class="container text-[14px]">
    <el-affix :offset="10">
      <div class="w-[110%] flex bg-gray-200 p-2 mt-[-10px] ml-[-10px] mr-[20px] opacity-95 items-center">
        <el-button @click="back">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          {{ t('common.back') }}
        </el-button>
        <div class="!ml-auto !mr-auto">
          {{purchaseData.fsm_cur_state}}
        </div>
        <el-button class="!ml-auto mr-5" @click="freshPage">
          <Icon icon="solar:refresh-broken" />
          刷新
        </el-button>
      </div>
    </el-affix>

    <el-card id="check" shadow="never" v-if="purchaseData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%] mt-4 mb-4">
        审核原因：
        <el-input v-model="purchaseData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        <div class="mt-4 flex justify-end">
            <ElButton v-show="purchaseData.fsm_can_trig_data.审核触发.includes('同意')" type="success" @click="handleCheck('同意')" >同意</ElButton>
            <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                <template #reference>
                    <ElButton v-show="purchaseData.fsm_can_trig_data.审核触发.includes('驳回')" type="warning" >驳回</ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                <template #reference>
                    <ElButton v-show="purchaseData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                </template>
            </el-popconfirm>  
        </div>
    </el-card>



    <div class="header">
      <h1>{{ title }}</h1>
    </div>

    <div class="flex justify-center items-center border-b border-t pt-2 pb-2 border-gray-400">
      <div class="flex-1">
        {{ checkPermissionApi('供应商名称显示')?( purchaseData.supplier_nick?purchaseData.supplier_nick:'请选择'):'***' }}
      </div>
      <div class="flex-1 text-center ">
        单号:{{ purchaseData.buy_order_num }}
      </div>
    </div>
    <div class="flex justify-center items-center border-b  pt-2 pb-2 border-gray-400">
      <div class="flex-1 bo">
        客户单号:{{ purchaseData.diy_order_num }}
      </div>
      <div class="flex-1 text-center">
        创建日期:{{ purchaseData.create_date.split(' ')[0]  }}
      </div>
    </div>
    <div class="flex justify-center items-center border-b  pt-2 pb-2 border-gray-400">
      <div class="flex-1">
        币种:{{ purchaseData.money_type }}
      </div>
      <div class="flex-1 text-center">
        交货日期:{{ purchaseData.delivery_date }}
      </div>
    </div>
    <div class="flex justify-center items-center border-b  pt-2 pb-2 border-gray-400">
      <div class="flex-1">
        采购:{{ purchaseData.buy_man_name }}
      </div>
      <div class="flex-1 text-center">
        跟单:{{ purchaseData.follow_man_name }}
      </div>
    </div>
    <el-collapse v-model="activeItems" class="mt-2">
      <el-collapse-item name="1">
        <template #title>
          <div class='title'>
            采购商品:
            <span class="text-[13px] text-red-600 ml-3" v-show="checkPermissionApi('采购订单价格显示')">总价:{{ totlePurchasePrice }}</span>
            
          </div>
        </template>
        <div class="border border-gray-300">
          <div v-for="pdt in purchaseData.pdt_list.filter(pdt => pdt.id !== undefined)" :key="pdt.id"  class="flex justify-start items-center w-[100%] p-1 border-b border-gray-400">
            <el-image v-if="pdt.pics?.length>0"  class="object-fill w-[50px] h-[50px] min-w-[50px] cursor-pointer" :src="pdt.pics[0].url" />
            <el-image v-if="pdt.pics?.length<=0"  class="object-fill w-[50px] h-[50px] min-w-[50px] cursor-pointer" src="/nopic.jpg" />
            <div class="ml-2 inline-block text-left w-[80%] ">
              <div style="white-space: normal;font-size:'12px'" class="nameStyle" >{{ '['+pdt.name+']'+pdt.nick }}</div>
              <div class="flex text-[13px] mt-1">
                <span class="text-red-400 mr-2" v-show="checkPermissionApi('采购订单价格显示')">{{ getMoneyFlag(purchaseData.money_type)+parseFloat(pdt.buy_price_aft_tax.toFixed(4))}}</span>{{'x '+pdt.采购数量 +' 备:'+pdt.采购备品数量 +'  '+pdt.base_unit }}
              </div>
              <div class="mt-1">
                交货日期:{{ pdt.交货日期 }}
              </div>
              <div>
                备注:{{ pdt.采购备注 }}
              </div>
            </div>
            <div class="text-red-600 text-right flex-1" v-if="purchaseData.pdt_list.length>1 " v-show="checkPermissionApi('采购订单价格显示')">
                {{getMoneyFlag(purchaseData.money_type)+ pdt.总价 }}
            </div>  

          </div>
          <div class=" text-blue-500 ml-3 mt-2 text-left">总数量:{{ totlePurchase }}</div>
        </div>
      </el-collapse-item>
      <el-collapse-item name="2">
        <template #title>
          <div class='title'>
            备注:
          </div>
        </template>
        <div>
          {{  purchaseData.note }}
        </div>
      </el-collapse-item>
      <el-collapse-item name="3">
        <template #title>
          <div class='title'>
            合计金额:
            <span class="text-[13px] text-red-600 ml-3" v-show="checkPermissionApi('采购订单价格显示')">总价:{{ totlePurchasePrice }}</span>
          </div>
        </template>
        <div>
          <div class="flex justify-center items-center">
            <div class="flex-1">
              运费:{{ purchaseData.express_fee }}
            </div>
            <div class="flex-1 text-center">
              其他费用:{{ purchaseData.other_fee }}
            </div>
          </div>
          <div class="flex justify-center items-center ">
            <div class="flex-1">
              付款方式:{{ purchaseData.pay_type }}
            </div>
            <div class="flex-1 text-center" v-show="checkPermissionApi('采购订单价格显示')">
              产品合计:{{ totlePurchasePrice }}
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>


<!-- 
    <el-row :gutter="10">
      <el-col :span="24" v-for="(item, index) in purchaseData" :key="index" class="menu-item flex flex-col">



      </el-col>
    </el-row> -->

  </div>

  <el-drawer v-model="bShowSearch" :size="'80%'" :direction="'btt'">
    <template #header>
      <h4>筛选</h4>
    </template>
    <template #default>
      <div>12</div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="searchCondition.reset()">清除</el-button>
        <el-button type="primary" @click="getSaleList();bShowSearch = false">查询</el-button>
      </div>
    </template>
  </el-drawer>


</template>

<style lang="less" scoped>
.container {
  padding: 1px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  margin: 1px 0 20px; /* 调整标题栏的上下边距 */
  color: #333;
}

.back-button {
  font-size: 24px;
  margin-bottom: 20px; /* 调整按钮与标题栏之间的距离 */
}

.el-row {
  margin-bottom: 10px; /* 纵向间距 */
}

.el-row:last-child {
  margin-bottom: 0;
}

.menu-item {
  margin-right: 0;
  margin-bottom: 10px;
  justify-content: flex-start; /* 调整对齐方式 */
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;

  .icon {
    font-size: 50px;
    color: #fff;
    margin-right: 20px; /* 调整图标和标题之间的间距 */
    padding: 10px; /* 调整图标内边距 */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 18px;
    color: #333;
    font-weight: 500;
  }

  .ellipsis {
    max-width: 70%; /* 限制最大宽度 */
    white-space: nowrap; /* 防止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 添加省略号 */
  }
}

//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt{ //未收货
  background-color: #79bbff;
  // border: #d9d9d9 1px solid;
}
.title_partial{ //部分收货
  background-color: #eebe77;
}
.title_ok{ //完全收货
  background-color: #95d475;
}
.title_over{//超量收货
  background-color: #FFC0CB;
}
.title_create{ //已创建
  color: #409EFF;
  background-color: color-mix(in oklch, #409EFF, transparent 80%);
}
.title_checked{ //已审核
  color: #67C23A;
  background-color: color-mix(in oklch, #67C23A, transparent 80%);
}
.title_wait{//等待修改
  background-color: #FFC0CB;
  background-color: color-mix(in oklch, #FFC0CB, transparent 80%);
}
.title_ok2{ //完全收货
  background-color: #95d475;
  background-color: color-mix(in oklch, #95d475, transparent 80%);
}


.searchItem{
  width: 150px;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}



//----------------------------
//标题设置
.title {
  font-weight: 900;
  font-size: large;
  margin-left: 20px;
}

//标题前提示
.title::before {
  content: 'I';
  color: red;
  margin-right: 10px;
}

:deep(.el-collapse-item__header) {
  height: 40px; /* 你想要设置的高度 */
  line-height: 40px; /* 确保内容垂直居中 */
  background-color: #eeeeee;
}
</style>

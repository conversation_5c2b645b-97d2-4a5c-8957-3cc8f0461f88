<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDialog,ElForm,ElFormItem,ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi, updatePayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, closeOneTagByPath, downloadFile, getTodayDate } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { DialogGetMoney } from '@/components/DialogGetMoney'
import { exportSellReportMonthApi, exportSellReportMonthInfoApi } from '@/api/extra';
import { addLcmApi, delLcmApi, getLcmListApi, updateLcmApi } from '@/api/usermanage';
import { DialogUser } from '@/components/DialogUser'
import { DialogFileList } from "@/components/DialogFileList";

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    部门: '',
    员工编号: '',
    员工姓名: '',
    员工状态: '',
    合同状态: '',
    合同类型: '',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//对账单数据源
const lcmData = reactive([])
//当前选中行
const currentRow = ref(null)

const loading = ref(false)
//查询合同数据
const getLcmList = async () => {
    let tmp = cloneDeep(searchCondition)
    loading.value = true
    const ret = await getLcmListApi(tmp)
    if (ret) {
        lcmData.splice(0, lcmData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
    loading.value = false
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getLcmList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getLcmList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getLcmList()
}

//创建
const onAddLcm = () => {
    showSelUserDlg.value = true
}

//处理表格对象操作
const handleOper = (type, row) => {
    if (type === 'edit' || type == 'info') {
        //弹出创建窗口
        showLcmEdit.value = true
        console.log(row)
        selItem.start_end = [row.start_date, row.end_date]
        Object.assign(selItem, row)
    }
    else if (type === 'del') {
        ElMessageBox.confirm(
            '确定是否删除该合同？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {

                const ret = await delLcmApi({
                    "ids": [row.id],
                })
                if (ret) {
                    getLcmList();
                    ElMessage({
                        type: 'success',
                        message: t('msg.delOK'),
                    });
                }
            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: t('msg.delChannel'),
                })
            })
    }
}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}


onMounted(() => {

    if (currentRoute.value.query.name != undefined) {
        searchCondition.员工编号 = currentRoute.value.query.name
    }

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    //刷新表格
    getLcmList()
})


const loadingExport = ref(false)
//导出列表
const onExport = async () => {
    //确认是否导出当前质检单列表
    ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
    }
    ).then(async () => {
        ElMessage.success('暂不支持导出');
        return;

        // loadingExport.value = true
        // let tmp = cloneDeep(searchCondition)

        // const ret = await exportSellReportMonthApi(tmp)
        // if (ret) {

        //     ElMessage.success('导出成功，等待下载！')
        //     setTimeout(() => {
        //       loadingExport.value = false
        //       downloadFile(ret.data.download,ret.data.filename)
        //     }, 4000)
        // }


    })

    setTimeout(() => {
        loadingExport.value = false
    }, 10000)

}

//显示隐藏选择人员
const showSelUserDlg = ref(false)
//选择销售员回调
const onSelUserCallback = (id, name,param,user) => {
    console.log(id, name,user)
    //弹出创建窗口
    showLcmEdit.value = true
    selItem.reset();
    selItem.user_name = user.username
    selItem.user_nick = user.resident_name
    selItem.sign_date = getTodayDate()
    selItem.expect_date = getTodayDate(30)
    selItem.start_end = [getTodayDate(), getTodayDate(365)]
    console.log(selItem.id)
}


const showLcmEdit = ref(false)
const selItemdef = {
    user_name:'',
    user_nick:'',
    pact_type:'劳动合同',
    pact_status:'',
    pact_number:'',
    pact_name:'',
    sign_date: '',
    start_end:'',
    start_date:'',
    end_date:'',
    expect_date:'',
    pact_note: '',
}
const selItem = reactive({
    ...selItemdef,
    reset() {
        for (let key in this) {
            if (this.hasOwnProperty(key) &&!(key in selItemdef) && key != 'reset') {
                    delete this[key];
            }
        }
        Object.assign(this, selItemdef)
    }
})

const onModifyLcm = async () => {
    //校验内容
    if (selItem.pact_number === '') {
        ElMessage.error('合同编号不能为空')
        return
    }
    if (selItem.pact_name === '') {
        ElMessage.error('合同名称不能为空')
        return
    }
    if (selItem.sign_date === '' || selItem.sign_date == null) {
        ElMessage.error('签订日期不能为空')
        return
    }
    if(selItem.pact_type === '劳动合同')
    {
        if (selItem.pact_status === '') {
            ElMessage.error('合同状态不能为空')
            return
        }
        if (selItem.start_end.length == 0) {
            ElMessage.error('合同开始结束日期不能为空')
            return
        }

        
    }



    showLcmEdit.value = false
    let tmp = cloneDeep(selItem)
    tmp.start_date = selItem.start_end[0] 
    tmp.end_date = selItem.start_end[1]
    if (tmp.id == undefined) {
        const ret = await addLcmApi(tmp)
        if (ret) {
            console.log(ret)
            ElMessage.success('新增成功')
            getLcmList()
        }
    }
    else {
        const ret = await updateLcmApi(tmp)
        if (ret) {
            console.log(ret)
            ElMessage.success('修改成功')
            getLcmList()
        }
    }

}


const curSelItem = ref({id:'0',file_list:[],pic_list:[]})
//显示文件列表
const curSelFileType = ref('图片')
const curTitle = ref('')
const showFileList = ref(false)
const onShowFileList = (item,type)=>{
  showFileList.value = true
  console.log(item)
  curSelItem.value = item
  curSelFileType.value = type 

  if(curSelFileType.value === '图片')
  {
    curTitle.value = '合同图片查看'
  }
  else
  {
    curTitle.value = '合同文件查看'
  }
}
const onUpdateFileList = async(list)=>{
  if(curSelFileType.value === '图片')
  {
    curSelItem.value.pic_list = [...list]
  }
  else
  {
    curSelItem.value.file_list = [...list]
  }
  // console.log('11111',curSelItem)
//   curSelItem.value.fsm_exe_trig= '保存'
  //更新文件列表
  const ret =await updateLcmApi(curSelItem.value)
  if(ret)
  {
      ElMessage.success('更新文件成功！')
      getLcmList()
  }
}

</script>

<template>
    <div ref="rootRef">
        <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">
            <div class="absolute top-5 left-8">
                <ElButton type="success" @click="onAddLcm">
                    <Icon icon="fluent-mdl2:people-add" />
                    <div class="pl-2">新增合同</div>
                </ElButton>
                <!-- <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                    <Icon icon="carbon:export" />
                    <div class="pl-2">{{ t('button.export') }}</div>
                </ElButton> -->
            </div>
            <div class="text-center mb-5 font-bold" style="color:#333">员工合同列表</div>
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">部门</div>
                    <el-input size="small" v-model="searchCondition.部门" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">员工编号</div>
                    <el-input size="small" v-model="searchCondition.员工编号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">员工姓名</div>
                    <el-input size="small" v-model="searchCondition.员工姓名" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">员工状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.员工状态" placeholder="">
                        <el-option v-for="item in ['在职','离职']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">合同状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.合同状态" placeholder="">
                        <el-option v-for="item in ['未签劳动合同','1周内到期','1月内到期','3月内到期','6月内到期','已到期']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>   
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">合同类型</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.合同类型" placeholder="">
                        <el-option v-for="item in ['劳动合同','入职申请书','离职申请书','合作协议','保密协议','其他']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div> 

                <div class="flex justify-end items-center mr-6 mt-4">
                    <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="ml-1">查询</div>
                    </ElButton>
                    <ElButton class="ml-4" type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="ml-1">清除</div>
                    </ElButton>
                </div>
            </div>

            <el-table v-loading.lock="loading" ref="userTableRef11" header-cell-class-name="tableHeader" :data="lcmData"
                style="width: 100%;color: #666666;"
                @current-change="setCurrentRow" border stripe>
                <el-table-column align="center" show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60">
                    <template #default="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="user_name" :label="'员工账号'" />
                <el-table-column align="center" show-overflow-tooltip prop="user_nick" :label="'员工姓名'" />
                <el-table-column align="center" show-overflow-tooltip prop="pact_number" :label="'合同编号'" />
                <el-table-column align="center" show-overflow-tooltip prop="pact_name" :label="'合同名称'" />
                <el-table-column align="center" show-overflow-tooltip prop="pact_type" :label="'合同类型'" />
                <el-table-column align="center" show-overflow-tooltip prop="sign_date" :label="'签订日期'" />
                <el-table-column align="center" show-overflow-tooltip  :label="'合同起止日期'" :min-width="120">
                    <template #default="scope">
                        {{ scope.row.start_date }} - {{ scope.row.end_date }}
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="pact_note" :label="'备注'" />
                <el-table-column align="center" show-overflow-tooltip prop="pact_note" :label="'员工状态'" />
                <el-table-column align="center" show-overflow-tooltip  :label="'文件'" :min-width="100">
                    <template #default="scope">
                        <div class="flex justify-center items-center">
                            <el-button :type="scope.row.pic_list.length>0?'warning':'info'" size="small" @click="onShowFileList(scope.row,'图片')">图片{{ scope.row.pic_list.length }}</el-button>
                            <el-button :type="scope.row.pic_list.length>0?'warning':'info'" size="small" @click="onShowFileList(scope.row,'文件')">文件{{ scope.row.file_list.length }}</el-button>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
                    <template #default="scope">
                        <el-dropdown trigger="click" placement="bottom">
                            <span class="el-dropdown-link">
                                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                            </span>
                            <template #dropdown>
                                <ElDropdownMenu>
                                    <el-dropdown-item @click="handleOper('edit', scope.row)">编辑</el-dropdown-item>
                                    <el-dropdown-item  @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                                </ElDropdownMenu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>

        <DialogUser :param="''" v-model:show="showSelUserDlg" :title="t('msg.selectUser')"
            @on-submit="onSelUserCallback" />
        <el-dialog :title="selItem.id == undefined?'新增合同':'合同编辑'" v-model="showLcmEdit" width="700" align-center destroy-on-close>
            <el-form>
                <el-form-item label="员工编号:">
                    {{selItem.user_name}}
                </el-form-item>
                <el-form-item label="员工姓名:">
                    {{selItem.user_nick}}
                </el-form-item>
                <el-form-item label="合同类型:">
                    <el-select  class="searchItem1" v-model="selItem.pact_type" placeholder="" >
                        <el-option v-for="item in ['劳动合同','入职申请书','离职申请书','合作协议','保密协议','其他']" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="签订状态:" v-if="selItem.pact_type == '劳动合同'">
                    <el-select   class="searchItem1" v-model="selItem.pact_status" placeholder="" >
                        <el-option v-for="item in ['新入职','续签','续签(无固定期限)']" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同编号:">
                    <el-input v-model="selItem.pact_number" placeholder="请输入合同编号"></el-input>
                </el-form-item>
                <el-form-item label="合同名称:">
                    <el-input v-model="selItem.pact_name" placeholder="请输入合同名称"></el-input>
                </el-form-item>
                <el-form-item label="签订日期:">
                    <el-date-picker   v-model="selItem.sign_date"  range-separator="到"
                    start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" :clearable="false"/>
                </el-form-item>
                <el-form-item label="有效日期:" v-if="selItem.pact_type == '劳动合同'">
                    <el-date-picker   v-model="selItem.start_end" type="daterange" range-separator="到"
                    start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="转正日期:" v-if="selItem.pact_type == '劳动合同'">
                    <el-date-picker   v-model="selItem.expect_date"  range-separator="到"
                    start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="合同备注:">
                    <el-input v-model="selItem.pact_note" placeholder="请输入合同备注"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="showLcmEdit = false">取消</el-button>
                    <el-button type="danger" @click="onModifyLcm">{{ selItem.id == undefined?'新增':'修改' }}</el-button>
                    <!-- <el-popconfirm  title="确定是否修改职位?" @confirm="onModifyJob">
                <template #reference>
                  
                </template>
    </el-popconfirm> -->
                </div>
            </template>

        </el-dialog>

        <DialogFileList :path="'file/ht/'+curSelItem.user_name+'/'" v-model:show="showFileList" :files="(curSelFileType=='图片'?curSelItem.pic_list:curSelItem.file_list)" @on-update="onUpdateFileList" :type="curSelFileType" :title="curTitle"/>
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>

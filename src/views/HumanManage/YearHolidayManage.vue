<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTooltip,ElRadioGroup, ElRadioButton, ElTabs, ElTabPane, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { delFinanceWaterApi, getFinanceAccountListApi, getFinanceWaterListApi } from '@/api/finance';
import type { TabsInstance } from 'element-plus'
import { DialogMoneyDetail } from '@/components/DialogMoneyDetail'
import { delHolidayApi, getHolidayListApi, getHolidayUserListApi, getYearHolidayUserListApi } from '@/api/usermanage';

const { push,currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    年份: '2024',
    员工编号: '',
    员工姓名: '',
    部门:'',
  page: 1,
  count: 20
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})


//数据源
const holidayData = reactive([])

//获取明细
const getYearHolidayList = async (page = 1) => {
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  const ret = await getYearHolidayUserListApi(tmp)
  if (ret) {
      holidayData.splice(0, holidayData.length, ...ret.data);
    totleCount.value = ret.count
  }
}


//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getYearHolidayList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getYearHolidayList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getYearHolidayList(val)
}
//创建
const onAddHoliday = () => {
    // push({
    //   path: '/holidaymanage/holiadyadd',
    //   query: {
    //     id: '',
    //   }
    // })
}

//处理表格对象操作
const handleOper = (type, row) => {
  if (type === 'edit' || type == 'info') {
    // push({
    //   path: '/holidaymanage/holiadyadd',
    //   query: {
    //     id: row[0],
    //     type: type
    //   }
    // })
  }
//   else if (type === '删除') {

//     ElMessageBox.confirm(
//         '确定是否删除该假期？',
//         t('msg.warn'),
//         {
//             confirmButtonText: t('msg.ok'),
//             cancelButtonText: t('msg.channel'),
//             type: 'warning',
//         }
//     )
//     .then(async () => {

//         const ret = await delHolidayApi({ "ids": [row.id] })
//         if (ret) {
//             getYearHolidayList()

//             ElMessage({
//                 type: 'success',
//                 message: t('msg.delOK'),
//             })
//         }


//     })
//     .catch(() => {
//         ElMessage({
//             type: 'info',
//             message: t('msg.delChannel'),
//         })
//     })
//   }
  
//   else if(type === '修改')
//   {
//     ElMessageBox.confirm(
//         '确定是否修改该流水？',
//         t('msg.warn'),
//         {
//             confirmButtonText: t('msg.ok'),
//             cancelButtonText: t('msg.channel'),
//             type: 'warning',
//         }
//     )
//     .then(async () => {

//       showDetail.value = true
//       curDetailData.value = row
//       showMode.value = 'edit'

//     })
//     .catch(() => {
//         ElMessage({
//             type: 'info',
//             message: '取消修改',
//         })
//     })



//   }
}
const dayData = reactive([])

onMounted(async () => {

  const currentDate = new Date();
  const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
  
  await getYearHolidayList()
  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });


})



const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
    console.log('======', row, column, rowIndex, columnIndex)
    return { backgroundColor: 'yellow' }; 
}
</script>

<template>
  <div ref="rootRef" class="">
    <div class="pb-[100px] w-[100%] !bg-white flex-grow " style="color:#666666">
      <div class="absolute top-10 left-8">
        <!-- <ElButton type="success" @click="onAddHoliday">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">新增请假单</div>
        </ElButton> -->
      </div>
      <div class="text-center mb-5 font-bold pt-5" style="color:#333">{{ searchCondition.年份 + '年 ' }}员工年假记录
      </div>
      <div class="flex flex-col justify-center items-center">
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">年份</div>
          <el-date-picker @change="getYearHolidayList" @calendar-change="getYearHolidayList" :clearable="false" v-model="searchCondition.年份"
            type="year" placeholder="Pick a year" format="YYYY" value-format="YYYY" />
        </div>
      </div>

      <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-1 pl-5 bg-light-200">
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">员工账号</div>
          <el-input size="small" v-model="searchCondition.员工编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">员工姓名</div>
          <el-input size="small" v-model="searchCondition.员工姓名" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">部门</div>
          <el-input size="small" v-model="searchCondition.部门" placeholder="" class="searchItem" />
        </div>
        

        <div class="flex justify-end items-center mr-6">
          <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
            <Icon icon="tabler:search" />
            <div class="ml-1">查询</div>
          </ElButton>
          <ElButton class="ml-4" type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="ml-1">清除</div>
          </ElButton>
        </div>
      </div>

      <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="holidayData"
        style="width: 100%;color: #666666;" border stripe>
        <el-table-column align="center" show-overflow-tooltip fixed prop="姓名" :label="'姓名'" :min-width="120"/>
        <el-table-column align="center" show-overflow-tooltip fixed prop="部门" :label="'部门'" :min-width="200"/>
        <el-table-column align="center" show-overflow-tooltip fixed prop="参加工作时间" :label="'参见工作时间'" />
        <el-table-column align="center" show-overflow-tooltip fixed prop="离职日期" :label="'离职日期'" />
        <el-table-column align="center" show-overflow-tooltip fixed prop="工龄" :label="'工龄(年)'" />
        <el-table-column align="center" show-overflow-tooltip fixed prop="当前获得天数" :label="'当前获得天数'" />
        <el-table-column align="center" show-overflow-tooltip fixed prop="已使用" :label="'已使用'" />
        <el-table-column align="center" show-overflow-tooltip fixed prop="剩余天数" :label="'剩余天数'" />
        

    
        <!-- <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
          <template #default="scope">
            <el-dropdown trigger="click" placement="bottom">
              <span class="el-dropdown-link">
                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
              </span>
              <template #dropdown>
                <div class="flex flex-wrap w-[200px]">
                  <el-dropdown-item @click="handleOper('流水明细', scope.row)">流水明细</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('修改', scope.row)">修改</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('删除', scope.row)">删除</el-dropdown-item>
                </div>
              </template>
            </el-dropdown>
          </template>
        </el-table-column> -->
      </el-table>
      <el-pagination class="justify-end mt-8 mb-[200px]" v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
        layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
 
    </div>
  </div>

</template>

<style lang="less" scoped>
:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  color: #00BA80;
  cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.searchItem {
  width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
  width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
  font-size: 12px;
  white-space: normal;
  color: black;
  padding-left: 1px;
  padding-right: 1px;
}
</style>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDialog,ElForm,ElFormItem,ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi, updatePayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, closeOneTagByPath, downloadFile, getTodayDate } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { DialogGetMoney } from '@/components/DialogGetMoney'
import { exportSellReportMonthApi, exportSellReportMonthInfoApi } from '@/api/extra';
import { addLcmApi, delLcmApi, getLcmListApi, getUserListApi, updateLcmApi } from '@/api/usermanage';
import { DialogUser } from '@/components/DialogUser'
import { DialogFileList } from "@/components/DialogFileList";

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    转正预警:'转正不足30天',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

//数据源
const warnData = reactive([])
//当前选中行
const currentRow = ref(null)

const loading = ref(false)
//查询合同数据
const getWarnList = async () => {
    let tmp = cloneDeep(searchCondition)
    loading.value = true
    const ret = await getUserListApi(tmp)
    if (ret) {
        warnData.splice(0, warnData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
    loading.value = false
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getWarnList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getWarnList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getWarnList()
}

//创建
const onAddLcm = () => {

}

//处理表格对象操作
const handleOper = (type, row) => {
    return;
    if (type === 'edit' || type == 'info') {
        //弹出创建窗口
        showLcmEdit.value = true
        Object.assign(selItem, row)
    }
    else if (type === 'del') {
        ElMessageBox.confirm(
            '确定是否删除该合同？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {

                const ret = await delLcmApi({
                    "ids": [row.id],
                })
                if (ret) {
                    getLcmList();
                    ElMessage({
                        type: 'success',
                        message: t('msg.delOK'),
                    });
                }
            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: t('msg.delChannel'),
                })
            })
    }
}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}


onMounted(() => {
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    //刷新表格
    getWarnList()
})




</script>

<template>
    <div ref="rootRef">
        <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">
            <div class="absolute top-5 left-8">
                <ElButton type="success" @click="onAddLcm">
                    <Icon icon="fluent-mdl2:people-add" />
                    <div class="pl-2">新增合同</div>
                </ElButton>
                <!-- <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                    <Icon icon="carbon:export" />
                    <div class="pl-2">{{ t('button.export') }}</div>
                </ElButton> -->
            </div>
            <div class="text-center mb-5 font-bold" style="color:#333">员工合同列表</div>
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">部门</div>
                    <el-input size="small" v-model="searchCondition.部门" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">员工编号</div>
                    <el-input size="small" v-model="searchCondition.员工编号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">员工姓名</div>
                    <el-input size="small" v-model="searchCondition.员工姓名" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">员工状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.员工状态" placeholder="">
                        <el-option v-for="item in ['在职','离职']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">合同状态</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.合同状态" placeholder="">
                        <el-option v-for="item in ['未签劳动合同','1周内到期','1月内到期','3月内到期','6月内到期','已到期']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div>   
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">合同类型</div>
                    <el-select size="small" class="searchItem" v-model="searchCondition.合同类型" placeholder="">
                        <el-option v-for="item in ['入职合同','合作协议','劳动合同','保密协议','离职合同','其他']" :key="item" :label="item"
                            :value="item" />
                    </el-select>
                </div> 

                <div class="flex justify-end items-center mr-6 mt-4">
                    <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="ml-1">查询</div>
                    </ElButton>
                    <ElButton class="ml-4" type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="ml-1">清除</div>
                    </ElButton>
                </div>
            </div>

            <el-table v-loading.lock="loading" ref="userTableRef11" header-cell-class-name="tableHeader" :data="warnData"
                style="width: 100%;color: #666666;"
                @current-change="setCurrentRow" border stripe>
                <el-table-column align="center" show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60">
                    <template #default="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="username" :label="'员工账号'" />
                <el-table-column align="center" show-overflow-tooltip prop="resident_name" :label="'员工姓名'" />
                <el-table-column align="center" show-overflow-tooltip prop="depts_name" :label="'部门'" />
                <el-table-column align="center" show-overflow-tooltip prop="entry_date" :label="'入职日期'" />
                <el-table-column align="center" show-overflow-tooltip prop="sign_date" :label="'签订日期'" />
                <el-table-column align="center" show-overflow-tooltip prop="合同开始日期" :label="'合同开始日期'" />
                <el-table-column align="center" show-overflow-tooltip prop="合同结束日期" :label="'合同结束日期'" />
                <el-table-column align="center" show-overflow-tooltip prop="信息" :label="'信息'" />

                <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
                    <template #default="scope">
                        <ElButton type="primary" size="small">合同列表</ElButton>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>

    
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>

<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { ElRadioGroup,ElRadioButton,ElDialog,ElDropdown,ElDropdownMenu,ElDropdownItem,ElForm,ElFormItem ,ElButton, ElInput,ElPopconfirm,ElTable, ElTableColumn,ElTag, ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getHolidayTypeListApi,delHolidayTypeApi, updateUserApi, addHolidayTypeApi } from '@/api/usermanage'
import { DialogUser } from '@/components/DialogUser'

const { push,currentRoute } = useRouter()
const { t } = useI18n()
//权限数据源
const typeData = reactive([])


//新增
const onAddHolidayType = () => {
    console.log(currentRoute.value.name);
    showModifyHolidayType.value = true;
    selItem.reset()
}

//获取假期数据
const getHolidayTypeList = async () => {
  const res = await getHolidayTypeListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    typeData.splice(0,typeData.length,...res.data)
  }

}

// //编辑假期
// const onEditHolidayType = (row) => {
//   //进入编辑页面
//   push({
//     path: '/humanmanage/addHolidayType',
//     query: {
//       id: row.id
//     }
//   })
// }


//删除假期
const onDelHolidayType = async(row)=>{
    //删除用户
    ElMessageBox.confirm('是否确定删除假期:'+row.name, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'warning'
    }).then(async () => {
      const ret  = await delHolidayTypeApi({ "ids": [row.id] })
      if(ret)
      {
        //提示删除成功
        ElMessage.success(t('msg.delOK'))
        getHolidayTypeList()
      }
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: t('msg.delChannel'),
      })
    })






}

onMounted(() => {
  getHolidayTypeList()
})


const showModifyHolidayType = ref(false)
const selItemdef = {
    name: '',
    nick: '',
    unit: '天',
    shortest_time: '1',
    longest_date: '',
    limited_sex: '无限制',
    paid_leave:'不带薪',
}
const selItem = reactive({
    ...selItemdef,
    reset() {
        for (let key in this) {
            if (this.hasOwnProperty(key) &&!(key in selItemdef) && key != 'reset') {
                    delete this[key];
            }
        }
        Object.assign(this, selItemdef)
    }
})

const onModifyHolidayType = async () => {
    showModifyHolidayType.value = false
    const ret = await addHolidayTypeApi(selItem)
    if (ret) {
        console.log(ret)
        ElMessage.success('新增成功')
        getHolidayTypeList()
    }
}

</script>

<template>
  <ContentWrap :title="'假期管理'">
    <div class="mb-2">
      <ElButton type="success" @click="onAddHolidayType">
        <Icon icon="fluent-mdl2:people-add" />
        <div class="pl-2">新增</div>
      </ElButton>
    </div>
    <el-table ref="tableRef" :data="typeData" style="width: 100%;min-height: 60vh;" row-key="id" border stripe
       header-cell-class-name="tableHeader">
      <el-table-column label="编号" prop="id" width="100px" />
      <el-table-column align="center"  prop="nick" label="假期名称" />
      <el-table-column align="center"  prop="unit" label="单位" />
      <el-table-column align="center"  prop="limited_sex" label="假期性别限制" />
      <el-table-column align="center"  prop="paid_leave" label="是否带薪" />
      <el-table-column align="center"  prop="shortest_time" label="最短小时" />
      <el-table-column align="center"  prop="longest_date" label="最长天数" />
      

      <el-table-column align="center" width="100px" prop="name" label="操作" >
        <template #default="scope">
          <el-dropdown trigger="click" placement="bottom">
            <span class="el-dropdown-link">
              <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
            </span>
            <template #dropdown>
              <el-dropdown-menu>     
                <el-dropdown-item @click="onDelHolidayType(scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
        title="请输入新假期"
        v-model="showModifyHolidayType"
        width="500"
        align-center
        destroy-on-close
      >
      <el-form>
        <!-- <el-form-item label="职位编号:">
          {{selItem.name}}
        </el-form-item> -->
        <el-form-item label="假期名称:">
          <el-input  v-model="selItem.nick" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="职位单位:">
          <el-input  v-model="selItem.unit" placeholder="请输入单位"></el-input>
        </el-form-item>
        <el-form-item label="最短小时:">
          <el-input type="number" v-model="selItem.shortest_time" placeholder="请最短时间"></el-input>
        </el-form-item>
        <el-form-item label="最长天数:">
          <el-input type="number" v-model="selItem.longest_date" placeholder="请最长时间"></el-input>
        </el-form-item>
        <el-form-item label="性别限制:">
            <el-radio-group  v-model="selItem.limited_sex">
                <el-radio-button  label="男"/>
                <el-radio-button  label="女"/>
                <el-radio-button  label="无限制"/>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="是否带薪:">
            <el-radio-group  v-model="selItem.paid_leave">
                <el-radio-button  label="带薪"/>
                <el-radio-button  label="不带薪"/>
            </el-radio-group>
        </el-form-item>
      </el-form> 
      <template #footer>
          <div class="dialog-footer">
            <el-button @click="showModifyHolidayType = false">取消</el-button>
            <el-button type="danger" @click="onModifyHolidayType">新增</el-button> 
            <!-- <el-popconfirm  title="确定是否修改职位?" @confirm="onModifyHolidayType">
                <template #reference>
                  
                </template>
            </el-popconfirm>   -->
          </div>
        </template>

      </el-dialog>
  </ContentWrap>
</template>

<style lang="less" scoped>

</style>

<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { ElDialog,ElDropdown,ElDropdownMenu,ElDropdownItem,ElForm,ElFormItem ,ElButton, ElInput,ElPopconfirm,ElTable, ElTableColumn,ElTag, ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getJobListApi,delJobApi, updateUserApi, addJobApi } from '@/api/usermanage'
import { DialogUser } from '@/components/DialogUser'

const { push,currentRoute } = useRouter()
const { t } = useI18n()
//权限数据源
const jobData = reactive([])


//新增职位
const onAddJob = () => {
    console.log(currentRoute.value.name);
    showModifyJob.value = true;
    selItem.name = '';
}

//获取职务数据
const getJobList = async () => {
  const res = await getJobListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    jobData.splice(0,jobData.length,...res.data)
  }

}

//编辑职务
const onEditJob = (row) => {
  //进入编辑页面
  push({
    path: '/humanmanage/addjob',
    query: {
      id: row.id
    }
  })
}


//删除职务
const onDelJob = async(row)=>{
    //删除用户
    ElMessageBox.confirm('是否确定删除职务:'+row.name, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'warning'
    }).then(async () => {
      const ret  = await delJobApi({ "ids": [row.id] })
      if(ret)
      {
        //提示删除成功
        ElMessage.success(t('msg.delOK'))
        getJobList()
      }
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: t('msg.delChannel'),
      })
    })






}

onMounted(() => {
  getJobList()
})


// const lastJob = ref({})
// //显示隐藏选择员窗口变量
// const showAddUser = ref(false)
// //显示选择销售员弹窗
// const onAddUser = (row)=>{
//   showAddUser.value = true
//   lastJob.value = row
// }
// //选择销售员回调
// const onSelSaleCallback = async(id,name,data,user)=>{
//     console.log(id,name,user,lastJob.value)
//     if(user.jobs.includes(lastJob.value.id))
//     {
//       ElMessage.error('该用户已在该职务中')
//       return
//     }

//   //设置用户
//   const ret = await updateUserApi({
//     ids:[id],
//     jobs:[...user.jobs,lastJob.value.id]
//   })
//   if (ret) {
//       console.log(ret)
//       getJobList()
//   }
// }
const showModifyJob = ref(false)
const selItem = reactive({
    name: '',
})
const onModifyJob = async () => {
    showModifyJob.value = false
    const ret = await addJobApi(selItem)
    if (ret) {
        console.log(ret)
        ElMessage.success('新增成功')
        getJobList()
    }
}
</script>

<template>
  <ContentWrap :title="'职务管理'">
    <div class="mb-2">
      <ElButton type="success" @click="onAddJob">
        <Icon icon="fluent-mdl2:people-add" />
        <div class="pl-2">新增</div>
      </ElButton>
      <ElButton color="#409EFF" plain>
        <Icon icon="clarity:import-line" />
        <div class="pl-2">导入</div>
      </ElButton>
      <ElButton color="#409EFF" plain>
        <Icon icon="carbon:export" />
        <div class="pl-2">导出</div>
      </ElButton>
    </div>
    <el-table ref="tableRef" :data="jobData" style="width: 100%;min-height: 60vh;" row-key="id" border stripe
       header-cell-class-name="tableHeader">
      <el-table-column label="编号" prop="id" width="100px" />
      <el-table-column align="center" width="150px" prop="name" label="职务名称" />
      <el-table-column align="center" min-width="10%" prop="users_name" label="职务对应员工" >
        <template #default="scope">
          <el-tag class="mr-1 mb-2" effect="dark" color="#409EFF" v-for="item in scope.row.users_resident_name" :key="item">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" width="100px" prop="name" label="操作" >
        <template #default="scope">
          <el-dropdown trigger="click" placement="bottom">
            <span class="el-dropdown-link">
              <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
            </span>
            <template #dropdown>
              <el-dropdown-menu>     
                <el-dropdown-item @click="onDelJob(scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
        title="请输入新职务"
        v-model="showModifyJob"
        width="500"
        align-center
        destroy-on-close
      >
      <el-form>
        <!-- <el-form-item label="职位编号:">
          {{selItem.name}}
        </el-form-item> -->
        <el-form-item label="职位名称:">
          <el-input  v-model="selItem.name" placeholder="请输入名称"></el-input>
        </el-form-item>
      </el-form> 
      <template #footer>
          <div class="dialog-footer">
            <el-button @click="showModifyJob = false">取消</el-button>
            <el-button type="danger" @click="onModifyJob">新增</el-button> 
            <!-- <el-popconfirm  title="确定是否修改职位?" @confirm="onModifyJob">
                <template #reference>
                  
                </template>
            </el-popconfirm>   -->
          </div>
        </template>

      </el-dialog>
  </ContentWrap>
</template>

<style lang="less" scoped>

</style>

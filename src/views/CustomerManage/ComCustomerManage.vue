<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessage,ElMessageBox,ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption,ElDatePicker,ElCheckbox,ElDropdown,ElDropdownItem,ElDropdownMenu,ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { geBuyerListApi,delBuyerApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { computed } from 'vue';
import { checkPermissionApi } from '@/api/tool';

const { push } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(300)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  type:'公司',
  buyer_nick: '',  //客户名称
  buyer_name: '',  //客户编号
  corp_name: '',   //公司名称
  phone: '',      //手机1
  telephone: '',    //电话
  mainer_name: '',      //负责人
  followe_name: '',    //跟单员
  address: '',     //地址
  remark: '',      //备注
  create_date: '',  //创建时间范围
  modify_date: '',  //修改时间范围
  status:'',      //状态
  level:'',     //重要程度
  linkman:'',    //联系人
  page: 1,
  count:20
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)
//客户状态数据源
const cusStatus = reactive([
  t('customer.wait_check_customer'),
  t('customer.potential_customer'),
  t('customer.saleing_customer'),
  t('customer.signed_customer'),
  t('customer.underMain_customer')
])
//客户重要等级
const cusLevel =reactive([
  t('customer.normal_customer'),
  t('customer.vip_customer'),
  t('customer.big_customer'),
  t('customer.small_customer')
])

//客户数据源
const customerData = reactive([])

//查询客户数据
const getCustomerData = async()=>{
  searchCondition.is_hot_sort = 0
  const ret = await geBuyerListApi(searchCondition)
  if(ret)
  {
    console.log(ret)
    customerData.splice(0,customerData.length,...ret.data)
    totleCount.value =  parseInt(ret.count)
  }
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getCustomerData()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 400
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getCustomerData()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getCustomerData()
}
//创建新客户
const onAddCustmer = ()=>{
  push({
    path: '/customermanage/addcorpcustomer',
    query:{
      type:'公司'
    }
  })
}

//处理表格对象操作
const handleOper = (type,row) => {
  console.log(row)
  if(type==='edit' || type == 'info' ){
    push({
      path: '/customermanage/addcorpcustomer',
      query:{
        id:row.id,
        type:'公司',
        mode:type
      }
    })
  }
  else if(type==='del'){
    ElMessageBox.confirm(
      '确定是否删除该客户？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        const ret = await delBuyerApi({ "ids": [row.id] })
        if (ret) {
          getCustomerData()

          ElMessage({
            type: 'success',
            message: t('msg.delOK'),
          })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })
  }
}

//计算获取主要联系人
const mainConnectorInfo = (row)=>{
  if(row.corp_linkman.length>0)
    {
    //找到主要联系人
    for(let item of row.corp_linkman)
      {
      if(item.first)
        {
        return item
      }
    }
    //没找到，默认返回第一条
    return row.corp_linkman[0]
  }
  return {
    name:'',
    phone:''
  }
}


onMounted(()=>{
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  //刷新表格
  getCustomerData()
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});
</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
    <div ref="rootRef" class="flex relative">
      <div class="absolute top-8 left-8">
        <ElButton type="success" @click="onAddCustmer">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
        <!-- <ElButton color="#409EFF" type="primary" plain>
          <Icon icon="clarity:import-line" />
          <div class="pl-2">{{ t('button.import') }}</div>
        </ElButton>
        <ElButton color="#409EFF" type="primary" plain>
          <Icon icon="carbon:export" />
          <div class="pl-2">{{ t('button.export') }}</div>
        </ElButton> -->
      </div>
      <div  class="h-[100%] bg-white p-7 w-[100%]"  style="color:#666666">
        <div class="text-center mb-5 font-bold" style="color:#333">{{ t('customer.comCustomerMgr') }}</div>
        <div style1="border: 1px solid rgb(143, 143, 143);color:#666;" class="pt-5 pb-5 mb-7 pl-12 bg-light-200">
          <div class="inline-flex items-center mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.comname') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.corp_name" placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.id') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.buyer_name" placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm">{{ t('customer.connector_phone') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.phone" placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5 mb-2">
            <div class="w-20 flex-none text-sm flex justify-end">{{ t('customer.mainer') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.mainer_name" placeholder="" />
          </div>
          <div class="inline-flex items-center mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end  text-sm" >{{ t('customer.follower') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.followe_name" placeholder="" />
          </div>
          <div v-if="senior" class="inline-flex items-center  mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.status') }}:</div>
            <el-select size="small"  class="ml-4 w-53" v-model="searchCondition.status" placeholder="" >
              <el-option v-for="item in cusStatus" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.address') }}:</div>
            <el-input size="small"  class="ml-4" v-model="searchCondition.address" placeholder="" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.remark') }}:</div>
            <el-input size="small"  class="ml-4"  v-model="searchCondition.remark" placeholder="" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.createdate') }}:</div>
            <el-date-picker size="small"  class="ml-4" v-model="searchCondition.create_date" type="daterange" range-separator="To"
              start-placeholder="Start date" end-placeholder="End date" value-format="YYYY-MM-DD" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.modifydate') }}:</div>
            <el-date-picker size="small"  class="ml-4"  v-model="searchCondition.modify_date" type="daterange" range-separator="To"
              start-placeholder="Start date" end-placeholder="End date" value-format="YYYY-MM-DD" />
          </div>
          <div v-if="senior" class="inline-flex items-center  mr-5 mb-2">
            <div class="w-20 flex-none flex justify-end text-sm">{{ t('customer.level') }}:</div>
            <el-select size="small"  class="ml-4" v-model="searchCondition.level" placeholder="">
              <el-option v-for="item in cusLevel" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div  class="text-center mt-5 mb-2 flex justify-end items-center">
            <el-checkbox :label="t('customer.senior')" size="small" v-model="senior"/>
            <ElButton class="ml-4" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton class="mr-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- <div class="mb-2 flex">
          <ElButton color="#409EFF" type="primary" plain size="small">{{ t('button.new_order') }}</ElButton>
          <ElButton color="#409EFF" type="primary" plain size="small">{{ t('button.change_to_per_cus') }}</ElButton>
          <ElButton color="#409EFF" type="primary" plain size="small">{{ t('button.changge_to_traded') }}</ElButton>
        </div> -->
        <el-table ref="userTableRef1" header-cell-class-name="tableHeader" :data="customerData" style="width: 100%;color: #666666;"
          :height1="tableHeight" border stripe>
          <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" />
          <el-table-column show-overflow-tooltip  prop="buyer_name" :label="t('customer.id')" width="100" >
            <template #default="scope">
              {{ checkPermissionApi('客户编码显示')?scope.row.buyer_name:'***' }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip  prop="corp_name" :label="t('customer.comname')" width="200">
            <template #default="scope">
              <div class="nameStyle" @click="handleOper('info', scope.row)">{{ checkPermissionApi('客户名称显示')?scope.row.corp_name:'***' }}</div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip  prop="linkman" :label="t('customer.connector')" width="120" >
            <template #default="scope">
              <div>{{ mainConnectorInfo(scope.row).name }}</div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip  prop="linkman" :label="t('customer.connector_phone')" width="200" >
            <template #default="scope">
              <div>{{ mainConnectorInfo(scope.row).phone }}</div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="mainer_name" :label="t('customer.mainer')" width="90" />
          <el-table-column show-overflow-tooltip prop="follower_name" :label="t('customer.follower')" width="80" />
          <el-table-column show-overflow-tooltip prop="status" :label="t('customer.status')" width="100" />
          <el-table-column show-overflow-tooltip prop="address" :label="t('customer.address')"  min-width="200"/>
          <el-table-column show-overflow-tooltip prop="remark" :label="t('customer.remark')"  min-width="200"/>
          <el-table-column show-overflow-tooltip prop="last_time" :label="t('customer.last_time')" width="130" />
          <el-table-column fixed="right" :label="t('userTable.operate')" width="90">
            <template #default="scope">
              <el-dropdown trigger="click" placement="left">
                <span class="el-dropdown-link">
                  <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleOper('info', scope.row)">{{ t('userOpt.detail') }}</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('edit', scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="mt-8 flex justify-end"
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          :page-sizes="[20, 50, 100, 300]"
          :background="true"
          layout="sizes, prev, pager, next"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />

      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
  color: #00BA80;
}
</style>

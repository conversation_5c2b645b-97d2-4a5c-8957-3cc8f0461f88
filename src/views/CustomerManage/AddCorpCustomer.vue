<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElButton, ElCollapse, ElCollapseItem,ElDescriptions,ElDescriptionsItem,ElInput,ElSelect,ElOption,ElTag,ElMessage,ElForm,ElFormItem, FormRules, FormInstance,ElTable,ElTableColumn,ElCheckbox,ElDatePicker, ElTabs,ElTabPane } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { addBuyerApi } from '@/api/customer'
import { onMounted } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import {updateBuyerApi,geBuyerListApi,getNewBuyerIDApi} from '@/api/customer'
import {checkFormRule, checkPermissionApi, closeOneTagByName} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { TabFollowInfo } from '@/components/TabFollowInfo'

const { currentRoute,back } = useRouter()
const appStore = useAppStore()
const { t } = useI18n()
const { wsCache } = useCache()
//当前选中Tab
const activeTab = ref('0')
//是否是新增模式
const addMode = ref(true)
//页面标题
const title = ref(t('customer.addPerCustomer'))
//展开内容缓存
const activeItems = ref(['1', '2','3','4','5','6'])
//客户数据缓存
const customerData = reactive(
{
    id:'',
    ids:[],
    type: "", //客户类型：个人，公司
    buyer_name: "", //客户编号
    buyer_nick: "", //客户名称
    buyer_note:'',  //客户称呼
    nation: "",     //个人：民族或者公司：国家
    corp_name: "",  //公司全称
    corp_nick: "",  //公司简称
    corp_phone: "", //公司电话
    corp_fax: "",   //公司传真
    corp_addr: "",  //公司地址
    corp_time: "",  //注册时间
    corp_regcap: "",//注册资本
    corp_taxnum: "",//公司税号
    corp_person: "",//公司法人
    corp_url: "",   //公司网址
    corp_bank: "",  //开户银行
    corp_account: "", //开户账号
    corp_type: "",    //所属行业
    corp_linkman: [] as any[], //公司联系人列表
    linkman: "",     //联系人
    phone1: "",      //手机1
    phone2: "",      //手机2
    telephone: "",   //联系电话
    phone_status: "",//电话状态
    email: "",       //邮箱
    address: "",     //通信地址
    mainer: "",      //负责人
    mainer_name:'',  //负责人姓名
    follower: "",    //跟单员
    follower_name:"", //跟单员姓名
    last_time: "",   //最后联系时间
    tax_type: "专票",    //发票类型
    tax_rate: "不含税",    //开票税率
    pay_type: "",     //支付方式
    money_type: "人民币",   //默认币种
    express_type: "", //常用快递公司
    checking_date: "",//对账日期
    status:'无',          //状态
    relation: "无",     //关系：客户，代理商，公共关系，合作伙伴
    level: "无",        //重要程度：微价值客户，普通客户，VIP客户，大客户
    business: "无",     //分类：渠道客户，终端客户
    creater_id: "",   //系统创建
    create_date: "",  //系统创建
    remark: ""        //备注
}
)
//发票类型
const taxTypeData = reactive([
    '普票',
    '专票',
    '收据',
    '不开票',
    '电子票'
])
//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])
//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])
//常用快递公司
const expressCompanyData = reactive([
    '顺丰快递',
    '中通快递',
    '韵达快递',
    '申通快递',
    '圆通快递',
    'EMS快递',
])
//客户状态数据源
const cusStatus = reactive([
    t('customer.null'),
  t('customer.wait_check_customer'),
  t('customer.potential_customer'),
  t('customer.saleing_customer'),
  t('customer.signed_customer'),
  t('customer.underMain_customer')
])
//客户重要等级
const cusLevel =reactive([
t('customer.null'),
  t('customer.normal_customer'),
  t('customer.vip_customer'),
  t('customer.big_customer'),
  t('customer.small_customer')
])
//客户属性
const cusProp = reactive([
    t('customer.null'),
    t('customer.prop_cus'),
    t('customer.prop_agent'),
    t('customer.prop_pub'),
    t('customer.prop_partner')
])
//客户分类
const cusClass = reactive([
t('customer.null'),
    t('customer.class_channel'),
    t('customer.class_terminal')
])
//开票税率tax_rate
const taxRate = reactive([
    t('customer.noTaxRat'),
    '1%',
    '2%',
    '3%',
    '4%',
    '5%',
    '6%',
    '7%',
    '8%',
    '9%',
    '10%',
    '11%',
    '12%',
    '13%',
    '14%',
    '15%',
    '16%',
    '17%',
    '18%',
    '19%',
    '20%'
])


//个人信息
const selfInfo = reactive({
    id:''
})
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    buyer_name: [{ required: true, message: t('msg.noCusID'), trigger: 'blur' }],
    corp_name: [{ required: true, message: t('msg.noCorpName'),trigger:'blur' }],
})

//显示跟单员选择用户窗口
const showSelFollower = ref(false)
//回调设置跟单员
const onSetFollower = (id,name)=>{
    customerData.follower = id
    customerData.follower_name = name
}
//显示责任人选择用户窗口
const showSelMainer = ref(false)
//回调设置责任人
const onSetMainer = (id,name)=>{
    customerData.mainer = id
    customerData.mainer_name = name
}

//添加联系人
const onAddConnecter = ()=>{
    const item = {
        name:'',
        phone:'',
        telephone:'',
        job:'',
        dept:'',
        first:false
    }
    customerData.corp_linkman.push(item)
}
//删除联系人
const onDelConnect = (row)=>{
    for(let i=0 ; i<customerData.corp_linkman.length;i++)
    {
        const item = customerData.corp_linkman[i]
        if(item.phone == row.phone && item.name == row.name)
        {
            customerData.corp_linkman.splice(i,1)
            break
        }
    }
}
//获取新ID
const changeID = async()=>{
    //获取新ID
    const ret = await getNewBuyerIDApi()
    if(ret)
    {
        customerData.buyer_name = ret.data.new_id
        ElMessage.success(t('msg.changeIDSuccess'))

    }
}

//保存数据
const onSave = async() => {
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    
    customerData.buyer_nick = customerData.corp_name
    console.log('111',customerData)
    //新增个人客户
    if(currentRoute.value.query.id == undefined)
    {
        const ret = await addBuyerApi(customerData)
        if(ret)
        {
            //提示添加成功             
            ElMessage.success('添加成功')
            //返回上一页
            baskFront()
        }
    }
    else    //修改用户
    {
        customerData.ids = [currentRoute.value.query.id]
        const ret = await updateBuyerApi(customerData)
        if(ret)
        {
            //提示添加成功             
            ElMessage.success('修改成功')
            //返回上一页
            baskFront()
        }
    }
}

onMounted(async () => {
    //更新个人信息
    const info = wsCache.get(appStore.getUserInfo)
    Object.assign(selfInfo, info)
    customerData.mainer = info.id
    console.log(currentRoute.value)
    //设置类型，是个人还是公司
    customerData.type = currentRoute.value.query.type as string
    if(currentRoute.value.query.id == undefined)
    {
        title.value = customerData.type=='个人'?t('customer.addPerCustomer'):t('customer.addComCustomer')
        customerData.mainer_name = info.resident_name

        changeID()
        addMode.value = true
    }
    else
    {
        addMode.value = false
        title.value = customerData.type=='个人'? t('customer.editPerCustomer'):t('customer.editComCustomer')
        const ret = await geBuyerListApi({
            ids:[currentRoute.value.query.id],
            page:1,
            count:100,
            realrole:'customer.comCustomerMgr'  //需要修复权限
        })
        if(ret)
        {
            if(ret.data.length<=0)
            {
                ElMessage({
                    type: 'error',
                    message: t('msg.searchFalse'),
                })
                return
            }
            Object.assign(customerData, ret.data[0])
           // console.log(ret)
        }
        if(currentRoute.value.query.mode === 'info') //查看模式
        {
            console.log('!!!!!!!!!!!')
            let components = document.querySelectorAll('.el-input__inner');
            components.forEach((component) => {
                component.setAttribute('disabled', true);
            });
            components = document.querySelectorAll('.el-input__wrapper');
            components.forEach((component) => {
                component.classList.add('infomode')
            });
            const suffixElements = document.querySelectorAll('.el-input__suffix');
            suffixElements.forEach(suffixElement => {
                suffixElement.style.display = 'none';
            });
        }
    }
})


//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}

</script>

<template>
    <ContentDetailWrap :title="title" @back="baskFront">
        <template #right>
            <ElButton v-show="currentRoute.query.mode != 'info'" type="primary" @click="onSave">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <div class="flex justify-center">
            <el-form class="max-w-[1000px]" :rules="rules" :model="customerData" ref="ruleFormRef">
                <el-collapse v-model="activeItems">
                    <el-collapse-item name="1">
                        <template #title>
                            <div class="title">{{ t('customer.corpinfo') }}</div>
                        </template>
                        <el-descriptions class="flex-1 mt-2" :column="3" border>
                            <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('customer.id')"
                                class="flex">
                                <el-form-item prop="buyer_name" >
                                    <div class="w-[100%] relative flex items-center" v-show="checkPermissionApi('客户编码显示')">
                                        <el-input style="width: 64%;margin-right: 10px;"  v-model="customerData.buyer_name"  :disabled="!addMode"/>
                                        <ElButton v-if="addMode" type="success" class=" absolute top-0 right-1" @click="changeID">
                                            <Icon class="mr-0.5" icon="radix-icons:update" />
                                            {{ t('button.update') }}
                                        </ElButton>
                                    </div>
                                    <div v-if="checkPermissionApi('客户编码显示') == false">***</div>
                                </el-form-item>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('customer.comname')">
                                <el-form-item prop="corp_name" >
                                    <el-input v-model="customerData.corp_name"  v-show="checkPermissionApi('客户名称显示')"/>
                                    <div v-if="checkPermissionApi('客户名称显示') == false">***</div>
                                </el-form-item>
                            </el-descriptions-item>
                            <!-- <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_taxnum')">
                                <el-form-item>
                                    <el-input v-model="customerData.corp_taxnum"  />
                                </el-form-item>
                            </el-descriptions-item> -->
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_person')">
                                <el-input v-model="customerData.corp_person"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_regcap')">
                                <el-input v-model="customerData.corp_regcap"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_time')">
                                <el-date-picker v-model="customerData.corp_time" type="date" placeholder="" format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD" />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_nick')">
                                <el-input v-model="customerData.corp_nick"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_phone')">
                                <el-input v-model="customerData.corp_phone"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_fax')">
                                <el-input v-model="customerData.corp_fax"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.email')">
                                <el-input v-model="customerData.email"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_bank')">
                                <el-input v-model="customerData.corp_bank"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_account')">
                                <el-input v-model="customerData.corp_account"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_type')">
                                <el-input v-model="customerData.corp_type"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_url')" :span="3">
                                <el-input v-model="customerData.corp_url"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_addr')" :span="3">
                                <el-input v-model="customerData.corp_addr"   />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.address')" :span="3">
                                <el-input v-model="customerData.address"   />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.remark')" :span="1">
                                <el-input v-model="customerData.remark"  :autosize="{ minRows: 3, maxRows: 3 }" type="textarea"/>
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-collapse-item>
                    <el-collapse-item name="4">
                        <template #title>
                            <div class="title">{{ t('customer.connecterMgr') }}</div>
                            <ElButton v-show="currentRoute.query.mode != 'info'" class="ml-4" type="success" size="small" @click.stop="onAddConnecter">
                                <Icon icon="fluent-mdl2:people-add" />
                                <div class="pl-2">{{ t('button.add') }}</div>
                            </ElButton>
                        </template>
                        <el-table ref="userTableRef" header-cell-class-name1="tableHeader" :data="customerData.corp_linkman" style="width: 100%"  border stripe>
                            <el-table-column show-overflow-tooltip fixed prop="name" :label="t('customer.connector')" >
                                <template #default="scope">
                                    <el-input class="!w-[100%]" v-model="scope.row.name" />
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip fixed prop="phone" :label="t('customer.connector_phone')" >
                                <template #default="scope">
                                    <el-input class="!w-[100%]" v-model="scope.row.phone" />
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip fixed prop="telephone" :label="t('customer.telephone')" >
                                <template #default="scope">
                                    <el-input class="!w-[100%]" v-model="scope.row.telephone" />
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip fixed prop="job" :label="t('customer.job')" >
                                <template #default="scope">
                                    <el-input class="!w-[100%]" v-model="scope.row.job" />
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip fixed prop="dept" :label="t('userTable.dept')" >
                                <template #default="scope">
                                    <el-input class="!w-[100%]" v-model="scope.row.dept" />
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip fixed :label="t('customer.first')" width="100">
                                <template #default="scope">
                                    <el-checkbox v-model="scope.row.first" />
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip fixed :label="t('userTable.operate')" width="80">
                                <template #default="scope">
                                    <ElButton v-show="currentRoute.query.mode != 'info'" type="danger" size="small" @click="onDelConnect(scope.row)">
                                        <Icon icon="material-symbols:delete-outline"/>
                                    </ElButton>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-collapse-item>

                    <el-collapse-item name="2">
                        <template #title>
                            <div class="title">{{ t('customer.orderinfo') }}</div>
                        </template>
                        <el-descriptions class="flex-1 mt-2" :column="3" border>    
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.tax_rate')">
                                <el-select class="ml-3" v-model="customerData.tax_rate" placeholder="Select" >
                                    <el-option v-for="item in taxRate" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.tax_type')">
                                <el-select class="ml-3" v-model="customerData.tax_type" placeholder="Select" >
                                    <el-option v-for="item in taxTypeData" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.pay_type')">
                                <el-select class="ml-3" v-model="customerData.pay_type" placeholder="Select" >
                                    <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.money_type')">
                                <el-select class="ml-3" v-model="customerData.money_type" placeholder="Select" >
                                    <el-option v-for="item in moneyTypeData" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.checking_date')">
                                <el-select class="ml-3" v-model="customerData.checking_date" placeholder="Select" >
                                    <el-option v-for="item in 31" :key="item" :label="item+'号'" :value="item+'号'" />
                                </el-select>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.express_type')">
                                <el-select class="ml-3" v-model="customerData.express_type" placeholder="Select" >
                                    <el-option v-for="item in expressCompanyData" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-collapse-item>
                    <el-collapse-item name="6">
                        <template #title>
                            <div class="title">{{ '开票信息' }}</div>
                        </template>
                        <el-descriptions class="flex-1 mt-2" :column="3" border>    
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'税号'">
                                <el-input v-model="customerData.corp_taxnum"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'开户银行'">
                                <el-input v-model="customerData.corp_bank"  />
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'银行账号'">
                                <el-input v-model="customerData.corp_account"  />
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-collapse-item>


                    <el-collapse-item name="5">
                        <template #title>
                            <div class="title">{{ t('customer.userProp') }}</div>
                        </template>
                        <el-descriptions class="flex-1 mt-2" :column="3" border>    
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.status')">
                                <el-select class="ml-3" v-model="customerData.status" placeholder="Select" >
                                    <el-option v-for="item in cusStatus" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.prop')">
                                <el-select class="ml-3" v-model="customerData.relation" placeholder="Select" >
                                    <el-option v-for="item in cusProp" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.type')">
                                <el-select class="ml-3" v-model="customerData.business" placeholder="Select" >
                                    <el-option v-for="item in cusClass" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.level')">
                                <el-select class="ml-3" v-model="customerData.level" placeholder="Select" >
                                    <el-option v-for="item in cusLevel" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-collapse-item>




                    <el-collapse-item name="3">
                        <template #title>
                            <div class="title">{{ t('customer.systeminfo') }}</div>
                        </template>
                        <el-descriptions class="flex-1 mt-2" :column="3" border>    
                            <el-descriptions-item  label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.mainerdept')" :span="3">
                                <el-tag effect="dark" color="#409EFF" class="mr-1 mb-2" v-for="item in selfInfo.depts_name" :key="item">{{ item }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.mainer')">
                                <ElButton v-show="currentRoute.query.mode != 'info'" type="success" size="small" @click="showSelMainer = true">{{ customerData.mainer == ''?'设置':customerData.mainer_name }}</ElButton>
                            </el-descriptions-item>

                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.follower')" :span="2">
                                <ElButton v-show="currentRoute.query.mode != 'info'" type="success" size="small" @click="showSelFollower = true">{{ customerData.follower == ''?'设置':customerData.follower_name }}</ElButton>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.creater')">
                                <div style="min-width: 100px;">{{ customerData.mainer_name }}</div>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.create_time')">
                                <div style="min-width: 100px;">{{ new Date().toISOString().slice(0, 10) + ' ' + new Date().toTimeString().slice(0, 8)  }}</div>
                            </el-descriptions-item>
                            <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.last_time')">
                                <div style="min-width: 100px;">-</div>
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-collapse-item>
                </el-collapse>
                <!-- <div style="height: 200px;"></div> -->
            </el-form>
        </div>
        
        <!-- 客户其他信息展示 -->
        <el-tabs v-show="false" type="border-card" v-model="activeTab" class="mb-60" >
            <el-tab-pane :label="t('customer.record')" name="0">               
                <TabFollowInfo log_type="" :cus_id="customerData.id" :self_id="selfInfo.id" />
            </el-tab-pane>
            <el-tab-pane :label="t('customer.product')" name="1">无</el-tab-pane>
        </el-tabs>
        <div class="mb-[20px]"></div>
        <DialogUser :param="{}" :title="t('title.seluser')" v-model:show="showSelMainer" @on-submit="onSetMainer"/>
        <DialogUser :param="{}" :title="t('title.seluser')" v-model:show="showSelFollower" @on-submit="onSetFollower"/>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle1) {
    width: 15% !important;
}

:deep(.conentStyle1) {
    width: 30%;
}

.el-input{
    width: 80%;
}
.el-textarea{
    width: 50%;
}
//标题设置
.title{
    font-weight:900;
    font-size: large;
    margin-left: 20px;
}
//标题前提示
.title::before{
    content: 'I';
    color: red;
    margin-right: 10px;
}

/* 只针对 .require 元素添加伪元素 */
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

//展开控件标题颜色
:deep(.el-collapse) {
    //--el-collapse-header-text-color: #c05454;
    --el-collapse-header-bg-color: #f3f3f3;
}

.el-form-item--default{
    margin-bottom: unset;
}
.formItem{
    height: 100%; 
    min-width: max-content;
    display: flex;
    align-items: center; 
}

//查看模式
:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}

</style>
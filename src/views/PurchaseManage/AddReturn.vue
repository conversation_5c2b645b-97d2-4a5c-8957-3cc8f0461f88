<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElPopconfirm,ElTable,ElTag,ElDatePicker,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import { getInventoryListApi,getInventoryDetailListApi,getStoreListApi,getReturnNewnumApi,getPurchaseInfoApi,delReturnApi,updateReturnApi,getReturnInfoApi,getReturnListApi,addReturnApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount } from 'vue'
import {checkFormRule, checkPermissionApi, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed,nextTick } from 'vue'
import { cloneDeep } from 'lodash-es'



const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})

//采购单数据
const purchaseData = reactive({})

//出库单数据
const returnData = reactive(
    {
    "supplier_id": "",
    "buy_order_num": "",
    "buy_cancel_num": "",
    "cancel_date": "",
    "cancel_man_id": "", //领取人员
    cancel_man_name:'',
    "store_id": "",
    "pdt_list": [],
    "note": "",
    "express_fee": 0.00,
    "other_fee": 0.00,  
    order_man_name:''  ,
    supplier_nick:'',
    order_date:'',
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:['保存']
    }, //审核决策
    fsm_cur_state:'订单创建',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)

//产品仓库分布数据
const detailData  = reactive([])

//获取最新ID
const onChangeID = async()=>{
    const ret = await getReturnNewnumApi()
    if(ret)
    {
        console.log(ret)
        returnData.buy_cancel_num = ret.data.new_id
    }
}


//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)

    returnData.store_id = storeData[0].id

    updatePdtStock(returnData.store_id)
  }

}

//更新pdt库存
const updatePdtStock = async (store_id:string) => {
    if(returnData.pdt_list.length<=0)
        return
    //let arrPdtIDs = []
    let arrPdtBS = []
    for(let one of returnData.pdt_list)
    {
        //arrPdtIDs.push(one.id)
        arrPdtBS.push(one.标识)
    }

    //查询这个产品在那些仓库有库存
    const ret = await getInventoryListApi({
                store_id:store_id,
                //pdt_ids:[...arrPdtIDs],
                pdt_biaoshi_list:[...arrPdtBS],
                buy_order_num:purchaseData.buy_order_num,
                page: 1,
                count: 100
            })
    if(ret)
    {
        detailData.splice(0,detailData.length, ...ret.data)
        console.log('--------',detailData)
        //更新统计数据到
        for(let item of returnData.pdt_list)
        {
            item.可用数量 = 0
            item.锁定数量  =0
            for(let one of detailData)
            {
                let bFind = false
                if(item.id == one.pdt_id)
                {
                    item.可用数量 = one.良品数量+one.不良品数量-one.锁定数量
                    item.锁定数量 = one.锁定数量

                    //退货数量如果大于可能则需要改下
                    // if(item.退货数量>(one.良品数量+one.不良品数量))
                    // {
                    //     item.退货数量 = item.可用数量
                    // }

                    bFind = true
                }
                if(bFind)
                    break
            }
        }
    }
    console.log(returnData.pdt_list)
}

//计算输入
const recomputeNum = (row)=>{
    if(row.退货数量<0)
        row.退货数量 = 0
    else if(row.退货数量>row.已收货-row.已退货)
    {
        row.退货数量 = row.已收货-row.已退货
        recomputeNum(row)
        //提示退货数量不能超过已收货数量减去已退货数量
        ElMessage.warning(t('msg.returnNumTooBig'))
    }
    else if(row.退货数量>(row.可用数量+row.锁定数量))
    {
        row.退货数量 = row.可用数量+row.锁定数量
        if(row.退货数量>row.已收货-row.已退货)
        {
            row.退货数量 = row.已收货-row.已退货
        }
        //提示退货数量不能超过已入库数量
        ElMessage.warning(t('msg.returnNumTooBig2'))
    }
}


onMounted(async() => {    
    console.log('----------------')
    //查询获取关联采购单信息
    const ret = await getPurchaseInfoApi({
        buy_order_num:currentRoute.value.query.buy_order_num as string
    })
    if(!ret)
    {
        return
    }
    console.log(ret)    
    Object.assign(purchaseData,ret.data)



    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('return.add')
        onChangeID()
        returnData.buy_order_num  = currentRoute.value.query.buy_order_num as string
        returnData.cancel_date = getTodayDate()
        //所有产品到收货列表
        returnData.pdt_list = purchaseData.pdt_list

        //设置出库人员
        const info = wsCache.get(appStore.getUserInfo)
        returnData.cancel_man_id = info.id
        returnData.cancel_man_name = info.resident_name
    }
    else
    {
        title.value = t('return.modify')
        //查询产品信息 
        const ret = await getReturnInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(returnData, ret.data)
            returnData.pdt_list = ret.data.pdt_list;
        }
        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
        
    }
    await getStoreList()
 
})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})


//切换仓库更新库存
const onChangeStore = async (store_id) => {
    updatePdtStock(store_id)
}


//显示隐藏选择出库员窗口变量
const showSelReturnUserDlg = ref(false)
//显示选择出库员弹窗
const onSelReturnUser = ()=>{
    showSelReturnUserDlg.value = true
}
//选择出库员回调
const onSelReturnCallback = (id,name)=>{
    console.log(id,name)
    returnData.cancel_man_id = id
    returnData.cancel_man_name = name
}



//保存
const onSave = async()=>{
    console.log(returnData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }


    //删除returnData.pdt_list中入库数量为0的行
    const tmp = cloneDeep(returnData)
    tmp.pdt_list = tmp.pdt_list.filter(pdt=>(pdt.退货数量!=0&&pdt.退货数量!=undefined))
    
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }

    //增加审核流程参数
    const info = wsCache.get(appStore.getUserInfo)
    tmp.fsm_exe_man_name = info.resident_name
    tmp.fsm_exe_trig = '直接出库'

    if(tmp.id == undefined)
    {
        const ret = await addReturnApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newReturnSuccess'))
            back()
        }
    }
    else //修改
    {
        const ret =await updateReturnApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateReturnSuccess'))
            back()
        }
    }

    closeOneTagByPath('/purchasemanage/purchasemanage')

}


</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>

            <el-popconfirm  title="是否确认发起退货?操作将影响库存！" @confirm="onSave">
                <template #reference>
                    <ElButton v-if="returnData.id==undefined" type="primary">
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        提交
                    </ElButton>
                </template>
            </el-popconfirm>
        </template>
        <el-form :rules="rules" :model="returnData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('return.num')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input class="mr-1" v-model="returnData.buy_cancel_num" :disabled="returnData.id!=undefined" />
                            <ElButton v-if="returnData.id==undefined" type="primary"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('purchase.id')"
                    class="flex">
                    <div>{{ purchaseData.buy_order_num }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('store.store')"
                    class="flex">
                    <el-select v-model="returnData.store_id" placeholder="Select" @change="onChangeStore(returnData.store_id)">
                        <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('supplier.nick')"
                    class="flex">
                    <div>{{ checkPermissionApi('供应商名称显示')? purchaseData.supplier_nick:'***' }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('supplier.person')"
                    class="flex">
                    <div>{{ purchaseData.buy_man_name }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('purchase.date')"
                    class="flex">
                    <div>{{ purchaseData.business_date }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('return.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ returnData.cancel_man_name }}</div> 
                        <ElButton @click="onSelReturnUser">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('return.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="returnData.cancel_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>

            </el-descriptions>
        
        </el-form>

        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="returnData.pdt_list" style="width: 100%" border stripe>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="120" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130" >
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick}}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.id != undefined"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('purchase.count')" >
                <template #default="scope">
                    <div>{{ scope.row.采购数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('receipt.receipt_count')" >
                <template #default="scope">
                    <div>{{ scope.row.已收货 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('return.count')" >
                <template #default="scope">
                    <div>{{ scope.row.已退货 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="t('inventory.avali_count')" >
                <template #default="scope">
                    <div>{{ scope.row.可用数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip  :label="'锁定库存'" >
                <template #default="scope">
                    <div>{{ scope.row.锁定数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('return.count2')" >
                <template #default="scope">
                    <el-input v-model="scope.row.退货数量" @input="recomputeNum(scope.row)" type="number"/>
                </template>
            </el-table-column>            

            <el-table-column show-overflow-tooltip  prop="采购备注" :label="t('purchase.remark')" />
            
            <el-table-column show-overflow-tooltip  :label="t('return.remark')" >
                <template #default="scope">
                    <el-input v-model="scope.row.退货备注" />
                </template>
            </el-table-column>
        </el-table>
    <!-- 显示合计 -->
    <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">采购单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ returnData.order_note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">出库单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="returnData.note" clearable :autosize="{ minRows: 10, maxRows: 4 }"     type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>

    </div>

        
        




        <!-- <div class="mb-60"></div> -->

        <!-- 选择退货员 -->
        <DialogUser :param="''" v-model:show="showSelReturnUserDlg" :title="t('msg.selectUser')" @on-submit="onSelReturnCallback"/>

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}
//扩展文字
.ex_text{
  font-size: 11px;
  color: #646464;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
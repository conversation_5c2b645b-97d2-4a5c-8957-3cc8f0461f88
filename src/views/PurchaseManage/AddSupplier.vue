<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElButton, ElCollapse, ElCollapseItem,ElDescriptions,ElDescriptionsItem,ElInput,ElSelect,ElOption,ElTag,ElMessage,ElForm,ElFormItem, FormRules, FormInstance,ElTable,ElTableColumn,ElCheckbox,ElDatePicker, ElTabs,ElTabPane } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { addBuyerApi } from '@/api/customer'
import { onMounted } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import {addSupplierApi,updateSupplierApi,getSupplierListApi,getNewSupplierIDApi} from '@/api/customer'
import {checkFormRule} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { TabFollowInfo } from '@/components/TabFollowInfo'

const { currentRoute,back } = useRouter()
const appStore = useAppStore()
const { t } = useI18n()
const { wsCache } = useCache()
//当前选中Tab
const activeTab = ref('0')
//是否是新增模式
const addMode = ref(true)
//页面标题
const title = ref('')
//展开内容缓存
const activeItems = ref(['1', '2','3','4','5'])
//客户数据缓存
const supplierData = reactive(
{
    id:'',
    ids:[],
    "supplier_name": "",
    "supplier_nick": "",
    "corp_regcap": "",
    "corp_taxnum": "",
    "corp_person": "",
    "corp_time": "",
    "corp_nick": "",
    "corp_type": "",
    "corp_bank": "",
    "corp_account": "",
    "corp_fax": "",
    "corp_addr": "",
    "corp_email": "",
    "corp_linkman": [],
    "status": "正常",
    "note": "",
    "tax_type": "专票",
    "tax_rate": "不含税",
    "pay_type": "",
    "money_type": "人民币",
    "checking_date": "",
    "mainer": "",
    "follower": "",
    "last_time": "",
    "remark": "",
    follower_name:'',
    mainer_name:'',
}
)
//发票类型
const taxTypeData = reactive([
    '普票',
    '专票',
    '收据',
    '不开票',
    '电子票'
])
//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])
//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])
//常用快递公司
const expressCompanyData = reactive([
    '顺丰快递',
    '中通快递',
    '韵达快递',
    '申通快递',
    '圆通快递',
    'EMS快递',
])

//开票税率tax_rate
const taxRate = reactive([
    t('customer.noTaxRat'),
    '1%',
    '2%',
    '3%',
    '4%',
    '5%',
    '6%',
    '7%',
    '8%',
    '9%',
    '10%',
    '11%',
    '12%',
    '13%',
    '14%',
    '15%',
    '16%',
    '17%',
    '18%',
    '19%',
    '20%'
])


//个人信息
const selfInfo = reactive({
    id:''
})
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_name: [{ required: true, message: t('msg.noSupplierName'), trigger: 'blur' }],
    supplier_nick: [{ required: true, message: t('msg.noSupplierNick'),trigger:'blur' }],
})

//显示跟单员选择用户窗口
const showSelFollower = ref(false)
//回调设置跟单员
const onSetFollower = (id,name)=>{
    supplierData.follower = id
    supplierData.follower_name = name
}
//显示责任人选择用户窗口
const showSelMainer = ref(false)
//回调设置责任人
const onSetMainer = (id,name)=>{
    supplierData.mainer = id
    supplierData.mainer_name = name
}

//添加联系人
const onAddConnecter = ()=>{
    const item = {
        name:'',
        phone:'',
        telephone:'',
        job:'',
        dept:'',
        first:false
    }
    supplierData.corp_linkman.push(item)
}
//删除联系人
const onDelConnect = (row)=>{
    for(let i=0 ; i<supplierData.corp_linkman.length;i++)
    {
        const item = supplierData.corp_linkman[i]
        if(item.phone == row.phone && item.name == row.name)
        {
            supplierData.corp_linkman.splice(i,1)
            break
        }
    }
}
//获取新ID
const changeID = async()=>{
    //获取新ID
    const ret = await getNewSupplierIDApi()
    if(ret)
    {
        supplierData.supplier_name = ret.data.new_id
        ElMessage.success(t('msg.changeIDSuccess'))

    }
}

//保存数据
const onSave = async() => {
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }
    //新增个人客户
    if(currentRoute.value.query.id == undefined)
    {
        const ret = await addSupplierApi(supplierData)
        if(ret)
        {
            //提示添加成功             
            ElMessage.success('添加成功')
            //返回上一页
            back()
        }
    }
    else    //修改用户
    {
        supplierData.ids = [currentRoute.value.query.id]
        const ret = await updateSupplierApi(supplierData)
        if(ret)
        {
            //提示添加成功             
            ElMessage.success('修改成功')
            //返回上一页
            back()
        }
    }
}

onMounted(async () => {
    //更新个人信息
    const info = wsCache.get(appStore.getUserInfo)
    Object.assign(selfInfo, info)
    supplierData.mainer = info.id
    console.log(currentRoute.value)

    if(currentRoute.value.query.id == undefined)
    {
        title.value = t('supplier.add_supplier')
        supplierData.mainer_name = info.resident_name

        changeID()
        addMode.value = true
    }
    else
    {
        addMode.value = false
        title.value = t('supplier.update_supplier')
        const ret = await getSupplierListApi({
            ids:[currentRoute.value.query.id],
            page:1,
            count:100
        })
        if(ret)
        {
            if(ret.data.length<=0)
            {
                ElMessage({
                    type: 'error',
                    message: t('msg.searchFalse'),
                })
                return
            }
            Object.assign(supplierData, ret.data[0])
           // console.log(ret)
        }

    }
})




</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <ElButton color="#409EFF" style="color: #fff;" @click="onSave">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <el-form :rules="rules" :model="supplierData" ref="ruleFormRef">
            <el-collapse v-model="activeItems">
                <el-collapse-item name="1">
                    <template #title>
                        <div class="title">{{ t('supplier.base_info') }}</div>
                    </template>
                    <el-descriptions class="flex-1 mt-2" :column="3" border>
                        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('supplier.name')"
                            class="flex">
                            <el-form-item prop="supplier_name" >
                                <div class="w-[100%] relative flex">
                                    <el-input class="!mr-1" style="width: 70%;margin-right: 10px;"  v-model="supplierData.supplier_name"  />
                                    <ElButton v-if="addMode" type="primary" class=" absolute top-0 right-1" @click="changeID">
                                        <Icon class="mr-0.5" icon="radix-icons:update" />
                                        {{ t('button.update') }}
                                    </ElButton>
                                </div>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('supplier.nick')">
                            <el-form-item prop="supplier_nick" >
                                <el-input v-model="supplierData.supplier_nick"  />
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_taxnum')">
                            <el-form-item>
                                <el-input v-model="supplierData.corp_taxnum"  />
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_person')">
                            <el-input v-model="supplierData.corp_person"  />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_regcap')">
                            <el-input v-model="supplierData.corp_regcap"  />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_time')">
                            <el-date-picker v-model="supplierData.corp_time" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_nick')">
                            <el-input v-model="supplierData.corp_nick"  />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('supplier.type')">
                            <el-input v-model="supplierData.corp_type"  />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.email')">
                            <el-input v-model="supplierData.corp_email"  />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_bank')">
                            <el-input v-model="supplierData.corp_bank"  />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_account')">
                            <el-input v-model="supplierData.corp_account"  />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('supplier.status')">
                            <el-select v-model="supplierData.status" placeholder="请选择" >
                                <el-option v-for="item in ['正常','禁用','待审核']" :key="item" :label="item" :value="item" />
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('supplier.fax')">
                            <el-input v-model="supplierData.corp_fax"  />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.corp_addr')" >
                            <el-input v-model="supplierData.corp_addr"   />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.address')" :span="3">
                            <el-input v-model="supplierData.address"   />
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.remark')" :span="1">
                            <el-input v-model="supplierData.remark"  :autosize="{ minRows: 3, maxRows: 3 }" type="textarea"/>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-collapse-item>
                <el-collapse-item name="4">
                    <template #title>
                        <div class="title">{{ t('customer.connecterMgr') }}</div>
                        <ElButton class="ml-3" type="success" size="small" @click.stop="onAddConnecter">
                            <Icon icon="fluent-mdl2:people-add" />
                            <div class="pl-2">{{ t('button.add') }}</div>
                        </ElButton>
                    </template>
                    <el-table ref="userTableRef" header-cell-class-name1="tableHeader" :data="supplierData.corp_linkman" style="width: 100%"  border stripe>
                        <el-table-column show-overflow-tooltip fixed prop="name" :label="t('customer.connector')" >
                            <template #default="scope">
                                <el-input class="!w-[100%]" v-model="scope.row.name" />
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip fixed prop="phone" :label="t('customer.connector_phone')" >
                            <template #default="scope">
                                <el-input class="!w-[100%]" v-model="scope.row.phone" />
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip fixed prop="telephone" :label="t('customer.telephone')" >
                            <template #default="scope">
                                <el-input class="!w-[100%]" v-model="scope.row.telephone" />
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip fixed prop="job" :label="t('customer.job')" >
                            <template #default="scope">
                                <el-input class="!w-[100%]" v-model="scope.row.job" />
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip fixed prop="dept" :label="t('userTable.dept')" >
                            <template #default="scope">
                                <el-input class="!w-[100%]" v-model="scope.row.dept" />
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip fixed :label="t('customer.first')" width="100">
                            <template #default="scope">
                                <el-checkbox v-model="scope.row.first" />
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip fixed :label="t('userTable.operate')" width="80">
                            <template #default="scope">
                                <ElButton type="danger" size="small" @click="onDelConnect(scope.row)">
                                    <Icon icon="material-symbols:delete-outline"/>
                                </ElButton>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-collapse-item>

                <el-collapse-item name="2">
                    <template #title>
                        <div class="title">{{ t('customer.orderinfo') }}</div>
                    </template>
                    <el-descriptions class="flex-1 mt-2" :column="3" border>    
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.tax_type')">
                            <el-select class="ml-3" v-model="supplierData.tax_type" placeholder="Select" >
                                <el-option v-for="item in taxTypeData" :key="item" :label="item" :value="item" />
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.tax_rate')">
                            <el-select class="ml-3" v-model="supplierData.tax_rate" placeholder="Select" >
                                <el-option v-for="item in taxRate" :key="item" :label="item" :value="item" />
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.pay_type')">
                            <el-select class="ml-3" v-model="supplierData.pay_type" placeholder="请选择" >
                                <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.money_type')">
                            <el-select class="ml-3" v-model="supplierData.money_type" placeholder="请选择" >
                                <el-option v-for="item in moneyTypeData" :key="item" :label="item" :value="item" />
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.checking_date')">
                            <el-select class="ml-3" v-model="supplierData.checking_date" placeholder="请选择" >
                                <el-option v-for="item in 31" :key="item" :label="item+'号'" :value="item+'号'" />
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.express_type')">
                            <el-select class="ml-3" v-model="supplierData.express_type" placeholder="请选择" >
                                <el-option v-for="item in expressCompanyData" :key="item" :label="item" :value="item" />
                            </el-select>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-collapse-item>

                <el-collapse-item name="3">
                    <template #title>
                        <div class="title">{{ t('customer.systeminfo') }}</div>
                    </template>
                    <el-descriptions class="flex-1 mt-2" :column="3" border>    
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.mainerdept')" :span="3">
                            <el-tag effect="dark" color="#409EFF" style="color: #fff;" class="mr-1 mb-2" v-for="item in selfInfo.depts_name" :key="item">{{ item }}</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.mainer')">
                            <ElButton type="success" size="small" @click="showSelMainer = true">{{ supplierData.mainer == ''?'设置':supplierData.mainer_name }}</ElButton>
                        </el-descriptions-item>

                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.follower')" :span="2">
                            <ElButton type="success" size="small" @click="showSelFollower = true">{{ supplierData.follower == ''?'设置':supplierData.follower_name }}</ElButton>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.creater')">
                            <div style="min-width: 100px;">{{ supplierData.mainer_name }}</div>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.create_time')">
                            <div style="min-width: 100px;">{{ new Date().toISOString().slice(0, 10) + ' ' + new Date().toTimeString().slice(0, 8)  }}</div>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('customer.last_time')">
                            <div style="min-width: 100px;">-</div>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-collapse-item>
            </el-collapse>
            <!-- <div style="height: 200px;"></div> -->
        </el-form>
        <!-- 客户其他信息展示 -->
        <el-tabs v-if="!addMode" type="border-card" v-model="activeTab" >
            <el-tab-pane :label="t('customer.record')" name="0">               
                <TabFollowInfo log_type="supplier" :cus_id="supplierData.id" :self_id="selfInfo.id" />
            </el-tab-pane>
            <el-tab-pane :label="t('customer.product')" name="1">无</el-tab-pane>
        </el-tabs>

        <DialogUser :param="{}" :title="t('title.seluser')" v-model:show="showSelMainer" @on-submit="onSetMainer"/>
        <DialogUser :param="{}" :title="t('title.seluser')" v-model:show="showSelFollower" @on-submit="onSetFollower"/>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle1) {
    width: 15% !important;
}

:deep(.conentStyle1) {
    width: 30%;
}

.el-input{
    width: 80%;
}
.el-textarea{
    width: 50%;
}
//标题设置
.title{
    font-weight:900;
    font-size: large;
    margin-left: 20px;
}
//标题前提示
.title::before{
    content: 'I';
    color: red;
    margin-right: 10px;
}

/* 只针对 .require 元素添加伪元素 */
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

//展开控件标题颜色
:deep(.el-collapse) {
    //--el-collapse-header-text-color: #c05454;
    --el-collapse-header-bg-color: #f3f3f3;
}

.el-form-item--default{
    margin-bottom: unset;
}
.formItem{
    height: 100%; 
    min-width: max-content;
    display: flex;
    align-items: center; 
}
</style>
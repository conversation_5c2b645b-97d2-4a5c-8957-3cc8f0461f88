<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton, ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio, ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted,onBeforeUnmount,watch } from 'vue'
import { useRouter } from 'vue-router'
import { getPutinListApi, delPutinApi, updatePutinApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { DialogPurchaseSel } from '@/components/DialogPurchaseSel'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { ceilToFixed, checkPermissionApi, downloadFile, getDateArea, getMoneyFlag } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { exportBuyPutinListApi } from '@/api/extra';
import PrintModal from '@/views/PrintManage/PrintModal.vue'

import { DynamicScroller,DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const { currentRoute, push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()

//入库单数据源
const putinData = reactive([])
const rootData = {
  added: {
    
  }
}

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state: '',
  入库单号: '',
  产品名称: '',
  产品编码: '',
  收货单号: '',
  入库时间: ['', ''],
  store_type: '',
  入库标识: '',
  产品规格: '',
  供应商: '',
  采购单号: '',
  采购人员: '',
  仓库: '',
  入库人员: '',
  默认排序: '入库时间倒序',
  入库备注: '',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getPutinList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getPutinList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getPutinList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
  //编辑
  if (type === 'edit' || type === 'info') {
    const retPath = (currentRoute.value.name.indexOf("Stone") >= 0) ? '/inventorymanage/stoneaddputin' : '/purchasemanage/addputin'

    push({
      path: retPath,
      query: {
        id: item.id,
        mode: type,
        cmd: bCheckMode.value ? '审核' : '',
        store_id:item.store_id
      }
    })
  }
  else if (type === 'del') {
    ElMessageBox.confirm(t('msg.confirm_del_putin') + '--> ' + item.buy_drawin_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)
      const ret = await delPutinApi({
        ids: [item.id],
        fsm_exe_man_name: info.resident_name,
        fsm_exe_trig: '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getPutinList()
      }
    }
    ).catch(() => { })
  }
  else if (type === 'putin') {
    ElMessageBox.confirm('是否入库，操作后仓库库存将发生变化！' + '--> ' + item.buy_drawin_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)
      const ret = await updatePutinApi({
        id: item.id,
        fsm_exe_man_name: info.resident_name,
        fsm_exe_trig: '入库'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getPutinList()
      }
    }
    ).catch(() => { })
  }
}


//查询入库单列表
const getPutinList = async (page = 1) => {
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)

  tmp.入库时间 = tmp.入库时间[0]+','+tmp.入库时间[1]

  if (searchCondition.store_type == '全部') {
    tmp.store_type = ''
  }
  isLoading.value = true
  const ret = await getPutinListApi(tmp)
  if (ret) {
    putinData.splice(0, putinData.length, ...ret.data)
    for (let one of putinData) {
      one.checked = false
    }
    totleCount.value = parseInt(ret.count)
    Object.assign(rootData, ret)
  }
  isLoading.value = false
}

//审核模式
const bCheckMode = ref(false)

onMounted(async () => {

  //初始化默认时间
  let days = getDateArea(180)
  defaultCondition.入库时间 = [days[0], days[1]]
  searchCondition.reset()

  if (currentRoute.value.name === "PutinCheckList" || currentRoute.value.name === "StonePutinCheckList") {
    bCheckMode.value = true
    searchCondition.fsm_cur_state = '等待审核'
  }

  if (currentRoute.value.query.buy_drawin_num != undefined)
    searchCondition.收货单号 = currentRoute.value.query.buy_drawin_num as string
  if (currentRoute.value.query.type != undefined)
    searchCondition.store_type = currentRoute.value.query.type as string+'库'
  console.log(putinData)
  getPutinList()
  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  adjustScrollerHeight()
  window.addEventListener('resize', adjustScrollerHeight)
})

onBeforeMount(() => {

});



const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    const tmp = cloneDeep(searchCondition)
    tmp.入库时间 = tmp.入库时间[0]+','+tmp.入库时间[1]

    if (searchCondition.store_type == '全部') {
      tmp.store_type = ''
    }
    const ret = await exportBuyPutinListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}

const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: '采购入库' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}
//批量打印
const toPrintPageMul = () => {
  //拿到所有勾选
  let arrayAll = putinData.filter((one) => one.checked)
  if (arrayAll.length == 0)
  {
    ElMessage.warning('请先勾选要打印的单据！')
    return
  }
  console.log(arrayAll)
  let params = []
  for(let one of arrayAll)
  {
    params.push({ ...one, printType: '采购入库' })
  }
  // let printInfo = { ...item, printType: '采购入库' }
  sessionStorage.setItem('printInfo', JSON.stringify(params))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}

const checkAll = ref(false)
const onCheckedAll = () => {
  for (let one of putinData)
  {
    one.checked = checkAll.value
  }
}


const adjustScrollerHeight = () => {
  const height = document.getElementById('mainscroll')?.clientHeight
  const scroller = document.getElementById('dynamicScroller');
  if (scroller) {
    scroller.style.height = `${height}px`;
  }
}
onBeforeUnmount(() => {
  window.removeEventListener('resize', adjustScrollerHeight)
})

watch(putinData, () => {
  adjustScrollerHeight()
})

const isLoading = ref(false)
</script>

<template>
  <!-- 列表 -->
  <div ref="rootRef">
    <div class="p-2 pt-0">
      <div class="pt-1 pb-1 pl-1 pr-51 pb-1 mb-5 bg-white relative">
        <div class="absolute top-3 left-10">
            <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                <Icon icon="carbon:export" />
                <div class="pl-2">{{ t('button.export') }}</div>
            </ElButton>
          </div>
        <div class="text-center mb-5 font-bold">{{ t('putin.list') + (bCheckMode ? '-审核' : '') }}</div>
        <!-- 检索条件 -->
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.check_status') }}</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择">
            <el-option v-for="item in ['订单创建', '等待审核', '等待修改', '等待提交', '审核通过', '已拒绝', '已关闭']" :key="item" :label="item"
              :value="item" />
          </el-select>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">入库类型</div>
          <el-select size="small" class="searchItem" v-model="searchCondition.store_type" placeholder="请选择">
            <el-option v-for="item in ['全部', '良品库', '不良品库']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">入库单号</div>
          <el-input size="small" v-model="searchCondition.入库单号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品名称</div>
          <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品编码</div>
          <el-input size="small" v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">收货单号</div>
          <el-input size="small" v-model="searchCondition.收货单号" placeholder="" class="searchItem" />
        </div>
 
        <!-- <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">入库标识</div>
          <el-select class="searchItem" v-model="searchCondition.入库标识" placeholder="请选择">
            <el-option v-for="item in ['未入库', '已入库']" :key="item" :label="item" :value="item" />
          </el-select>
        </div> -->
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品规格</div>
          <el-input size="small" v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">供应商</div>
          <el-input size="small" v-model="searchCondition.供应商" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购单号</div>
          <el-input size="small" v-model="searchCondition.采购单号" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购人员</div>
          <el-input size="small" v-model="searchCondition.采购人员" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">仓库</div>
          <el-input size="small" v-model="searchCondition.仓库" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">入库人员</div>
          <el-input size="small" v-model="searchCondition.入库人员" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">默认排序</div>
          <el-select class="searchItem" v-model="searchCondition.默认排序" placeholder="请选择">
            <el-option v-for="item in ['入库时间正序','入库时间倒序','入库编号正序','入库编号倒序']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">入库备注</div>
          <el-input size="small" v-model="searchCondition.入库备注" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">入库时间</div>
          <el-date-picker size="small" class="searchItem" v-model="searchCondition.入库时间" type="daterange"
            range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>

        <div class="flex justify-end items-center mr-6 mt-6 mb-2">
          <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" />
          <ElButton class="ml-5" type="primary" @click="onSearch">
            <Icon icon="tabler:search" />
            <div class="pl-1">查询</div>
          </ElButton>
          <ElButton type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="pl-2">清除</div>
          </ElButton>
        </div>
      </div>
      <!-- 图例 -->
      <div class="flex mb-2">
        <div class="flex text-sm items-center mr-6">
          <div class="title_create w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          已创建
        </div>
        <div class="flex text-sm items-center mr-6">
          <div class="title_checked w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          审核通过
        </div>
        <div class="flex text-sm items-center mr-6">
          <div class="title_wait w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          驳回
        </div>
        <div class="flex text-sm items-center mr-6">
          <div class="title_ok w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          已入库
        </div>
        <ElButton type="success" class="ml-auto" plain @click="toPrintPageMul">
          <Icon title="打印" icon="material-symbols:print-outline"/>
          批量打印
        </ElButton>
      </div>
      <!-- 产品列表 -->
      <div v-loading.lock="isLoading">
        <!-- 表头 -->
        <div class="flex header headerBk" style="color: #333;">
          <div class="w-[70%] flex justify-center items-center">
            <ElCheckbox class="min-w-[14px] !h-[14px] ml-5" v-model="checkAll" @change="onCheckedAll" />
            <div class="min-w-[50%] text-center">
              产品名称
            </div>
            <div class="min-w-[10%] text-center">采购数量</div>
            <div class="min-w-[10%] text-center">入库数量</div>
            <div class="min-w-[10%] text-center">单价</div>
            <div class="min-w-[20%] text-center">金额</div>
          </div>
          <div class="flex flex-grow !p-0">
            <div class="rightcss rightcss_title">状态</div>
            <div class="rightcss rightcss_title">入库日期</div>
            <div class="rightcss rightcss_title">备注</div>
            <div class="rightcss_title !min-w-[70px]">操作</div>
          </div>
        </div>

        <!-- 表内容 -->
        <DynamicScroller :items="putinData" :min-item-size="89.57" key-field="id" class="scroller" id="dynamicScroller">
          <template #default="{ item, index,active }">
            <DynamicScrollerItem 
              :item="item" :size-dependencies="[
                  item.pdt_list.length
                ]" 
              :data-index="index"
              :active="active"
            >
            <div>
          <!-- 内容头 -->
            <div  class="mt-4 bg-blue-200" style="box-shadow:var(--el-box-shadow-lighter);">
              <div class="p-2 h-[30px] flex flex-nowrap text-[13px] justify-between font-bold border-1px items-center">
                <ElCheckbox v-model="item.checked" />
                <div class="ml-2 w-[45%]  flex items-center">
                  <div class="rounded p-1 pl-2 pr-2 font-bold mr-4 min-w-[70px] text-center" style="color:white"
                    :class="{ 'title_create': ['订单创建', '等待审核', '等待确认', '等待修改', '等待提交'].includes(item.fsm_cur_state), 'title_checked': item.fsm_cur_state === '审核通过', 'title_ok': ['已入库', '已关闭', '已拒绝'].includes(item.fsm_cur_state), 'title_wait': item.fsm_cur_state === '等待修改' }">
                    {{ item.fsm_cur_state }}
                  </div>
                  <div class="mr-4 font-bold">
                    {{ item.create_date.split(' ')[0] }}
                  </div>
                  <div class="mr-4">入库单号: {{ item.buy_putin_num }}</div>
                  <!-- <div class="mr-2">质检单号: {{ item.buy_check_num }}</div> -->
                  <div class="mr-4">收货单编号: {{ item.buy_drawin_num }}</div>
                  <div>采购单编号: {{ item.buy_order_num }}</div>
                </div>
                <div class="min-w-[200px] flex items-center justify-center">
                  <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                  {{ checkPermissionApi('供应商名称显示') ? item.supplier_nick : '***' }}
                </div>
                <div class="flex justify-center items-center min-w-[250px]">
                  <div class="mr-5">收货人:{{ item.drawin_man_name }}</div>
                  <Icon class='mr-3'
                          style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;"
                          icon="material-symbols:print-outline" @click="toPrintPage(item)" />
                  <!-- <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin-right: 5px;" icon="solar:file-bold" />
                      <div>文件(0)</div>
                      <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin:0 5px 0 10px;" icon="material-symbols:contract" />
                      <div>合同(0)</div> -->
                </div>
                <!-- 靠右的其他信息 -->
                <div class="ml-auto flex justify-center items-center min-w-[200px]">
                  <div class="mr-4">仓库: {{ item.store_nick }}</div>
                  <div>入库员: {{ item.drawin_man_name }}</div>
                  <Icon title="打印" style="scale: 1.5; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;" icon="material-symbols:print-outline" @click="toPrintPage(item)"/>
                  <!-- <Icon style="scale: 1.1; margin:0 5px 0 50px;" icon="ic:baseline-qrcode" />
                      <Icon style="scale: 1.1; margin:0 5px 0 10px;color: rgb(45, 153, 253);" icon="material-symbols:print-outline" />
                      <Icon style="scale: 1.1; margin:0 5px 0 10px;" icon="uil:copy" /> -->
                </div>
              </div>
            </div>
            <!-- 内容体 -->
            <div class="flex">
              <!-- 左边产品列表 -->
              <div class="w-[70%]  table_self">
                <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                  <div class="min-w-[50%] text-center flex-grow ">
                    <div class="flex justify-start items-center w-[100%] p-1">
                      <el-image v-if="pdt.pics.length > 0" class="object-fill w-[80px] h-[80px] min-w-[80px]"
                        :src="pdt.pics[0].url" />
                      <el-image v-if="pdt.pics.length <= 0" class="object-fill w-[80px] h-[80px] min-w-[80px]"
                        src="/nopic.jpg" />
                      <div class="ml-2 inline-block text-left max-w-[100%]">
                        <div style="white-space: normal;" class="nameStyle" @click="handleOper('info', item)">{{
                          '[' + pdt.name+']'+pdt.nick }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">{{ pdt.采购数量 + ' '+pdt.base_unit }}</div>
                  <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">{{ pdt.入库数量 }}</div>
                  <div class="min-w-[10%] flex justify-center items-center flex-col border-l-1px">
                    <div>
                      {{ getMoneyFlag(item.money_type) + (checkPermissionApi('采购入库显示价格') ? (pdt.buy_price_bef_tax) : '*') }}
                    </div>
                    <div>
                      {{ getMoneyFlag(item.money_type) + (checkPermissionApi('采购入库显示价格') ? (pdt.buy_price_aft_tax) : '*') }}
                    </div>
                    <div class="ex_text">含税{{ pdt.发票税率 }}%</div>
                  </div>
                  <div class="min-w-[20%] flex justify-center items-center flex-col border-l-1px">{{
                    getMoneyFlag(item.money_type) + (checkPermissionApi('采购入库显示价格') ?(ceilToFixed(pdt.buy_price_aft_tax * pdt.入库数量,4,0)):'*') }}</div>

                </div>
              </div>
              <!-- 右边其他数据 -->
              <div class="flex flex-grow text-center  right">
                <div class="rightcss" style="text-align: center;">
                  <div>{{ item.type }}</div>
                  <div style="font-size: 13px; font-weight: 800;">{{ item.fsm_cur_state }}</div>
                  <div class="text-red-400 mt-1">{{ item.fsm_log_list.length > 0 ? item.fsm_log_list[0][5] : '' }}</div>
                </div>
                <div class="rightcss">{{ item.putin_date }}</div>
                <div class="rightcss">{{ item.note }}</div>
                <div class="!min-w-[70px] flex justify-center items-center flex-col" style="text-align: center;">
                  <ElButton v-if="bCheckMode && item.fsm_can_trig_data.审核触发.length > 0" type="success" size="small"
                    @click="handleOper('info', item)">{{ t('cmd.check') }}</ElButton>
                  <el-dropdown v-if="!bCheckMode" trigger="click" placement="bottom">
                    <span class="el-dropdown-link">
                      <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.detail') }}</el-dropdown-item>
                        <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('入库')"
                          @click="handleOper('putin', item)">{{ t('cmd.putin') }}</el-dropdown-item>
                        <el-dropdown-item v-if="item.fsm_can_trig_data.操作触发.includes('保存')"
                          @click="handleOper('edit', item)">{{ t('userOpt.edit') }}</el-dropdown-item>
                        <el-dropdown-item 
                          @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>

            </div>

            </DynamicScrollerItem>
          </template>
        </DynamicScroller>

        <!-- 表内容 -->
        <div class="mt-4 bg-white" v-for="item in putinData" :key="item.id"
          style="box-shadow:var(--el-box-shadow-lighter);">

        </div>
      </div>
    </div>
  <div class="flex items-center mb-5">
    <div class="ml-auto ">
      <span class="font-bold">税前总金额:</span>
      <span class="mr-6">{{ rootData.added.税前合计 }}</span>
      <span class="font-bold">总金额:</span>
      <span class="mr-6">{{ rootData.added.税后合计 }}</span>
      <span class="font-bold">总数量:</span>
      <span class="mr-6">{{ rootData.added.入库合计 }}</span>
    </div>
    <el-pagination class="flex justify-end" v-model:current-page="searchCondition.page"
      v-model:page-size="searchCondition.count" :page-sizes="[10, 50, 100, 300]" :background="true"
      layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
      @current-change="handleCurrentChange" />
  </div>


      <PrintModal v-model:show="dialogVisible" />
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  cursor: pointer;
}

.nameStyle:hover {
  color: rgb(130, 130, 255);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem {
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  font-weight: bold;
  color: #333;
}

.headerBk {
  // background-color: #6d92b4 !important;
  background-color: #fff;
}

.content {
  &:extend(.header);
  font-size: 14px;
}

.header>div,
.content>div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  // white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}

.header>div:last-child,
.content>div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self {
  font-size: 14px;
}

.table_self>div,
.right>div {
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.test {
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text {
  font-size: 11px;
  color: #646464;
}

.ex_text_danger {
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
  flex: 1;
  min-width: 0;
  /* 设置最小宽度，防止内容撑大 */
  text-align: left;
  /* 文字居中对齐 */
  word-wrap: break-word;
  /* 文字超长时换行处理 */
  font-size: 11px;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.rightcss_title {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}

:deep(.el-checkbox__input){
  border: 1px solid #999;
}

//---------------列表对象标题栏条件颜色-------------------
.title_create {
  //已创建
  background-color: #79bbff;
}

.title_checked {
  //已审核
  background-color: #95d475;
}

.title_ok {
  //已入库
  background-color: #b1b3b8;
}

.title_wait {
  //等待修改
  background-color: #f89898;
}
</style>

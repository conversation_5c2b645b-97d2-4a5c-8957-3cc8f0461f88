<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElUpload,ElMessage,ElMessageBox,ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption,ElDatePicker,ElCheckbox,ElDropdown,ElDropdownItem,ElDropdownMenu,ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getSupplierListApi,delSupplierApi } from '@/api/customer'
import { checkPermissionApi } from '@/api/tool';
import { getOssSignApi, ossUpload } from '@/api/oss';
import { useAppStore } from '@/store/modules/app'
import { useCache } from '@/hooks/web/useCache'
import { importSupplierListApi } from '@/api/extra';
import { DialogImportTask } from '@/components/DialogImportTask'

const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const tableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  supplier_name:'',
  supplier_nick:'',
  phone: '',      //手机1
  telephone: '',    //电话
  mainer_name: '',      //负责人
  followe_name: '',    //跟单员
  address: '',     //地址
  remark: '',      //备注
  create_date: '',  //创建时间范围
  modify_date: '',  //修改时间范围
  status:'',      //状态
  linkman:'',    //联系人
  page: 1,
  count: 15
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//供应商数据源
const supplierData = reactive([])

//查询供应商数据
const getSupplierData = async()=>{
  const ret = await getSupplierListApi(searchCondition)
  if(ret)
  {
    console.log(ret)
    supplierData.splice(0,supplierData.length,...ret.data)
    totleCount.value = parseInt( ret.count)
  }
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getSupplierData()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (tableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 300
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getSupplierData()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getSupplierData()
}
//创建新客户
const onAddCustmer = ()=>{
  push({
    path: '/suppliermanage/addsupplier',
  })
}

//处理表格对象操作
const handleOper = (type,row) => {
  console.log(row)
  if(type==='edit' || type == 'info' ){
    push({
      path: '/suppliermanage/addsupplier',
      query:{
        id:row.id,
      }
    })
  }
  else if(type==='del'){
    ElMessageBox.confirm(
      '确定是否删除该供应商？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        const ret = await delSupplierApi({ "ids": [row.id] })
        if (ret) {
          getSupplierData()

          ElMessage({
            type: 'success',
            message: t('msg.delOK'),
          })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })
  }
}

//计算获取主要联系人
const mainConnectorInfo = (row)=>{
    if(row.corp_linkman.length>0)
    {
      //找到主要联系人
      for(let item of row.corp_linkman)
      {
        if(item.first)
        {
          return item
        }
      }
      //没找到，默认返回第一条
      return row.corp_linkman[0]
    }
    return {
      name:'',
      phone:''
    }
}


onMounted(async()=>{
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //刷新表格
  getSupplierData()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// // 在组件销毁前移除窗口大小变化的监听器
// onBeforeMount(() => {
//   window.removeEventListener('resize', handleWindowResize);
// });


//正在上传
const bShowTaskDlg = ref(false)
const onShowTaskDlg = async()=>{
  bShowTaskDlg.value = true
}
const loading = ref(false)
const uploadImg = async(file) => {
  const info = wsCache.get(appStore.getUserInfo)
  console.log(info)

    let path = 'import/'+info.username+'/supplier/'
    
    const ret =await getOssSignApi({upload_dir:path})
    if(ret)
    {    
        loading.value = true
        const end = await ossUpload(ret.data.token,file.file,path,(pro)=>{
            console.log('pppp',pro)
            
        })  
        loading.value = false   
        // fileData.push({
        //     name:file.file.name,
        //     url:end.url
        // })
        //上传完成调用导入
        const imp = await importSupplierListApi({
          url:end.url
        })
        if(imp)
        {
            ElMessage.success("上传文件成功，等待系统导入！")
            console.log('上传完成',end.url)
            onShowTaskDlg()
        }
        else
        {
          ElMessage.error("创建导入任务失败，请重试！")
        }

    }
}

</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
    <div class="absolute top-7 left-10 flex">
      <ElButton class="mr-3" type="success" @click="onAddCustmer">
        <Icon icon="fluent-mdl2:people-add" />
        <div class="pl-2">{{ t('button.add') }}</div>
      </ElButton>
      <el-upload
          class='mr-3'
          :http-request="(file) => uploadImg(file)"
          :auto-upload="true"
          :show-file-list = 'false'
          >
        <template #trigger>
          <el-button color="#409EFF" plain :loading="loading" :disabled="loading">
            <Icon icon="clarity:import-line" />
            {{ loading?'上传中..':'导入' }} 
          </el-button>
        </template>
      </el-upload>
    </div>
    <div  class="h-[100%] bg-white p-7 pb-5">
      <div class="text-center mb-5 font-bold">{{ t('purchase.supplier_manage') }}</div>
      <div style1="border: 1px solid rgb(143, 143, 143);" class="p-5 mb-4 bg-light-200">
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('supplier.name') }}</div>
          <el-input v-model="searchCondition.supplier_name" placeholder="" class="searchItem"/>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('supplier.nick') }}</div>
          <el-input v-model="searchCondition.supplier_nick" placeholder="" class="searchItem"/>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('customer.connector_phone') }}</div>
          <el-input v-model="searchCondition.phone" placeholder="" class="searchItem"/>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('customer.mainer') }}</div>
          <el-input v-model="searchCondition.mainer_name" placeholder=""  class="searchItem"/>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('customer.status') }}</div>
          <el-select v-model="searchCondition.status" placeholder="请选择" class="searchItem">
            <el-option v-for="item in ['正常','禁用']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('customer.address') }}</div>
          <el-input v-model="searchCondition.address" placeholder=""  class="searchItem"/>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('customer.remark') }}</div>
          <el-input v-model="searchCondition.remark" placeholder=""  class="searchItem"/>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('customer.createdate') }}</div>
          <el-date-picker v-model="searchCondition.create_date" type="daterange" range-separator="To" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD" class="searchItem"/>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('customer.modifydate') }}</div>
          <el-date-picker v-model="searchCondition.modify_date" type="daterange" range-separator="To" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD" class="searchItem"/>
        </div>
        <div  class="text-center mt-5 mb-2 flex justify-end items-center">
          <el-checkbox :label="t('customer.senior')" v-model="senior" size="small"/>
          <ElButton class="ml-5" type="primary" @click="onSearch">
            <Icon icon="ri:phone-find-line" />
            <div class="pl-2">查询</div>
          </ElButton>
          <ElButton type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="pl-2">清除</div>
          </ElButton>
        </div>
      </div>
 
      <el-table ref="tableRef" header-cell-class-name="tableHeader" :data="supplierData" style="width: 100%"
        :height1="tableHeight" border stripe>        
        <el-table-column show-overflow-tooltip  prop="supplier_name" :label="t('supplier.name')" width="150" >
          <template #default="scope">
            {{ checkPermissionApi('供应商编码显示')?scope.row.supplier_name:'***' }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip  prop="supplier_nick" :label="t('supplier.nick')" width="200">
          <template #default="scope">
            <div class="nameStyle" @click="handleOper('info', scope.row)">{{ checkPermissionApi('供应商名称显示')?scope.row.supplier_nick:"***" }}</div>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip  prop="linkman" :label="t('supplier.contact')" width="120" >
          <template #default="scope">
            <div>{{ mainConnectorInfo(scope.row).name }}</div>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip  prop="linkman" :label="t('supplier.phone')" width="120" >
          <template #default="scope">
            <div>{{ [mainConnectorInfo(scope.row).phone,mainConnectorInfo(scope.row).telephone].find(value => value !== '') || '' }}</div>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="mainer_name" :label="t('supplier.manager')" width="90" />
        <el-table-column show-overflow-tooltip prop="status" :label="t('supplier.status')" width="80" />
        <el-table-column show-overflow-tooltip prop="corp_addr" :label="t('supplier.address')" width="100" />
        <el-table-column show-overflow-tooltip prop="remark" :label="t('supplier.remark')"  min-width="200"/>
        <el-table-column fixed="right" :label="t('userTable.operate')" width="90">
          <template #default="scope">
            <el-dropdown trigger="click" placement="left">
              <span class="el-dropdown-link">
                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleOper('info', scope.row)">{{ t('userOpt.detail') }}</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('edit', scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination class="flex justify-end mb-4 mt-4"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[15, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

    </div>

    <DialogImportTask v-model:show="bShowTaskDlg" :mod="'supplier'" :cmd="'import_list'" @on-submit="getSupplierData"/>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }
.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}
.searchItem{
  width: 200px;
}
</style>

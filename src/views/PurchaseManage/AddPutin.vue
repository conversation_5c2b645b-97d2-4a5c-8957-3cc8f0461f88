<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard,ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,nextTick } from 'vue'
import { getStoreListApi,getPutinNewnumApi,getReceiptInfoApi,getPutinInfoApi,addPutinApi,updatePutinApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount,watch } from 'vue'
import {checkFormRule, checkPermission<PERSON><PERSON>, closeOneTagByName} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { DialogCheckShow } from '@/components/DialogCheckShow'



const { currentRoute,back,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//入库单数据
const putinData = reactive(
{
    "supplier_id": "",
    "buy_order_num": "",
    "buy_drawin_num": "",
    "buy_check_num": "",
    "buy_putin_num": "",
    "store_id": "", //仓库ID
    "pdt_list": [],
    "note": "", //入库单备注
    //入库方式（无用）
    drawin_man_name:'',
    order_man_name:'',
    putin_man_id:'',
    putin_man_name:'',
    putin_date:'',
    type:'良品',
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:[]
    }, //审核决策
    fsm_cur_state:'订单创建',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)


//获取最新ID
const onChangeID = async()=>{
    const ret = await getPutinNewnumApi()
    if(ret)
    {
        console.log(ret)
        putinData.buy_putin_num = ret.data.new_id
    }
}

const storeCur = computed(()=>{
    return storeType.value == '良品'?storeData.filter(item => item.type === '良品库'):storeData.filter(item => item.type === '不良品库')
})

//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    type: storeType.value == undefined?'':(storeType.value+'库'),
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)

    if(storeType.value == undefined)//需要确认当前是查询良品还是不良品
    {
        for(let item of storeData)
        {
            if(item.id == currentRoute.value.query.store_id)
            {
                storeType.value = item.type.replace('库','')
                break
            }
        }
    }
  }

}

const oldPutin = reactive({})
const storeType = ref('良品')

onMounted(async () => {    
    storeType.value = currentRoute.value.query.type
    if((currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)&& currentRoute.value.query.buy_putin_num == undefined)
    {
        await getStoreList()
        title.value = storeType.value == '良品'?t('putin.good_putin'):t('putin.bad_putin')

        onChangeID()
        //新建需要默认可以提审
        const tmp = [...putinData.fsm_can_trig_data.操作触发,'直接入库']
        putinData.fsm_can_trig_data.操作触发 = tmp


        //同步收货单数据
        const ret = await getReceiptInfoApi({
            id:currentRoute.value.query.receipt_id,
            page:1,
            count:100
        })
        const test = cloneDeep(ret.data)
        console.log('11111',test)

        console.log(ret)

        putinData.buy_drawin_num = ret.data.buy_drawin_num
        putinData.buy_order_num = ret.data.buy_order_num
        putinData.order_note = ret.data.order_note
        putinData.drawin_note = ret.data.note
        putinData.type = storeType.value as string
        putinData.pdt_list = ret.data.pdt_list

        //默认选择第一个仓库
        if(storeCur.value.length > 0)
            putinData.store_id = storeCur.value[0].id

        //追加产品额外属性
        for(let item of putinData.pdt_list)
        {
            item.已入库良品数量 = 0
            item.已入库不良品数量 = 0
          //  item.类型 = storeType.value
            item.入库备注 = ''
            if(putinData.type == '良品')
                item.入库数量 = item.良品数量-(item.已入库良品+item.未入库良品)
            else
                item.入库数量 = item.不良品数量-(item.已入库不良品+item.未入库不良品)
            
        }

        //设置入库人员
        const info = wsCache.get(appStore.getUserInfo)
        putinData.putin_man_id = info.id
        putinData.putin_man_name = info.resident_name
        //默认入库日期为今天
       // putinData.putin_date = getTodayDate()

    }
    else
    {        
        if(currentRoute.value.query.mode == 'info')
        {
            title.value = t('putin.look')
        }
        else
        {
            title.value = t('putin.modify')
        }



        
        //查询产品信息 
        const ret = await getPutinInfoApi({
            id:currentRoute.value.query.id,
            buy_putin_num:currentRoute.value.query.buy_putin_num == undefined?'':currentRoute.value.query.buy_putin_num,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(putinData, ret.data)
            putinData.pdt_list = ret.data.pdt_list;
            storeType.value = ret.data.type
            await getStoreList()
            putinData.type = storeType.value
            console.log(putinData)
        }

        if(currentRoute.value.query.mode === 'info') //查看模式
        {
            let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
            let excludeDiv = document.getElementById('check');
            if(excludeDiv != null)
            {
                // 使用 :not() 伪类排除特定的 div 元素下的子元素
                let filteredComponents = Array.from(components).filter(
                    component => !excludeDiv.contains(component)
                );
                filteredComponents.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }
            else
            {
                components.forEach((component) => {
                    component.setAttribute('disabled', true);
                });
            }


            components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
  
            let i = 0
            components.forEach((component) => {
                if(i>0)
                    component.classList.add('infomode')
                i++
            });
            const suffixElements = document.querySelectorAll('.el-input__suffix');
            suffixElements.forEach(suffixElement => {
                suffixElement.style.display = 'none';
            });
        }
    }

    //先备份下旧对象
    Object.assign(oldPutin,putinData)
})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})



//显示隐藏选择入库员窗口变量
const showSelPutinUserDlg = ref(false)
//显示选择入库员弹窗
const onSelPutinUser = ()=>{
    showSelPutinUserDlg.value = true
}
//选择入库员回调
const onSelPutinCallback = (id,name)=>{
    console.log(id,name)
    putinData.putin_man_id = id
    putinData.putin_man_name = name
}


//重新校验输入
const recomputeNum = (pdt)=>{
    if(putinData.type == '良品')
    {
        if(pdt.入库数量>(pdt.良品数量-(pdt.已入库良品+pdt.未入库良品)))
        {
            pdt.入库数量 = pdt.良品数量-(pdt.已入库良品+pdt.未入库良品)
        }        
    }
    else if(putinData.type == '不良品')
    {
        if(pdt.入库数量>(pdt.不良品数量-(pdt.已入库不良品+pdt.未入库不良品)))
        {
            pdt.入库数量 = pdt.不良品数量-(pdt.已入库不良品+pdt.未入库不良品)
        }
    }
    if(pdt.入库数量<0)
    {
        pdt.入库数量 = 0
    }
}

// //保存
// const onSave = async()=>{
//     console.log(putinData)

//     const rule = await checkFormRule(ruleFormRef.value)
//     if(!rule)
//     {
//         ElMessage.warning(t('msg.checkRule'))
//         return
//     }

//     if(putinData.store_id == "")
//     {
//         ElMessage.error('请选择仓库！')
//         return
//     }

//     //删除putinData.pdt_list中入库数量为0的行
//     putinData.pdt_list = putinData.pdt_list.filter(pdt=>pdt.入库数量!=0)
    
//     if(!checkPdt())
//     {
//         console.log('???')
//         return 
//     }

//     if(putinData.id == undefined)
//     {
//         const ret = await addPutinApi(putinData)
//         if(ret)
//         {
//             ElMessage.success(t('msg.newPutinSuccess'))
//             back()
//         }
//     }
//     else //修改
//     {
//         const ret =await updatePutinApi(putinData)
//         if(ret)
//         {
//             ElMessage.success(t('msg.updatePutinSuccess'))
//             back()
//         }
//     }


// }

//校验pdt
const checkPdt = ()=>{
    if(putinData.pdt_list.length <= 0)
    {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}

//提交审核意见
const handleCheck = async(btn)=>{
    console.log(currentRoute.value)
    // return
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    if(putinData.store_id == "")
    {
        ElMessage.error('请选择仓库！')
        return
    }
    

    const info = wsCache.get(appStore.getUserInfo)
    putinData.fsm_exe_man_name = info.resident_name
    putinData.fsm_exe_trig = btn
    //删除putinData.pdt_list中入库数量为0的行
    putinData.pdt_list = putinData.pdt_list.filter(pdt=>pdt.入库数量!=0)
    
    if(putinData.pdt_list.length<=0)
    {
        ElMessage.warning('入库数量不能为0')
        return
    }

    // console.log(putinData.pdt_list)
    // return

    if(putinData.id == undefined)
    {
        const ret = await addPutinApi(putinData)
        if(ret)
        {
            ElMessage.success(t('msg.newPutinSuccess'))
            if(checkPermissionApi('采购入库同页面审核'))
            {
                const oldPath = currentRoute.value.path
                const store_id = currentRoute.value.query.store_id
                push({
                    path: '/systemmanage/tmp'
                }).then(()=>{
                    //刷新当前页面
                    console.log('刷新了！！！！')
                    push({
                    path: oldPath,
                    query: {
                        id: '',
                        buy_putin_num:ret.data.buy_putin_num,
                        mode: 'info',
                        cmd: '审核',
                        store_id:store_id
                    }
                    })
                })
            }
            else    
            {
                baskFront()
            }
           
        }
    }
    else //修改
    {
        //特殊处理同意
        if(btn === '同意')
        {
            // ElMessageBox.confirm('是否入库，操作后仓库库存将发生变化！', t('msg.notify'), {
            // confirmButtonText: t('msg.ok'),
            // cancelButtonText: t('msg.channel'),
            // type: 'error',
            // }
            // ).then(async () => {
                const ret =await updatePutinApi({
                    id:putinData.id,
                    putin_date:putinData.putin_date,
                    fsm_exe_trig:putinData.fsm_exe_trig,
                    fsm_exe_man_name:putinData.fsm_exe_man_name,
                    fsm_exe_log:putinData.fsm_exe_log,
                })
                if(ret)
                {
                    ElMessage.success(t('msg.updatePutinSuccess'))
                    closeOneTagByName(currentRoute.value.meta.title)
                    push({
                        path: '/purchasemanage/receiptlist'
                    })
                }
            //})


            
        }
        else    
        {
            const ret =await updatePutinApi(putinData)
            if(ret)
            {
                ElMessage.success(t('msg.updatePutinSuccess'))
                closeOneTagByName(currentRoute.value.meta.title)
                    push({
                        path: '/purchasemanage/putinlist'
                    })
            }
        }

    }
}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = ()=>{
    showCheckHisDlg.value = true
}


//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}
</script>

<template>
    <ContentDetailWrap :title="title+'-'+putinData.fsm_cur_state" @back="baskFront()">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史
            </ElButton>
        </template>
        <template #right>
            <!-- <ElButton type="primary" @click="onSave">
                {{ t('exampleDemo.save') }}
            </ElButton> -->
            <ElButton  v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.mode != 'info' && putinData.fsm_can_trig_data.操作触发.includes('保存')" type="primary" @click="handleCheck('保存')">保存</ElButton>
            <el-popconfirm  title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && putinData.fsm_can_trig_data.操作触发.includes('提交审核')"   type="success" >提交审核</ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm  title="是否确认直接入库?" @confirm="handleCheck('直接入库')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && putinData.fsm_can_trig_data.操作触发.includes('直接入库')"   type="success" >直接入库</ElButton>
                </template>
            </el-popconfirm>
            <!-- <el-popconfirm  title="是否入库，操作后仓库库存将发生变化！" @confirm="handleCheck('入库')"> 
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && putinData.fsm_can_trig_data.操作触发.includes('入库')"   type="success" >入库</ElButton>
                </template>
            </el-popconfirm> -->
            
            <el-popconfirm  title="是否确认关闭订单?" @confirm="handleCheck('关闭')">
                <template #reference>
                    <ElButton  v-show="currentRoute.query.cmd != '审核' && putinData.fsm_can_trig_data.操作触发.includes('关闭')" type="danger">关闭订单</ElButton>
                </template>
            </el-popconfirm>    
        </template>
        <el-card id="check" v-if="putinData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%]">
            <template #header>
            <div class="flex items-center">
                <span>当前节点:</span>
                <span class="text-red-500 mr-3">{{ putinData.fsm_cur_state }}</span>
                <!-- <ElButton @click="handleCheck(btn)" v-for="btn in putinData.fsm_can_trig_data.审核触发.toReversed()" :key="btn" :type="btn=='同意'?'success':'danger'">{{ btn }}</ElButton> -->
                <ElButton v-show="putinData.fsm_can_trig_data.审核触发.includes('同意')" type="success" @click="handleCheck('同意')" >同意</ElButton>
                <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="putinData.fsm_can_trig_data.审核触发.includes('驳回')" type="danger" >驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="putinData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                    </template>
                </el-popconfirm>

                <div class="flex ml-4 items-center">
                    <div class="mr-2">入库日期:</div>
                    <el-date-picker v-model="putinData.putin_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                </div>
                
                
            </div>
            </template>
            <el-input v-model="putinData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        </el-card>
        <el-form :rules="rules" :model="putinData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('sale.name')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input  v-model="putinData.buy_putin_num" :disabled="putinData.id!=undefined" />
                            <ElButton v-if="putinData.id==undefined" type="warning"  @click="onChangeID">{{ t('button.update') }}</ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('receipt.id')"
                    class="flex">
                    <div>{{ putinData.buy_drawin_num }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('store.store')"
                    class="flex">
                    <el-select v-model="putinData.store_id" placeholder="Select" >
                        <el-option v-for="item in storeCur" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('purchase.id')"
                    class="flex">
                    <div>{{ putinData.buy_order_num }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('supplier.nick')"
                    class="flex">
                    <div>{{ checkPermissionApi('供应商名称显示')?putinData.supplier_nick:'***' }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('receipt.user')"
                    class="flex">
                    <div>{{ putinData.drawin_man_name }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('supplier.person')"
                    class="flex">
                    <div>{{ putinData.order_man_name }}</div>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('putin.user')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ putinData.putin_man_name }}</div> 
                        <ElButton @click="onSelPutinUser">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <!-- <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'入库日期'"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="putinData.putin_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item> -->

            </el-descriptions>
        
        </el-form>

        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="putinData.pdt_list" style="width: 100%" border stripe>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="120" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130" >
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick}}</div>
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.id != undefined"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    {{ scope.row.specs_name=='自定义规格'?scope.row.specs_text:scope.row.specs_name }}
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column v-if="storeType == '良品'" show-overflow-tooltip  :label="t('quality.good_count')" >
                <template #default="scope">
                    <div>{{ scope.row.良品数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column v-if="storeType != '良品'" show-overflow-tooltip  :label="t('quality.bad_count')" >
                <template #default="scope">
                    <div>{{ scope.row.不良品数量 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column v-if="storeType == '良品'" show-overflow-tooltip  :label="'已发起入库良品数'" min-width="110">
                <template #default="scope">
                    <div>{{ scope.row.未入库良品 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column v-if="storeType == '良品'" show-overflow-tooltip  :label="t('putin.putin_good')" min-width="110">
                <template #default="scope">
                    <div>{{ scope.row.已入库良品 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column v-if="storeType != '良品'" show-overflow-tooltip  :label="'已发起入库不良品数'" min-width="110">
                <template #default="scope">
                    <div>{{ scope.row.未入库不良品 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column v-if="storeType != '良品'" show-overflow-tooltip  :label="t('putin.putin_bad')" min-width="130">
                <template #default="scope">
                    <div>{{ scope.row.已入库不良品 }}</div>
                    <div>{{ scope.row.base_unit }}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('putin.count')" >
                <template #default="scope">
                    <el-input v-model="scope.row.入库数量" @input="recomputeNum(scope.row)" type="number"/>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('receipt.price')" >
                <template #default="scope">
                    {{ checkPermissionApi('采购入库显示价格')?(scope.row.buy_price_bef_tax+'/'+scope.row.buy_price_aft_tax):'*' }}
                        <div class="ex_text">含税{{ scope.row.发票税率 }}%</div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip   :label="t('receipt.totle_price')" >
                <template #default="scope">
                    {{ checkPermissionApi('采购入库显示价格')?scope.row.总价:'*' }}
                </template>
            </el-table-column>

            <el-table-column show-overflow-tooltip  prop="采购备注" :label="t('purchase.remark')" />
            <el-table-column show-overflow-tooltip  prop="收货备注" :label="t('receipt.remark')" />
            
            <el-table-column show-overflow-tooltip  :label="t('putin.remark')" >
                <template #default="scope">
                    <el-input v-model="scope.row.入库备注" />
                </template>
            </el-table-column>
        </el-table>
    <!-- 显示合计 -->
    <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[100%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">采购单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ putinData.order_note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">收货单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ putinData.drawin_note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">入库单备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="putinData.note" clearable :autosize="{ minRows: 10, maxRows: 4 }"     type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>

    </div>

        
        




        <!-- <div class="mb-60"></div> -->

        <!-- 选择质检员 -->
        <DialogUser :param="''" v-model:show="showSelPutinUserDlg" :title="t('msg.selectUser')" @on-submit="onSelPutinCallback"/>
        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="putinData.fsm_log_list" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}
//扩展文字
.ex_text{
  font-size: 11px;
  color: #646464;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
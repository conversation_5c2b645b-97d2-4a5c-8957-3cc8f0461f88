<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCheckbox,ElTable,ElPopconfirm,ElTag,ElCard,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,set } from 'vue'
import { getPurchaseNewnumApi } from '@/api/product'
import { getProductListApi,getProductInfoApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogProductSel } from '@/components/DialogProductSel'
import {ceilToFixed, checkFormRule, closeOneTagByName, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getPurchaseInfoApi,addPurchaseApi,updatePurchaseApi } from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed,nextTick } from 'vue'
import { cloneDeep } from 'lodash-es'
import { getGUID } from '@/api/tool'
import { getSupplierListApi } from '@/api/customer'
import { DialogCheckShow } from '@/components/DialogCheckShow'
import {checkPermissionApi} from '@/api/tool'

const { currentRoute,back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//采购单数据
const purchaseData = reactive(
{
    "supplier_id": "",
    supplier_name:'',
    supplier_nick:'',
    "buy_order_num": "",
    "supplier_order_num": "",
    "money_type": "人民币",
    "delivery_date": "",
    "buy_man_id": "",
    buy_man_name:'',
    "follow_man_id": "",
    follow_man_name:'',
    "buy_dept_id": "",
    "create_date": "",
    "modify_date": "",
    "pdt_list": [],
    "note": "",
    "express_fee": 0.00,
    "other_fee": 0.00,
    "pay_type": "月结",
    business_date:'', //业务日期
    is_same_delivery:0,
    合计费用:0,
    source:'',
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:['提交']
    }, //审核决策
    fsm_cur_state:'订单创建',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
}
)

const is_same_delivery_BOOL = computed({
    get: () =>{
        return purchaseData.is_same_delivery == 0?false:true
    },
    set:(value)=>{
        purchaseData.is_same_delivery = value?1:0
    }
}
)

//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])
//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])

//treeProp属性
const treeProps = {
    multiple: true,
    checkStrictly: true,
    value:'id', 
    children: 'sub_dept',
    label: 'name',
    emitPath:false
}

//获取最新ID
const onChangeID = async()=>{
    const ret = await getPurchaseNewnumApi()
    if(ret)
    {
        console.log(ret)
        purchaseData.buy_order_num = ret.data.new_id
    }
}


//输入发生变化时处理,要支持延时处理
const delaytime =ref<NodeJS.Timeout | null>(null); 
const onItemInput = (val)=>{
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)
    
    delaytime.value = setTimeout(() => {
      // 输入停止后触发的方法
      getProductList(val)
    }, 200) 
} 


//查询产品绑定的查询结果
const searchPdtData = reactive<any>([])
//查询产品列表根据编号
const getProductList = async (val='') => {
  const ret = await getProductListApi({
    name:val,
    nick:val,
    _or:true,
    prop_list:['采购产品','物料'],
    status:'正常',
    page: 1,
    count:30,
  })
  if(ret)
  {
    searchPdtData.splice(0,searchPdtData.length, ...ret.data)
    if(searchPdtData.length >0)
    {
        setCurrent(searchPdtData[0])
    }
  }
}

//根据产品列表中数量计算采购总数
const totlePurchase = computed(() => {

    let totle = 0
    for(const item of purchaseData.pdt_list)
    {
        if(item.采购数量 == undefined || item.id == undefined)
            continue
        totle += parseFloat(item.采购数量)
        totle += parseFloat(item.采购备品数量)
    }
    return totle
})

//根据产品列表计算采购总价
const totlePurchasePrice = computed(() => {
    
    let totle = 0
    let span = 0
    for(const item of purchaseData.pdt_list)
    {
        if(item.总价 == undefined || item.id == undefined)
            continue
        if(item.sell_buy_digit != undefined)
            span = parseFloat(item.sell_buy_digit)
        totle += parseFloat(item.总价)
    }
    
    return  parseFloat(totle.toFixed(span))
})

//计算合计费用(包含运费和其他费用)
const totlePurchasePriceAll = computed(() => {
    return totlePurchasePrice.value + parseFloat(purchaseData.express_fee)+parseFloat(purchaseData.other_fee)
})

//计算备品价格
const countBakPrice = (pdt)=>{
    //备品数量占采购数量的3%以内不算钱，超过3%最多只计算1%的钱
    // if(pdt.采购备品数量<=pdt.采购数量*0.03)
    // {
    //     return 0
    // }
    // else
    // {
    //     return Math.min(parseFloat((pdt.采购备品数量*pdt.buy_price_aft_tax*pdt.折扣).toFixed(pdt.sell_buy_digit)) , parseFloat((pdt.采购数量*0.01*pdt.buy_price_aft_tax*pdt.折扣).toFixed(pdt.sell_buy_digit)))
    // }
    return 0
}

// //重新计算产品相关数据
// let timeoutId: ReturnType<typeof setTimeout> | null = null;
// const reComputePdtInfo = (pdt,mode) => {
//     // 取消之前的延迟处理
//     if (timeoutId) {
//         clearTimeout(timeoutId);
//     }

//     // 创建新的延迟处理
//     timeoutId = setTimeout(() => {
//         reComputePdtInfoFun(pdt,mode)
//     }, 1000); // 设置延迟时间，单位为毫秒
// }

//重新分配PDT中的来源数量  销售备料
const reComputeSourceCountSell = (pdt) => {
    //组合PDT 才需要调整
    console.log(purchaseData.source)
    console.log('pdt.原始需求数量', pdt.原始需求数量, pdt)
    if(pdt.原始需求数量.indexOf(',')>=0)
    {
        let nTotle = parseFloat(pdt.采购数量) + parseFloat(pdt.采购备品数量)
        let arCount:string[] = pdt.原始需求数量.split(',')
        let arSourceTotle = 0
        //计算总需求
        for(const item of arCount)
        {
            arSourceTotle += parseFloat(item)
        }
        if (nTotle < arSourceTotle) {
            //需要按照从前往后的顺序来分nTotle，每份不超过原始需求数量中对应每一份的数量
            let arOut:string[] = []
            for(let one of arCount)
            {
                if(nTotle>0)
                {
                    if(nTotle>=parseFloat(one))
                    {
                        arOut.push(one)
                        nTotle -= parseFloat(one)
                    }
                    else
                    {
                        arOut.push(nTotle)
                        nTotle = 0
                    }
                }
            }
            pdt.需求数量 = arOut.join(',')
            console.log('调整后:',pdt.需求数量)
        }
        else
        {
            //还原默认
            pdt.需求数量 = pdt.原始需求数量            
            console.log('不需要调整:',pdt.采购数量,pdt.采购备品数量,nTotle,arSourceTotle)
        }

        //先还原
        pdt.sell_order_num = pdt.原始sell_order_num
        pdt.sell_order_sub = pdt.原始sell_order_sub
        pdt.父子任务单号 = pdt.原始父子任务单号
        pdt.标识 = pdt.原始标识

        //pdt.sell_order_num,pdt.sell_order_sub 对应的字符传逗号分割的数量要和 pdt.需求数量  保持一直 后面多的删除
        let arSell:string[] = pdt.sell_order_num.split(',')
        let arSub: string[] = pdt.sell_order_sub.split(',')
        let arZRWDH: string[] = pdt.父子任务单号.split(',')
        let arNeed: string[] = pdt.需求数量.split(',')
        let arBS:string[] = pdt.标识.split(',')
        //arSell arsUB 数组slice前arNeed长度 的内容
        let arSellOut:string[] = arSell.slice(0,arNeed.length)
        let arSubOut: string[] = arSub.slice(0, arNeed.length)
        let arZRWDHOut: string[] = arZRWDH.slice(0, arNeed.length)
        let arBSOut:string[] = arBS.slice(0,arNeed.length)

        console.log('arSellOut',arNeed.length,arSell.slice(0,arNeed.length))

        pdt.sell_order_num = arSellOut.join(',')
        pdt.sell_order_sub = arSubOut.join(',')
        pdt.父子任务单号 = arZRWDHOut.join(',')
        pdt.标识 = arBSOut.join(',')
    }
    else  //针对非组单也需要调整数量
    {
        let nTotle = parseFloat(pdt.采购数量) + parseFloat(pdt.采购备品数量)
        if(nTotle < parseFloat(pdt.原始需求数量))
        {
            pdt.需求数量 = nTotle 
        }
        else
        {
            pdt.需求数量 = pdt.原始需求数量
        }
    }
}



const reComputeSourceCount = (pdt) => {
    //组合PDT 才需要调整
    console.log('pdt.原始需求数量', pdt.原始需求数量, pdt)
    if(pdt.原始需求数量.indexOf(',')>=0)
    {
        let nTotle = parseFloat(pdt.采购数量) + parseFloat(pdt.采购备品数量)
        let arCount:string[] = pdt.原始需求数量.split(',')
        let arSourceTotle = 0
        //计算总需求
        for(const item of arCount)
        {
            arSourceTotle += parseFloat(item)
        }
        if (nTotle < arSourceTotle) {
            //需要按照从前往后的顺序来分nTotle，每份不超过原始需求数量中对应每一份的数量
            let arOut:string[] = []
            for(let one of arCount)
            {
                if(nTotle>0)
                {
                    if(nTotle>=parseFloat(one))
                    {
                        arOut.push(one)
                        nTotle -= parseFloat(one)
                    }
                    else
                    {
                        arOut.push(nTotle)
                        nTotle = 0
                    }
                }
            }
            pdt.需求数量 = arOut.join(',')
            console.log('调整后:',pdt.需求数量)
        }
        else
        {
            //还原默认
            pdt.需求数量 = pdt.原始需求数量            
            console.log('不需要调整:',pdt.采购数量,pdt.采购备品数量,nTotle,arSourceTotle)
        }

        //先还原
        pdt.oem_order_num = pdt.原始oem_order_num
        pdt.oem_order_sub = pdt.原始oem_order_sub
        pdt.父子任务单号 = pdt.原始父子任务单号
        pdt.标识 = pdt.原始标识

        //pdt.oem_order_num,pdt.oem_order_sub 对应的字符传逗号分割的数量要和 pdt.需求数量  保持一直 后面多的删除
        let arSell:string[] = pdt.oem_order_num.split(',')
        let arOem: string[] = pdt.oem_order_sub.split(',')
        let arZRWDH: string[] = pdt.父子任务单号.split(',')
        let arNeed: string[] = pdt.需求数量.split(',')
        let arBS:string[] = pdt.标识.split(',')
        //arSell arOem 数组slice前arNeed长度 的内容
        let arSellOut:string[] = arSell.slice(0,arNeed.length)
        let arOemOut: string[] = arOem.slice(0, arNeed.length)
        let arZRWDHOut: string[] = arZRWDH.slice(0, arNeed.length)
        let arBSOut:string[] = arBS.slice(0,arNeed.length)

        console.log('arSellOut',arNeed.length,arSell.slice(0,arNeed.length))

        pdt.oem_order_num = arSellOut.join(',')
        pdt.oem_order_sub = arOemOut.join(',')
        pdt.父子任务单号 = arZRWDHOut.join(',')
        pdt.标识 = arBSOut.join(',')
    }
    else  //针对非组单也需要调整数量
    {
        let nTotle = parseFloat(pdt.采购数量) + parseFloat(pdt.采购备品数量)
        if(nTotle < parseFloat(pdt.原始需求数量))
        {
            pdt.需求数量 = nTotle 
        }
        else
        {
            pdt.需求数量 = pdt.原始需求数量
        }
    }
}



const reComputePdtInfo = (pdt,mode) => {
    pdt.采购数量 = pdt.采购数量 == ''?0:pdt.采购数量
    pdt.采购备品数量 = pdt.采购备品数量 == ''?0:pdt.采购备品数量
    pdt.buy_price_bef_tax = (pdt.buy_price_bef_tax == ''||pdt.buy_price_bef_tax == null)?0:pdt.buy_price_bef_tax
    pdt.buy_price_aft_tax = (pdt.buy_price_aft_tax == '' ||pdt.buy_price_aft_tax == null)?0:pdt.buy_price_aft_tax
    pdt.折扣 = pdt.折扣 == ''?1:pdt.折扣

    if (isNaN(Number(pdt.发票税率))) {
        pdt.发票税率 = 0
    }

    if(mode == '采购数量')
    {
        //调整默认备品数量
       // pdt.采购备品数量 = parseFloat(pdt.采购数量*0.01)

        //根据采购数量计算总价 总价等于税后单价乘以采购数量
        //重新计算税后价格
        // pdt.buy_price_aft_tax = parseFloat(pdt.buy_price_bef_tax)+ parseFloat(pdt.buy_price_bef_tax)*pdt.发票税率/100
        pdt.总价 = ceilToFixed(parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣)),6,pdt.总价)+countBakPrice(pdt)

        //调整来源中的数量
        if(purchaseData.source === '销售转采购物料')
            reComputeSourceCountSell(pdt)
        else if(purchaseData.source .indexOf('委外转采购')>=0)
            reComputeSourceCount(pdt)
    }
    else if(mode  == '采购备品数量')
    {
        pdt.总价 = ceilToFixed(parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣)),6,pdt.总价)+countBakPrice(pdt)
        //调整来源中的数量
        if(purchaseData.source === '销售转采购物料')
            reComputeSourceCountSell(pdt)
        else if(purchaseData.source .indexOf('委外转采购')>=0)
            reComputeSourceCount(pdt)
    }
    else if(mode == '税前单价')
    {
        //根据税前单价和税率计算出税后单价         
        pdt.buy_price_aft_tax =  ceilToFixed(parseFloat(pdt.buy_price_bef_tax)+ parseFloat(pdt.buy_price_bef_tax)*pdt.发票税率/100,8,pdt.buy_price_aft_tax)

        //计算备品价格

        //计算出总价
        
        pdt.总价 = ceilToFixed(parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣)),6,pdt.总价)+countBakPrice(pdt)
    }
    else if(mode == '税后单价')
    {
        //根据税后单价和税率计算出税前单价         
        pdt.buy_price_bef_tax =  ceilToFixed(parseFloat((pdt.buy_price_aft_tax/(1+pdt.发票税率/100))),8,pdt.buy_price_bef_tax)
        console.log('--',parseFloat(pdt.buy_price_aft_tax),(1+pdt.发票税率/100))
        //计算出总价
        pdt.总价 = ceilToFixed(parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣)),6,pdt.总价)+countBakPrice(pdt)
    }
    else if(mode == '折扣')
    {
        //根据折扣重新计算总价
        pdt.总价 = ceilToFixed(parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣)),6,pdt.总价)+countBakPrice(pdt)
    }
    else if(mode == '发票税率')
    {
        //根据发票类型调整税率数
        if(!['普票','专票','电子票'].includes(pdt.发票类型))
        {
            pdt.发票税率 = 0
            //重新调整税后价格
            pdt.buy_price_aft_tax = pdt.buy_price_bef_tax
        }
        //重新计算税后价格
        // pdt.buy_price_aft_tax = parseFloat(pdt.buy_price_bef_tax)+ parseFloat(pdt.buy_price_bef_tax)*pdt.发票税率/100
        pdt.buy_price_bef_tax = ceilToFixed(parseFloat((pdt.buy_price_aft_tax/(1+pdt.发票税率/100))),8,pdt.buy_price_bef_tax)
        //重新计算总价
        pdt.总价 = ceilToFixed(parseFloat((pdt.buy_price_aft_tax*pdt.采购数量*pdt.折扣)),6,pdt.总价)+countBakPrice(pdt)
    }
    else if(mode == '总价')
    {
        //根据总价重新调整税前税后价格
        pdt.buy_price_aft_tax = ceilToFixed(parseFloat((pdt.总价/(parseFloat(pdt.采购数量)))),8,pdt.buy_price_aft_tax)
        pdt.buy_price_bef_tax = ceilToFixed(parseFloat((pdt.总价/((1+pdt.发票税率/100)*(parseFloat(pdt.采购数量))))),8,pdt.buy_price_bef_tax)
    }

    pdt.采购数量 = parseFloat(pdt.采购数量, 10)
    pdt.采购备品数量 = parseFloat(pdt.采购备品数量, 10)
    pdt.buy_price_bef_tax = parseFloat(pdt.buy_price_bef_tax, 10)
    pdt.buy_price_aft_tax = parseFloat(pdt.buy_price_aft_tax, 10)
    pdt.折扣 = parseFloat(pdt.折扣, 10)
}



onMounted(async () => {    
    getDepartmentTreeInfo()
 
    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('purchase.add')
        

        //新建需要默认可以提审
        // const tmp = [...purchaseData.fsm_can_trig_data.操作触发,'提交审核']
        const tmp = [...purchaseData.fsm_can_trig_data.操作触发]
        purchaseData.fsm_can_trig_data.操作触发 = tmp

        //设置默认人员部门
        const info = wsCache.get(appStore.getUserInfo)
        purchaseData.buy_man_id = info.id
        purchaseData.buy_man_name = info.resident_name
        purchaseData.follow_man_id = info.id
        purchaseData.follow_man_name = info.resident_name
        purchaseData.buy_dept_id = info.depts[0]
        //默认销售日期为今天
        purchaseData.business_date = getTodayDate()
        

        //是否有销售转采购任务
        if(currentRoute.value.query.tobuy == '1')
        {
            purchaseData.source = currentRoute.value.query.source
            console.log('----',purchaseData.source,)
            const arPdtNum = wsCache.get('tobuy')
            console.log('===',arPdtNum)



            //添加pdt到列表
            for(const pdt of arPdtNum)
            {
                const ret = await getProductInfoApi({
                    name:pdt.pdt_name,
                    page: 1,
                    count:20,
                })
                if(ret)
                {

                    //如果有多个找到匹配的
                    let find =ret.data

                    handleCurrentSelectPdt(find,'',true)
                    if(purchaseData.source == '销售转采购')
                    {
                        if(pdt.销售备品数量 == undefined)
                            pdt.销售备品数量 = 0
                        find.sell_order_num = pdt.sell_order_num
                        find.需求数量 = pdt.当前需求
                        find.采购数量 = pdt.当前需求-pdt.销售备品数量
                        find.采购备品数量 = pdt.销售备品数量


                        //没有订单号表示不关联
                        if(pdt.sell_order_num == '')
                        {
                            find.标识 = getGUID(5)  
                            find.sell_order_sub = getGUID(5)
                            purchaseData.source = ''
                        }
                        else
                        {
                            find.标识 = pdt.标识   
                            find.sell_order_sub = pdt.sell_order_sub
                            find.父子任务单号 = pdt.子任务单号
                        }
                        //用户需求来源订单号为采购单号
                        if(arPdtNum[0].sell_order_num.indexOf(',') > 0)
                        {
                            arPdtNum[0].buy_order_num =  arPdtNum[0].sell_order_num.split(',')[0]
                        }
                        else
                            purchaseData.buy_order_num = arPdtNum[0].sell_order_num
                    }
                    if(purchaseData.source .indexOf('委外转采购')>=0)
                    {
                        if(pdt.委外备品数量 == undefined)
                            pdt.委外备品数量 = 0
                        find.oem_order_num = pdt.oem_order_num
                        find.需求数量 = pdt.需求数量
                        find.采购数量 = pdt.当前需求-pdt.委外备品数量
                        find.采购备品数量 = pdt.委外备品数量

                        find.原始需求数量 = pdt.需求数量
                        find.原始oem_order_num = pdt.oem_order_num
                        find.原始oem_order_sub = pdt.oem_order_sub
                        find.原始父子任务单号 = pdt.子任务单号
                        find.原始标识 = pdt.标识

                        find.物料总用量 = pdt.物料总用量

                        //没有订单号表示不关联
                        if(pdt.oem_order_num == '')
                        {
                            find.标识 = getGUID(5)  
                            find.oem_order_sub = getGUID(5)
                            purchaseData.source = ''
                        }
                        else
                        {
                            find.标识 = pdt.标识   
                            find.oem_order_sub = pdt.oem_order_sub
                            find.父子任务单号 = pdt.子任务单号
                        }
                        //用户需求来源订单号为采购单号
                        if(arPdtNum[0].oem_order_num.indexOf(',') > 0)
                            purchaseData.buy_order_num = arPdtNum[0].oem_order_num.split(',')[0]
                        else
                            purchaseData.buy_order_num = arPdtNum[0].oem_order_num
                    }                            
                    if(purchaseData.source == '销售转采购物料')
                    {
                        if(pdt.销售备品数量 == undefined)
                            pdt.销售备品数量 = 0
                        find.sell_order_num = pdt.sell_order_num
                        find.需求数量 = pdt.需求数量
                        find.采购数量 = pdt.当前需求-pdt.销售备品数量
                        find.采购备品数量 = pdt.销售备品数量
                        find.订单来源 = pdt.订单来源


                        find.原始需求数量 = pdt.需求数量
                        find.原始sell_order_num = pdt.sell_order_num
                        find.原始sell_order_sub = pdt.sell_order_sub
                        find.原始父子任务单号 = pdt.子任务单号
                        find.原始标识 = pdt.标识

                        find.物料总用量 = pdt.物料总用量  //传递销售单物料用量， 用于服务器计算 需求总量 （备料时打印需求）

                        //没有订单号表示不关联
                        if(pdt.sell_order_num == '')
                        {
                            find.标识 = getGUID(5)  
                            find.sell_order_sub = getGUID(5)
                            purchaseData.source = ''
                        }
                        else
                        {
                            find.标识 = pdt.标识   
                            find.sell_order_sub = pdt.sell_order_sub
                            find.父子任务单号 = pdt.子任务单号
                        }
                        //用户需求来源订单号为采购单号
                        if(arPdtNum[0].sell_order_num.indexOf(',') > 0)
                        {
                            arPdtNum[0].buy_order_num =  arPdtNum[0].sell_order_num.split(',')[0]
                        }
                        else
                            purchaseData.buy_order_num = arPdtNum[0].sell_order_num
                    }


                                
                    reComputePdtInfo(find,'采购备品数量')
                    console.log('=========11',find.标识)
                
                }
            }
        }

        if(purchaseData.buy_order_num == '')
        {
            await onChangeID()
        }

        console.log(purchaseData.pdt_list)

        //追加默认行
        purchaseData.pdt_list.push({总价:'0'}) 
    }
    else
    {        
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('purchase.look')
        }
        else
        {
            title.value = t('purchase.modify')
        }
        
        //查询产品信息 
        const ret = await getPurchaseInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(purchaseData, ret.data)


            // //更新供应商信息
            const ret2 = await getSupplierListApi({
                ids:[purchaseData.supplier_id],
                page:1,
                count:100
            });
            if (ret2) {
                //更新供应商发票和税率
                if(ret2.data.length>0)
                {
                    purchaseData.tax_type = ret2.data[0].tax_type=='纸质专票'?'专票':ret2.data[0].tax_type
                    purchaseData.tax_rate = ret2.data[0].tax_rate=='不含税'?0:ret2.data[0].tax_rate.replace('%','')
                }

            }


            purchaseData.pdt_list = ret.data.pdt_list;
            purchaseData.pdt_list.push({总价:'0'}) 
        }

        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
        
    }
})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})

//产品查找的输入
const txtSearch = ref('')
//点击了查找产品input
const onClickSearchPdt = ()=>{
    console.log('点了')
    getProductList(txtSearch.value)
    showProductSel.value = true
    // 添加全局点击事件监听器
    window.addEventListener('click', handleGlobalClick);
}
const handleGlobalClick = (event)=>{

    const elTableContainers = document.querySelectorAll('.el-table-container');      
    let clickedInsideTable = false;
    for (const container of elTableContainers) {
        if (container.contains(event.target)) {
            // 点击的元素在 el-table 容器内部
            clickedInsideTable = true;
            break;
        }
    }
        
    if (!clickedInsideTable) {
        // 判断点击位置是否在输入框和浮动窗口外部
        // const isOutsideClick = !inputElement.contains(event.target) && !floatWindowElement.contains(event.target);
        const bIn = (event.target as HTMLElement).classList.contains('el-input__inner')
        // 根据判断结果来决定是否隐藏浮动窗口
        if(!bIn)
        {
            showProductSel.value = false
        }
    }
}
//处理输入
const onInputSearch =(val)=>{
    showProductSel.value = true
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)
    
    delaytime.value = setTimeout(() => {
      // 输入停止后触发的方法
      getProductList(val)
    }, 200) 
}


//显示隐藏选择供应商弹窗
const showSelSupplierDlg = ref(false)
const onSelSupplier = ()=>{
    showSelSupplierDlg.value = true
}
//选择供应商回调
const onSelSupplierCallback = (id,name,nick,tax_type,tax_rate:string)=>{
    console.log(id,name,nick,tax_type,tax_rate)
    purchaseData.supplier_id = id
    purchaseData.supplier_name = name
    purchaseData.supplier_nick = nick
    purchaseData.tax_type = tax_type=='纸质专票'?'专票':tax_type
    purchaseData.tax_rate = tax_rate=='不含税'?0:(tax_rate.replace('%',''))
    console.log(purchaseData)

    //同步更新所有产品税率
    for(let item of purchaseData.pdt_list)
    {
        item.发票类型 = purchaseData.tax_type
        item.发票税率 =  parseFloat(purchaseData.tax_rate)
        reComputePdtInfo(item,'发票税率')
    }
}


//显示隐藏选择采购员窗口变量
const showSelPurchaseUserDlg = ref(false)
//显示选择采购员弹窗
const onSelPurchase = ()=>{
    showSelPurchaseUserDlg.value = true
}
//选择采购员回调
const onSelPurchaseCallback = (id,name)=>{
    console.log(id,name)
    purchaseData.buy_man_id = id
    purchaseData.buy_man_name = name
}

//显示隐藏选择跟单员窗口变量
const showSelFollowerUserDlg = ref(false)
//显示选择跟单员弹窗
const onSelFollower = ()=>{
    showSelFollowerUserDlg.value = true
}
//选择跟单员回调
const onSelFollowerCallback = (id,name)=>{
    console.log(id,name)
    purchaseData.follow_man_id = id
    purchaseData.follow_man_name = name
}

//部门数据源
const deptData = reactive([])
//查询组织信息
const getDepartmentTreeInfo = async () => {
  const ret = await getDepartmentListApi({})
  console.log(ret)
  if (ret) {

    //deptData.splice(0, deptData.length, ...data)
    deptData.splice(0, deptData.length, ...ret.data.all_depts)
    console.log(deptData.length)
  }
}

//勾选使用统一交付时间事件
const onChangeDeliveryTime = (val)=>{
    if(val) //勾选
    {
        if(purchaseData.delivery_date == "")
        {
            purchaseData.delivery_date = getTodayDate()
        }
    }
}

//显示隐藏选择产品浮窗
const showProductSel = ref(false)
//选择了某一个产品
const handleCurrentSelectPdt = (item,old,fource = false) =>{
    if( item == undefined)
    {
        return
    }

    nCurSelPdtID.value = item.id
    if(!fource){
        return
    }
    txtSearch.value = ''
    
    setTimeout(() => {
        const inputElement = document.querySelector('.input-search input');
        inputElement?.focus()
        onClickSearchPdt()
    },500)
       
    
  
  
    //增加属性
   // item.当前库存 = ''
    item.采购数量 = 1
    item.折扣 = 1
    item.发票类型 = '专票'
    item.发票税率 = 0
    item.总价 = 0
    item.交货日期 = purchaseData.delivery_date == ''? getTodayDate():purchaseData.delivery_date
    item.类型 = '正常'
    item.供应商产品编码 = ''
    item.已收货 = 0
    item.未收货 = 0
    item.已退货 = 0
    item.已质检 = 0
    item.已入库 = 0
    item.标识 = getGUID(10)
    item.sell_order_num = ''
    item.采购备品数量 = 0
    item.原始需求数量 = ''
    item.原始父子任务单号 = ''
    item.原始oem_order_num = ''
    item.原始oem_order_sub = ''
    item.原始标识 = ''
    item.原始sell_order_num = ''
    item.原始sell_order_sub = ''

    //如果已经选择了供应商则自动同步税率
    if(purchaseData.tax_rate != undefined)
    {
        item.发票类型 = purchaseData.tax_type
        item.发票税率 = purchaseData.tax_rate =='不含税'?0:purchaseData.tax_rate
    }


    //更新产品价格
    reComputePdtInfo(item,'采购数量')

    //构造一行产品
    purchaseData.pdt_list.splice(-1,0,item)
    //隐藏浮窗
    showProductSel.value = false

    

    console.log(purchaseData)

    
}

//删除某一个产品
const onDelPdt = (index) => {
    if(purchaseData.pdt_list[index].已收货>0)
    {
        ElMessage.error('已收货的产品不能删除!')
        return
    }
    purchaseData.pdt_list.splice(index,1)
}


//保存
const onSave = async()=>{
    console.log(purchaseData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //删除最后一行临时数据(克隆新数据提交不影响原数据)
    const data = cloneDeep(purchaseData)
    data.pdt_list.splice(-1,1)

    if(!checkPdt())
    {
        console.log('???')
        return 
    }

    if(purchaseData.id == undefined)
    {
        const ret = await addPurchaseApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.newPurchaseSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        // purchaseData.fsm_exe_trig = '提交修改'
        const ret =await updatePurchaseApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.updatePurchaseSuccess'))
            baskFront()
        }
    }


}

//校验pdt
const checkPdt = ()=>{
    if(purchaseData.pdt_list.length <= 1)
    {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}

//提交审核意见
const handleCheck = async(btn)=>{
    console.log(11)
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    const data = cloneDeep(purchaseData)
    if(!checkPdt())
    {
        return
    }
    data.pdt_list.splice(-1,1)

    //处理下空数据问题
    for(let pdt of data.pdt_list)
    {
        pdt.采购数量 = pdt.采购数量 == ''?0:pdt.采购数量
        pdt.采购备品数量 = pdt.采购备品数量 == ''?0:pdt.采购备品数量
        pdt.buy_price_bef_tax = pdt.buy_price_bef_tax == ''?0:pdt.buy_price_bef_tax
        pdt.buy_price_aft_tax = pdt.buy_price_aft_tax == ''?0:pdt.buy_price_aft_tax
        pdt.折扣 = pdt.折扣 == ''?1:pdt.折扣
    }


    const info = wsCache.get(appStore.getUserInfo)
    data.fsm_exe_man_name = info.resident_name
    data.fsm_exe_trig = btn
    
    if(data.source == '销售转采购')
    {
        closeOneTagByPath('/salemanage/saledemand')
    }
    else if(data.source.indexOf('委外转采购')>=0)
    {
        closeOneTagByPath('/oemmanage/wlneedinfo')
        closeOneTagByPath('/oemmanage/wlneedlist')
    }
    if(data.source == '销售转采购物料')
    {
        closeOneTagByPath('/salemanage/sellwlprepare')
    }
    

    if(data.id == undefined)
    {
        const ret = await addPurchaseApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.newPurchaseSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        const ret =await updatePurchaseApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.updatePurchaseSuccess'))

            if(btn == '恢复')
            {
                //查询产品信息 
                const ret = await getPurchaseInfoApi({
                    id:currentRoute.value.query.id,
                    page:1,
                    count:100
                })
                if(ret)
                {
                    console.log(ret)
                    Object.assign(data, ret.data)


                    // //更新供应商信息
                    const ret2 = await getSupplierListApi({
                        ids:[data.supplier_id],
                        page:1,
                        count:100
                    });
                    if (ret2) {
                        //更新供应商发票和税率
                        data.tax_type = ret2.data[0].tax_type
                        data.tax_rate = ret2.data[0].tax_rate.replace('%','')
                    }


                    data.pdt_list = ret.data.pdt_list;
                    data.pdt_list.push({总价:'0'}) 
                }
            }
            else    
            {
                baskFront()
            }
            
        }
    }


}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = ()=>{
    showCheckHisDlg.value = true
}


//返回上一页
const baskFront = ()=>{
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}


// 使用 computed 定义计算属性
const sameDay = computed({
  get: () => (purchaseData.delivery_date === '' ? false : true),
  set: (value) => {
    if (value) {
        purchaseData.delivery_date = getTodayDate();
      //更新下面列表
      purchaseData.pdt_list.forEach((item, index) => {
          item.交货日期 = purchaseData.delivery_date;
      })
    } else {
        purchaseData.delivery_date = '';
    }
    console.log('->',purchaseData.delivery_date);
  },
});

const onChangeDeliverData = (value)=>{
    purchaseData.pdt_list.forEach((item, index) => {
          item.交货日期 = purchaseData.delivery_date;
      })
}

//键盘上下只切换不选择
const nCurSelPdtID = ref('')
const bKeyDown = ref(true)
const onKeyDownOnePdt = (event)=>{
    if (event.keyCode === 38 || event.keyCode === 40) {
        // 阻止默认行为，以保持光标位置不变
        event.preventDefault();
    }
    //esc按键关闭table
    if(event.keyCode === 27)
    {
        showProductSel.value = false  
        return
    }
    bKeyDown.value = true
    if(nCurSelPdtID.value == '')
    {
        setCurrent(searchPdtData[0])
    }
    else  
    {


        for(let i = 0;i<searchPdtData.length;i++)
        {
            if(searchPdtData[i].id == nCurSelPdtID.value)
            {
                if(event.keyCode === 38 && i>0)
                    setCurrent(searchPdtData[i-1])
                else if(event.keyCode === 40 && i<searchPdtData.length-1)
                    setCurrent(searchPdtData[i+1])
                //如果是回车，直接选择
                else if(event.keyCode === 13)
                {
                    onRowClick(searchPdtData[i])
                    return
                }
                return
            }
        }
    }

}
const searchRef = ref<InstanceType<typeof ElTable>>()

const setCurrent = (row?) => {
    if(row == undefined)
        nCurSelPdtID.value = ''
    console.log('setCurrent',searchRef)
    searchRef.value!.setCurrentRow(row)
}
const onRowClick = (row)=>{
    console.log('xuanle',row)
    handleCurrentSelectPdt(row,null,true)
}


</script>

<template>
    <ContentDetailWrap :title="title+'--'+purchaseData.fsm_cur_state" @back="baskFront()">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史
            </ElButton>
        </template>
        <template #right>
            <ElButton  v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && purchaseData.fsm_can_trig_data.操作触发.includes('保存')"  color="#409EFF" style="color: #fff;" @click="handleCheck('保存')">
                <Icon class="mr-0.5" icon="carbon:save" />
                保存
            </ElButton>
            <ElButton  v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && purchaseData.fsm_can_trig_data.操作触发.includes('提交')"  color="#409EFF" style="color: #fff;" @click="handleCheck('提交')">
                <Icon class="mr-0.5" icon="carbon:save" />
                提交
            </ElButton>
            <el-popconfirm  title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && purchaseData.fsm_can_trig_data.操作触发.includes('提交审核')"   type="success" >
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        提交审核
                    </ElButton>
                </template>
            </el-popconfirm>
            
            <el-popconfirm  title="是否确认关闭订单?" @confirm="handleCheck('关闭')" v-if="checkPermissionApi('采购显示关闭订单')">
                <template #reference>
                    <ElButton  v-show="currentRoute.query.cmd != '审核' && purchaseData.fsm_can_trig_data.操作触发.includes('关闭')" type="danger">
                        <Icon class="mr-0.5" icon="carbon:close-outline" />
                        关闭订单
                    </ElButton>
                </template>
            </el-popconfirm>       
            <el-popconfirm  title="是否恢复已关闭订单?" @confirm="handleCheck('恢复')" v-if="checkPermissionApi('采购显示关闭订单')">
                <template #reference>
                    <ElButton  v-show="currentRoute.query.cmd != '审核' && purchaseData.fsm_can_trig_data.操作触发.includes('恢复')" type="danger">
                        <Icon class="mr-0.5" icon="carbon:close-outline" />
                        恢复订单
                    </ElButton>
                </template>
            </el-popconfirm>    

        </template>

        <el-form :rules="rules" :model="purchaseData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="4" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="t('purchase.supplier')"
                    class="flex">
                    <el-form-item prop="supplier_nick">
                        <div class="rounded mr-2 border pl-2 pr-2 w-150px" style="color: #606266;">
                            {{ checkPermissionApi('供应商名称显示')?( purchaseData.supplier_nick?purchaseData.supplier_nick:'请选择'):'***' }}
                        </div> 
                        <ElButton v-if="currentRoute.query.type != 'info'" @click="onSelSupplier">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('purchase.id')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input class="mr-1" v-model="purchaseData.buy_order_num" :disabled="purchaseData.id!=undefined" />
                            <ElButton v-if="purchaseData.id==undefined" type="primary"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="t('purchase.supplier_purchase_id')" :span="2"
                    class="flex">
                    <el-form-item prop="name" >
                        <el-input  v-model="purchaseData.supplier_order_num"  />
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('sale.mondyType')" 
                    class="flex">
                    <el-select v-model="purchaseData.money_type" placeholder="Select">
                        <el-option v-for="item in moneyTypeData" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('supplier.person')"
                    class="flex">
                    <el-form-item>
                        <div class="rounded mr-2 border pl-2 pr-2 w-150px" style="color: #606266;">
                            {{ purchaseData.buy_man_name?purchaseData.buy_man_name:'请选择' }}
                        </div> 
                        <ElButton v-if="currentRoute.query.type != 'info'" @click="onSelPurchase">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('sale.follower')" :span="2"
                    class="flex">
                    <el-form-item>
                        <div class="rounded mr-2 border pl-2 pr-2 w-150px" style="color: #606266;">
                            {{ purchaseData.follow_man_name?purchaseData.follow_man_name:'请选择' }}
                        </div> 
                        <ElButton v-if="currentRoute.query.type != 'info'" @click="onSelFollower">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('purchase.dept')" 
                    class="flex">
                    <el-form-item>
                        <el-tree-select v-model="purchaseData.buy_dept_id" :data="deptData"  check-strictly :render-after-expand="false" :props="treeProps">
                            <template #default="scope">
                            <Icon icon="bx:category"  />
                            {{ scope.data.name }}
                            </template>
                        </el-tree-select>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('purchase.business_date')"
                    class="flex">
                    <el-form-item >
                        <el-checkbox style="margin-right: 10px;" v-model="sameDay" label="统一" size="large" />
                        <el-date-picker v-if="sameDay" v-model="purchaseData.delivery_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" @update:model-value="onChangeDeliverData" :clearable="false"/>
                    </el-form-item>
                </el-descriptions-item>
                <!-- <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('sale.same_date')"
                    class="flex">
                    <el-form-item >
                        <el-checkbox v-model="is_same_delivery_BOOL"  size="large"  class="!mr-3" @change="onChangeDeliveryTime"/>
                        <el-date-picker v-if="is_same_delivery_BOOL" v-model="purchaseData.delivery_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item> -->
            </el-descriptions>
        
        </el-form>

        <el-table class="mt-4 mb-4" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="purchaseData.pdt_list" style="width: 100%" border stripe>
            <el-table-column  :label="t('process.opt')" width="60" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined"  type="primary">
                        <el-popconfirm title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon v-if="currentRoute.query.type != 'info'" icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>
                        
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        {{ scope.$index+1 }}
                    </div>
                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick}}</div>
                    <el-input class="input-search" @keydown='onKeyDownOnePdt' @keyup='bKeyDown = false' placeholder="请输入" v-model="txtSearch" @input="onInputSearch" @click="onClickSearchPdt" v-if="(scope.row.nick=='' || scope.row.nick == undefined)&&currentRoute.query.type != 'info'">{{ scope.row.id }}</el-input>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.id != undefined && scope.row.specs != ''"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name=='自定义规格'?scope.row.specs_text:scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.inventory')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="!text-left">
                        {{ scope.row.可用+'('+scope.row.base_unit+')' }} 
                    </div>
                    <div v-if="purchaseData.pdt_list.length>1 && scope.row.id == undefined" class=" font-bold">
                        数量合计:
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('purchase.count')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="items-center">
                        <el-input v-model="scope.row.采购数量" class="!text-center" @blur="reComputePdtInfo(scope.row,'采购数量')" type="number"/>
                        <div style="font-size: smaller;">{{ scope.row.base_unit }}</div>
                    </div>  
                    <div v-if="purchaseData.pdt_list.length>1 && scope.row.id == undefined" >
                        {{ totlePurchase }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.bak_count')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="items-center">
                        <el-input v-model="scope.row.采购备品数量" class="!text-center" @blur="reComputePdtInfo(scope.row,'采购备品数量')" type="number"/>
                        <div style="font-size: smaller;">{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.bef_tax')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.buy_price_bef_tax" class="!text-center" @blur="reComputePdtInfo(scope.row,'税前单价')" v-show="checkPermissionApi('采购订单价格显示')" type="number"/>
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.after_tax')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.buy_price_aft_tax" class="!text-center" @blur="reComputePdtInfo(scope.row,'税后单价')" v-show="checkPermissionApi('采购订单价格显示')" type="number"/>
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('purchase.cut')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.折扣" class="!text-center" @blur="reComputePdtInfo(scope.row,'折扣')" type="number"/>
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.tax_rate')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class1="flex">
                        <el-select v-if="scope.row.id != undefined"  v-model="scope.row.发票类型" placeholder="Select" size="small" @change="reComputePdtInfo(scope.row,'发票税率')">
                            <el-option  v-for="item in ['普票','专票','收据','不开票','电子票']" :key="item" :label="item" :value="item" />
                        </el-select>
                        <div class="flex mt-1" v-if="['普票','专票','电子票'].includes(scope.row.发票类型) ">
                            <el-select v-if="scope.row.id != undefined"  v-model="scope.row.发票税率" placeholder="Select" size="small" @change="reComputePdtInfo(scope.row,'发票税率')">
                                <el-option  v-for="item in [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]" :key="item" :label="item" :value="item" />
                            </el-select>
                            %
                        </div>      
                    </div>   
                    <div v-if="purchaseData.pdt_list.length>1 && scope.row.id == undefined" class=" font-bold">
                        总价合计:
                    </div>                 
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.totle_price')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.总价" class="!text-center" @blur="reComputePdtInfo(scope.row,'总价')" v-show="checkPermissionApi('采购订单价格显示')" type="number"/>
                    </div>      
                    <div v-if="purchaseData.pdt_list.length>1 && scope.row.id == undefined">
                        {{ checkPermissionApi('采购订单价格显示')?totlePurchasePrice:'*' }}
                    </div>              
                </template>
            </el-table-column>
            <!-- <el-table-column  :label="t('sale.type')"  >
                <template #default="scope">
                    <el-select v-if="scope.row.id != undefined"  v-model="scope.row.类型" placeholder="Select" >
                        <el-option  v-for="item in ['正常','备品','赠品','样品']" :key="item" :label="item" :value="item" />
                    </el-select>
                </template>
            </el-table-column> -->
            <el-table-column  prop="delivery" :label="t('sale.delivery')"  min-width="150">
                <template #default="scope">
                    <div class="relative w-[100%]">                        
                        <el-date-picker :disabled="purchaseData.delivery_date != ''" class=" absolute left-0 top-0 !w-[100%]"  v-if="scope.row.id != undefined" v-model="scope.row.交货日期" type="date" placeholder="" format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD" :clearable="false"/>
                    </div>
                </template>
            </el-table-column>
            <el-table-column  :label="t('purchase.remark')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.采购备注" class="!text-center"/>
                    </div>                    
                </template>
            </el-table-column>
            <!-- <el-table-column  label="标识"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.标识" class="!text-center"/>
                    </div>                    
                </template>
            </el-table-column> -->


            <!-- "186viy5nak" -->



            <el-table-column  :label="t('purchase.supplier_pdt_id')"  width="140">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.供应商产品编码" class="!text-center"/>
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('purchase.source')"  width="140">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined &&purchaseData.source !='' " style="text-align: left; font-size: smaller;">
 
                        <div v-if="purchaseData.source == '销售转采购'">{{ '销售单:'+scope.row.sell_order_num }}</div>      
                        

                        <div v-if="scope.row.sell_order_num != undefined && scope.row.sell_order_num.indexOf(',')<0">
                            <div v-if="purchaseData.source == '销售转采购物料'">{{ '销售单:'+scope.row.sell_order_num }}</div>
                            <div v-if="purchaseData.source == '销售转采购物料'">{{ 'SUB号:'+scope.row.sell_order_sub }}</div>
                            <div v-if="purchaseData.source == '销售转采购物料'">{{ '用量:'+scope.row.需求数量 }}</div>
                        </div>
                        <div v-if="scope.row.sell_order_num != undefined && scope.row.sell_order_num.indexOf(',')>0">
                            <div v-if="purchaseData.source == '销售转采购物料' && scope.row.sell_order_num != undefined">
                                {{ '销售单:' }}
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.sell_order_num"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small" >组单</el-tag>                                
                                </el-tooltip>
                            </div>
                            <div v-if="purchaseData.source == '销售转采购物料' && scope.row.sell_order_num != undefined">
                                {{ 'SUB号:' }}
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.sell_order_sub"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small">组单</el-tag>                                
                                </el-tooltip>
                            </div>
                            <div  v-if="purchaseData.source == '销售转采购物料'&& purchaseData.source != undefined && scope.row.需求数量 != undefined">
                                 {{ '总数量:' }}
                                 <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.需求数量"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small">组单</el-tag>                                
                                </el-tooltip>
                            </div>
                        </div>
     

                        <div v-if="scope.row.oem_order_num != undefined && scope.row.oem_order_num.indexOf(',')<0">
                            <div v-if="purchaseData.source .indexOf('委外转采购')>=0">{{ '委外单:'+scope.row.oem_order_num }}</div>
                            <div v-if="purchaseData.source .indexOf('委外转采购')>=0">{{ '子任务单:'+scope.row.父子任务单号 }}</div>
                            <div v-if="purchaseData.source .indexOf('委外转采购')>=0">{{ '数量:'+scope.row.需求数量 }}</div>
                        </div>
                        <div v-if="purchaseData.source .indexOf('委外转采购')>=0 && scope.row.oem_order_num != undefined && scope.row.oem_order_num.indexOf(',')>0">
                            <div v-if="purchaseData.source .indexOf('委外转采购')>=0 && scope.row.oem_order_num != undefined">
                                {{ '委外单:' }}
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.oem_order_num"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small" >组单</el-tag>                                
                                </el-tooltip>
                            </div>
                            <div v-if="purchaseData.source .indexOf('委外转采购')>=0 && scope.row.oem_order_num != undefined">
                                {{ '子单号:' }}
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.父子任务单号"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small">组单</el-tag>                                
                                </el-tooltip>
                            </div>
                            <div  v-if="purchaseData.source .indexOf('委外转采购')>=0 && purchaseData.source != undefined && scope.row.需求数量 != undefined">
                                 {{ '总数量:' }}
                                 <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.需求数量"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small">组单</el-tag>                                
                                </el-tooltip>
                            </div>
                            <!-- <div  v-if="orderData.source != ''&& orderData.source != undefined && scope.row.需求数量 != undefined">
                                 {{ '标识:' }}
                                 <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.标识"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small">组单</el-tag>                                
                                </el-tooltip>
                            </div> -->
                        </div>



                    </div>                    
                </template>
            </el-table-column>
        </el-table>
        <div v-if="showProductSel"  class=" bg-light-600 w-[90%] max-h-[400px] absolute z-99 p-1 shadow el-table-container">
            <el-table ref="searchRef"  :data="searchPdtData" style="width: 100%;" row-key="guuid" border
                highlight-current-row @current-change="handleCurrentSelectPdt"
                header-cell-class-name="tableHeader" height="300" @row-click='onRowClick'>
                <el-table-column show-overflow-tooltip fixed prop="name" :label="t('product_manage.id')" width="150" />
                <el-table-column show-overflow-tooltip fixed prop="nick" :label="t('product_manage.name')"  width="600"/>
                <el-table-column show-overflow-tooltip fixed prop="brand" :label="t('product_manage.brand')"  />                
                <el-table-column show-overflow-tooltip fixed prop="specs_text" :label="t('product_manage.specify_info')"  />
                <el-table-column show-overflow-tooltip fixed prop="nick_brif" :label="t('product_manage.short_name')"  />
                <el-table-column show-overflow-tooltip fixed prop="note" :label="t('product_manage.help_name')"  />
            </el-table>
        </div> 
        <!-- 显示合计 -->
    <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[60%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="purchaseData.note" clearable :autosize="{ minRows: 3, maxRows: 3 }" type="textarea" />
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">付款方式:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-select v-model="purchaseData.pay_type" placeholder="Select" class="w-[100%]">
                                <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
                            </el-select>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="flex-grow text-center">
            <div class="flex">
                <div class="w-[100%]">
                    <div>
                        <td class="table_self_title min-w-[100px] p-2 ">合计:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ checkPermissionApi('采购订单价格显示')?totlePurchasePrice:'*' }}
                        </td>
                    </div>
                    <div>
                        <td class="table_self_title min-w-[100px] p-2">运费:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="purchaseData.express_fee" v-show="checkPermissionApi('采购订单价格显示')" type="number"/>
                        </td>
                    </div>
                    <div>
                        <td class="table_self_title min-w-[100px] p-2">其他费用:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="purchaseData.other_fee" v-show="checkPermissionApi('采购订单价格显示')" type="number"/>
                        </td>
                    </div>
                    <div>
                        <td class="table_self_title min-w-[100px] p-2 ">合计金额:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ checkPermissionApi('采购订单价格显示')?totlePurchasePriceAll:'*' }}
                        </td>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <el-card id="check" shadow="never" v-if="purchaseData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%] h-[100%] mt-4">
        审核原因：
        <el-input v-model="purchaseData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        <div class="mt-4 flex justify-end">
            <div class="flex items-center text-12px">
                <span>当前节点:</span>
                <span class="text-red-500 mr-3">{{ purchaseData.fsm_cur_state }}</span>
            </div>
            <!-- <ElButton @click="handleCheck(btn)" v-for="btn in purchaseData.fsm_can_trig_data.审核触发.toReversed()" :key="btn" :type="btn=='同意'?'success':'danger'">{{ btn }}</ElButton> -->
            <ElButton v-show="purchaseData.fsm_can_trig_data.审核触发.includes('同意')" type="success" @click="handleCheck('同意')" >同意</ElButton>
            <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                <template #reference>
                    <ElButton v-show="purchaseData.fsm_can_trig_data.审核触发.includes('驳回')" type="warning" >驳回</ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                <template #reference>
                    <ElButton v-show="purchaseData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                </template>
            </el-popconfirm>
        </div>
    </el-card>
        
        




        <!-- <div class="mb-60"></div> -->

        <!-- 选择供应商弹窗 -->
        <DialogSelSupplier v-model:show="showSelSupplierDlg" :title="t('purchase.sel_supplier')" @on-submit="onSelSupplierCallback"/>
        <!-- 选择销售 -->
        <DialogUser :param="''" v-model:show="showSelPurchaseUserDlg" :title="t('msg.selectUser')" @on-submit="onSelPurchaseCallback"/>
        <!-- 选择跟单员 -->
        <DialogUser :param="''" v-model:show="showSelFollowerUserDlg" :title="t('msg.selectUser')" @on-submit="onSelFollowerCallback"/>
        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="purchaseData.fsm_log_list" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
  white-space: nowrap;
  text-align: center;
  font-size: 13px;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
//   text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
    font-size: 14px;
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 14px;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}

</style>
<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,set } from 'vue'
import { getPurchaseNewnumApi, getStoreListApi } from '@/api/product'
import { getProductListApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { DialogProcessSel } from '@/components/DialogProcessSel';
import { DialogProcess } from '@/components/DialogProcess'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogProductSel } from '@/components/DialogProductSel'
import {checkFormRule, closeOneTagByName, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { addReceiptApi,updateReceiptApi,getReceiptInfoApi,getPurchaseInfoApi,getReceiptNewnumApi  } from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed,nextTick } from 'vue'
import { cloneDeep } from 'lodash-es'
import { DialogSelReceiptPdt } from '@/components/DialogSelReceiptPdt'


const { currentRoute,back,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    supplier_nick: [{ required: true, message: t('msg.noPurchase'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//采购单数据
const purchaseData = reactive(
{
    "supplier_id": "",
    supplier_name:'',
    supplier_nick:'',
    "buy_order_num": "",
    "supplier_order_num": "",
    "money_type": "人民币",
    "delivery_date": "",
    "buy_man_id": "",
    buy_man_name:'',
    "follow_man_id": "",
    follower_name:'',
    "buy_dept_id": "",
    "create_date": "",
    "modify_date": "",
    "pdt_list": [],
    "note": "",
    "express_fee": 0.00,
    "other_fee": 0.00,
    "pay_type": "月结",
    business_date:'', //业务日期
    is_same_delivery:0,
    合计费用:0,

}
)

//收货单数据
const receiptData = reactive(
{
    "supplier_id": "",
    supplier_name:'',
    supplier_nick:'',
    "buy_order_num": "",
    "buy_drawin_num": "",
    "drawin_date": "",
    "buy_man_id": "",
    "business_date": "",
    "pdt_list": [],
    "note": "",
    "express_fee": 0.00,
    "other_fee": 0.00,
    drawin_man_id:'',
    drawin_man_name:'',
    fsm_can_trig_data:{
        审批触发:[],
        操作触发:['保存']
    }, //审批决策
    fsm_cur_state:'订单创建',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[],
    store_data: {'良品库': 0, '不良品库':0}
}
)




//获取最新ID
const onChangeID = async()=>{
    const ret = await getReceiptNewnumApi()
    if(ret)
    {
        console.log(ret)
        receiptData.buy_drawin_num = ret.data.new_id
    }
}


//缓存采购单产品列表
const pdtList = ref([])

onMounted(async () => {    
    await getStoreList()
    //查询获取关联采购单信息
    const ret = await getPurchaseInfoApi({
        buy_order_num:currentRoute.value.query.purchase_id
    })
    if(!ret)
    {
        return
    }


    Object.assign(purchaseData, ret.data)
    Object.assign(pdtList.value, purchaseData.pdt_list)

    if(currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)
    {
        title.value = t('receipt.add')
        onChangeID()
        //克隆必要信息
        receiptData.buy_order_num  = purchaseData.buy_order_num
        // receiptData.supplier_nick = purchaseData.supplier_nick
        // receiptData.supplier_id = purchaseData.supplier_id

        // const info = wsCache.get(appStore.getUserInfo)
        // receiptData.drawin_man_id = info.id
        // receiptData.drawin_man_name = info.resident_name
        receiptData.drawin_date = getTodayDate()
        //追加所有产品到收货列表
        receiptData.pdt_list = purchaseData.pdt_list

        //新增属性
        for(let item of receiptData.pdt_list)
        {
            //item.质检数量 = 0
            item.良品数量 = 0
            item.不良品数量 = 0
            item.收货数量 =  (parseFloat(item.采购数量)+parseFloat(item.采购备品数量))-item.已收货+item.已退货
            if(item.收货数量<0)
                item.收货数量 = 0
            //自动填写收货数为
            item.重量 = ''
        }

        //过滤收货数量大于等于采购数量的pdt
        receiptData.pdt_list = receiptData.pdt_list.filter(item => (item.已收货-item.已退货) < (item.采购数量+item.采购备品数量));

    }
    else
    {        
        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('receipt.look')
        }
        else
        {
            title.value = t('receipt.modify')
        }
        
        //查询信息 
        const ret = await getReceiptInfoApi({
            id:currentRoute.value.query.id,
            page:1,
            count:100
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(receiptData, ret.data)
            receiptData.pdt_list = ret.data.pdt_list;
        }
        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
        
    }
})

//校验输入
const recomputeCount = (pdt)=>{
    // let nMax = (parseInt(pdt.采购数量)+parseInt(pdt.采购备品数量))-pdt.已收货+pdt.已退货

    // //可以超量收货5%
    // nMax = parseInt(nMax*1.05)

    // if(pdt.收货数量>nMax)
    // {
    //     pdt.收货数量 = nMax
    //     ElMessage.warning('超量收货不能超过5%')
    // }
    let nMax = ((parseFloat(pdt.采购数量)+parseFloat(pdt.采购备品数量)))*1.05-pdt.已收货+pdt.已退货
    if(pdt.收货数量>nMax)
    {
        pdt.收货数量 = nMax
        ElMessage.warning('超量收货不能超过5%')
    }

    if(pdt.收货数量<0)
    {
        pdt.收货数量 = 0
    }
}

//unmounted的时候移除监听
onBeforeUnmount(() => {

})



//添加收货产品
const showSelReceiptPdt = ref(false)
const onAddPdt = ()=>{
    showSelReceiptPdt.value = true
}
//选择产品回调
const onSelReceiptPdtCallback = (pdt)=>{
    //检测是否已经存在了
    const index = receiptData.pdt_list.findIndex(item=>item.标识==pdt.标识)
    console.log(index)
    if(index<0)
        receiptData.pdt_list.push(pdt)
    else
        ElMessage.warning('该产品已经存在')
}

//删除某一个产品
const onDelPdt = (index)=>{
    receiptData.pdt_list.splice(index,1)
}



//保存
const onSave = async()=>{
    console.log(receiptData)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //提交时自动过滤数量为0或者为空的产品
    receiptData.pdt_list = receiptData.pdt_list.filter(item=>parseFloat(item.收货数量)>0) 
    const tmp = cloneDeep(receiptData)
    tmp.pdt_list = tmp.pdt_list.filter(item=>parseFloat(item.收货数量)>0) 
    if(tmp.pdt_list.length<=0)
    {
        ElMessage.warning(t('msg.noCount'))
        return Promise.resolve()
    }


    //增加审核流程参数
    const info = wsCache.get(appStore.getUserInfo)
    tmp.fsm_exe_man_name = info.resident_name
    tmp.fsm_exe_trig = '提交'
    
    if(tmp.id == undefined)
    {
        const ret = await addReceiptApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.newReceiptSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        const ret =await updateReceiptApi(tmp)
        if(ret)
        {
            ElMessage.success(t('msg.updateReceiptSuccess'))
            baskFront()
        }
    }


}
//返回上一页
const baskFront = ()=>{
    // back()
    let path =currentRoute.value.path.indexOf('inventorymanage')>=0?'/inventorymanage/stonereceiptlist':'/purchasemanage/receiptlist'

    closeOneTagByPath(path)
    push(path)
    closeOneTagByName(currentRoute.value.meta.title)
}


//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...res.data)

    if(currentRoute.value.query.id == '')
    {
        //设置默认良品不良品仓库
        let a = storeData.find(item=>item.type=='良品库')
        console.log(a)
        receiptData.store_data.良品库 = storeData.find(item=>item.type=='良品库').id
        receiptData.store_data.不良品库 = storeData.find(item=>item.type=='不良品库').id
    }

  }

}


//选择某项后
let selRow = []
const handleSelectionChange = (val)=>{
  selRow = val
}
//批量删除选择产品
const delMul = () => {
    ElMessageBox.confirm('是否确认删除所选产品条目？', t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
        console.log(selRow)

        // 获取 selRow 中的所有 id
        const selRowIds = selRow.map(item => item.标识);
        receiptData.pdt_list = receiptData.pdt_list.filter(item => !selRowIds.includes(item.标识));
    }
    ).catch(() => { })
}

</script>

<template>
    <ContentDetailWrap :title="title" @back="baskFront()">
        <template #right>
            <ElButton style="color: white;" color="#409EFF" @click="onSave"  v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && receiptData.fsm_can_trig_data.操作触发.includes('保存')">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <el-form :rules="rules" :model="purchaseData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="4" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="t('purchase.id')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ receiptData.buy_order_num }}</div> 
                        <!-- <ElButton @click="onSelPurchase">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton> -->
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('receipt.id')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input class="mr-1"  v-model="receiptData.buy_drawin_num" :disabled="receiptData.id!=undefined" />
                            <ElButton v-if="receiptData.id==undefined" type="primary"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('receipt.date')"
                    class="flex">
                    <el-form-item >
                        <el-date-picker v-model="receiptData.drawin_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'良品入库仓库'"
                    class="flex">
                    <el-select v-model="receiptData.store_data.良品库" placeholder="Select" >
                        <el-option v-for="item in storeData.filter(item => item.type === '良品库')" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" :label="'不良入库仓库'"
                    class="flex">
                    <el-select v-model="receiptData.store_data.不良品库" placeholder="Select" >
                        <el-option v-for="item in storeData.filter(item => item.type === '不良品库')" :key="item.id" :label="item.nick" :value="item.id" />
                    </el-select>
                </el-descriptions-item>
            </el-descriptions>
        
        </el-form>
        <div class="mt-2 mb-2">
            <ElButton type="success" @click="onAddPdt" v-show="currentRoute.query.type != 'info' && receiptData.fsm_can_trig_data.操作触发.includes('保存')">
                <Icon class="mr-0.5" icon="material-symbols:add"/>
                新增
            </ElButton>
            <ElButton plain @click="delMul">批量删除</ElButton>
        </div>
        
        <el-table header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="receiptData.pdt_list" style="width: 100%" border stripe @selection-change="handleSelectionChange">
            <el-table-column align="center"  type="selection" width="50" />
            <el-table-column  :label="t('process.opt')" width="60" >
                <template #default="scope">
                    <div  type="primary">
                        <el-popconfirm title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && receiptData.fsm_can_trig_data.操作触发.includes('保存')"/>
                            </template>
                        </el-popconfirm>
                        
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                <template #default="scope">
                    {{ scope.$index+1 }}
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div  class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick}}</div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip                        
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('purchase.count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.采购数量 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="备品"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.采购备品数量 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('purchase.receipt_count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.已收货 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('purchase.return_count')"  >
                <template #default="scope">
                    <div  class="flex justify-center items-center">
                        <div class="mr-1">{{ scope.row.已退货 }}</div>
                        <div>{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column  :label="t('purchase.now_count')"  >
                <template #default="scope">
                        <el-input v-model="scope.row.收货数量" class="!text-center" @blur="recomputeCount(scope.row)" type="number"/>                  
                </template>
            </el-table-column>
            <el-table-column  label="重量"  >
                <template #default="scope">
                        <el-input v-model="scope.row.重量" class="!text-center"/>                  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="采购备注" :label="t('purchase.remark')"  />

            <el-table-column  :label="t('receipt.remark')"  >
                <template #default="scope">                    
                        <el-input v-model="scope.row.收货备注" class="!text-center"/>                                      
                </template>
            </el-table-column>
        </el-table>

        <!-- 显示合计 -->
        <div class="flex">
        <!-- 左边部分 -->
        <div class="w-[60%] text-center">
            <div class="flex">
                <table class="table-auto border-collapse table_self w-[100%]">
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">{{ t('purchase.remark') }}</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ purchaseData.note }}
                        </td>
                    </tr>
                    <tr>
                        <td class="table_self_title min-w-[100px] p-2">备注:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="receiptData.note" clearable :autosize="{ minRows: 3, maxRows: 3 }" type="textarea" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="flex-grow text-center">
            <div class="flex">
                <div class="w-[100%]">
                    <div>
                        <td class="table_self_title min-w-[100px] p-2">运费:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="receiptData.express_fee" type="number"/>
                        </td>
                    </div>
                    <div>
                        <td class="table_self_title min-w-[100px] p-2">其他费用:</td>
                        <td class="table_self !w-[100%] p-3">
                            <el-input v-model="receiptData.other_fee" type="number"/>
                        </td>
                    </div>
                    <div>
                        <td class="table_self_title min-w-[100px] p-2 ">合计金额:</td>
                        <td class="table_self !w-[100%] p-3">
                            {{ parseFloat(receiptData.express_fee)+parseFloat(receiptData.other_fee) }}
                        </td>
                    </div>
                </div>
            </div>
        </div>
    </div>

        
        




    <!-- <div class="mb-60"></div> -->

    <DialogSelReceiptPdt v-model:show="showSelReceiptPdt" :param="pdtList" :title="t('receipt.add_pdt')" @on-submit="onSelReceiptPdtCallback"/>


    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
:deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
  white-space: nowrap;
  text-align: center;
}
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    // font-size: 18px;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>
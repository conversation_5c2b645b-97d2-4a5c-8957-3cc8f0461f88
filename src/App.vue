<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { ConfigGlobal } from '@/components/ConfigGlobal'
import { isDark } from '@/utils/is'
import { useDesign } from '@/hooks/web/useDesign'
import { useCache } from '@/hooks/web/useCache'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('app')

const appStore = useAppStore()

const currentSize = computed(() => appStore.getCurrentSize)

const greyMode = computed(() => appStore.getGreyMode)

const { wsCache } = useCache()

// 根据浏览器当前主题设置系统主题色
const setDefaultTheme = () => {
  // if (wsCache.get('isDark') !== null) {
  //   appStore.setIsDark(wsCache.get('isDark'))
  //   return
  // }
  // const isDarkTheme = isDark()
  //默认启动固定浅色主题
  appStore.setIsDark(false)
}

setDefaultTheme()

window.DEBUG_MODE = import.meta.env.MODE;
console.log('window.DEBUG_MODE', window.DEBUG_MODE);
</script>

<template>
  <ConfigGlobal :size="currentSize">
    <RouterView :class="greyMode ? `${prefixCls}-grey-mode` : ''" />
  </ConfigGlobal>
</template>

<style lang="less">
@prefix-cls: ~'@{namespace}-app';

.size {
  width: 100%;
  height: 100%;
}
html,
body {
  padding: 0 !important;
  margin: 0;
  overflow: hidden;
  .size;

  #app {
    .size;
  }
}

.@{prefix-cls}-grey-mode {
  filter: grayscale(100%);
}
.table_self{
  font-size: 13px !important;
}
.el-table--default {
    font-size: 13px !important;
}
.el-dialog{
    --el-dialog-margin-top: 10vh !important;
    margin-top:var(--el-dialog-margin-top,10vh) !important;
    &.is-fullscreen{
      --el-dialog-margin-top: 0 !important;
  }
}
//修改选中时的颜色
// .current-row {
//   background-color: #000 !important;
// }

.tableHeader {
  background-color: #f4f4f5 !important;
  color: #333;
  font-weight: bold;
}
.el-table__body tr.current-row>td.el-table__cell {
    background-color: #7ef884 !important;
}
.el-button--primary {
    --el-button-hover-bg-color: #00ba80bf !important;
    --el-button-hover-border-color: #00ba80bf !important;
}

input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

input[type="number"] {
  -moz-appearance: textfield;
}

// body {
//     line-height: 24px;
//     font: 14px Helvetica Neue,Helvetica,PingFang SC,\5FAE\8F6F\96C5\9ED1,Tahoma,Arial,sans-serif;
// }

:root {
  --left-menu-max-width: 170px; /* 设置你想要的宽度 */
}
// /* 强制显示滚动条 */
// .overflow-y-scroll::-webkit-scrollbar {
//   display: block;
// }

// /* 强制显示滚动条 */
// .overflow-y-scroll::-webkit-scrollbar {
//   width: 100px; /* 设置滚动条宽度 */
//   height: 100%;
// }

// /* 或者使用 visibility 属性 */
// .overflow-y-scroll::-webkit-scrollbar {
//   visibility: visible; /* 显示滚动条 */
// }


// div{
//   font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "微软雅黑", "Tahoma", "Arial", "sans-serif";
//   font-style: normal;
//     font-variant-caps: normal;
//     font-variant-ligatures: normal;
//     font-variant-numeric: normal;
//     font-variant-east-asian: normal;
//     font-variant-alternates: normal;
//     font-kerning: auto;
//     font-optical-sizing: auto;
//     font-feature-settings: normal;
//     font-variation-settings: normal;
//     font-variant-position: normal;
//     font-weight: normal;
//     font-stretch: normal;
//     line-height: normal;
// }
// .el-scrollbar__bar{
//   width: 0px!important;
// }

// /* 修改滚动条的样式 */
// ::-webkit-scrollbar {
//     /* 设置滚动条的宽度：如果设置为0就是隐藏滚动条但是滚动效果还在非常完美 */
// 	width: 15px !important;
// }
 
// /* 滚动条的轨道 */
// ::-webkit-scrollbar-track {
//     /* 设置滚动条轨道的背景色 */
//     background-color: #f1f1f1;
// }
 
// /* 滚动条的轨道的hover颜色 */
// ::-webkit-scrollbar-track:hover {
//     background-color: #eaeaea;
// }
 
// /* 滚动条的滑块 */
// ::-webkit-scrollbar-thumb {
//     /* 设置滚动条滑块的背景色 */
//     background-color: #888;
//     border-radius: 8px;
// }
 
// /* 鼠标悬停在滚动条上的样式 */
// /* 设置鼠标悬停时滚动条滑块的背景色 */
// ::-webkit-scrollbar-thumb:hover {
//     background-color: #555;
// }
 
// /* 滚动条的滑块在非激活状态下的样式（滑块未被点击或拖动） */
// ::-webkit-scrollbar-thumb:inactive {
//     background-color: #ccc;
// }
 
// /* 滚动条的按钮（上下按钮） */
// ::-webkit-scrollbar-button {
//     display: none;
// }
 
// /* 滚动条的角落（轨道和滑块交汇的部分） */
// ::-webkit-scrollbar-corner {
//     background-color: #f1f1f1;
// }

</style>

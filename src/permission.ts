import router, { getRootNameByPath } from './router'
import {getRouterTabItem} from './router'
import { useAppStoreWithOut } from '@/store/modules/app'
import { useCache } from '@/hooks/web/useCache'
import type { RouteRecordRaw } from 'vue-router'
import { useTitle } from '@/hooks/web/useTitle'
import { useNProgress } from '@/hooks/web/useNProgress'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { useDictStoreWithOut } from '@/store/modules/dict'
import { usePageLoading } from '@/hooks/web/usePageLoading'
import { getDictApi } from '@/api/common'
import { Layout, getParentLayout } from '@/utils/routerHelper'
import { isMobileDevice } from './api/tool'
import { computed } from 'vue'
const isMobile = computed(() => appStore.getMobile)

const permissionStore = usePermissionStoreWithOut()

const appStore = useAppStoreWithOut()

const dictStore = useDictStoreWithOut()

const { wsCache } = useCache()

const { start, done } = useNProgress()

const { loadStart, loadDone } = usePageLoading()

const whiteList = ['/login'] // 不重定向白名单

router.beforeEach(async (to, from, next) => {
  console.log("进入before",to)
  start()
  loadStart()
  if (wsCache.get(appStore.getUserInfo)) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      // if (!dictStore.getIsSetDict) {
      //   // 获取所有字典
      //   console.log('-----111--------')
      //   const res = await getDictApi()
      //   console.log('-------------',res)
      //   if (res) {
      //     dictStore.setDictObj(res.data)
      //     dictStore.setIsSetDict(true)
      //   }
      // }
      //校验要调整的是否是非展示页面，且是否有权限

      // 如果是404 则需要跳转测试
      // console.log(router.getRoutes())
      if(to.path === '/404')
      {

        //找到重定向地址
        const arrAll =  permissionStore.getRouters
        let redireUrl = ''
        for(const one of arrAll)
          {
            if(one.children == undefined)
              continue
            for(const two of one.children)
              {
                if(to.redirectedFrom?.path.indexOf(two.path)>=0)
                  {
                    redireUrl = one.path+'/'+two.path
                    break;
                  }
              }
              if(redireUrl.length>0)
                {
                  break
                }
          }
          
        
        const { query, params } = to;
        // const fullPath = `/monthlycheck/rrtest${to.path}${to.hash}`;
        if (redireUrl == '') {
          next()
          return;
        }
        next({
          path: redireUrl,
          query,
          params,
        });
        // next(to.redirectedFrom);
        return;
      }
      

      if (permissionStore.getIsAddRouters) {
        next()
        return
      }

      //在当前自己的权限列表中找到对应有这个路由的第一个Tab进行初始化路由
      let routerMap = wsCache.get('roleRoutersLaba')
      if (routerMap == undefined)
      {
        routerMap = []
      }


      let arrayRole:string[] = []
      let strCurTabName = ""
      for(let i=0;i<routerMap.length;i++)
      {
        //console.log(routerMap[i])
        const arrayItem = routerMap[i]["routes"]
        let bFind = false
        for(let j=0;j<arrayItem.length;j++)
        {
          if(arrayItem[j] == to.path)
          {
            bFind = true
            break
          }
        }
        if(bFind)
        {
          arrayRole = arrayItem
          strCurTabName = routerMap[i]["name"]
          break
        }
      }

      // //如果当前时手机版本则直接加载手机路由
      // if (isMobile.value) {
      //   //如果是手机版切换PC，则需要修复路由
      //   if (to.path.indexOf('/mobile') < 0) {
      //     to.path = '/mobile/main'
      //   }

      //   const srcMap = getRouterTabItem('mobileRouterMap')?.routes
      //   console.log(srcMap)
      //   //构造所有roles
      //   arrayRole = []
      //   for (const one of srcMap) {
      //     arrayRole.push(one.path)
      //     for (const item of one.children) {
      //       arrayRole.push(one.path+'/'+item.path)
      //     }
      //    }

      //   await permissionStore.generateRoutes(srcMap, arrayRole)
      //   permissionStore.getAddRouters.forEach((route) => {
      //     console.log('add router:',route.path)
      //     router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
      //   })
      //   const redirectPath = from.query.redirect || to.path
      //   const redirect = decodeURIComponent(redirectPath as string)
      //   const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
      //   permissionStore.setIsAddRouters(true)
      //   next(nextData)
      //   return
      // }

      // //如果是手机版切换PC，则需要修复路由
      // if (to.path.indexOf('/mobile') >= 0) {
      //   to.path = '/customermanage/percustomermanage'
      // }


      //权限中找不到，需要看看是否上不需要权限的页面
      if(strCurTabName == "")
        {
          strCurTabName = getRootNameByPath(to.path)
          //拿到权限
        arrayRole = routerMap.find(item => item.name == strCurTabName)?.routes
        if (arrayRole == undefined)
          arrayRole = []
        }


      const srcMap = getRouterTabItem(strCurTabName)?.routes
      await permissionStore.generateRoutes(srcMap, arrayRole)


      permissionStore.getAddRouters.forEach((route) => {
        console.log('add router:',route.path)
        router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
      })
      const redirectPath = from.query.redirect || to.path
      const redirect = decodeURIComponent(redirectPath as string)
      const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
      permissionStore.setIsAddRouters(true)
      next(nextData)
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`) // 否则全部重定向到登录页
    }
  }
})

router.afterEach((to) => {
  useTitle(to?.meta?.title as string)
  done() // 结束Progress
  loadDone()
  console.log('路由切换结束')
  // setTimeout(() => {
  //   console.log('@@@@@@@@@@@@@@@')
  //   window.dispatchEvent(new Event('resize'));
  // },1000)
})

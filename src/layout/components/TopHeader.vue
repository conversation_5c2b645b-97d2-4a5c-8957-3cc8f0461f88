<script setup lang="ts">
import {  computed, onMounted, ref} from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { ElImage, ElMessage } from 'element-plus'
import { useCache } from '@/hooks/web/useCache'
import { getHashSysInfoApi } from '@/api/extra'
const { wsCache } = useCache()
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()
const title = computed(() => appStore.getTitle)

const onJumpUrl = () => {
  // let token = JSON.parse(sessionStorage.getItem('Token')||'{}')
  // let userInfo = sessionStorage.getItem('userInfo')
  // window.open('http://admin.mbtoys.net:8088/#/bigScreen?Token='+JSON.parse(token.v)+'&userInfo='+userInfo, '_blank'); 
}

const mmmode = ref('')
mmmode.value = import.meta.env.VITE_TEST_MODE
console.log('====', mmmode.value)

const agentUrl = ref('')
onMounted(async() => {
  const ret = await getHashSysInfoApi({
        name:'推广地址前缀'
  })
  if (ret) {
    const info = wsCache.get(appStore.getUserInfo)
    agentUrl.value = ret.data.text+'?agent_id='+info.six_id
  }

})

const onCopy = () => {
  const input = document.createElement('input');
  input.value = agentUrl.value;
  document.body.appendChild(input);
  input.select();
  document.execCommand('Copy');
  document.body.removeChild(input);

  ElMessage.success('复制成功')
}
</script>

<template>
  <div class=" bg-light-50 h-[52%] flex items-center" :class="mmmode" style="background: rgba(240, 242, 245, 1);">{{ mmmode }}
    <div
      :class="[
        prefixCls,
        true ? `${prefixCls}__Top` : '',
        'flex !h-[var(--logo-height)] items-center  pl-16px relative',
        'dark:bg-[var(--el-bg-color)]'
      ]"
      to="/"
    >
      <img
        v-if="mmmode==undefined"
        src="@/assets/imgs/wenbologo.png"
        class=" h-[calc(var(--logo-height)-20px)]"
      />
      <div
        :class="[
          'ml-16px text-16px font-700',
        ''
        ]"
      >
        {{ title }}
      </div>
      <div class="ml-20 border border-red-800  p-1">
        推广地址:<span class="text-blue-400 cursor-pointer ml-5" @click="onCopy">{{ agentUrl }}</span>
      </div>
    </div>
    <div class="ml-auto mr-6 flex" v-if="mmmode==undefined">
      <!-- <el-image class="xiangpian w-[50px] h-[50px] mt-4 cursor-pointer" title="大屏监控" src="/computer.jpg"  @click="onJumpUrl"/> -->
      <!-- <Icon icon="ep:data-analysis" :size="24"  @click="onJumpUrl"  class="xiangpian w-[50px] h-[50px] mt-4 cursor-pointer"/> -->
    </div>
  </div>
</template>

<style lang="less" scoped>
.test{
  background-color: red !important;
}
.demo{
  background-color: #cbffd0 !important;
}
</style>
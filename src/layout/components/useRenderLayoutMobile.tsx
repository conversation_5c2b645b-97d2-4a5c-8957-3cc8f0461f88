import { computed } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { Menu } from '@/components/Menu'
import { TabMenu } from '@/components/TabMenu'
import { TagsView } from '@/components/TagsView'
import { Logo } from '@/components/Logo'
import AppView from './AppView.vue'
import ToolHeader from './ToolHeader.vue'
import { useDesign } from '@/hooks/web/useDesign'
import TopHeader from './TopHeader.vue'
import TopNavi from '@/components/TopNavi/TopNavi.vue'
import { ElRow, ElCol, ElScrollbar } from 'element-plus'


const { getPrefixCls } = useDesign()
import { useRouter } from 'vue-router'

const bottomNavStyle = {
    position: 'fixed',
    bottom: '0',
    width: '100%',
    backgroundColor: '#fff',
    boxShadow: '0 -1px 6px rgba(0, 0, 0, 0.1)',
};

const navItemStyle = {
    textAlign: 'center',
    padding: '10px 0',
    cursor: 'pointer',
};

const bottomNavHeight = 50; // 底部导航栏的高度
const getContentHeight = () => {
    return `${window.innerHeight - bottomNavHeight}px`;
  };
  
  const contentStyle = {
    height: getContentHeight(), // 动态计算高度
    overflowY: 'auto',
  };

export const useRenderLayout = () => {
    const router = useRouter(); // 确保在组合函数中调用 useRouter

    const navigateTo = (route) => {
        router.push(route);
    };

    const renderClassic = () => {
        return (
            <>
                <div>
                <div style={contentStyle}>
                        <AppView />
                </div>               
                    <div style={bottomNavStyle}>
                        <ElRow>
                            <ElCol span={6} style={navItemStyle} onClick={() => navigateTo({ path: '/mobile/main' })}>主页</ElCol>
                            <ElCol span={6} style={navItemStyle} onClick={() => navigateTo({ path: '/mobile_salemanage/salemain' })}>销售</ElCol>
                            <ElCol span={6} style={navItemStyle} onClick={() => navigateTo('/mobile_purchasemanage/purchasemain')}>采购</ElCol>
                            <ElCol span={6} style={navItemStyle} onClick={() => navigateTo('/mobile_oemmanage/oemmain')}>委外</ElCol>
                        </ElRow>
                    </div>
                </div>
            </>
        )
    }

    return {
        renderClassic,
    }
};

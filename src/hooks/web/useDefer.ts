
import { onUnmounted, ref } from 'vue';
const count = ref(0)
//界面延迟刷新
export function useDefer(maxCount = 1000){
    let reqId = null;
    function updateFrame(){
        
        count.value++;
        // if(count.value > maxCount)
        // {
        //     return;
        // }
       reqId = setTimeout(() => {
            updateFrame()
        },20)
        // reqId = requestAnimationFrame(updateFrame);
    }
    updateFrame();
    onUnmounted(() => {
        // cancelAnimationFrame(reqId);
        clearTimeout(reqId);
    })
    return function(n){
        // console.log('return',count.value)
        return count.value>=n;
    }
}
export function clearDefer(){
    count.value = 0
}
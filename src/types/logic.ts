//采购定义
export interface purchaseDef {
    supplier_id: string;
    supplier_name: string;
    supplier_nick: string;
    buy_order_num: string;
    supplier_order_num: string;
    money_type: string;
    delivery_date: string;
    buy_man_id: string;
    buy_man_name: string;
    follow_man_id: string;
    follow_man_name: string;
    buy_dept_id: string;
    create_date: string;
    modify_date: string;
    pdt_list: any[];
    note: string;
    express_fee: number;
    other_fee: number;
    pay_type: string;
    business_date: string;
    is_same_delivery: number;
    '合计费用': number;
  }

//销售定义
export interface RootObject {
  sell_order_num: string;
  diy_order_num: string;
  buy_man_id: string;
  is_same_delivery: number;
  delivery_date: string;
  sell_man_id: string;
  follow_man_id: string;
  sell_dept_id: string;
  sell_date: string;
  note: string;
  express_fee: number;
  other_fee: number;
  money_type: string;
  pay_type: string;
  delivery_type: string;
  express_type: string;
  setup_type: string;
  pack_type: string;
  receive_man_id: string;
  pdt_list: any[];
  remark: string;
  sell_man_name?:string;
  follow_man_name?:string;
}
import { service } from './service'

import { config } from './config'
import { sendCommand } from '@/websocket'
import router from '@/router';

const { default_headers } = config

const request = (option: any) => {
  const { url, method, params, data, headersType, responseType } = option


  if(params != undefined)
  {
    if(params.realrole != undefined && params.realrole != '')
    {
      params.module_name = params.realrole
    }
    else
      params.module_name = router.currentRoute.value.meta.title
  }
  if(data != undefined)
  {
    if(data.realrole != undefined && data.realrole != '')
    {
      data.module_name = data.realrole
    }
    else
      data.module_name = router.currentRoute.value.meta.title
  }

  if(import.meta.env.VITE_SERVER_TYPE == 'ws')
  {
    if(method == 'get')
      return sendCommand(method,url,params)
    else
      return sendCommand(method,url,data)
  }
  else{
    return service({
      url: url,
      method,
      params,
      data,
      responseType: responseType,
      headers: {
        'Content-Type': headersType || default_headers
      }
    })
  }




}
export default {
  get: <T = any>(option: any) => {
    return request({ method: 'get', ...option }) as unknown as T
  },
  post: <T = any>(option: any) => {
    return request({ method: 'post', ...option }) as unknown as T
  },
  delete: <T = any>(option: any) => {
    return request({ method: 'delete', ...option }) as unknown as T
  },
  put: <T = any>(option: any) => {
    return request({ method: 'put', ...option }) as unknown as T
  }
}

import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import {  type App } from 'vue'
import { Layout, getParentLayout } from '@/utils/routerHelper'
import { useI18n } from '@/hooks/web/useI18n'
const { t } = useI18n()


export const constantRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    redirect: '/paymanage/paytxmanage',
    name: 'Root',
    meta: {
      hidden: true
    }
  },
  {
    path: '/redirect',
    component: Layout,
    name: 'Redirect',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect11',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFind',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/print',
    component: () => import('@/views/PrintManage/print.vue'),
    name: 'print',
    meta: {
      hidden: true,
      title: 'print',
      noTagsView: true
    }
  }
]

//支付管理
export const tongjiRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/tongjimanage',
    component: Layout,
    redirect: '/tongjimanage/playermanage',
    name: 'TongjiManageMain',
    meta: {
      title: t('tongji.datatongji'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles: [
      { name: '允许查看游戏数据' }, 
      { name: '显示活跃用户统计' },
      { name: '显示游戏在线统计' },
    ],
    children: [
      {
        path: 'playermanage',
        component: () => import('@/views/Tongji/PlayerManage.vue'),
        name: 'PlayerManage',
        meta: {
          title: t('tongji.playermanage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'channelmanage',
        component: () => import('@/views/Tongji/ChannelManage.vue'),
        name: 'ChannelManage',
        meta: {
          title: t('tongji.channelmanage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'cashwaterlist',
        component: () => import('@/views/Tongji/CashWaterList.vue'),
        name: 'CashWaterList',
        meta: {
          title: t('tongji.cashwaterlist'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'gamewinlosemanage',
        component: () => import('@/views/Tongji/GameWinLoseManage.vue'),
        name: 'GameWinLoseManage',
        meta: {
          title: t('tongji.gamewinlosemanage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'downloadlist',
        component: () => import('@/views/Tongji/DownloadList.vue'),
        name: 'DownloadList',
        meta: {
          title: t('tongji.downloadlist'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'registelist',
        component: () => import('@/views/Tongji/RegisteList.vue'),
        name: 'RegisteList',
        meta: {
          title: t('tongji.registelist'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'playertongji',
        component: () => import('@/views/Tongji/PlayerTongji.vue'),
        name: 'PlayerTongji',
        meta: {
          title: t('tongji.playertongji'),
          noCache: true,
          affix: true
        }
      },
            {
        path: 'test',
        component: () => import('@/views/Tongji/test.vue'),
        name: 'Test',
        meta: {
          title: '测试',
          noCache: true,
          affix: true
        }
      },
    ]
  }
]

//支付管理
export const payRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/paymanage',
    component: Layout,
    redirect: '/paymanage/paytxmanage',
    name: 'PayManageMain',
    meta: {
      title: t('pay.paymanager'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles: [
      { name: '允许操作提现'},
      {name:'允许操作充值'},      
    ],
    children: [
      {
        path: 'paytxmanage',
        component: () => import('@/views/PayManage/PayTXManage.vue'),
        name: 'PayTXManage',
        meta: {
          title: t('pay.paytx'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'payczmanage',
        component: () => import('@/views/PayManage/PayCZManage.vue'),
        name: 'PayCZManage',
        meta: {
          title: t('pay.paycz'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'payczconfig',
        component: () => import('@/views/PayManage/PayCZConfig.vue'),
        name: 'PayCZConfig',
        meta: {
          title: t('pay.czconfig'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'paycusczconfig',
        component: () => import('@/views/PayManage/PayCusCZConfig.vue'),
        name: 'PayCusCZConfig',
        meta: {
          title: t('pay.cusczconfig'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'exchangecode',
        component: () => import('@/views/PayManage/ExchangeCode.vue'),
        name: 'ExchangeCode',
        meta: {
          title: t('pay.exchangecode'),
          noCache: true,
          affix: true
        }
      },
    ]
  }
]


//系统设置
export const sysConfigRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/usermanage',
    component: Layout,
    redirect: '/usermanage/usermanage',
    name: 'sysManageMain',
    meta: {
      title: t('userManage.userManage'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'usermanage',
        component: () => import('@/views/UserManage/UserManage.vue'),
        name: 'UserManage',
        meta: {
          title: t('userManage.userManage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'deptmanage',
        component: () => import('@/views/UserManage/DeptManage.vue'),
        name: 'DeptManage',
        meta: {
          title: t('userManage.deptManage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'adduser',
        component: () => import('@/views/UserManage/AddUser.vue'),
        name: 'AddUser',
        meta: {
          title: t('userTable.addUser'),
          noTagsView: true, 
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/usermanage/usermanage'
        }
      },
      {
        path: 'rolemanage',
        component: () => import('@/views/UserManage/RoleManage.vue'),
        name: 'RoleManage',
        meta: {
          title: t('userManage.roleManage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addrole',
        component: () => import('@/views/UserManage/AddRole.vue'),
        name: 'AddRole',
        meta: {
          title: t('roleTable.addRole'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/usermanage/rolemanage'
        }
      },
      {
        path: 'addroledata',
        component: () => import('@/views/UserManage/AddRoleData.vue'),
        name: 'AddRoleData',
        meta: {
          title: '角色数据权限',
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/usermanage/rolemanage'
        }
      }
    ]
  },
  {
    path: '/systemmanage',
    component: Layout,
    redirect: '/systemmanage/systemcmd',
    name: 'SystemManage',
    meta: {
      title: t('sysmgr.sysmgr'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles: [
      { name: '允许查看关键配置'},
      {name:'自定义配置'},      
    ],
    children: [
      {
        path: 'hashconfig',
        component: () => import('@/views/SystemManage/HashConfig.vue'),
        name: 'SystemCmd',
        meta: {
          title: t('systemconfig.hashconfig'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'jiangchiconfig',
        component: () => import('@/views/SystemManage/JiangchiConfig.vue'),
        name: 'JiangchiConfig',
        meta: {
          title: t('systemconfig.jiangchiconfig'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'fishconfig',
        component: () => import('@/views/SystemManage/FishConfig.vue'),
        name: 'FishConfig',
        meta: {
          title: t('systemconfig.fishconfig'),
          noCache: true,
          affix: true
        }
      },
    ]
  },
]


const isMobileDevice =()=> {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

//主功能列表路由
export const abilityTabMap: AbilityTabItem[] = [] 

export const getAbilityTabMap = ()=>{
  if(getAbilityTabMap.length == 0)
  {
    initTab()
  }
  return abilityTabMap
}

const getDefRouter = () => {
  if (isMobileDevice()) {
    constantRouterMap[0].redirect = '/mobile/main'
  }
  return constantRouterMap
}

const router = createRouter({
  history: createWebHashHistory(),
  strict: true,
  routes: getDefRouter() as RouteRecordRaw[],//constantRouterMap as RouteRecordRaw[],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export const resetRouter = (): void => {
  const resetWhiteNameList = ['Redirect', 'Login', 'NoFind', 'Root']
  router.getRoutes().forEach((route) => {
    const { name } = route
    if (name && !resetWhiteNameList.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  console.log(router)
  initTab()
  app.use(router)

}



//初始化总TAB功能列表
const initTab =()=>
{
  abilityTabMap.length = 0  
  
  
  abilityTabMap.push({ "name": "tongjiRouterMap", "title": '数据分析', "routes": tongjiRouterMap });
  abilityTabMap.push({ "name": "payRouterMap", "title": '支付管理', "routes": payRouterMap });
  abilityTabMap.push({ "name": "sysConfigRouterMap", "title": t("router.sysConfigRouterMap"), "routes": sysConfigRouterMap });
}

export const getRouterTabItem = (name: string) => {
  abilityTabMap.length = 0  
  abilityTabMap.push({ "name": "tongjiRouterMap", "title": '数据分析', "routes": tongjiRouterMap });
  abilityTabMap.push({"name":"payRouterMap","title":'支付管理',"routes":payRouterMap})
  abilityTabMap.push({"name":"sysConfigRouterMap","title":t("router.sysConfigRouterMap"),"routes":sysConfigRouterMap})  



  for(const item of abilityTabMap)
  {
    if(item.name == name)
    {
      return item
    }
  }
  return {
    "name":"",
    "title":"",
    "routes":[]
  }
}
export const getRootNameByPath = (path)=>{
  for(const item of abilityTabMap)
    {
      for(const route of item.routes)
      {
        for(const one of route.children)
        {
            const parts = path.split('/');
            const lastPart = parts[parts.length - 1];
            if(lastPart == one.path)
            {
              return item.name
            }

          }
      }
    }
    return ''
}
//拿到所有不需要权限的页面
export const getAllNoQXRoute = ()=>{
  const arrayAll = []
  for(const item of abilityTabMap)
    {
      for(const route of item.routes)
      {
        for(const one of route.children)
          {
            if(one.meta.hidden == true )
              {
                one.test = route.path+'/'+one.path
                arrayAll.push(one)
              }
          }
      }
    }
  return arrayAll;
}

export const gotoRouter = (path:string)=>{
  //push(path)
}

export default router

export default {
  common: {
    inputText: '请输入',
    selectText: '请选择',
    startTimeText: '开始时间',
    endTimeText: '结束时间',
    login: '登录',
    required: '该项为必填项',
    loginOut: '退出系统',
    document: '项目文档',
    reminder: '温馨提示',
    loginOutMessage: '是否退出本系统？',
    back: '返回',
    ok: '确定',
    cancel: '取消',
    reload: '重新加载',
    closeTab: '关闭标签页',
    closeTheLeftTab: '关闭左侧标签页',
    closeTheRightTab: '关闭右侧标签页',
    closeOther: '关闭其他标签页',
    closeAll: '关闭全部标签页',
    prevLabel: '上一步',
    nextLabel: '下一步',
    skipLabel: '跳过',
    doneLabel: '结束',
    menu: '菜单',
    menuDes: '以路由的结构渲染的菜单栏',
    collapse: '展开缩收',
    collapseDes: '展开和缩放菜单栏',
    tagsView: '标签页',
    tagsViewDes: '用于记录路由历史记录',
    tool: '工具',
    toolDes: '用于设置定制系统',
    query: '查询',
    reset: '重置',
    shrink: '收起',
    expand: '展开',
    delMessage: '是否删除所选中数据？',
    delWarning: '提示',
    delOk: '确定',
    delCancel: '取消',
    delNoData: '请选择需要删除的数据',
    delSuccess: '删除成功',



    save:'保存',
    channel:'取消'
  },
  error: {
    noPermission: `抱歉，您无权访问此页面。`,
    pageError: '抱歉，您访问的页面不存在。',
    networkError: '抱歉，服务器报告错误。',
    returnToHome: '返回首页'
  },
  setting: {
    projectSetting: '项目配置',
    theme: '主题',
    layout: '布局',
    systemTheme: '系统主题',
    menuTheme: '菜单主题',
    interfaceDisplay: '界面显示',
    breadcrumb: '面包屑',
    breadcrumbIcon: '面包屑图标',
    collapseMenu: '折叠菜单',
    hamburgerIcon: '折叠图标',
    screenfullIcon: '全屏图标',
    sizeIcon: '尺寸图标',
    localeIcon: '多语言图标',
    tagsView: '标签页',
    logo: '标志',
    greyMode: '灰色模式',
    fixedHeader: '固定头部',
    headerTheme: '头部主题',
    cutMenu: '切割菜单',
    copy: '拷贝',
    clearAndReset: '清除缓存并且重置',
    copySuccess: '拷贝成功',
    copyFailed: '拷贝失败',
    footer: '页脚',
    uniqueOpened: '菜单手风琴',
    tagsViewIcon: '标签页图标',
    dynamicRouter: '动态路由',
    reExperienced: '请重新退出登录体验',
    fixedMenu: '固定菜单'
  },
  size: {
    default: '默认',
    large: '大',
    small: '小'
  },
  login: {
    welcome: '欢迎使用',
    message: '开箱即用的ERP系统',
    username: '用户名',
    password: '密码',
    register: '注册',
    checkPassword: '确认密码',
    login: '登录',
    otherLogin: '其他登录方式',
    remember: '记住我',
    hasUser: '已有账号？去登录',
    forgetPassword: '忘记密码',
    usernamePlaceholder: '请输入用户名',
    passwordPlaceholder: '请输入密码',
    code: '验证码',
    codePlaceholder: '请输入验证码',
  },
  router: {
    login: '登录',
    level: '多级菜单',
    menu: '菜单',
    menu1: '菜单1',
    menu11: '菜单1-1',
    menu111: '菜单1-1-1',
    menu12: '菜单1-2',
    menu2: '菜单2',
    dashboard: '首页',
    analysis: '分析页',
    workplace: '工作台',
    guide: '引导',
    component: '组件',
    icon: '图标',
    echart: '图表',
    countTo: '数字动画',
    watermark: '水印',
    qrcode: '二维码',
    highlight: '高亮',
    infotip: '信息提示',
    form: '表单',
    defaultForm: '全部示例',
    search: '查询',
    table: '表格',
    defaultTable: '基础示例',
    editor: '编辑器',
    richText: '富文本',
    dialog: '弹窗',
    imageViewer: '图片预览',
    descriptions: '描述',
    example: '综合示例',
    exampleDialog: '综合示例 - 弹窗',
    examplePage: '综合示例 - 页面',
    exampleAdd: '综合示例 - 新增',
    exampleEdit: '综合示例 - 编辑',
    exampleDetail: '综合示例 - 详情',
    errorPage: '错误页面',
    authorization: '权限管理',
    user: '用户管理',
    role: '角色管理',
    document: '文档',
    inputPassword: '密码输入框',
    sticky: '黏性',
    customerRouterMap:"客户管理",
    saleRouterMap:"销售管理",
    procureRouterMap:"采购管理",
    sysConfigRouterMap:"系统设置",
    prjectRouterMap:'工程管理',
    storeRouterMap:'仓库管理'
  },
  permission: {
    hasPermission: '请设置操作权限值'
  },
  analysis: {
    newUser: '新增用户',
    unreadInformation: '未读消息',
    transactionAmount: '成交金额',
    totalShopping: '购物总量',
    monthlySales: '每月销售额',
    userAccessSource: '用户访问来源',
    january: '一月',
    february: '二月',
    march: '三月',
    april: '四月',
    may: '五月',
    june: '六月',
    july: '七月',
    august: '八月',
    september: '九月',
    october: '十月',
    november: '十一月',
    december: '十二月',
    estimate: '预计',
    actual: '实际',
    directAccess: '直接访问',
    mailMarketing: '邮件营销',
    allianceAdvertising: '联盟广告',
    videoAdvertising: '视频广告',
    searchEngines: '搜索引擎',
    weeklyUserActivity: '每周用户活跃量',
    activeQuantity: '活跃量',
    monday: '周一',
    tuesday: '周二',
    wednesday: '周三',
    thursday: '周四',
    friday: '周五',
    saturday: '周六',
    sunday: '周日'
  },
  workplace: {
    goodMorning: '早安',
    happyDay: '祝你开心每一天!',
    toady: '今日晴',
    project: '项目数',
    access: '项目访问',
    toDo: '待办',
    introduction: '一个正经的简介',
    more: '更多',
    shortcutOperation: '快捷操作',
    operation: '操作',
    index: '指数',
    personal: '个人',
    team: '团队',
    quote: '引用',
    contribution: '贡献',
    hot: '热度',
    yield: '产量',
    dynamic: '动态',
    push: '推送',
    pushCode: 'Archer 推送 代码到 Github',
    follow: '关注'
  },
  formDemo: {
    input: '输入框',
    inputNumber: '数字输入框',
    default: '默认',
    icon: '图标',
    mixed: '复合型',
    textarea: '多行文本',
    slot: '插槽',
    position: '位置',
    autocomplete: '自动补全',
    select: '选择器',
    selectGroup: '选项分组',
    selectV2: '虚拟列表选择器',
    cascader: '级联选择器',
    switch: '开关',
    rate: '评分',
    colorPicker: '颜色选择器',
    transfer: '穿梭框',
    render: '渲染器',
    radio: '单选框',
    button: '按钮',
    checkbox: '多选框',
    slider: '滑块',
    datePicker: '日期选择器',
    shortcuts: '快捷选项',
    today: '今天',
    yesterday: '昨天',
    aWeekAgo: '一周前',
    week: '周',
    year: '年',
    month: '月',
    dates: '日期',
    daterange: '日期范围',
    monthrange: '月份范围',
    dateTimePicker: '日期时间选择器',
    dateTimerange: '日期时间范围',
    timePicker: '时间选择器',
    timeSelect: '时间选择',
    inputPassword: '密码输入框',
    passwordStrength: '密码强度',
    defaultForm: '全部示例',
    formDes: '基于 ElementPlus 的 Form 组件二次封装，实现数据驱动，支持所有 Form 参数',
    example: '示例',
    operate: '操作',
    change: '更改',
    restore: '还原',
    disabled: '禁用',
    disablement: '解除禁用',
    delete: '删除',
    add: '添加',
    setValue: '设置值',
    resetValue: '重置值',
    set: '设置',
    subitem: '子项',
    formValidation: '表单验证',
    verifyReset: '验证重置'
  },
  guideDemo: {
    guide: '引导页',
    start: '开始',
    message:
      '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。引导页基于 intro.js'
  },
  iconDemo: {
    icon: '图标',
    localIcon: '本地图标',
    iconify: 'Iconify组件',
    recommendedUse: '推荐使用',
    recommendeDes:
      'Iconify组件基本包含所有的图标，你可以查询到你想要的任何图标。并且打包只会打包所用到的图标。',
    accessAddress: '访问地址'
  },
  echartDemo: {
    echart: '图表',
    echartDes:
      '基于 echarts 二次封装组件，自适应宽度，只需传入 options 与 height 属性即可展示对应的图表。'
  },
  countToDemo: {
    countTo: '数字动画',
    countToDes: '基于 vue-count-to 进行改造，支持所有 vue-count-to 参数。',
    suffix: '后缀',
    prefix: '前缀',
    separator: '分割符号',
    duration: '持续时间',
    endVal: '结束值',
    startVal: '开始值',
    start: '开始',
    pause: '暂停',
    resume: '继续'
  },
  watermarkDemo: {
    watermark: '水印',
    createdWatermark: '创建水印',
    clearWatermark: '清除水印',
    resetWatermark: '重置水印'
  },
  qrcodeDemo: {
    qrcode: '二维码',
    qrcodeDes: '基于 qrcode 二次封装',
    basicUsage: '基础用法',
    imgTag: 'img标签',
    style: '样式配置',
    click: '点击事件',
    asynchronousContent: '异步内容',
    invalid: '失效',
    logoConfig: 'logo配置',
    logoStyle: 'logo样式',
    size: '大小配置'
  },
  highlightDemo: {
    highlight: '高亮',
    message: '种一棵树最好的时间是十年前，其次就是现在。',
    keys1: '十年前',
    keys2: '现在'
  },
  infotipDemo: {
    infotip: '信息提示',
    infotipDes: '基于 Highlight 组件二次封装',
    title: '注意事项'
  },
  levelDemo: {
    menu: '多级菜单缓存'
  },
  searchDemo: {
    search: '查询',
    searchDes: '基于 Form 组件二次封装，实现查询、重置功能',
    operate: '操作',
    change: '更改',
    grid: '栅格',
    button: '按钮',
    restore: '还原',
    inline: '内联',
    bottom: '底部',
    position: '位置',
    left: '左',
    center: '中',
    right: '右',
    dynamicOptions: '动态选项'
  },
  stickyDemo: {
    sticky: '黏性'
  },
  tableDemo: {
    table: '表格',
    tableDes: '基于 ElementPlus 的 Table 组件二次封装',
    index: '序号',
    title: '标题',
    author: '作者',
    displayTime: '创建时间',
    importance: '重要性',
    pageviews: '阅读数',
    action: '操作',
    important: '重要',
    good: '良好',
    commonly: '一般',
    operate: '操作',
    example: '示例',
    show: '显示',
    hidden: '隐藏',
    pagination: '分页',
    reserveIndex: '叠加序号',
    restoreIndex: '还原序号',
    showSelections: '显示多选',
    hiddenSelections: '隐藏多选',
    showExpandedRows: '显示展开行',
    hiddenExpandedRows: '隐藏展开行',
    changeTitle: '修改标题',
    header: '头部',
    selectAllNone: '全选/全不选'
  },
  richText: {
    richText: '富文本',
    richTextDes: '基于 wangeditor 二次封装'
  },
  dialogDemo: {
    dialog: '弹窗',
    dialogDes: '基于 ElementPlus 的 Dialog 组件二次封装',
    open: '打开',
    close: '关闭',
    combineWithForm: '与表单结合',
    submit: '提交'
  },
  imageViewerDemo: {
    open: '打开',
    imageViewer: '图片预览',
    imageViewerDes: '基于 ElementPlus 的 ImageViewer 组件二次封装'
  },
  descriptionsDemo: {
    descriptions: '描述',
    descriptionsDes: '基于 ElementPlus 的 Descriptions 组件二次封装',
    username: '用户名',
    nickName: '昵称',
    phone: '联系电话',
    email: '邮箱',
    addr: '地址',
    form: '与 Form 组件组合'
  },
  exampleDemo: {
    title: '标题',
    add: '新增',
    del: '删除',
    edit: '编辑',
    author: '作者',
    displayTime: '创建时间',
    importance: '重要性',
    pageviews: '阅读数',
    important: '重要',
    content: '内容',
    save: '保存',
    detail: '详情'
  },
  userDemo: {
    title: '用户管理',
    message: '由于是模拟数据，所以只提供了两种不同权限的帐号，开发者可根据实际情况自行改造结合。',
    index: '序号',
    action: '操作',
    username: '用户名',
    password: '密码',
    role: '角色',
    remark: '备注',
    remarkMessage1: '后端控制路由权限',
    remarkMessage2: '前端控制路由权限'
  },
  inputPasswordDemo: {
    title: '密码输入框',
    inputPasswordDes: '基于 ElementPlus 的 Input 组件二次封装'
  },












  userManage:{
    userManage:'账号管理',
    deptManage:'团队管理',
    roleManage:'角色管理'
  },
  userTable:{
    name:'员工/团队 名称',
    role:'角色',
    status:'状态',
    lastlogintime:'最后登陆时间',
    modifyDep:'修改团队',
    addDep:'新增团队',
    delDep:'删除团队',
    modifyUser:'修改人员',
    infoUser:'查看任意',
    addUser:'新增人员',
    delUser:'删除人员',
    delUserMul:'批量删除人员',
    id:'序号',
    userid:'用户编号',
    username:'称呼',
    dept:'部门',
    job:'职务',
    sex:'性别',
    birthday:'生日',
    age:'年龄',
    ethnicity:'民族',
    idcardnumber:'身份证号',
    marrystatus:'婚姻状况',
    phone:'手机',
    edu:'学历',
    category:"类型",
    addr:'地址',
    entrydate:'入职时间',
    entrylen:'在职时长',
    operate:'操作',
    moveUser:'移动人员',
    moveUserMul:'批量移动人员'
  },
  modifyDept:{
    depname:'团队名称:'
  },
  msg:{
    noUserID:'请输入员工编号',    
    noName:'请输入姓名',
    notify:'提示',
    pwdinput:'请输入登录密码!',
    pwdcheck:'两次密码不一致！',
    delUserMsg:'请确定是否删除该用户？',
    warn:'警告',
    ok:'确定',
    channel:'取消',
    delOK:'删除成功！',
    delChannel:'删除被取消',
    noDepName:'请输入团队名称！',
    success:"操作成功",
    delDeptMsg:'请确认是否删除该团队',
    selectUser:'请选择人员',
    delUser:'是否确认删除用户 ',
    person:'人',
    searchFalse:'查询失败',
    noRole:'未找到该角色',
    checkRule:'请检查必填项',
    noCusID:'请输入客户编号',
    noCusName:'请输入客户名称',
    changeIDSuccess:'更新编号成功！',
    noCorpName:'请输入公司名称！',
    inputCategName:'请输入分类名称！',
    delCateg:'是否确认删除该分类？',
    noProductName:'请输入产品编号',
    noProductNick:'请输入产品名称',
    confirm_stop:'是否确认停用该产品？',
    confirm_start:'是否确认启用该产品？',
    confirm_del:'是否确认删除该产品？',
    confirm_del_spec:'是否确认删除该规格？',
    confirm_del_process:'是否确认删除该工序？',
    pleaseInputSpesName:'请输入规格名称',
    noBomID:'请输入BOM编号',
    no_work_hours:'请输入标准工时',
    pleaseInputProcessName:'请输入工序名称或编号',
    pleaseInputProductInfo:'请输入产品信息',
    noBomName:'请输入BOM编号',
    noPdtName:'请输入产品编号',
    noPdtNick:'请输入产品名称',
    newBomSuccess:'BOM创建成功',
    exsitBom:'该BOM已存在，请重新选择',
    updateBomSuccess:'BOM更新成功',
    noSupplierName:'请输入供应商编号',
    noSupplierNick:'请输入供应商名称',
    noStoreName:'请输入仓库编号',
    noStoreNick:'请输入仓库名称',
    delStore:'是否确认删除该仓库？',
    confirm_del_sale:'是否确认删除该销售订单？',
    newSaleSuccess:'销售订单创建成功',
    updateSaleSuccess:'销售订单更新成功',
    noCustomer:'请输入关联客户',
    noSaleName:'请输入编号',
    newPurchaseSuccess:'采购订单创建成功',
    updatePurchaseSuccess:'采购订单更新成功',
    noPurchase:'请选择供应商',
    confirm_del_purchase:'是否确认删除该采购订单？',
    confirm_del_receipt:'是否确认删除该收货单？',
    pleaseInputNameOrID:'请输入编号或名称',
    newReceiptSuccess:'收货单创建成功',
    updateReceiptSuccess:'收货单更新成功',
    newQualityCheckSuccess:'质检单创建成功',
    updateQulityCheckSuccess:'质检单更新成功',
    confirm_del_qualitycheck:'是否确认删除该质检单？',
    newPutinSuccess:'入库单创建成功',
    updatePutinSuccess:'入库单更新成功',
    confirm_del_putin:'是否确认删除该入库单？',
    noRoleName:'请输入角色名称',
    delRole:'是否确认删除该角色？',
    newReturnSuccess:'退货单创建成功',
    updateReturnSuccess:'退货单修改成功',
    returnNumTooBig:'退货数量不能大于收货数量减去已退货数量',
    returnNumTooBig2:'退货数量不能大于库存数量',
    confirm_del_presale:'是否确认删除该售前报价订单？',
    newPreSaleSuccess:'售前报价单创建成功',
    updatePreSaleSuccess:'售前报价单修改成功',
    noNum:'请输入单号',
    noCount:'请输入数量',
    submitBasePriceSuccess:'提交出厂报价成功！',
    submitFinalPriceSuccess:'提交销售报价成功！',
    confirm_mll_low:'当前毛利率低于20%需要提交审核流程！',
    confirm_price_cus_ok:'是否已和客户确认价格，确认后将生成销售订单！',
    confirm_price_review_ok:'该订单价格是否审核通过？',
    confirm_price_review_channel:'是否确认该订单审核退回？',
    noCustom:'请选择客户',
    confirm_del_saleout:'是否确认删除该销售出库订单？',
    newSaleOutSuccess:'销售出库单创建成功',
    confirm_lock_pdt:'是否确认锁定该产品？',
    noParterName:'请输入受托商编号',
    noParterNick:'请输入受托商名字',
    confirm_del_oemorder:'是否确认删除该委外订单？',
    newOemOrderSuccess:'委外订单创建成功',
    updateOemOrderSuccess:'委外订单修改成功',
    noPayType:'请选择支付方式',
    selectMgrUser:'请选择负责人',
    confirm_update_wl:'确定是否修改物料？',
    newLLSuccess:'新增领料成功',
    updateLLSuccess:'更新领料成功',
    newTLSuccess:'新增退料成功',
    updateTLSuccess:'更新退料成功',
    confirm_del_takechannel:'是否确认删除该领料单？',
    newRemainSuccess:'返料成功！',
    updateRemainSuccess:'返料修改成功！',
    confirm_del_remain:'是否确认删除该返料单',
    confirm_del_drawin:'是否删除该收货单',
    noLogin:'是否确定禁止用户登录？',
    canLogin:'是否确定允许用户登录？',
    optOK:'操作成功！',
    optFail:'操作失败！',
    pdtEmpty:'请选择产品',
    noParter:'请选择受托商',
  },
  userOpt:{
    detail:'详情',
    edit:'编辑',
    del:'删除',
    info:'明细',
    nologin:'禁止登录',
    canlogin:'允许登录'
  },
  title:{
    seluser:'选择用户',
    notify:'提示',
    product_encoder:'产品编号编码器',
    categ_param:'分类参数设置'
  },
  roleTable:{
    id:'角色编号',
    name:'角色名称',
    type:'类型',
    area:'登录区域',
    owner:'拥有角色的人员',
    opt:'操作',
    addRole:'新增角色',
    modifyRole:'修改角色'
  },
  customer:{
    customermgr:'客户管理',
    perCustomerMgr:'个人客户管理',
    comCustomerMgr:'公司客户管理',
    connecterMgr:'公司联系人管理',
    name:'客户名称',
    id:'客户编号',
    callname:'称呼',
    comname:'公司名称',
    phone1:'手机1',
    phone2:'手机2',
    telephone:'电话',
    mainer:'责任人',
    follower:'跟单员',
    phone_status:'电话状态',
    address:'通讯地址',
    remark:'备注',
    normal:'正常',
    nonumber:'空号',
    closephone:'关机',
    noanswer:'无人接听',
    incall:'通话中',
    noinarea:'不在服务区',
    createdate:'创建时间',
    modifydate:'修改时间',
    senior:'高级检索',
    addPerCustomer:'新增个人客户',
    editPerCustomer:'修改个人客户',
    addComCustomer:'新增公司客户',
    editComCustomer:'修改公司客户',
    email:'邮箱',
    nation:'籍贯',
    perinfo:'个人信息',
    orderinfo:'订单属性',
    systeminfo:'系统信息',
    tax_type:'发票类型',
    pay_type:'支付方式',
    money_type:'默认币种',
    checking_date:'对账日期',
    express_type:'常用快递公司',
    mainerdept:'负责人团队',
    lastmodify:'最后操作员',
    lastopttime:'最后操作时间',
    last_time:'最后联系时间',
    creater:'创建者',
    create_time:'创建时间',
    connector:'联系人',
    connector_phone:'联系人手机',
    wait_check_customer:'待识别',
    potential_customer:'潜在客户',
    saleing_customer:'销售中',
    signed_customer:'已签约',
    underMain_customer:'维护中',
    normal_customer:'普通客户',
    vip_customer:'VIP客户',
    big_customer:'大客户',
    small_customer:'微价值客户',
    status:'状态',
    level:'重要程度',
    prop_cus:'客户',
    prop_agent:'代理',
    prop_pub:'公共关系',
    prop_partner:'合作伙伴',
    class_channel:'渠道客户',
    class_terminal:'终端客户',
    corpinfo:'公司信息',
    corp_taxnum:'税号',
    corp_person:'法人',
    corp_regcap:'注册资本',
    corp_time:'成立日期',
    corp_nick:'公司简称',
    corp_phone:'公司电话',
    corp_fax:'公司传真',
    corp_bank:'开户行',
    corp_account:'银行账号',
    corp_type:'行业',
    corp_url:'公司网址',
    corp_addr:'公司地址',
    job:'职位',
    first:'主联系人',
    tax_rate:'开票税率',
    noTaxRat:'不含税',
    userProp:'客户属性',
    null:'无',
    prop:'属性',
    type:'分类',
    record:'跟踪记录',
    product:'产品',
    follow_type:'跟进方式',
    follow_status:'跟进状态',
    follow_text:'内容',
    time:'时间',
    operator:'操作员',
  },
  product_manage:{
    product_manage:'产品管理',
    add_product:'新增产品',
    set_encode:'设置编码',
    def_type:'默认产品类别',
    def_acc:'默认产品精度',
    add_categ:'添加分类',
    update_categ:'修改分类',
    delete_categ:'删除分类',
    categ:'产品分类',
    def_header:'编号自定义头',
    cur_index:'当前序号',
    show_rule:'规则展示',
    tail_len:'尾部长度',
    price_acc:'价格精度',
    unit_acc:'单位精度',
    categ_param_set:'分类参数设置',
    name:'产品名称',
    id:'产品编号',
    update_product:'修改产品',
    look_product:'查看产品',
    
    baseinfo:'基本信息',
    props:'产品属性',
    en_name:'英文名称',
    short_name:'简称',
    help_name:'助记码',
    brand:'产品品牌',
    en_brand:'英文品牌',
    base_unit:'基本计量单位',
    b_unit:'单位',
    base_digit:'单位精度',
    specify_manage:'规格管理',
    specify_info:'规格信息',
    specify_type:'规格类型',
    specify_type_static:'静态规格',
    specify_type_customer:'自定义规格',
    pic:'产品图片',
    buy_props:'采购属性',
    buy_price:'采购价格',
    buy_price_low:'采购最低价格',
    buy_price_high:'采购最高价格',
    bef_tax:'税前',
    aft_tax:'税后',
    rmb:'(人民币)',
    cost_price:'核定成本价',
    buy_quality_check:'收货是否质检',
    other:'其他',
    type:'产品类型',
    type_normal:'标准产品',
    type_service:'服务',
    type_custom:'定制',
    type_service_notify:'向客户提供的服务，服务讲没有入库出库等操作！',
    type_custom_notify:'按客户要求而定做的产品,定制产品将不维护库存!',
    bar_code:'产品条形码',
    status:'产品状态',
    status_normal:'正常',
    status_stop:'停用',
    status_start:'启用',
    bom_research:'BOM反查',
    mul_modify:'批量修改',
    
  },
  specs_manage:{
    manage:'规格管理',
    id:'规格编号',
    name:'规格名称',
    content:'规格内容',
    last_time:'最后编辑时间',
    search_prod:'反查产品',
    add:'新增规格',
    modify:'修改规格',
  },
  button:{
    add:'新增',
    import:'导入',
    export:'导出',
    search:'查询',
    new_order:'新建订单',
    change_to_corp_cus:'转公司客户',
    change_to_per_cus:'转个人客户',
    changge_to_traded:'转已成交客户',
    update:'更新',
    addNewFollowinfo:'新增跟进记录',
    ok:'确定',
    cancel:'取消',
  },
  project_manage:{
    bom_manage:'BOM 管理',
    process_manage:'工序管理',
    process_list:'工序列表',
    bom_list:'BOM 列表',
    bom_list2:'BOM 列表2',
    bom_set:'BOM 设置',
    back:'返回',
    submit:'提交',
    approval_his:'审核历史',
    add_process:'添加工序',
    process_config:'工序配置',

  },
  process:{
    name:'工序编号',
    nick:'工序名称',
    work_hours:'标准工时',
    tech:'加工工艺',
    peple:'人员',
    remark:"备注",
    status:'状态',
    sort:'排序',
    opt:'操作',
    job_fee:'人工费',
    piece_fee:'计件费用',
    time_fee:'计时费用',
    depn_fee:'折旧费',
    share_rate:'分摊系数',
    label:'工序标识',
    entrust:'是否委外',
    sync:'同步BOM',
    desc:'工艺描述',
    peple_mgr:'人员管理',
    opter:'操作员',
    manager:'负责人',
    checker:'质检员',
  },
  bom:{
    id:'BOM编号',
    ver:'版本号',
    custom:'关联客户',
    cus_id:'客户对应编码',
    remark:'BOM备注',
    danwei:'单位',
    cust:'损耗率(%)',
    price:'单价',
    num:'用量',
    totle_price:'总价',
    supporter:'供应商',
    sel_process:'选择工序',
    check_type:'质检方式',
    process_type:'工序标识',
    sel_cus:'选择客户',
    sel_pdt:'选择产品'  ,
    modify_bom:'修改BOM',
    process_count:'工序数',
    part_count:'物料数',
    part_price:'物料费',
    job_price:'人工费',
    cost_price:'成本价',
    last_edit_time:'最后编辑时间'
  },
  purchase:{
    manage:'采购管理',
    list:'采购订单',
    check:'采购订单审核',
    supplier:'供应商',
    supplier_manage:'供应商管理',
    id:'采购单号',
    status:'订单状态',
    receipt_status:'收货状态',
    supplier_sup_id:'供应商单号',
    add:'创建采购单',
    modify:'修改采购单',
    look:'查看采购单',
    supplier_purchase_id:'供应商订单单号',
    dept:'采购部门',
    business_date:'交货日期',
    count:'采购数量',
    cut:'折扣',
    supplier_pdt_id:'供应商产品编号',
    sel_supplier:'选择供应商',
    totle_price:'订单金额',
    receipt_count:'收货数量',
    return_count:'退货数量',
    now_count:'现收货数量',
    remark:'采购备注',
    date:'采购日期',
    source:'来源',
    check_status:'审核状态'
  },
  supplier:{
    status_normal:'正常',
    status_disable:'禁用',
    name:'供应商编号',
    nick:'供应商名称',
    contact:'联系人',
    phone:'联系电话',
    manager:'负责人',
    status:'状态',
    address:'地址',
    remark:'备注',
    add_supplier:'添加供应商',
    update_supplier:'修改供应商',
    base_info:'基本信息',
    type:'公司行业',
    fax:'传真',
    person:'采购人员'
  },
  store:{
    manage:'仓库管理',
    name:"仓库编号",
    nick:'仓库名称',
    prop:'仓库属性',
    manager:'管理员',
    linkman:'联系人',
    phone:'联系电话',
    email:'邮箱',
    zip:'邮编',
    address:'地址',
    remark:'备注',
    alarm_user:'预警人员',
    inventory:'库存管理',
    oeminventory:'委外库存管理',
    inventory_search:'库存查询',
    store:'仓库',
    booktakein:'领料申请单',
    add_booktakein: '新增领料申请单',
    check: '库存盘点',
    check_add:'新增盘点单',
    check_modify:'修改盘点单',
  },
  sale:{
    list:'销售订单',
    item:'销售订单',
    add:'新增销售订单',
    edit:'修改销售订单',
    name:'销售单号',
    cus_sale_name:'客户订单号',
    mondyType:'币种',
    saler:'销售人员',
    follower:'跟单人员',
    saler_dep:'销售部门',
    sale_date:'销售日期',
    same_date:'统一交付日期',
    img:'图片',
    material:'材质',
    type:'类型',
    specs:'规格',
    inventory:'库存',
    count:'销售数量',
    bak_count:'备品',
    delivery:'交货日期',
    bef_tax:'税前单价',
    after_tax:'税后单价',
    large_mod_cost:'大模具费',
    tax_rate:'发票税率',
    totle_price:'总价',
    remark:'销售备注',
    print_factory:'印刷厂',
    print_pos:'印刷位',
    his_price:'历史价格',
    cutome_def_id:'客户订单号',
    status:'入库状态',
    cus_sef_id:'客户订单号',
    price:'销售价格',
    out_list:'销售发货列表',
    out_list_check:'销售出库审核',
    add_out:'新增销售出库单',
    modify_out:'修改销售出库单',
    out:'销售发货',
    out_date:'交货日期',
    dy_count:'订单数',
    lock:'锁定数量',
    return_list:'销售退货列表',
    return:'销售退货',
    add_return:'新增销售退货单',
    modify_return:'修改销售退货单',
    date:'销售日期',
    demand:'销售需求',
    prepare_count:'备货数量',
    need_count:'缺口数量',
    look:'查看销售订单',
    list_PMCCheck:'PMC交期审核',
    list_ProductCheck:'生产经理交期审核',
    list_ProjectCheck:'业务部门经理审核',
    prepare:'销售备料表'
  },
  receipt:{
    get:'采购收货',
    list:'采购收货单',
    add:'新增采购收货单',
    modify:'修改采购收货单',
    id:'收货单编号',
    date:'收货日期',
    remark:'收货备注',
    add_pdt:'选择收货产品',
    price:'单价',
    cut:'折扣',
    tax:'税率',
    totle_price:'总价',
    supplier_pdt_num:'供应商产品编码',
    type:'收货类型',
    user:'收货人员',
    out:'采购退货',
    get_list:'收货单列表',
    out_list:'退货单列表',
    receipt_count:'已收货数量',
    look:'查看收货单',
  },
  quality:{
    list:'采购质检单',
    add:'新增质检单',
    modify:'修改质检单',
    check:'品质检测',
    check_count:'质检单列表',
    user:'质检员',
    date:'质检日期',
    type:'质检方式',
    count:'质检数量',
    good_count:'良品数量',
    bad_count:'不良品数量',
    good_per:'良品率',
    desc:'情况说明',
    result:'质检结果',
    look:'查看质检单'
  },
  putin:{
    list:'采购入库单',
    check:'采购入库单审核',
    add:'新增采购入库单',
    modify:'修改采购入库单',
    good_putin:'良品入库',
    good_putin_list:'良品入库单列表',
    bad_putin:'不良品入库',
    bad_putin_list:'不良品入库单列表',
    date:'入库日期',
    user:'入库人员',
    putin_good:'已入库良品数量',
    putin_bad:'已入库不良品数量',
    count:'入库数量',
    remark:'备注',
    look:'查看采购入库单'
  },
  return:{
    list:'采购退货出库',
    add:'新增采购退货出库',
    modify:'修改采购退货出库',
    num:'出库单编号',
    user:'领取人员',
    date:'出库日期',
    count:'已退货数量',
    count2:'退货数量',
    remark:'退货原因',
    look:'查看采购退货出库'
  },
  inventory:{
    list:'库存查询',
    count:'库存数量',
    cur_count:'当前库存',
    avalid_count:'可用数量',
    lock_count:'锁定数量',
    bad_count:'不良数量',
    price_bef_tax:'货物价值(未税)',
    price_aft_tax:'货物价值(含税)',
    price_avr_bef_tax:'均价(未税)',
    price_avr_aft_tax:'均价(含税)',
    last_date:'最后变动时间',
    detail:'库存明细',
    detail_list:'进出流水',
    time:'时间',
    look:'查看',
    old_count:'初始数量',
    span_count:'变化数量',
    left_count:'库存余额',
    avali_count:'可用库存',
    lock:'锁定',
    unlock:'解锁',
    input_count:'数量',
    date:'入库日期',
    outlist:'出库单',
    addoutlist:'新增出库单',
    inlist:'入库单',
    addinlist:'新增入库单',
    changelist:'仓库调拨',
    addchange:'新增仓库调拨',
  },
  presale:{
    name:'申报单号',
    list:'报价单',
    project_list:'工程报价单',
    review_list:'报价审核',
    add:'新增售前报价单',
    modify:'修改售前报价单',
    mgr:'报价管理',
    engineer_buy:'工程师/采购',
    specs_weight:'规格/克重',
    rule:'工艺要求',
    print:'Logo要求/印刷',
    count:'报价数量',
    color:'颜色',
    pack_requir:'包装要求',
    other:'其他',
    hurry:'紧急程度',
    img:'图片',
    status:'状态',
    create_date:'创建时间',
    input_base_price:'填写出厂报价',
    detail:'申报明细',
    base_price_list:'出厂报价单',
    final_price_list:'成交报价单',
    opt:'意见',
    down_excel:'下载报价',
    look:'查看售前报价单'
  },
  base_price:{
    main:'主体工序',
    add:'新增',
    set_price:'工程报价',
  },
  status:{
    wait_base_price:'等待出厂报价',
    wait_final_price:'等待成交报价',
    wait_review:'等待审核',
    review_back:'审核拒绝',
    wait_cus_comfirm:'等待客户确认',
    cus_comfirm:'客户已确认',
    btn_review:'审核报价',
  },
  saleout:{
    add:'新增销售发货单',
    modify:'修改销售发货单',
    id:'发货单编号',
    add_pdt:"选择发货产品",
    get_list:'发货单列表',
    status:'发货状态',
    date:'发货日期',
    count:'已发数量',
    left_count:'未发数量',
    now_count:'发货数量',
    remark:'备注',
    look:'查看销售发货单'
  },
  salereturn:{
    num:'退货单号',
    can_count:'可退数量',
    list:"退货单列表",
  },
  saledemand:{
    list:'销售产品需求表',
    totle:'需求总量',
    outed:'已发货',
    returned:'已退货',
    cur:'当前需求',
    locked:'锁定数量',
    canuse:'可用库存',
    curbuy:'正在采购',
    curreceive:'正在收货',
    cursc:'正在生产',
    curoutbuy:'正在委外',
    totlesup:'总供应',
    curneed:'目前缺口',
    detail:'详情',
    tobuy:'转采购',
    toProduct:'转生产',
    toOut:'转委外',
    refType:'关联类型',
    refNum:'关联单号',
    refCus:'关联客户',
  },
  oem:{
    mgr:'委外管理',
    parter:'受托商列表',
    addparter:'新增受托商',
    modifyparter:'修改受托商',
    order_list:'委托生产单',
    add_order:'新增委托生产单',
    modify_order:'修改委托生产单',
    name:'委外单号',
    opter:'委外下单人员',
    date:'下单日期',
    pay_type:'支付方式',
    task_num:'任务单号',
    opt_bef_tax:'加工费(税前)',
    opt_aft_tax:'加工费(税后)',
    count:'委外数量',
    min_pack:'最小包装',
    mgr_man:'负责人',
    sell_order_num:'关联单号',
    remark:'委外备注',
    sel_sell:'关联销售单',
    t_modify:'修改',
    t_del:'删除',
    t_wl:'物料',
    t_wlxq:'物料需求',
    t_wwfl:'委外发料',
    t_flmx:'发料明细',
    t_wwll:'委外领料',
    t_llmx:'领料明细',
    t_wlth:'物料退回',
    t_thmx:'退料明细',
    t_ylfh:'余料返回',
    t_fhmx:'返回明细',
    t_cpsh:'成品收货',
    t_shmx:'收货明细',
    t_cpth:'良品退货',
    t_ttmx:'良品退货明细',
    t_blth:'不良退货',
    t_blthmx:'不良退明细',
    t_rkmx:'入库明细',
    t_tbbom:'同步BOM',
    t_fjfy:'附件费用',
    t_lock:'库存锁定',
    wlneed:"物料需求表",
    wlprepare:'物料备料表',
    wl_name:'物料编号',
    wl_nick:'物料名称',
    parter_stone:'受托商可用库存',
    parter_lock:'受托商锁定库存',
    out_count:'已出库',
    takeout_list:'发料列表',
    add_takeout:'新增发料',
    modify_takeout:'修改发料',
    wl_lock:'物料锁定',
    pay_per:'赔付税后',
    pay_per_aft:'赔付税前',
    get:'已领料',
    btn_modify:'修改',
    takein_list:'领料列表',
    add_takein:'新增领料',
    look:'查看委外生产单',
    order_list_check:'委外生产单审核',
    modify_takeout_look:'查看发料',
    modify_takein:'修改领料',
    modify_takein_look:'查看领料',
    channel_type:'退货类型'
  },
  parter:{
    name:'受托商编号',
    nick:'受托商名称',
    pay_type:'付款方式',
    def_tax:'默认税率',    
    sel_parter:'选择受托商',
    stone:'委外库存'
  },
  takeout:{
    id:'发料单号',
    one_use:'单个用量',
    jh_count:'计划数量',
    actual_count:'实际发料数量',
    parter_stone:'委外商库存',
    cur_lock:'此单锁定',
    need:'所需数量',
    out:'发料数量'
  },
  parterstone:{
    type:'类型',
    liushui:'委外仓库流水',
  },
  takein:{
    id:'领料单号',
    date:'领料日期',
    out_stone:'委外商可用库存',
    actual_count:'实际领料数量',
    back:'已退数量',
    in:'领料数量',
    left:'剩余没领',
    list:'领料列表',
  },
  takechannel:{
    list:'物料退回单',
    add:'新增退料',
    modify:'修改退料',
    id:'退料单号',
    date:'退料日期',
    user:'申请人',
    actual_count:"已领数量",
    canback:'可退数量',
    add_takechannel:'新增退料',
    look:'查看退料',
    count:'退料数量'
  },
  remain:{
    add:'新增余料返回',
    modify:'修改余料返回',
    user:'操作员',
    parter_val_stone:'委外商可用库存',
    parter_cur_lock:'委外商此单锁定',
    count:'返料数量',
    id:'返料单号',
    oemremian_check:'委外返料单审核',
    look:'查看余料返回',
    oemremian:'委外返料单',
  },
  drawin:{
    list:'委外收货列表',
    add:'新增收货单',
    modify:'修改委外收货单',
    id:'收货单号',
    date:'收货日期',
    pre_count:'已收货',
    count:'收货数量',
    return:'已退货',
    instone:'已入库',
    good:'良品数量',
    bad:'不良数量',
    look:'查看委外收货单'
  },
  qualitycheck:{
    list:'委外质检单列表',
    add:'新增委外质检单',
    modify:'修改委外质检单',
    name:'质检单号',
  },
  oemputin:{
    list:'委外入库单列表',
    add:'新增委外入库单',
    modify:'修改委外入库单',
    id:'入库单号',
  },
  oemreturn:{
    list:'成品退货单',
    add:'新增成品退货单',
    modify:'修改成品退货单',
    check:'成品退货单审核',
    look:'查看成品退货单',
    id:'退货单号'
  },
  cmd:{
    check:'审核',
    checked:'已审核',
    sub_check:'提交审核',
    putin:'入库',
    takeout:'出库'
  },
  sysmgr:{
    sysmgr:'系统管理'
  },
  dealtime:{
    list:'交货日期查询',
  },
  monthlycheck:{
    title:'月结对账',
    list:'销售月结对账单'
  },
  print:{
    list:'打印模板',
  },
  finance:{
    sell_montylycheck:'销售月结对账单',
    buy_montylycheck:'采购月结对账单',
    oem_montylycheck:'委外月结对账单',
    cus_list:'销售月结客户列表',
    sell_add:'新增销售月结对账单',
    sell_info:'销售月结对账单详情',
    inout_detail:'收支明细',
    bank_manage:'账号维护',
    supplier_list:'采购月结供应商列表',
    buy_add:'新增采购月结对账单',
    buy_info:'采购月结对账单详情',
    parter_list:'委外月结受托方列表',
    oem_add:'新增委外月结对账单',
    oem_info:'委外月结对账单详情',
    sell_montylycheck_tj:'销售月结统计表',
    buy_montylycheck_tj:'采购月结统计表',
    oem_montylycheck_tj: '委外月结统计表',
    paymentapplication:'付款申请单',
    paymentapplication_add:'新增付款申请单',
  },
  report:{
    sell_reportall:'销售统计总表',
    sell_cus:'销售统计(客户)',
    sell_user:'销售统计(业务员)',
    sell_out_cus:'出库统计(客户)',
    sell_out_user:'出库统计(业务员)',
    sell_value_cus:'客户毛利统计表',
    sell_value_order:'订单毛利统计表',
    sell_value_out:'发货毛利统计表',
    sell_value_user:'员工毛利统计表',
    sell_value_sell:'销售利润统计表',

    buy_reportall:'采购统计总表',
    buy_supplier:'采购统计(供应商)',
    buy_in_supplier:'采购入库统计(供应商)',

    oem_reportall:'委外统计报表',
    stone_report_month:'仓库月报表',
    stone_report_month_detail:'仓库月报表明细',
  },
  human: {
    jobmanage: '职务管理',
    lcmlist: '合同列表',
    addlcm: '新增员工合同',
    nolcmwarning: '未签合同预警',
    nearwarning: '合同到期预警',
    zzwarning: '转正到期预警',
    holidaymanage: '请假管理',
    zz: '在职员工花名册',
    lz: '离职员工花名册',
    holidayadd: '新增请假',
    yearholiday: '年休假管理',
    holidytype:'请假类型',
  },
  
  mobile: {
    main:'主页'
  },



  pay:{
    paymanager: '支付管理',
    paytx: '提现管理',
    paycz: '充值管理',
    czconfig: '收款账号配置',
    cusczconfig:'特定用户充值',
    exchangecode:'兑换码管理',
  },
  systemconfig: {
    hashconfig: '配置管理',
    jiangchiconfig: 'LABA奖池配置',
    fishconfig:'捕鱼奖池配置'
  },
  tongji: {
    datatongji:'数据分析',
    playermanage: '用户统计',
    channelmanage: '渠道统计',
    cashwaterlist: '资金流水',
    gamewinlosemanage:'游戏输赢统计',
    downloadlist: '下载统计明细',
    registelist: '注册统计明细',
    playertongji: '玩家数据明细',
  }
}

///////////////////////////////////////////////////////
// 将日期类型转换成字符串型格式 yyyy-MM-dd hh:mm:ss
////////////////////////////////////////////////////////
export function DateFormat(DateIn, sFormat) {
    if (DateIn == null) {
        return "";
    }

    var sYear = (DateIn.getYear() < 1900) ? (1900 + DateIn.getYear()) : DateIn.getYear();
    var sMonth = DateIn.getMonth() + 1;
    var sDay = DateIn.getDate();
    var sHour = DateIn.getHours();
    var sMinute = DateIn.getMinutes();
    var sSecond = DateIn.getSeconds();
    var sReturn = sFormat;




    sYear = "" + sYear;
    if (sMonth < 10) {
        sMonth = "0" + sMonth;
    } else {
        sMonth = "" + sMonth;
    }
    if (sDay < 10) {
        sDay = "0" + sDay;
    } else {
        sDay = "" + sDay;

    }

    if (sHour < 10) {
        sHour = "0" + sHour;
    } else {
        sHour = "" + sHour;
    }

    if (sMinute < 10) {
        sMinute = "0" + sMinute;
    } else {
        sMinute = "" + sMinute;
    }

    if (sSecond < 10) {
        sSecond = "0" + sSecond;
    } else {
        sSecond = "" + sSecond;
    }

    sReturn = sReturn.replace("yyyy", sYear);
    sReturn = sReturn.replace("MM", sMonth);
    sReturn = sReturn.replace("dd", sDay);
    sReturn = sReturn.replace("hh", sHour);
    sReturn = sReturn.replace("mm", sMinute);
    sReturn = sReturn.replace("ss", sSecond);


    return sReturn;
}

export function MaxDay(year, month) {

    var new_year = year; //取当前的年份
    var new_month = month++; //取下一个月的第一天，方便计算（最后一天不固定）
    if (month > 12) //如果当前大于12月，则年份转到下一年
    {
        new_month -= 12; //月份减
        new_year++; //年份增
    }
    var new_date = new Date(new_year, new_month, 1); //取当年当月中的第一天
    return new Date(new_date.getTime() - 1000 * 60 * 60 * 24).getDate();

}
//将一个数字转化为一个字符串 比如104秒 是1分钟44秒  
//传入数字 104 得到 字符串 00：01：44 
export function formatTimeLen(nLen) {
    var sTime = "";
    var nLength = nLen % (60 * 60 * 24);
    var nHour = parseInt(nLength / (60 * 60));
    nLength = nLength % (60 * 60);
    var nMinute = parseInt(nLength / 60);
    var nSeconde = nLength % 60;

    if (nHour < 10) {
        sTime += "0";
    }
    sTime += nHour + ":";

    if (nMinute < 10) {
        sTime += "0";
    }
    sTime += nMinute + ":";

    if (nSeconde < 10) {
        sTime += "0";
    }
    sTime += nSeconde;


    return sTime;

}

//判断一个数字是否为整字
export function isInteger(str) {
    var sRegExp = "^[-]{0,1}[0-9]{1,}$";
    var rRegExp = new RegExp(sRegExp, "g");
    if (str.match(rRegExp) == null) {
        return false;
    }
    return true;
}




//判断是否为手机号码
export function isMobile(str) {
    var sRegExp = "^\\d{11}$";
    var rRegExp = new RegExp(sRegExp, "g");
    if (str.match(rRegExp) == null) {
        return false;
    }
    return true;
}




//判断是否为固定电话：电话号码正则表达式（3-4位区号，7-8位直播号码，1－4位分机号）
export function isTelephone(str) {
    var sRegExp = /^((\d{7,8})|(\d{4}|\d{3})-(\d{7,8})|(\d{4}|\d{3})-(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d)|(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d))$/;
    var rRegExp = new RegExp(sRegExp, "g");
    if (str.match(rRegExp) == null) {
        return false;
    }
    return true;
}




//判断一个数字是否为浮点数
export function isDecimal(str) {
    var sRegExp = "^[-]{0,1}[\\d]{1,}[\\.]{0,1}[\\d]{0,10}$";
    var rRegExp = new RegExp(sRegExp, "g");
    if (str.match(rRegExp) == null) {
        return false;
    }
    return true;
}
//判断一个日期格式是否正确
export function IsDate(str) {
    var arr = str.split("-");
    if (arr.length == 3) {
        var intYear = parseInt(arr[0], 10);
        var intMonth = parseInt(arr[1], 10);
        var intDay = parseInt(arr[2], 10);
        if (isNaN(intYear) || isNaN(intMonth) || isNaN(intDay)) {
            return false;
        }
        if (intYear > 2100 || intYear < 1900 || intMonth > 12 || intMonth < 0 || intDay > 31 || intDay < 0) {
            return false;
        }
        if ((intMonth == 4 || intMonth == 6 || intMonth == 9 || intMonth == 11) && intDay > 30) {
            return false;
        }
        if (intMonth == 2) {
            if (intYear % 100 == 0 && intYear % 400 || intYear % 100 && intYear % 4 == 0) {
                if (Number(intDay) > Number(29)) return false;
            } else {
                if (Number(intDay) > Number(28)) return false;
            }
        }
        return true;
    } else {
        return false;
    }
}

//从数据的星期返回一个中文的星期
export function getWeekOfInteger(n) {
    var weekDays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
    return weekDays[n - 1];
}


export function toChina(n) {
    if (!isNaN(n)) {
        n = n.toFixed(2);

        var fraction = ['角', '分'];
        var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
        var unit = [
            ['元', '万', '亿'],
            ['', '拾', '佰', '仟']
        ];
        var head = n < 0 ? '欠' : '';
        n = Math.abs(n);
        var s = '';
        for (var i = 0; i < fraction.length; i++) {
            var nFormatN = n * 10 * Math.pow(10, i);
            nFormatN = DecimalFormat(Number(nFormatN).toFixed(10));
            s += (digit[Math.floor(nFormatN) % 10] + fraction[i]).replace(/零./, '');
        }
        n = Math.floor(n);
        for (var i = 0; i < unit[0].length && n > 0; i++) {
            var p = '';
            for (var j = 0; j < unit[1].length && n > 0; j++) {
                p = digit[n % 10] + unit[1][j] + p;
                n = Math.floor(n / 10);
            }
            s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
        }
        return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零');
    }
}


//计算一个字符串表达式的值
function Express(source) {
    var n = source.indexOf("=");
    var s = source;
    if (n > 0) {
        s = source.substr(0, n);
    }
    var result = 0;
    //判断是否存在加减乘法，如果存在，则进行运算，如果只是单一数字，则不进行运算
    var n1 = s.indexOf("+");
    var n2 = s.indexOf("-");
    var n3 = s.indexOf("*");
    var n4 = s.indexOf("/");

    if (n1 > 0 || n2 > 0 || n3 > 0 || n4 > 0) {
        try {
            result = eval(s);
            s = s + " = " + result;
        } catch (e) {}
    }
    return s;
}
//进行规格转化
function ConvertProductNorms(sNorms) {
    var sList = "";
    if (sNorms != null && sNorms != "") {
        var sNormsList = sNorms.split("§");
        for (var i = 0; i < sNormsList.length; i++) {
            var sNormsListItem = sNormsList[i].split("┆");
            var sIndex = sNormsListItem[0];
            var sTempItem = "";
            if (Number(sIndex) < 10) {
                sTempItem = "C00" + sIndex;
            } else {
                sTempItem = "C0" + sIndex;
            }
            if (sList != "") {
                sList += "∩";
            }
            sList += sTempItem + "↑" + sNormsListItem[1];

        }

    }
    return sList;
}
//进行属性转化
function ConvertProductProperty(sProperty) {
    var sList = "";
    if (sProperty != null && sProperty != "") {
        var sPropertyList = sProperty.split("§");
        for (var i = 0; i < sPropertyList.length; i++) {
            var sPropertyListItem = sPropertyList[i].split("┆");
            var sIndex = sPropertyListItem[0];
            var sTempItem = "";
            if (Number(sIndex) < 10) {
                sTempItem = "P00" + sIndex;
            } else {
                sTempItem = "P0" + sIndex;
            }
            if (sList != "") {
                sList += "∩";
            }
            sList += sTempItem + "↑" + sPropertyListItem[1];
        }
    }
    return sList;
}
//处理js中toFixed方法四舍五入的坑
function HzxToFixed(data, len) {
    var numbers = '';
    // 保留几位小数后面添加几个0
    for (var i = 0; i < len; i++) {
        numbers += '0';
    }
    var s = 1 + numbers;
    // 如果是整数需要添加后面的0
    var spot = "";
    if (numbers != '') {
        spot = "." + numbers;
    }
    // Math.round四舍五入
    //  parseFloat() 函数可解析一个字符串，并返回一个浮点数。
    var value = Math.round(parseFloat(data) * s) / s;
    // 从小数点后面进行分割
    var d = value.toString().split(".");
    if (d.length == 1) {
        value = value.toString() + spot;
        return value;
    }
    if (d.length > 1) {
        if (d[1].length < 2) {
            value = value.toString() + "0";
        }
        return value;
    }
}

function DecimalFormat(number) {
    var sReturn = "";
    var sNumber = Number(number).toFixed(10);
    if (sNumber.indexOf('.') != -1) { //有小数点，去掉小数点后的0
        for (var i = 10; i > 0; i--) {
            var nLen = sNumber.length;
            var s = sNumber.charAt(nLen - 1); //取最后一位
            if (s == "0") { //如果是零，则截取，放弃最后一位，如果不是，跳出循环
                sNumber = sNumber.substr(0, nLen - 1);
            } else {
                break;
            }
        }
    }
    var nLen2 = sNumber.length;
    var sLast = sNumber.charAt(nLen2 - 1);
    if (sLast == ".") { //检查最后一位是不是小数，如果是，则去除小数点
        sReturn = sNumber.substr(0, nLen2 - 1);
    } else {
        sReturn = sNumber;
    }
    return sReturn;
}
//随机数生成 len生成的长度
function randomString(len) {
    let pos
    var str = "",
        arr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k',
            'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
            'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F',
            'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
            'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
        ];
    for (var i = 0; i < len; i++) {
        pos = Math.round(Math.random() * (arr.length - 1));
        str += arr[pos];
    }
    return str;
}

export function randomNumber(len) {
    let pos
    var str = "",
        arr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    for (var i = 0; i < len; i++) {
        pos = Math.round(Math.random() * (arr.length - 1));
        str += arr[pos];
    }
    return str;
}
//判断值是否为undefined，是则给默认值
function getValue(sValue, sDefault) {
    var sReturn = sValue;
    if (typeof(sValue) == 'undefined') {
        sReturn = sDefault;
    }
    sReturn = $.trim(sReturn);
    return sReturn;
}

function HiddenPhoneNumber(sPhone) {
    var sReturn = "";
    var nLength = sPhone.length;
    if (sPhone.length < 7) {
        sReturn = sPhone;
    } else if (nLength < 9) {
        sReturn = sPhone.substring(0, 2) + RepeatNumber(nLength - 4) + sPhone.substring(nLength - 2, nLength);

    } else {
        sReturn = sPhone.substring(0, 3) + RepeatNumber(nLength - 6) + sPhone.substring(nLength - 3, nLength);

    }
    return sReturn;
}

function RepeatNumber(n) {
    var sReturn = "";
    for (var i = 0; i < n; i++) {
        sReturn += "*";
    }
    return sReturn;
}

//金额格式化   nMoneyFormat   1:千分位  2：万分位  0：不格式化
function HzxMoney(sMoney, nMoneyFormat) {
    nMoneyFormat = Number(nMoneyFormat);
    var sReturn = "";
    if (nMoneyFormat == 1) {
        sReturn = HzxSplit(sMoney, 3, ",");
    } else if (nMoneyFormat == 2) {
        sReturn = HzxSplit(sMoney, 4, "'");
    } else {
        sReturn = sMoney;
    }
    return sReturn;
}

function HzxSplit(sValue, nSplitLen, sSplitChar) {
    sValue = sValue + "";
    let nNegative = sValue.indexOf('-');
    let bNegative = false; //是否存在负数
    if (nNegative != -1) {
        bNegative = true;
        sValue = sValue.substring(nNegative + 1, sValue.length);
    }

    let sMoney = "";
    let nPoint = sValue.indexOf('.');
    let sBeginPoint = ""; //整数部分
    let sEndPoint = ""; //小数部分
    if (nPoint > 0) { //含有小数
        sBeginPoint = sValue.substring(0, nPoint);
        sEndPoint = sValue.substring(nPoint + 1);
    } else {
        sBeginPoint = sValue;
    }

    let dLen = sBeginPoint.length;
    let d = Number(nSplitLen);
    var tempn = Number(dLen / d);
    let nBeginLen = Math.ceil(tempn);

    let list = new Array();
    for (let i = 0; i < nBeginLen; i++) {
        var nBeginIndex = sBeginPoint.length - nSplitLen * (i + 1);
        var nEndIndex = sBeginPoint.length - nSplitLen * i;
        if (nBeginIndex < 0) {
            nBeginIndex = 0;
        }
        if (nEndIndex < 0) {
            nEndIndex = 0;
        }
        var s = sBeginPoint.substring(nBeginIndex, nEndIndex);
        list.push(s);
    }
    for (let i = list.length - 1; i >= 0; i--) {
        if (sMoney != "") {
            sMoney += sSplitChar;
        }
        sMoney += list[i];
    }
    if (nPoint > 0) {
        sMoney = sMoney + "." + sEndPoint;
    }
    if (bNegative) {
        sMoney = "-" + sMoney;
    }
    return sMoney;
}

function HzxReplaceNo(sNo) {
    return sNo.replace(/[\\]/g, "\\\\").replace(/[\']/g, "\\\'");
}